import React, { forwardRef, useRef, useEffect, useState } from "react";
import { getPrincipaList } from "@/service/api/L3Sevice.js";
import {
  Stack,
  Grid,
  Button,
  Typography,
  InputLabel,
  FormHelperText,
  OutlinedInput,
  InputAdornment,
  InputBase,
} from "@mui/material";
import ZKSelect from "@/components/ZKSelect";
import { useTranslation } from "react-i18next";
const MerchantSelect = forwardRef((props, ref) => {
  const [merchantList, setMerchantList] = useState([]);
  const [brandCooperate, setNrandCooperate] = useState("");
  const [error, setError] = useState("");
  const { t } = useTranslation();
  const { retailKey } = props;
  const getMerchant = () => {
    getPrincipaList(1).then((res) => {
      setMerchantList(res.data);
    });
  };
  useEffect(() => {
    getMerchant();
  }, []);
  useEffect(() => {
    setError(props.error);
  }, [props.error]);
  useEffect(() => {
    let retailData = localStorage.getItem(retailKey);
    if (retailData) {
      setNrandCooperate(retailData);
    }
  }, []);

  const handleChange = (e) => {
    let value = e.target.value;
    setNrandCooperate(value);
    if (props.onChange) {
      localStorage.setItem(retailKey, value);
      props.onChange(value);
    }
  };
  const handleBlur = (e) => {};
  const onClear = (e) => {
    setNrandCooperate("");
    if (props.onClear) {
      localStorage.removeItem(retailKey);
      props.onClear("");
    }
  };

  return (
    <Stack
      sx={{
        position: "relative",
      }}
      spacing={1}>
      {props.label && (
        <InputLabel htmlFor="brandCooperate">{props.label}</InputLabel>
      )}
      <ZKSelect
        name="brandCooperate"
        value={brandCooperate}
        onChange={handleChange}
        onBlur={handleBlur}
        size="small"
        options={merchantList}
        {...props.style}
        onClear={() => {
          onClear();
        }}
        placeholder={t("common.common_please_select_retail")}
        error={Boolean(error)}
      />
      {error && (
        <FormHelperText
          sx={{
            position: "absolute",
            bottom: "-25px",
          }}
          error>
          {error}
        </FormHelperText>
      )}
    </Stack>
  );
});
export default MerchantSelect;
