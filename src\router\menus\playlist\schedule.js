//  排期发布
const scheduleRoute = [
  {
    path: "/playList/schedule",
    component: () => import("@/pages/playlist/schedule"),
    meta: {
      title: "播放计划发布",
      i18n: "media_schedule_push",
      id: "516489808558686209",
    },
  },
  {
    path: "/playList/schedule/commonSchedule/add",
    component: () =>
      import("@/pages/playlist/schedule/components/AddCommonSchedule"),
    meta: {
      title: "新增数字标牌播放计划",
      i18n: "media_schedule_add_common",
      id: "00000000",
    },
  },
  {
    path: "/playList/schedule/commonSchedule/update",
    component: () =>
      import("@/pages/playlist/schedule/components/updateCommonSchedule"),
    meta: {
      title: "修改数字标牌播放计划",
      i18n: "media_schedule_edit_common",
      id: "00000000",
    },
  },
  {
    path: "/playList/schedule/linkSchedule/update",
    component: () =>
      import("@/pages/playlist/schedule/components/updateLinkSchedule"),
    meta: {
      title: "修改联屏播放计划",
      i18n: "media_schedule_edit_link",
      id: "00000000",
    },
  },
  {
    path: "/playList/schedule/linkSchedule/add",
    component: () =>
      import("@/pages/playlist/schedule/components/AddLinkSchedule"),
    meta: {
      title: "新增联屏播放计划",
      i18n: "media_schedule_add_link",
      id: "00000000",
    },
  },
];

export default scheduleRoute;
