import { useEffect, useRef, useState } from "react"
import * as echarts from "echarts";
import { useTranslation } from "react-i18next";

const DigitalSignageChart = (props) => {
    const chartRef = useRef(null);
    const [myEcharts, setMyEcharts] = useState(null);
    const { t } = useTranslation();

    const initChart = () => {
        let chart = echarts.init(chartRef.current, null, { renderer: "svg" });
        setMyEcharts(chart)
        // 设置初始大小
        chart.resize();
        // 监听窗口大小变化，自动调整图表大小
        window.addEventListener("resize", handleResize);
        const options = getOptions(props.chartData);
        chart.setOption(options);
    }

    const handleResize = () => {
        if (myEcharts) {
            myEcharts.resize();
        }
    };

    useEffect(() => {
        // 在组件挂载时进行初始化
        initChart();
        return () => {
            window.removeEventListener("resize", handleResize);
            if (myEcharts) {
                myEcharts.dispose();
                setMyEcharts(null)
            }
        };
    }, [])

    useEffect(() => {
        
        if (myEcharts === null) {
            initChart()
        } else {
            const options = getOptions(props.chartData);
            myEcharts.setOption(options);
        }
    }, [props.chartData])

    const getOptions = (data) => {
        let xAxis = []
        let valueArry = []
        data.forEach((item) => {
            xAxis.push(item.fieldName)
            valueArry.push(item.total)
        })
        return {
            title: {
                text: t('outlet.total_devices_installed'),
                left: '1%',
                top: '35%',
                textStyle:{
                    fontSize:'18px'
                }
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: [
                    { name: t('outlet.total_devices_installed'), icon: 'circle' }
                ],
                align: 'left',
                orient: 'vertical',
                left: '1%',
                top: '45%',
                icon: 'circle',
                itemWidth: 30,
                itemHeight: 27,
                itemGap: 20,//图例间距
                textStyle: {
                    fontSize: '20px',
                    fontWeight: 'bold',
                    lineHeight: 10,
                    rich: {
                        a: {
                            align: 'left',
                            color: '#7a7a7a',
                            fontSize: '16px',
                        }
                    },
                },
                formatter: function (name) {
                    return `{a| ${name} }`;
                }
            },
            grid: {
                left: '25%'
            },
            xAxis: {
                type: 'category',
                data:xAxis
            },
            yAxis: {
                type: 'value',
                axisTick: {
                    // 轴刻度
                    show: true,
                },
                axisLabel: {
                    // 轴文字
                    show: true,
                    color: "#000000",
                    fontSize: 12,
                },
                axisLine: {
                    // 轴线
                    show: true,
                    color: '#268C8C',
                }
            },
            series: [
                {
                    name: t('outlet.total_devices_installed'),
                    type: 'bar',
                    data: valueArry,
                    barWidth:'20px',
                    itemStyle: {
                        normal: {
                            color: '#85bc56'
                        }
                    }
                }
            ]
        };

    }
    return <div style={{ width: '100%', height: '100%' }} ref={chartRef}></div>
}

export default DigitalSignageChart