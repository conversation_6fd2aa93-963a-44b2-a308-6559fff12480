import React from "react";
import { Grid } from "@mui/material";
import { styled } from "@mui/material/styles";
import { getComponentId } from "../common/utils";
import { toast } from "react-toastify";
import NewsComponent from "./NewsComponent";
import TimeComponent from "./TimeComponent";
import WeatherComponent from "./WeatherComponent";
import LiveComponent from "./LiveComponent";
import { message } from "../common/i18n";
import { useConfirm } from "@/components/zkconfirm";
import { useTranslation } from "react-i18next";
import { pageDuration } from "../common/utils";
const CustomButtom = styled(Grid)(({ theme }) => ({
  "&.MuiGrid-root": {
    width: "100%",
    height: "80px",
    background: "#ffffff",
    borderRadius: "10px",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: "10px",
    cursor: "pointer",
  },
  "&:hover": {
    background: "#ffffffdb",
  },
}));

const WidgetComponent = (props) => {
  const confirm = useConfirm();
  const { t } = useTranslation();
  const currentType = props.currentType;
  const setCurrentType = props.setCurrentType;
  const currentPageIndex = props.currentPageIndex;
  const currentComponentId = props.currentComponentId;
  const setCurrentComponentId = props.setCurrentComponentId;
  const setCurrentComponentIndex = props.setCurrentComponentIndex;
  const pages = props.pages;
  const setPages = props.setPages;
  const activeTempIndex = props.activeTempIndex;
  const setActiveTempIndex = props.setActiveTempIndex;

  const addTime = () => {
    const newPages = JSON.parse(JSON.stringify(props.pages));
    let currentPage = newPages[currentPageIndex];
    if (currentPage.isTemplate) {
      if (activeTempIndex === "") {
        toast.success(message("editor_select_template_tip"));
      } else {
        let ComponentId = getComponentId(newPages);
        currentPage.tempLayout[activeTempIndex].componentList.push({
          title: "editor_time", //标题
          name: "editor_time", //名称
          type: "ZKTecoTime", //组件类型
          left: 12,
          top: 15,
          width: 500,
          height: 50,
          zIndex: 50,
          hide: false, //是否隐藏
          anim: "pulse", //动画样式名称
          fontColor: "#262626", //颜色
          font: "MyriadPro-Light", //字体
          fontSize: 32, //字体大小
          isBold: false, //是否加粗
          isItaly: true, //是否斜体
          isUnderline: true, //下划线
          textAlign: "center", //字体位置（居中还是左对齐右对齐等）
          lineHeight: 2.5, //行高
          rotate: 0, //宣传
          isScroll: true, //是否滚动
          duration: 60, //默认时长
          scrollDirection: "left", //滚动方向
          speed: 60, //滚动速度
          format: "0",
          transparency: 1,
          componentId: ComponentId,
        });
        setCurrentType("ZKTecoTime");
        setPages([...newPages]);
        setCurrentComponentId(ComponentId);
        let index =
          currentPage.tempLayout[activeTempIndex].componentList.length - 1;
        setCurrentComponentIndex(index);
      }
    } else {
      let ComponentId = getComponentId(newPages);
      newPages[currentPageIndex].componentList.push({
        title: "editor_time", //标题
        name: "editor_time", //名称
        type: "ZKTecoTime", //组件类型
        left: 12,
        top: 15,
        width: 500,
        height: 50,
        zIndex: 50,
        hide: false, //是否隐藏
        anim: "pulse", //动画样式名称
        fontColor: "#262626", //颜色
        font: "MyriadPro-Light", //字体
        fontSize: 32, //字体大小
        isBold: false, //是否加粗
        isItaly: true, //是否斜体
        isUnderline: true, //下划线
        textAlign: "center", //字体位置（居中还是左对齐右对齐等）
        lineHeight: 2.5, //行高
        rotate: 0, //宣传
        isScroll: true, //是否滚动
        duration: 60, //默认时长
        scrollDirection: "left", //滚动方向
        speed: 60, //滚动速度
        format: "0",
        transparency: 1,
        componentId: ComponentId,
      });
      setCurrentType("ZKTecoTime");
      setPages([...newPages]);
      setCurrentComponentId(ComponentId);
      let index = newPages[currentPageIndex].componentList.length - 1;
      setCurrentComponentIndex(index);
    }
  };

  const addWeather = () => {
    const newPages = JSON.parse(JSON.stringify(props.pages));
    let currentPage = newPages[currentPageIndex];
    if (currentPage.isTemplate) {
      if (activeTempIndex === "") {
        toast.success(message("editor_select_template_tip"));
      } else {
        let weatherList = currentPage.tempLayout[
          activeTempIndex
        ].componentList.filter((item) => {
          if (item.type === "ZKTecoWeather") {
            return true;
          } else {
            return false;
          }
        });

        if (weatherList.length < 1) {
          let ComponentId = getComponentId(newPages);
          currentPage.tempLayout[activeTempIndex].componentList.push({
            title: "editor_weather", //标题
            name: "editor_weather", //名称
            type: "ZKTecoWeather", //组件类型
            fontColor: "#262626",
            left: 12,
            top: 15,
            width: 350,
            height: 130,
            zIndex: 50,
            hide: false, //是否隐藏
            url: "",
            componentId: ComponentId,
          });

          setCurrentType("ZKTecoWeather");
          setPages([...newPages]);
          setCurrentComponentId(ComponentId);
          let index =
            currentPage.tempLayout[activeTempIndex].componentList.length - 1;
          setCurrentComponentIndex(index);
        } else {
          toast.success(message("editor_template_addweather_tip"));
        }
      }
    } else {
      let weatherList = newPages[currentPageIndex].componentList.filter(
        (item) => {
          if (item.type === "ZKTecoWeather") {
            return true;
          } else {
            return false;
          }
        }
      );

      if (weatherList.length < 1) {
        let ComponentId = getComponentId(newPages);
        newPages[currentPageIndex].componentList.push({
          title: "editor_weather", //标题
          name: "editor_weather", //名称
          type: "ZKTecoWeather", //组件类型
          fontColor: "#262626",
          left: 12,
          top: 15,
          width: 350,
          height: 130,
          zIndex: 50,
          hide: false, //是否隐藏
          url: "",
          componentId: ComponentId,
        });
        setCurrentType("ZKTecoWeather");
        setPages([...newPages]);
        setCurrentComponentId(ComponentId);

        let index = newPages[currentPageIndex].componentList.length - 1;
        setCurrentComponentIndex(index);
      } else {
        toast.error(message("editor_weather_error_message"));
      }
    }
  };

  const addNews = () => {
    const newPages = JSON.parse(JSON.stringify(props.pages));
    let currentPage = newPages[currentPageIndex];
    if (currentPage.isTemplate) {
      if (activeTempIndex === "") {
        toast.success(message("editor_select_template_tip"));
      } else {
        let ComponentId = getComponentId(newPages);
        currentPage.tempLayout[activeTempIndex].componentList.push({
          title: "editor_news", //标题
          name: "editor_news", //名称
          type: "ZKTecoNews", //组件类型
          left: 12,
          top: 15,
          hide: false,
          width: 200,
          height: 50,
          zIndex: 50,
          newsType: "",
          languageType: "en",
          newsArea: "",
          fontSize: 16, //字体大小
          fontColor: "#262626", //颜色
          scrollDirection: "left", //滚动方向
          speed: 60, //滚动速度
          transparency: 1,
          componentId: ComponentId,
        });

        setPages(newPages);
        setCurrentType("ZKTecoNews");
        setCurrentComponentId(ComponentId);
        let index =
          currentPage.tempLayout[activeTempIndex].componentList.length - 1;
        setCurrentComponentIndex(index);
        setCurrentComponentId(ComponentId);
      }
    } else {
      let ComponentId = getComponentId(newPages);
      newPages[currentPageIndex].componentList.push({
        title: "editor_news", //标题
        name: "editor_news", //名称
        type: "ZKTecoNews", //组件类型
        left: 12,
        top: 15,
        hide: false,
        width: 200,
        height: 50,
        zIndex: 50,
        newsType: "",
        languageType: "en",
        newsArea: "",
        fontSize: 16, //字体大小
        fontColor: "#262626", //颜色
        scrollDirection: "left", //滚动方向
        speed: 60, //滚动速度
        transparency: 1,
        componentId: ComponentId,
      });
      setCurrentType("ZKTecoNews");
      setPages(newPages);
      setCurrentComponentId(ComponentId);
      let index = newPages[currentPageIndex].componentList.length - 1;
      setCurrentComponentIndex(index);
      setCurrentComponentId(ComponentId);
    }
  };

  const addLive = () => {
    const newPages = JSON.parse(JSON.stringify(props.pages));
    let currentPage = newPages[currentPageIndex];
    if (currentPage.isTemplate) {
      if (activeTempIndex === "") {
        toast.success(message("editor_select_template_tip"));
      } else {
        let liveList = currentPage.tempLayout[
          activeTempIndex
        ].componentList.filter((item) => {
          if (item.type === "ZKTecoLive") {
            return true;
          } else {
            return false;
          }
        });

        const addCmp = () => {
          let ComponentId = getComponentId(newPages);
          currentPage.tempLayout[activeTempIndex].componentList.push({
            title: "editor_Live", //标题
            name: "editor_Live", //名称
            type: "ZKTecoLive", //组件类型
            componentId: ComponentId,
            hide: false,
            borderRadius: "0",
            duration: 3600,
            rotate: 0,
            transparency: "0.1",
            left: 12,
            top: 15,
            width: 350,
            height: 130,
            zIndex: 50,
            url: "",
          });
          currentPage.duration = 3600;
          setCurrentType("ZKTecoLive");
          setPages(pageDuration([...newPages]));
          setCurrentComponentId(ComponentId);
          let index =
            currentPage.tempLayout[activeTempIndex].componentList.length - 1;
          setCurrentComponentIndex(index);
        };

        if (liveList.length < 1) {
          addCmp();
        } else {
          confirm({
            title: message("delete_cmp_tip"),
            confirmationText: t("common.common_edit_ok"),
            cancellationText: t("common.common_edit_cancel"),
            description: message("liveTip"),
          }).then(() => {
            addCmp();
          });
        }
      }
    } else {
      let liveList = newPages[currentPageIndex].componentList.filter((item) => {
        if (item.type === "ZKTecoLive") {
          return true;
        } else {
          return false;
        }
      });

      const addTCmp = () => {
        let ComponentId = getComponentId(newPages);
        newPages[currentPageIndex].componentList.push({
          title: "editor_Live", //标题
          name: "editor_Live", //名称
          type: "ZKTecoLive", //组件类型
          componentId: ComponentId,
          hide: false,
          borderRadius: "0",
          duration: 3600,
          rotate: 0,
          transparency: 1,
          left: 12,
          top: 15,
          width: 350,
          height: 130,
          zIndex: 50,
          url: "",
        });
        setCurrentType("ZKTecoLive");

        setPages(pageDuration([...newPages]));
        setCurrentComponentId(ComponentId);
        let index = newPages[currentPageIndex].componentList.length - 1;
        setCurrentComponentIndex(index);
      };

      if (liveList.length < 1) {
        addTCmp();
      } else {
        confirm({
          title: message("delete_cmp_tip"),
          confirmationText: t("common.common_edit_ok"),
          cancellationText: t("common.common_edit_cancel"),
          description: message("liveTip"),
        }).then(() => {
          addTCmp();
        });

        // toast.error(message("editor_weather_error_message"));
      }
    }
  };

  return (
    <Grid
      sx={{
        display: "flex",
        justifyContent: "center",
        height: "100%",
      }}>
      {currentComponentId ? (
        <Grid
          sx={{
            width: "100%",
          }}>
          {currentType === "ZKTecoNews" && (
            <NewsComponent {...props}></NewsComponent>
          )}
          {currentType === "ZKTecoTime" && (
            <TimeComponent {...props}></TimeComponent>
          )}
          {currentType === "ZKTecoWeather" && (
            <WeatherComponent {...props}></WeatherComponent>
          )}

          {currentType === "ZKTecoLive" && (
            <LiveComponent {...props}></LiveComponent>
          )}
        </Grid>
      ) : (
        <Grid
          sx={{
            width: "100%",
          }}>
          <CustomButtom onClick={addTime}>
            {message("editor_time")}
          </CustomButtom>
          <CustomButtom onClick={addWeather}>
            {message("editor_weather")}
          </CustomButtom>
          <CustomButtom onClick={addNews}>
            {message("editor_news")}
          </CustomButtom>

          <CustomButtom onClick={addLive}>{message("live")}</CustomButtom>
        </Grid>
      )}
    </Grid>
  );
};

export default WidgetComponent;
