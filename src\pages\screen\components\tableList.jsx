/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/prop-types */
import React, { useEffect, useMemo, useState, useRef } from "react";
import {
  Button,
  Stack,
  Typography,
  Tooltip,
  Link,
  Grid,
  IconButton,
  TextField,
} from "@mui/material";

import SyncIcon from "@mui/icons-material/Sync";
import DictTag from "@/components/DictTag";
// api
import {
  listByPage,
  rebootScreen,
  removeScreen,
  doScreenShot,
} from "@/service/api/screen";
import { useNavigate, useLocation } from "react-router-dom";
import { tableI18n } from "@/utils/tableLang";
import ExportDialog from "@/components/ExportDialog";
// 消息提示
import { toast } from "react-toastify";
import { useTranslation } from "react-i18next";
import { useConfirm } from "@/components/zkconfirm";
import MaterialReactTable from "material-react-table";
import UploadUpgrade from "./UploadUpgrade";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import { useFormik } from "formik";
import { removeEmpty } from "@/utils/StringUtils";
import MainCard from "@/components/MainCard";
import ZKSelect from "@/components/ZKSelect";
import AuthButton from "@/components/AuthButton";
import { screenStatus, screendirections } from "@/dict/commonDict";
import { download } from "@/utils/downloadFile";
import DropdownMenu from "../../../components/dropdownMenu";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import DescriptionIcon from "@mui/icons-material/Description";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import ScreenLogList from "./screenLogList";
const ProgramScheduleTableList = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();
  const confirm = useConfirm();
  const [rowSelection, setRowSelection] = useState([]);
  const [isError, setIsError] = useState(false);
  const upload = useRef(null);
  const screenLogRef = useRef(null);
  const [open, setOpen] = useState(false);
  const [openScreenLog, setOpenScreenLog] = useState(false);
  const [exportOpen, setExportOpen] = useState(false);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  // 表格数据
  const [data, setData] = useState([]);
  // 分页的参数
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);
  // 显示搜索
  const [showSearch, setShowSearch] = useState(true);
  //设备id集合
  const [screenIdList, setScreenIdList] = useState([]);
  //屏幕表id
  const [screenId, setScreenId] = useState([]);
  // 查询参数
  const requestParams = useRef(null);
  // 构建参数
  const buildParams = () => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      ...requestParams.current,
    };
    return params;
  };

  // 关闭
  const handlCloseCancel = () => {
    setOpen(false);
    setScreenIdList([]);
  };
  const handlCloseLogCancel = () => {
    setOpenScreenLog(false);
    setScreenId([]);
  };
  // 获取数据
  const getTableData = async () => {
    if (!data.length) {
      setIsLoading(true);
    } else {
      setIsRefetching(true);
    }
    // // 开启加载
    setIsLoading(true);
    // setIsRefetching(true);
    await listByPage(buildParams())
      .then((res) => {
        // 设置数据
        setData(res.data.data);
        // 设置总记录数
        setRowCount(res.data.total);
        setIsLoading(false);
        setIsRefetching(false);
      })
      .catch((err) => {
        setIsError(true);
        setIsLoading(false);
        setIsRefetching(false);
      });
  };
  useEffect(() => {
    getTableData();
    setRowSelection([]);
  }, [pagination.pageIndex, pagination.pageSize, location]);
  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "name",
        header: t("ips.ips_device"),
        size: 180,
        Cell: ({ cell, row }) => {
          return (
            <Tooltip title={row.original.name} placement="top">
              <Typography className="textSpace">{row.original.name}</Typography>
            </Tooltip>
          );
        },
      },
      {
        accessorKey: "sn",
        header: t("ips.ips_device_sn"),
        size: 100,
      },
      // {
      //     accessorKey: 'screenType',
      //     header: t('ips.ips_type'),
      //     size: 70
      // },
      // {
      //     accessorKey: 'screenModel',
      //     header: t('ips.ips_screen_model'),
      //     size: 80
      // },
      {
        accessorKey: "resolution",
        header: t("ips.ips_resolution"),
        size: 80,
      },
      {
        accessorKey: "fwVersion",
        header: t("ips.ips_screen_app_version"),
        size: 120,
      },
      {
        accessorKey: "merchantName",
        header: t("ips.ips_store_client_name"),
        size: 140,
        Cell: ({ cell, row }) => {
          return (
            <Tooltip title={row.original.merchantName} placement="top">
              <Typography className="textSpace">
                {row.original.merchantName}
              </Typography>
            </Tooltip>
          );
        },
      },
      {
        accessorKey: "storeName",
        header: t("common.common_outlet_owner"),
        size: 140,
        Cell: ({ cell, row }) => {
          return (
            <Tooltip title={row.original.storeName} placement="top">
              <Typography className="textSpace">
                {row.original.storeName}
              </Typography>
            </Tooltip>
          );
        },
      },
      {
        accessorKey: "address",
        header: t("common.common_location"),
        Cell: ({ cell, row }) => {
          return (
            <Tooltip title={row.original.address} placement="top">
              <Typography className="textSpace">
                {row.original.address}
              </Typography>
            </Tooltip>
          );
        },
      },
      // {
      //   accessorKey: "wide",
      //   header: t("common.common_resolution_ratio"),
      //   Cell: ({ cell, row }) => {
      //     return (
      //       <>
      //         {row.original.wide && row.original.high ? (
      //           <>
      //             <Stack direction="row" spacing={1} alignItems="center">
      //               <Typography>
      //                 {row.original.wide} x {row.original.high}
      //               </Typography>
      //             </Stack>
      //           </>
      //         ) : (
      //           <>-</>
      //         )}
      //       </>
      //     );
      //   },
      // },
      {
        accessorKey: "direction",
        header: t("common.common_direction"),
        size: 100,
        Cell: ({ cell, row }) => {
          return (
            <DictTag
              dicts={screendirections}
              fieldName={{
                value: "value",
                title: "label",
                color: "color",
              }}
              value={row.original.direction}
            />
          );
        },
      },
      // {
      //     accessorKey: 'ipAddress',
      //     header: t('ips.ips_device_ip'),
      //     enableColumnFilter: false,
      //     size: 100
      // },
      {
        accessorKey: "status",
        header: t("ips.ips_device_online"),
        size: 120,
        Cell: ({ cell, row }) => {
          return (
            <DictTag
              dicts={screenStatus}
              fieldName={{
                value: "value",
                title: "label",
                color: "color",
              }}
              value={row.original.status}
            />
          );
        },
      },
    ],
    []
  );

  //设备截图
  const handeScreenShot = (id) => {
    doScreenShot(id).then((res) => {
      toast.success(res.message);
    });
  };
  // 重启设备
  const handelRebootScreen = (ids, names) => {
    if (ids.length > 1) {
      toast.error(t("common.common_select_one_operation"));
      return;
    }
    confirm({
      title: t("message.messageBox_title"),
      confirmationText: t("common.common_edit_ok"),
      cancellationText: t("common.common_edit_cancel"),
      description: t("common.common_confirm_reboot_device", {
        ids: names,
      }),
    }).then(() => {
      rebootScreen(ids).then((res) => {
        toast.success(res.message);
        // 重新请求数据
        getTableData();
        //重置选中行框
        setRowSelection([]);
        // setIsRefetching(true);
      });
    });
  };
  // 删除屏幕
  const handleRemoveScreen = (ids, names) => {
    confirm({
      title: t("message.messageBox_title"),
      confirmationText: t("common.common_edit_ok"),
      cancellationText: t("common.common_edit_cancel"),
      description: t("common.common_confirm_link_delete_device", {
        ids: names,
      }),
    }).then(() => {
      removeScreen(ids)
        .then((res) => {
          toast.success(res.message);
          // 重新请求数据
          getTableData();
          //重置选中行框
          setRowSelection([]);
          // setIsRefetching(true);
        })
        .catch((error) => {});
    });
  };
  // 查询表单
  const queryFormik = useFormik({
    initialValues: {
      name: "",
      sn: "",
      status: "",
      address: "",
      storeName: "",
      wide: "",
      high: "",
      merchantName: "",
    },
    onSubmit: async (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        // 拷贝value值
        const tempValue = { ...values };
        console.log(tempValue);
        await removeEmpty(tempValue);
        // setRequestParams(tempValue);
        requestParams.current = tempValue;
        setPagination({
          pageIndex: 0,
          pageSize: 10,
        });
        getTableData();
        // 查询table
        // setColumnFilters(tempValue);
        // handelSaveSubmit(values);
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
  });
  // 重置查询
  const resetQuery = () => {
    // 重置表单
    queryFormik.resetForm();
    requestParams.current = null;
    setPagination({
      pageIndex: 0,
      pageSize: 10,
    });
    getTableData();
  };

  const handleExport = (value) => {
    // 处理数据
    const params = {
      ...value,
      ...buildParams(),
    };
    return new Promise((resolve, reject) => {
      download(
        `/screen/export`,
        params,
        `${t("common.common_export_screen_title")}.xlsx`
      )
        .then(() => {
          resolve();
          setExportOpen(false);
        })
        .catch(() => {
          reject();
        });
    });
  };
  const handleOnActions = (key, original) => {
    switch (key) {
      case "delete":
        handleRemoveScreen(original.id, original.name);
        break;
      case "modify":
        navigate(`/screen/editScreen?id=${original.id}`);
        break;
      case "upgrade":
        setOpen(true);
        setScreenIdList([original.id]);
        break;
      case "screenLog":
        setOpenScreenLog(true);
        setScreenId(original.id);
        break;
    }
  };
  return (
    <>
      <MainCard style={{ marginBottom: "10px" }}>
        <form noValidate onSubmit={queryFormik.handleSubmit}>
          <Grid
            container
            direction="row"
            justifyContent="flex-start"
            alignItems="center"
            spacing={5}>
            <Grid item xs={12} md={4} lg={2}>
              <TextField
                label={t("ips.ips_device")}
                value={queryFormik.values.name}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                size="small"
                type="text"
                name="name"
                fullWidth
                placeholder={t("common.common_please_input")}
              />
            </Grid>
            <Grid item xs={12} md={4} lg={2}>
              <TextField
                name="merchantName"
                label={t("ips.ips_store_client_name")}
                value={queryFormik.values.merchantName}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                size="small"
                type="text"
                fullWidth
                placeholder={t("common.common_please_input")}
              />
            </Grid>
            <Grid item xs={12} md={4} lg={2}>
              <TextField
                name="sn"
                label={t("ips.ips_device_sn")}
                value={queryFormik.values.sn}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                size="small"
                type="text"
                fullWidth
                placeholder={t("common.common_please_input")}
              />
            </Grid>
            <Grid item xs={12} md={4} lg={2}>
              <TextField
                name="storeName"
                label={t("ips.ips_store_name")}
                value={queryFormik.values.storeName}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                size="small"
                type="text"
                fullWidth
                placeholder={t("common.common_please_input")}
              />
            </Grid>

            {!showSearch && (
              <>
                <Grid item xs={12} sm={4} md={2}>
                  <TextField
                    name="screenType"
                    label={t("ips.ips_type")}
                    value={queryFormik.values.screenNumber}
                    onChange={queryFormik.handleChange}
                    onBlur={queryFormik.handleBlur}
                    size="small"
                    type="text"
                    fullWidth
                  />
                </Grid>
                <Grid item xs={12} sm={4} md={2}>
                  <TextField
                    label={t("ips.ips_screen_model")}
                    size="small"
                    type="text"
                    name="screenModel"
                    fullWidth
                    value={queryFormik.values.contacts}
                    onChange={queryFormik.handleChange}
                    onBlur={queryFormik.handleBlur}
                  />
                </Grid>
                <Grid item xs={12} md={4} lg={2}>
                  <ZKSelect
                    size="small"
                    label={t("ips.ips_device_online")}
                    name="status"
                    value={queryFormik.values.status}
                    onChange={queryFormik.handleChange}
                    onBlur={queryFormik.handleBlur}
                    options={screenStatus}
                    onClear={() => {
                      queryFormik.setFieldValue("status", "");
                    }}
                    placeholder={t("common.common_select_status")}
                    menuWidth={100}
                  />
                </Grid>
                <Grid item xs={12} md={4} lg={2}>
                  <TextField
                    label={t("common.common_location")}
                    name="address"
                    size="small"
                    type="text"
                    fullWidth
                    placeholder={t("common.common_please_input")}
                    value={queryFormik.values.address}
                    onChange={queryFormik.handleChange}
                    onBlur={queryFormik.handleBlur}
                  />
                </Grid>
                <Grid item xs={12} md={4} lg={2}>
                  <TextField
                    label={t("ips.ips_resolution_wide")}
                    name="wide"
                    size="small"
                    type="text"
                    fullWidth
                    placeholder={t("common.common_please_input")}
                    value={queryFormik.values.wide}
                    onChange={queryFormik.handleChange}
                    onBlur={queryFormik.handleBlur}
                  />
                </Grid>
                <Grid item xs={12} md={4} lg={2}>
                  <TextField
                    label={t("ips.ips_resolution_high")}
                    name="high"
                    size="small"
                    type="text"
                    fullWidth
                    placeholder={t("common.common_please_input")}
                    value={queryFormik.values.high}
                    onChange={queryFormik.handleChange}
                    onBlur={queryFormik.handleBlur}
                  />
                </Grid>
              </>
            )}

            <Grid item xs={12} md={4} lg={2}>
              <Stack
                direction="row"
                justifyContent="flex-start"
                alignItems="flex-start"
                spacing={2}>
                <Button
                  disableElevation
                  type="submit"
                  variant="contained"
                  size="small">
                  {t("common.common_table_query")}
                </Button>
                <Button
                  variant="outlined"
                  onClick={resetQuery}
                  color="info"
                  size="small">
                  {t("common.common_op_reset")}
                </Button>
                <IconButton
                  onClick={() => setShowSearch(!showSearch)}
                  size="middle">
                  {showSearch ? (
                    <>
                      <ExpandMoreIcon sx={{ mt: -0.5 }} />
                      <Typography sx={{ mt: -0.5 }}>
                        {t("common.common_form_unfold")}
                      </Typography>
                    </>
                  ) : (
                    <>
                      <ExpandLessIcon sx={{ mt: -0.5 }} />
                      <Typography sx={{ mt: -0.5 }}>
                        {t("common.common_form_packup")}
                      </Typography>
                    </>
                  )}
                </IconButton>
              </Stack>
            </Grid>
          </Grid>
        </form>
      </MainCard>
      <MaterialReactTable
        // table 状态
        state={{
          // 加载状态
          isLoading,
          // 分页参数
          pagination,
          // 重新拉取
          showProgressBars: isRefetching,
          showAlertBanner: isError,
          columnPinning: { right: ["action"] },
          rowSelection,
        }}
        renderToolbarInternalActions={({ table }) => (
          <>
            <Tooltip
              arrow
              placement="top"
              title={t("common.common_op_refresh")}>
              <IconButton aria-label="sync" onClick={getTableData}>
                <SyncIcon />
              </IconButton>
            </Tooltip>
          </>
        )}
        muiTablePaperProps={{
          elevation: 0,
          sx: {
            borderRadius: "5px",
            border: "1px solid #f0f0f0",
          },
        }}
        muiTableHeadRowProps={{
          sx: { backgroundColor: "#fafafa", boxShadow: "none" },
        }}
        // 关闭过滤搜素
        enableColumnFilters={true}
        // 关闭排序
        enableSorting={false}
        // 布局方式
        layoutMode="grid"
        enableColumnActions={false}
        // 开启列对齐
        muiTableHeadCellProps={{
          sx: {
            "& .Mui-TableHeadCell-Content": {
              justifyContent: "space-between",
            },
          },
        }}
        // 解决列太多宽度太长问题
        enableColumnResizing
        // enablePinning
        // 初始化状态

        initialState={{
          columnVisibility: { createTime: true },
          columnPinning: { right: ["action"] },
        }}
        muiToolbarAlertBannerProps={
          isError
            ? {
                color: "error",
                children: "Error loading data",
              }
            : undefined
        }
        //行选中
        onRowSelectionChange={setRowSelection}
        muiTableBodyRowProps={({ row }) => ({
          onClick: row.getToggleSelectedHandler(),
          sx: { cursor: "pointer" },
        })}
        // 开启多选
        enableRowSelection
        // 列数
        rowCount={rowCount}
        // 固定头部
        enableStickyHeader
        // 处理表格高度
        muiTableContainerProps={{ sx: { maxHeight: "620px" } }}
        // 设置背景颜色
        muiTableBodyCellProps={{ sx: { backgroundColor: "white" } }}
        muiTableProps={{
          sx: { backgroundColor: "white", tableLayout: "fixed" },
        }}
        muiBottomToolbarProps={{ sx: { backgroundColor: "white" } }}
        muiTopToolbarProps={{ sx: { backgroundColor: "white" } }}
        // 分页回调函数
        onPaginationChange={setPagination}
        // 记得开启这些 tips:否则会出现useEffect 反作用函数发起多次请求点击下一页会出问题
        manualFiltering
        manualPagination
        manualSorting
        // 开启分页
        enablePagination
        // 列定义
        columns={columns}
        // 数据
        data={data}
        localization={tableI18n}
        // 自定义表头按钮
        renderTopToolbarCustomActions={({ table }) => {
          return (
            <Stack direction="row" spacing={1} alignItems="center">
              <AuthButton button="screen:screen:save">
                <Button
                  variant="contained"
                  onClick={() => {
                    navigate("/screen/add");
                  }}>
                  {t("common.common_add_screen")}
                </Button>
              </AuthButton>
              <AuthButton button="screen:screen:delete">
                <Button
                  variant="contained"
                  color="secondary"
                  disabled={
                    !table.getIsSomeRowsSelected() &&
                    !table.getIsAllRowsSelected()
                  }
                  onClick={() => {
                    const ids = [];
                    const names = [];
                    table.getSelectedRowModel().rows.map((row) => {
                      ids.push(row.original.id);
                      names.push(row.original.name);
                    });
                    handleRemoveScreen(ids, names);
                  }}>
                  {t("common.common_op_batch_del")}
                </Button>
              </AuthButton>
              <AuthButton button="screen:screen:delete">
                <Button
                  variant="contained"
                  disabled={
                    !table.getIsSomeRowsSelected() &&
                    !table.getIsAllRowsSelected()
                  }
                  onClick={() => {
                    let idList = [];
                    table.getSelectedRowModel().rows.map((row) => {
                      idList.push(row.original.id);
                    });
                    setScreenIdList(idList);
                    setOpen(true);
                  }}>
                  {t("common.common_batch_upgrade")}
                </Button>
              </AuthButton>
              <AuthButton button="screen:screen:save">
                <Button
                  variant="contained"
                  onClick={() => {
                    setExportOpen(true);
                  }}>
                  {t("common.common_op_export")}
                </Button>
              </AuthButton>
            </Stack>
          );
        }}
        // 多选底部提示
        positionToolbarAlertBanner="none"
        // 开启action操作
        enableRowActions
        // action操作位置
        positionActionsColumn="last"
        displayColumnDefOptions={{
          "mrt-row-actions": {
            size: 180,
          },
        }}
        renderRowActions={({ row, index, table }) => (
          <Stack direction="row" spacing={1} alignItems="center">
            <Link
              component="button"
              underline="none"
              onClick={() => {
                navigate(`/screen/detail?id=${row.original.id}`);
              }}>
              {t("common.common_op_detail")}
            </Link>
            <DropdownMenu
              btnText={t("common.common_operation")}
              options={[
                {
                  text: t("common.common_op_modify"),
                  key: "modify",
                  icon: <EditIcon sx={{ fontSize: "18px" }} />,
                  authCode: "screen:edit:button",
                },
                {
                  text: t("common.common_upgrade"),
                  icon: <CloudUploadIcon sx={{ fontSize: "18px" }} />,
                  key: "upgrade",
                  authCode: "screen:edit:button",
                },
                {
                  text: t("menu.media_deviceLog"),
                  icon: <DescriptionIcon sx={{ fontSize: "18px" }} />,
                  key: "screenLog",
                  authCode: "screen:edit:button",
                },
                {
                  text: t("common.common_op_del"),
                  icon: <DeleteIcon sx={{ fontSize: "18px" }} />,
                  key: "delete",
                  authCode: "screen:screen:delete",
                },
              ]}
              onActions={(key) => handleOnActions(key, row.original)}
            />
          </Stack>
        )}
      />
      <UploadUpgrade
        ref={upload}
        open={open}
        onCancel={handlCloseCancel}
        screenIdList={screenIdList}
      />
      <ScreenLogList
        ref={screenLogRef}
        openScreenLog={openScreenLog}
        onCancel={handlCloseLogCancel}
        screenId={screenId}
      />
      <ExportDialog
        title={t("common.common_export_screen_title")}
        open={exportOpen}
        columns={columns}
        onExport={handleExport}
        onClose={() => {
          setExportOpen(false);
        }}
      />
    </>
  );
};
export default ProgramScheduleTableList;
