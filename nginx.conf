#user  nobody;
worker_processes 1;

events {
    worker_connections 1024;
}


http {
    include mime.types;
    default_type application/octet-stream;

    #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
    #                  '$status $body_bytes_sent "$http_referer" '
    #                  '"$http_user_agent" "$http_x_forwarded_for"';

    #access_log  logs/access.log  main;
    sendfile on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout 65;

    #gzip  on;

    server {
        listen 8081;
        server_name localhost;

        #charset koi8-r;

        #access_log  logs/host.access.log  main;
        location /cms-app {
            alias /usr/share/nginx/html;
            try_files $uri $uri/ /cms-app/index.html;

            add_header 'Access-Control-Allow-Origin' *;
            add_header 'Access-Control-Allow-Headers' *;
            #允许带上cookie请求
            add_header 'Access-Control-Allow-Credentials' 'true';
            add_header 'X-Content-Type-Options' 'nosniff';
            add_header 'X-Frame-Options' 'sameorigin';
            add_header 'X-XSS-Protection' '1; mode=block';
            add_header 'Strict-Transport-Security' 'max-age=31536000; includeSubDomains; preload';

        }

        location ^~ /dev/ {
            proxy_pass http://**********:9090/;
        }

        #error_page  404              /404.html;

        # redirect server error pages to the static page /50x.html
        #
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root html;
        }

    }

}
