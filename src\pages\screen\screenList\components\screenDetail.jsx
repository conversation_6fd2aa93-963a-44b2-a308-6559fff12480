import React, { forwardRef, useEffect, useState, useRef } from "react";
import {
  Box,
  Stack,
  IconButton,
  Grid,
  AlertTitle,
  Button,
  Divider,
  Typography,
  Tab,
  Alert,
} from "@mui/material";
import MainCard from "@/components/MainCard";
import { useTheme } from "@mui/material/styles";
import { SyncOutlined } from "@ant-design/icons";
import DictTag from "@/components/DictTag";
import { useNavigate, useSearchParams } from "react-router-dom";
import { getScreenInfo, getRemoteDiagnosis ,remoteDiagnosis as requestDiagnosis,removeSSE} from "@/service/api/screen";
import Descriptions from "@/components/descriptions";
import Dot from "@/components/@extended/Dot";
import { useTranslation } from "react-i18next";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import {
  screenStatus,
  screendirections,
  currentScheduleStatus,
  deviceTypies,
  adbSwitchTab,
} from "@/dict/commonDict";
import CircularProgress from "@mui/material/CircularProgress";
import TabContext from "@mui/lab/TabContext";
import TabList from "@mui/lab/TabList";
import TabPanel from "@mui/lab/TabPanel";
import { styled } from "@mui/material/styles";
import LinearProgress from "@mui/material/LinearProgress";
const tabs = ["detail", "remoteDiagnosis","alert"];
import { getToken } from "@/utils/auth";
import { fetchEventSource } from "@microsoft/fetch-event-source";
import useFakeProgress from "@/hooks/useFakeProgress";
import dayjs from "dayjs";
import { useRequest } from 'ahooks';
import AlertTable from './alertTable'
const screenDetail = forwardRef((props, ref) => {
  const theme = useTheme();
  const { screenName, id, deviceId } = props;
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [search, setSearch] = useSearchParams();
  const [data, setData] = useState({});
  const [statusValue,setStatusValue] =useState(search.get("status")? search.get("status") :"0");
  const [loading, setLoading] = useState(true);
  const [tableValue, setTableValue] = useState(tabs[0]);
  const [timeZoneRemind,setTimeZoneRemind] = useState("");
  // const [progress, setProgress] = useState(0);
  const [spin, setSpin] = useState(false);

  // const [startProgress, setStartProgress] = useState(false);
  const { progress, isComplete, startProgress, setStartProgress, setProgress } =
    useFakeProgress();
  const ctrlAbout = useRef(null);
  const [remoteDiagnosis, setRemoteDiagnosis] = useState(undefined);
  const handleGetScreenInfo = () => {
    if (search.get("id")) {
      setLoading(true);
      getScreenInfo(search.get("id"))
        .then((res) => {
          const { data } = res;
          // const volume = null === data.volume ? 50 : data.volume;
          // const brightness = null === data.brightness ? 50 : data.brightness;
          setData({
            name: data.name,
            sn: data.sn,
            storeName: data.storeName,
            storeAddress: data.storeAddress,
            fwVersion: data.fwVersion,
            siteId: data.siteId,
            zoneId: data.zoneId,
            screenLuminance: data.screenLuminance,
            screenColourTemperature: data.screenColourTemperature,
            timeZone: data.timeZone,
            deviceModel: data.deviceModel,
            screenModel: data.screenModel,
            macAddress: data.macAddress,
            screenType: data.screenType,
            ipAddress: data.ipAddress,
            deviceAlias: data.deviceAlias,
            status: data.status,
            volume: data.volume,
            adbSwitch: data.adbSwitch,
            deviceType: data?.deviceType,
            // brightness: brightness,
          });
          setStatusValue(data?.status)
          setLoading(false);
          //LCD-L101不提示
          if (data?.screenModel != "LCD-L101") {
            setTimeZoneRemind("("+t("ips.ips_timezone_device_setting")+")");
          }
        })
        .catch((err) => {
          setLoading(true);
        });
    }
  };

  const featchRemoteDiagnosis = () => {
    if (search.get("id")) {
      setSpin(true);
      getRemoteDiagnosis(search.get("id"))
        .then((res) => {
          const { data } = res;
          setRemoteDiagnosis(data);
          setSpin(false);
          setProgress(100);
        })
        .catch((err) => {
          setSpin(false);
        });
    }
  };
  useEffect(() => {
    if (tableValue === tabs[0]) {
      handleGetScreenInfo();
      closeEES();
    } else if (tableValue === tabs[1]) {
      featchRemoteDiagnosis();
    }
  }, [tableValue]);


  const handleStartProgress = () => {
    setStartProgress(true); // 开始模拟进度条
  };

  const handleCompleteProgress = () => {
    setStartProgress(false); // 停止自动增加进度条
    setProgress(100); // 手动将进度设置为100%
    setSpin(false)
  };
  const resetRemoteDiagnosis = async () => {
   return new Promise( (reslove,reject)=>{
     //重新检测
     setSpin(true);
     setProgress(0);
     if (search.get("id")) {
       ctrlAbout.current=null;
        initSSE(search.get("id"));
        requestDiagnosis(search.get("id")).then(res=>{
            //连接成功
            handleStartProgress();
          reslove();
        }).catch(()=>{
          reject();
        });
     }
   })
  };

  const { run } = useRequest(resetRemoteDiagnosis, {
    throttleWait: 10000,
    manual: true
  });
  const initSSE = async (screenId) => {
    if (ctrlAbout && ctrlAbout.current) {
      return false;
    }
    ctrlAbout.current = new AbortController();
    fetchEventSource(
      `${import.meta.env.VITE_API_URL}/sse/connect/diagnosis/${screenId}`,
      {
        method: "POST",
        headers: {
          Accept: "text/event-stream",
          Authorization: import.meta.env.VITE_TOKEN_HEADER + getToken(),
        },
        signal: ctrlAbout.current.signal,
        body: JSON.stringify({}),
        onmessage(msg) {
          let resData = msg.data;
          resData = JSON.parse(resData);
          console.log(resData);
          if (resData.code === "00000") {
            console.log("Connection successful")
            //伪造进度条
          } else if(resData.code ==='R00000'){
            // setProgress(100)
            handleCompleteProgress();
            setRemoteDiagnosis(resData?.data)
            closeEES();
          }
        },
        onerror() {
          // 服务异常
          console.log("服务异常");
        },
        onclose() {
          // 服务关闭
          console.log("服务关闭");
        },
      }
    );
  };
  useEffect(() => {
    return () => {
      closeEES();
    };
  }, []);
  const closeEES = async () => {
    if (ctrlAbout && ctrlAbout.current) {
      setSpin(false)
      await removeConnection();
      ctrlAbout.current.abort();
      ctrlAbout.current = null;
    }
  };

  const removeConnection = async () => {
    if(search.get("id")){
      await removeSSE(search.get("id")).then((res) => {
        return Promise.resolve();
      });
    }
  };

  return (
    <>
      <MainCard
        divider={false}
        title={
          <Stack direction="row" alignItems="center" spacing={1}>
            <IconButton
              onClick={() => {
                navigate(-1);
              }}
            >
              <ArrowBackIcon />
            </IconButton>
            <Stack
              direction="row"
              justifyContent="center"
              alignItems="center"
              divider={<Divider orientation="vertical" flexItem />}
              spacing={1}
            >
              <Typography variant="h4" component="p">
                {t('detail.device_name')}{search.get("name") ? search.get("name") : "-"}
              </Typography>
              <Stack
                direction="row"
                justifyContent="center"
                alignItems="center"
              >
                <Typography variant="h5" component="p">
                  {t('detail.device_status')}
                </Typography>
                <DictTag
                  dicts={screenStatus}
                  fieldName={{ value: "value", title: "label", color: "color" }}
                  value={statusValue}
                />
              </Stack>
            </Stack>
          </Stack>
        }
        border={false}
      >
        <TabContext value={tableValue}>
          <Box
            sx={{
              borderBottom: 1,
              borderColor: "divider",
              display: "flex",
              alignItems: "center",
              background: "white",
              justifyContent: "space-between",
            }}
          >
            <TabList
              style={{ background: "white" }}
              onChange={(event, newValue) => {
                setTableValue(newValue);
              }}
            >
              <Tab label={t('detail.label_detail')} value={tabs[0]} key="detail" />
              <Tab label={t('detail.label_remote_diagnosis')} value={tabs[1]} key="remoteDiagnosis" />
              <Tab label={t('event.device_alert')} value={tabs[2]} key="alert" />
            </TabList>
          </Box>
          <TabPanel value="detail" sx={{ padding: "10px" }}>
            {loading ? (
              <Box
                sx={{
                  width: "100%",
                  height: 200,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <CircularProgress />
              </Box>
            ) : (
              <Descriptions
                title={null}
                bordered
                size="large"
                colon={false}
                column={2}
              >
                <Descriptions.Item label={t("ips.ips_device")}>
                  {data.name ? data.name : "-"}
                </Descriptions.Item>
                <Descriptions.Item label={t("ips.ips_device_sn")}>
                  {data.sn ? data.sn : "-"}
                </Descriptions.Item>
                <Descriptions.Item label={t("ips.ips_device_online")}>
                  {data.status ? (
                    <DictTag
                      dicts={screenStatus}
                      fieldName={{
                        value: "value",
                        title: "label",
                        color: "color",
                      }}
                      value={String(data.status)}
                    />
                  ) : (
                    "-"
                  )}
                </Descriptions.Item>
                <Descriptions.Item label={t("ips.ips_fwversion")}>
                  {data.fwVersion ? data.fwVersion : "-"}
                </Descriptions.Item>
                <Descriptions.Item label={t("ips.ips_screen_model")}>
                  {data.screenModel ? data.screenModel : "-"}
                </Descriptions.Item>
                <Descriptions.Item label={t("common.common_deviceType")}>
                  {data.deviceType ? (
                    <DictTag
                      dicts={deviceTypies}
                      fieldName={{ value: "value", title: "label" }}
                      value={String(data.deviceType)}
                    />
                  ) : (
                    "-"
                  )}
                </Descriptions.Item>
                <Descriptions.Item label={t("ips.ips_type")}>
                  {data.screenType ? data.screenType : "-"}
                </Descriptions.Item>
                <Descriptions.Item label={t("ips.ips_timezone")}>
                  {data.timeZone ? data.timeZone : "-"}
                  &nbsp;&nbsp;&nbsp;{timeZoneRemind}
                </Descriptions.Item>
                <Descriptions.Item label={t("common.common_macAddress")}>
                  {data.macAddress ? data.macAddress : "-"}
                </Descriptions.Item>
                <Descriptions.Item label={t("ips.ips_device_ip")}>
                  {data.ipAddress ? data.ipAddress : "-"}
                </Descriptions.Item>
                <Descriptions.Item label={t("ips.ips_screen_alias")}>
                  {data.deviceAlias ? data.deviceAlias : "-"}
                </Descriptions.Item>
                <Descriptions.Item label={t("common.common_outlet_owner")}>
                  {data.storeName ? data.storeName : "-"}
                </Descriptions.Item>
                <Descriptions.Item label={t("ips.ips_device_volume")}>
                  {data.volume ? data.volume + "%" : "-"}
                </Descriptions.Item>
                <Descriptions.Item label={t("ips.ips_screen_brightnes")}>
                  {data.screenLuminance ? data.screenLuminance + "%" : "-"}
                </Descriptions.Item>
                <Descriptions.Item
                  label={t("common.common_screen_colour_temperature")}
                >
                  {data.screenColourTemperature
                    ? data.screenColourTemperature + "K"
                    : "-"}
                </Descriptions.Item>
                <Descriptions.Item label={t("common.adb_mode")}>
                  {data.adbSwitch ? (
                    <DictTag
                      dicts={adbSwitchTab}
                      fieldName={{ value: "value", title: "label" }}
                      value={String(data.adbSwitch)}
                    />
                  ) : (
                    "-"
                  )}
                </Descriptions.Item>
              </Descriptions>
            )}
          </TabPanel>
          <TabPanel value="remoteDiagnosis" sx={{ padding: "10px" }}>
            {/* 远程诊断 */}
            <Alert
              sx={{
                padding:"25px 10px 25px 10px",
                width: "100%",
                ".MuiAlert-icon": {
                  alignItems: "center",
                  justifyContent: "center",
                },
                ".MuiAlert-message": {
                  width: "100%",
                },
              }}
              icon={
                <div style={{ width: "40px" }}>
                  <SyncOutlined
                    size={10}
                    style={{ fontSize: "30px" }}
                    spin={spin}
                  />
                </div>
              }
            >
              <AlertTitle>
                <Typography variant="h4" component="p">
                  {progress !==100 ? t('detail.current_remote_diagnosis') :t('detail.current_remote_diagnosis_finsh')}-{progress}%
                </Typography>
              </AlertTitle>
              <LinearProgress
                sx={{ width: "100%", height: 12, borderRadius: 6 }}
                variant="determinate"
                value={progress}
              />
            </Alert>
            <Box sx={{ mt: 2 }}>
              <Grid container>
                <Grid item xs={12}>
                  <Stack
                    direction={"row"}
                    justifyContent={"space-between"}
                    alignItems={"center"}
                  >
                    <Typography variant="h5" component="p">
                    {t('detail.diagnosis_result')}
                    </Typography>
                    <Button variant="contained" onClick={run}>
                      {remoteDiagnosis ? t('detail.device_rediagnosis') : t('detail.device_start_rediagnosis')}
                    </Button>
                  </Stack>
                </Grid>
                <Grid item xs={12} sx={{ p: 2 }}>
                  <Stack
                    sx={{ pt: 3, pb: 3 }}
                    direction={"row"}
                    justifyContent={"space-between"}
                    alignItems={"center"}
                  >
                    <Typography variant="h5" component="p">
                      {t('detail.total_memory_free')}
                    </Typography>
                    <Typography variant="h6" component="p">
                      {remoteDiagnosis?.deviceCheckInfoEvent?.totalMemory
                        ? remoteDiagnosis?.deviceCheckInfoEvent?.totalMemory
                        : "0"}
                      MB /
                      {remoteDiagnosis?.deviceCheckInfoEvent?.availableMemory
                        ? remoteDiagnosis?.deviceCheckInfoEvent?.availableMemory
                        : "0"}
                      MB
                    </Typography>
                  </Stack>
                  <Divider />
                  <Stack
                    sx={{ pt: 3, pb: 3 }}
                    direction={"row"}
                    justifyContent={"space-between"}
                    alignItems={"center"}
                  >
                    <Typography variant="h5" component="p">
                    {t('detail.cpu_usage')}
                    </Typography>
                    <Typography variant="h6" component="p">
                      {remoteDiagnosis?.deviceCheckInfoEvent?.usageCpu
                        ? (remoteDiagnosis.deviceCheckInfoEvent.usageCpu * 100).toFixed(2)
                        : 0}{" "}
                      %
                    </Typography>
                  </Stack>
                  <Divider />
                  <Stack
                    sx={{ pt: 2, pb: 2 }}
                    direction={"row"}
                    justifyContent={"space-between"}
                    alignItems={"center"}
                  >
                    <Typography variant="h5" component="p">
                    {t('detail.current_schedule_status')}
                    </Typography>
                    <Typography variant="h6" component="p">
                      {/* <DictTag value ?.> */}
                      {remoteDiagnosis?.deviceCheckInfoEvent?.scheduleStatus ? (
                        <DictTag
                          dicts={currentScheduleStatus}
                          fieldName={{ value: "value", title: "label" }}
                          value={String(
                            remoteDiagnosis?.deviceCheckInfoEvent
                              ?.scheduleStatus
                          )}
                        />
                      ) : (
                        t('detail.current_schedule_status_unknown')
                      )}
                    </Typography>
                  </Stack>
                  <Divider />
                  <Stack
                    sx={{ pt: 3, pb: 3 }}
                    direction={"row"}
                    justifyContent={"space-between"}
                    alignItems={"center"}
                  >
                    <Typography variant="h5" component="p">
                      {/* 总存储空间/剩余存储空间 */}
                      {
                        t('detail.total_storage_free')
                      }
                    </Typography>
                    <Stack>
                      <Typography variant="h6" blod component="p">
                        {remoteDiagnosis?.deviceCheckInfoEvent?.totalDiskSpace
                          ? (
                              remoteDiagnosis?.deviceCheckInfoEvent
                                ?.totalDiskSpace / 1024
                            ).toFixed(2)
                          : "0"}
                        GB /
                        {remoteDiagnosis?.deviceCheckInfoEvent?.freeDiskSpace
                          ? (
                              remoteDiagnosis?.deviceCheckInfoEvent
                                ?.freeDiskSpace / 1024
                            ).toFixed(2)
                          : "0"}
                        GB
                      </Typography>
                    </Stack>
                  </Stack>
                  <Divider />
                  {remoteDiagnosis?.updateTime && (
                    <Stack
                      sx={{ pt: 3, pb: 3 }}
                      direction={"row"}
                      justifyContent={"space-between"}
                      alignItems={"center"}
                    >
                      <Typography variant="h5" component="p">
                      {
                        t('detail.update_time')
                      }
                      </Typography>
                      <Typography variant="h6" blod component="p">
                        {remoteDiagnosis?.updateTime}
                        {/* {()=>forUpdateTime(remoteDiagnosis?.updateTime)} */}
                      </Typography>
                    </Stack>
                  )}

                  <Divider />
                </Grid>
              </Grid>
            </Box>
          </TabPanel>
          <TabPanel value="alert" sx={{ padding: "10px" }}>
            <AlertTable screenId={search.get("id")} />
          </TabPanel>
        </TabContext>

        <Grid
          container
          justifyContent="flex-end"
          alignItems="center"
          sx={{ marginTop: 2 }}
        >
          <Button
            color="info"
            variant="outlined"
            onClick={() => {
              navigate("/screen/screenList");
            }}
          >
            {t("common.common_op_return")}
          </Button>
        </Grid>
      </MainCard>
    </>
  );
});

export default screenDetail;
