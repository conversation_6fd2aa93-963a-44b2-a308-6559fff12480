import { message } from "./i18n";
export const getComponentId = (pages) => {
  let maxId = 0;
  pages.forEach((page) => {
    page.componentList.forEach((component) => {
      if (component.componentId > maxId) {
        maxId = component.componentId;
      }
    });
    page?.tempLayout?.forEach((cmplist) => {
      cmplist?.componentList.forEach((component) => {
        if (component.componentId > maxId) {
          maxId = component.componentId;
        }
      });
    });
  });
  return maxId + 1;
};

export const fontList = [
  "Aa剑豪体",
  "MyriadPro-Semibold",
  "庞门正道标题体",
  "演示佛系体",
  "阿里妈妈东方大楷",
  "鸿雷行书简体",
  "Aa厚底黑",
  "Roboto Bold",
  "思源宋体 Regular",
  "演示夏行楷",
  "阿里妈妈刀隶体",
  "MyriadPro-Black",
  "字体圈欣意冠黑体",
  "思源黑体 Light",
  "演示悠然小楷",
  "阿里妈妈数黑体",
  "Roboto+Bold",
  "MyriadPro-Light",
  "字体圈欣意吉祥宋",
  "斗鱼追光体",
  "演示秋鸿楷",
  "MyriadPro-Regular",
  "字制区喜脉体",
  "沐瑶软笔手写体",
  "站酷庆科黄油体",
  "阿里巴巴普惠体 Regular",
  "阿里巴巴普惠体 Heavy",
];

export const fontSizeList = [
  12, 16, 18, 20, 22, 24, 26, 28, 30, 32, 36, 40, 60, 80, 100, 150,200,250,300,350
];

export const animationList = [
  {
    label: message("edit_emphasize"),
    options: [
      {
        label: message("edit_animation_bounce"),
        value: "bounce",
      },
      { label: message("edit_animation_flash"), value: "flash" },
      { label: message("edit_animation_pulse"), value: "pulse" },
      {
        label: message("edit_animation_rubberBand"),
        value: "rubberBand",
      },
      { label: message("edit_animation_shake"), value: "shake" },
      { label: message("edit_animation_swing"), value: "swing" },
      { label: message("edit_animation_tada"), value: "tada" },
      {
        label: message("edit_animation_wobble"),
        value: "wobble",
      },
    ],
  },
  {
    label: message("edit_spring"),
    options: [
      {
        label: message("edit_animation_bounceIn"),
        value: "bounceIn",
      },
      {
        label: message("edit_animation_bounceInDown"),
        value: "bounceInDown",
      },
      {
        label: message("edit_animation_bounceInLeft"),
        value: "bounceInLeft",
      },
      {
        label: message("edit_animation_bounceInRight"),
        value: "bounceInRight",
      },
      {
        label: message("edit_animation_bounceInUp"),
        value: "bounceInUp",
      },
    ],
  },
  {
    label: message("edit_fadeIn"),
    options: [
      {
        label: message("edit_animation_fadeIn"),
        value: "fadeIn",
      },
      {
        label: message("edit_animation_fadeInDown"),
        value: "fadeInDown",
      },
      {
        label: message("edit_animation_fadeInLeft"),
        value: "fadeInLeft",
      },
      {
        label: message("edit_animation_fadeInRight"),
        value: "fadeInRight",
      },
      {
        label: message("edit_animation_fadeInUp"),
        value: "fadeInUp",
      },
    ],
  },
  {
    label: message("edit_flip"),
    options: [
      { label: message("edit_animation_flip"), value: "flip" },
      {
        label: message("edit_animation_flipInX"),
        value: "flipInX",
      },
      {
        label: message("edit_animation_flipInY"),
        value: "flipInY",
      },
    ],
  },
  {
    label: message("edit_slide"),
    options: [
      {
        label: message("edit_animation_slideInUp"),
        value: "slideInUp",
      },
      {
        label: message("edit_animation_slideInDown"),
        value: "slideInDown",
      },
      {
        label: message("edit_animation_slideInLeft"),
        value: "slideInLeft",
      },
      {
        label: message("edit_animation_slideInRight"),
        value: "slideInRight",
      },
    ],
  },
  {
    label: message("edit_zoom"),
    options: [
      {
        label: message("edit_animation_zoomIn"),
        value: "zoomIn",
      },
      {
        label: message("edit_animation_zoomInDown"),
        value: "zoomInDown",
      },
      {
        label: message("edit_animation_zoomInLeft"),
        value: "zoomInLeft",
      },
      {
        label: message("edit_animation_zoomInRight"),
        value: "zoomInRight",
      },
      {
        label: message("edit_animation_zoomInUp"),
        value: "zoomInUp",
      },
    ],
  },
];

export const formatList = [
  {
    label: "yyyy-MM-dd HH:mm:ss " + message("editor_week"),
    value: "0",
  },
  { label: "yyyy", value: "1" },
  { label: "yyyy-MM", value: "2" },
  { label: "yyyy-MM-dd", value: "3" },
  { label: "yyyy-MM-dd HH", value: "4" },
  { label: "yyyy-MM-dd HH:mm", value: "5" },
  { label: "yyyy-MM-dd HH:mm:ss", value: "6" },
  { label: "yyyy-MM-dd " + message("editor_week"), value: "7" },
  { label: "MM-dd HH:mm", value: "8" },
  { label: "MM-dd", value: "9" },
  { label: "HH:mm", value: "10" },
];

export const dateFormat = (date, fmt) => {
  if (fmt === undefined || fmt === null) {
    fmt = "yyyy-MM-dd HH:mm:ss";
  }
  if (date === "" || date === undefined) {
    return "";
  }
  if (typeof date === "number") {
    date = new Date(date);
  }
  var o = {
    "M+": date.getMonth() + 1,
    "d+": date.getDate(),
    "h+": date.getHours() % 12 === 0 ? 12 : date.getHours() % 12,
    "H+": date.getHours(),
    "m+": date.getMinutes(),
    "s+": date.getSeconds(),
    "q+": Math.floor((date.getMonth() + 3) / 3),
    S: date.getMilliseconds(),
  };
  var week = {
    0: "/u65e5",
    1: "/u4e00",
    2: "/u4e8c",
    3: "/u4e09",
    4: "/u56db",
    5: "/u4e94",
    6: "/u516d",
  };
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (date.getFullYear() + "").substr(4 - RegExp.$1.length)
    );
  }
  if (/(E+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (RegExp.$1.length > 1
        ? RegExp.$1.length > 2
          ? "/u661f/u671f"
          : "/u5468"
        : "") + week[this.getDay() + ""]
    );
  }
  for (var k in o) {
    let rg = "(" + k + ")";
    if (new RegExp(rg).test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length)
      );
    }
  }
  return fmt;
};

export const isPositiveInteger = (str) => {
  return /^[1-9]\d*$/.test(str);
};

export const dictScrollDirection = [
  { label: "none", value: "" },
  { label: message("edit_scrollDirection_left"), value: "left" },
  {
    label: message("edit_scrollDirection_right"),
    value: "right",
  },
  { label: message("edit_scrollDirection_up"), value: "up" },
  { label: message("edit_scrollDirection_down"), value: "down" },
];

export const dictScrollSpeed = [
  { label: message("common_slow"), value: 20 },
  { label: message("common_general"), value: 60 },
  { label: message("common_fast"), value: 120 },
  { label: message("common_faster"), value: 200 },
];

export const photoLabelList = [
  { label: "BackgroundImage2", value: 20 },
  { label: "BackgroundImage3", value: 60 },
  { label: "BackgroundImage1", value: 120 },
  { label: "BackgroundImage4", value: 200 },
];

export const textLabelList = [
  { label: "Headline", value: 20 },
  { label: "Body Text 1", value: 60 },
  { label: "Body Text 2", value: 120 },
  { label: "Body Text 3", value: 200 },
];




export const pageDuration = (pagesInfo) => {
  pagesInfo.forEach((currentPage) => {
    if (currentPage.isTemplate) {
      try {
        let imageDuration = currentPage.tempLayout
          .map((cmpList) => {
            return cmpList.componentList
              .filter((item) => {
                if (item.type === "ZKTecoSlideShowImg") {
                  return true;
                } else {
                  return false;
                }
              })
              .map((item) => {
                return item.imgList
                  ?.map((imgItem) => {
                    try {
                      return parseInt(imgItem.duration);
                    } catch (e) {
                      return 0;
                    }
                  })
                  .reduce(
                    (accumulator, currentValue) => accumulator + currentValue
                  );
              });
          })
          .reduce(function (acc, curr) {
            return acc.concat(curr);
          }, []);

        let videoDuration = currentPage.tempLayout
          .map((cmpList) => {
            return cmpList.componentList
              .filter((item) => {
                if (item.type === "ZKTecoVideo") {
                  return true;
                } else {
                  return false;
                }
              })
              .map((item) => {
                return item.videoList
                  ?.map((imgItem) => {
                    try {
                      return parseInt(imgItem.duration);
                    } catch (e) {
                      return 0;
                    }
                  })
                  .reduce(
                    (accumulator, currentValue) => accumulator + currentValue
                  );
              });
          })
          .reduce(function (acc, curr) {
            return acc.concat(curr);
          }, []);

        let zkTecoLiveDuration = currentPage.tempLayout
          .map((cmpList) => {
            return cmpList.componentList
              .filter((item) => {
                if (item.type === "ZKTecoLive") {
                  return true;
                } else {
                  return false;
                }
              })
              .map((item) => {
                return item.duration;
              });
          })
          .reduce(function (acc, curr) {
            return acc.concat(curr);
          }, []);
        

        let durationArry = [
          ...imageDuration,
          ...zkTecoLiveDuration,
          ...videoDuration,
        ];
        
        if (durationArry.length > 0) {
          const maxNum = Math.max(...durationArry);
          currentPage.duration = maxNum;
        } else {
          currentPage.duration = 10;
        }
      } catch (e) {
        console.log(e);
      }
    } else {
      try {
        let imageDuration = currentPage.componentList
          .filter((item) => {
            if (item.type === "ZKTecoSlideShowImg") {
              return true;
            } else {
              return false;
            }
          })
          .map((item) => {
            return item.imgList
              ?.map((imgItem) => {
                try {
                  return parseInt(imgItem.duration);
                } catch (e) {
                  return 0;
                }
              })
              .reduce(
                (accumulator, currentValue) => accumulator + currentValue
              );
          });

        let videoSize = currentPage.componentList.filter((item) => {
          if (item.type === "ZKTecoVideo") {
            return true;
          } else {
            return false;
          }
        });

        if (videoSize.length === 1) {
          const sum = videoSize[0].videoList
            ?.map((item) => {
              try {
                return parseInt(item.duration);
              } catch (e) {
                return 0;
              }
            })
            .reduce(
              (accumulator, currentValue) => accumulator + currentValue
            );

          imageDuration.push(sum);
        }

        currentPage.componentList
          .filter((item) => {
            if (item.type === "ZKTecoLive") {
              return true;
            } else {
              return false;
            }
          })
          .forEach((item) => {
            try {
              let sum = parseInt(item.duration);

              imageDuration.push(sum);
            } catch (error) {
              console.log(error);
            }
          });

        if (imageDuration.length > 0) {
          const maxNum = Math.max(...imageDuration);
          currentPage.duration = maxNum;
        } else {
          currentPage.duration = 10;
        }
      } catch (e) {
        console.log(e);
      }
    }
  });
  return pagesInfo;
};
