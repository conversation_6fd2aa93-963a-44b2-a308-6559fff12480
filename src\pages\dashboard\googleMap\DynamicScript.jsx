import React, { useEffect } from "react";

const DynamicScript = ({ src, onLoad }) => {
  useEffect(() => {
    const script = document.createElement("script");
    script.src = src;
    script.async = true;

    window["__onGoogleMapLoaded"] = onLoad; // 定义回调函数

    script.addEventListener("load", () => {
      // 在脚本加载完成后判断并执行回调函数
      if (typeof window["__onGoogleMapLoaded"] === "function") {
        window["__onGoogleMapLoaded"]();
      }
    });

    document.body.appendChild(script);

    return () => {
      delete window["__onGoogleMapLoaded"]; // 移除回调函数
      script.removeEventListener("load", () => {
        if (typeof window["__onGoogleMapLoaded"] === "function") {
          window["__onGoogleMapLoaded"]();
        }
      });
      document.body.removeChild(script);
    };
  }, [src, onLoad]);

  return null;
};

export default DynamicScript;
