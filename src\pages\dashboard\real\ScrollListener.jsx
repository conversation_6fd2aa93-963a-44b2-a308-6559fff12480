import React, { useRef, useEffect } from 'react';
import './scroll.css'
const ScrollListener = () => {
  const scrollRef = useRef(null);
 
  const handleScroll = () => {
    const { scrollTop, scrollHeight, clientHeight } = scrollRef.current;
    console.log('Scroll Position:', scrollTop);
    // 你可以在这里添加更多的逻辑，比如当滚动到某个位置时触发某些操作
  };
 
  useEffect(() => {
    const element = scrollRef.current;
    element.addEventListener('scroll', handleScroll);
    return () => {
      element.removeEventListener('scroll', handleScroll);
    };
  }, []); // 空数组[]意味着effect只在组件挂载时执行一次
 
  return (
    <div className='scroll_box' ref={scrollRef} style={{ 
        height: '400px',
        width:'60px',
        overflow: 'auto',
        position:'absolute',
        right:'-5px',
        top:'50%',
        marginTop:'-200px'
     }}>
       <div style={{height:'1600px',width:'1px'}}></div>
    </div>
  );
};
 
export default ScrollListener;