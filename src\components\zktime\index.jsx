/* eslint-disable react-refresh/only-export-components */
/* eslint-disable react/prop-types */
/* eslint-disable react-hooks/rules-of-hooks */
import React, {
  forwardRef,
  useState,
  useImperativeHandle,
  useRef,
  createRef,
  memo,
} from "react";
import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import { Typography, Button } from "@mui/material";
import { makeStyles } from "@material-ui/core/styles";
import MenuList from "@mui/material/MenuList";
import MenuItem from "@mui/material/MenuItem";
import Divider from "@mui/material/Divider";

import { format, getHours } from "date-fns";
import { string } from "prop-types";
const useStyles = makeStyles({
  root: {},
  box: {
    width: 140,
    border: "1px solid #cccc",
    height: 180,
    marginTop: 5,
  },
  menu: {
    height: 180,
    overflow: "hidden",
    overflowX: "hidden",
    overflowY: "scroll",
    borderRight: "1px solid #cccc",
  },
  footer: {
    //background: 'red'
  },
  menuItem: {
    "&$selected": {
      backgroundColor: "rgb(122, 193, 67)", // 设置选中状态下的背景颜色
      color: "white",
    },
  },
  selected: {}, // 这个空对象必须存在，用于在 '&$selected' 中引用
});

const MenuData = memo(
  forwardRef((props, ref) => {
    const classes = useStyles();
    const [hour, setHour] = useState();
    const [minute, setMinute] = useState();
    const [seconds, setSeconds] = useState();
    const hours = [];
    const minutes = [];
    useImperativeHandle(ref, () => ({
      hour,
      minute,
      seconds,
      setHour,
      setMinute,
      setSeconds,
    }));
    const getMinutes = () => {
      for (let i = 0; i < 60; i++) {
        if (i < 10) {
          minutes.push("0" + i);
        } else {
          minutes.push("" + i);
        }
      }
    };
    const getHours = () => {
      for (let i = 0; i < 24; i++) {
        if (i < 10) {
          hours.push("0" + i);
        } else {
          hours.push("" + i);
        }
      }
    };

    React.useMemo(() => {
      getMinutes();
      getHours();
    });

    const handleHours = (hours) => {
      setHour(hours);
      props.setConfirmDisable(false);
      if (minute == null || seconds == null) {
        setMinute("00");
        setSeconds("00");
      }
    };

    const handleMinutes = (minute) => {
      setMinute(minute);
    };
    const handleSeconds = (second) => {
      setSeconds(second);
    };
    return (
      <Box className={classes.box}>
        <Grid
          container
          direction="row"
          justifyContent="center"
          alignItems="center"
        >
          <Grid item xs={4}>
            <MenuList className={classes.menu}>
              {hours.map((item) => {
                return (
                  <MenuItem
                    key={item}
                    className={`${classes.menuItem} ${
                      item == hour ? classes.selected : ""
                    }`}
                    selected={item == hour ? true : false}
                    onClick={() => {
                      handleHours(item);
                    }}
                  >
                    <Typography>{item}</Typography>
                  </MenuItem>
                );
              })}
            </MenuList>
          </Grid>
          <Grid item xs={4}>
            <MenuList className={classes.menu}>
              {minutes.map((item) => {
                return (
                  <MenuItem
                    key={item}
                    className={`${classes.menuItem} ${
                      item == minute ? classes.selected : ""
                    }`}
                    selected={item == minute ? true : false}
                    onClick={() => {
                      handleMinutes(item);
                    }}
                  >
                    <Typography>{item}</Typography>
                  </MenuItem>
                );
              })}
            </MenuList>
          </Grid>
          <Grid item xs={4}>
            <MenuList className={classes.menu}>
              {minutes.map((item) => {
                return (
                  <MenuItem
                    key={item}
                    className={`${classes.menuItem} ${
                      item == seconds ? classes.selected : ""
                    }`}
                    selected={item == seconds ? true : false}
                    onClick={() => {
                      handleSeconds(item);
                    }}
                  >
                    <Typography>{item}</Typography>
                  </MenuItem>
                );
              })}
            </MenuList>
          </Grid>
        </Grid>
      </Box>
    );
  })
);

const TimePicker = memo(
  ({ onChange = () => {}, startTimeText, endTimeText, confirmText }, props) => {
    const classes = useStyles(props);
    // const { startTimeText, endTimeText } = props;
    console.log(props);
    const [confirmDisable, setConfirmDisable] = useState(true);
    const [endConfirmDisable, setEndConfirmDisable] = useState(true);
    const startRef = useRef(null);
    const endRef = useRef(null);

    const handleChange = () => {
      const startTime = numberToDate(
        startRef.current.hour,
        startRef.current.minute,
        startRef.current.seconds
      );
      const endTime = numberToDate(
        endRef.current.hour,
        endRef.current.minute,
        endRef.current.seconds
      );
      const time = { startTime: startTime, endTime: endTime };
      onChange(time);
    };
    // 此刻
    // const handelNow = () => {
    //     const now = format(new Date(), 'HH:mm:ss');
    //     const nows = now.split(':');
    //     const hours = getHours(nows[0]);
    //     const minute = getMinute(nows[1]);
    //     const second = getSecond(nows[2]);
    //     console.log(hours, minute, second);
    //     setValue(hours, minute, second);
    //     setConfirm();
    // };
    // const setConfirm = () => {
    //     setConfirmDisable(false);
    //     setEndConfirmDisable(false);
    // };
    // const setValue = (hours, minute, second) => {
    //     startRef.current.setHour(hours);
    //     startRef.current.setMinute(minute);
    //     startRef.current.setSeconds(second);
    //     endRef.current.setHour(hours);
    //     endRef.current.setMinute(minute);
    //     endRef.current.setSeconds(second);
    // };
    // const getSecond = (now) => {
    //     const second = now.startsWith('0') ? now.substring(1, 2) : now;
    //     return second;
    // };
    // const getMinute = (now) => {
    //     const minute = now.startsWith('0') ? now.substring(1, 2) : now;
    //     return minute;
    // };
    // const getHours = (now) => {
    //     const hour = now.startsWith('0') ? now.substring(1, 2) : now;
    //     return hour;
    // };
    const numberToDate = (hours, minutes, secondss) => {
      const time = hours + ":" + minutes + ":" + secondss;
      return time;
    };
    return (
      <Paper elevation={5} square className={classes.root}>
        <Grid container spacing={2} sx={{ padding: "10px" }}>
          <Grid item xs={6}>
            <Grid
              container
              direction="column"
              justifyContent="center"
              alignItems="center"
            >
              <Typography>
                {startTimeText ? startTimeText : "开始时间"}
              </Typography>
              <MenuData ref={startRef} setConfirmDisable={setConfirmDisable} />
            </Grid>
          </Grid>
          <Grid item xs={6}>
            <Grid
              container
              direction="column"
              justifyContent="center"
              alignItems="center"
            >
              <Typography>{endTimeText ? endTimeText : "结束时间"}</Typography>
              <MenuData ref={endRef} setConfirmDisable={setEndConfirmDisable} />
            </Grid>
          </Grid>
        </Grid>
        <div className={classes.footer}>
          <Divider sx={{ marginLeft: 0 }} variant="inset" />
          <Grid
            container
            direction="row"
            justifyContent="flex-end"
            alignItems="center"
          >
            {/* <Grid item sx={2}>
                        <Button
                            onClick={() => {
                                handelNow();
                            }}
                        >
                            此刻
                        </Button>
                    </Grid> */}
            <Grid item sx={{ marginBottom: "6px", marginRight: "6px" }}>
              <Button
                disabled={confirmDisable || endConfirmDisable}
                variant="contained"
                onClick={() => {
                  handleChange();
                }}
              >
                {confirmText ? confirmText : "确定"}
              </Button>
            </Grid>
          </Grid>
        </div>
      </Paper>
    );
  }
);

export default TimePicker;
