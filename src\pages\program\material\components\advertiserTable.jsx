import React from "react";
/* eslint-disable react/prop-types */
import {
  useEffect,
  useMemo,
  useState,
  useRef,
  forwardRef,
  useImperativeHandle,
} from "react";
import MaterialReactTable, {
  MRT_ToggleGlobalFilterButton,
} from "material-react-table";
import TableCell, { tableCellClasses } from "@mui/material/TableCell";
import {
  Button,
  Stack,
  Typography,
  Box,
  Link,
  IconButton,
} from "@mui/material";
import RefreshIcon from "@mui/icons-material/Refresh";
import { useConfirm } from "@/components/zkconfirm";
// api
import { listByPage } from "@/service/api/merchant";
import { toast } from "react-toastify";
import { tableI18n } from "@/utils/tableLang";
// i18n
import { useTranslation } from "react-i18next";
const AdvertiserTable = forwardRef((props, ref) => {
  const { t } = useTranslation();
  const { type, setTableObject = () => {} } = props;
  const confirm = useConfirm();
  const [rowSelection, setRowSelection] = useState([]);
  useImperativeHandle(ref, () => ({
    getTableData,
  }));

  const [isError, setIsError] = useState(false);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  // 表格数据
  const [data, setData] = useState([]);
  // 分页的参数
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 5,
  });
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);
  // 过滤参数
  const [globalFilter, setGlobalFilter] = useState("");
  // 排序参数
  const [sorting, setSorting] = useState([]);
  // 列过滤
  const [columnFilters, setColumnFilters] = useState([]);

  // 构建参数
  const buildParams = () => {
    console.log(globalFilter);
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      //orderProp: 'create_time',
      orderBy: "desc",
      name: globalFilter,
      type: type,
    };
    console.log(params);
    return params;
  };
  // 获取数据
  const getTableData = async () => {
    // if (!data.length) {
    //   setIsLoading(true);
    // } else {
    //   setIsRefetching(true);
    // }
    setIsRefetching(true);
    // 开启加载
    await listByPage(buildParams())
      .then((res) => {
        // 设置数据
        setData(res.data.data);
        // 设置总记录数
        setRowCount(res.data.total);
        setIsLoading(false);
        setIsRefetching(false);
      })
      .catch((err) => {
        setIsError(true);
        setIsLoading(false);
        setIsRefetching(false);
      });
  };
  useEffect(() => {
    // const params = buildParams();
    // 发请求
    setRowSelection([]);
    getTableData();
  }, [
    columnFilters,
    globalFilter,
    sorting,
    pagination.pageIndex,
    pagination.pageSize,
  ]);
  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "name", //access nested data with dot notation
        header:
          type == 1
            ? t("common.common_retailer_name")
            : t("common.common_advertiser_name"),
        //列头点击相关事件关闭
        enableColumnActions: false,
        size: "200",
      },
    ],
    []
  );
  return (
    <>
      <MaterialReactTable
        // table 状态
        state={{
          // 加载状态
          isLoading,
          // 分页参数
          pagination,
          // 重新拉取
          showProgressBars: isRefetching,
          showAlertBanner: isError,
          columnFilters,
          globalFilter,
          sorting,
          rowSelection,
        }}
        renderToolbarInternalActions={({ table }) => (
          <>
            <MRT_ToggleGlobalFilterButton table={table} />
          </>
        )}
        muiTablePaperProps={{
          elevation: 0,
          sx: {
            borderRadius: "5px",
            // border: "1px solid #f0f0f0",
          },
        }}
        enableMultiRowSelection={false}
        muiTableHeadRowProps={{
          sx: { backgroundColor: "#fafafa", boxShadow: "none" },
        }}
        // 解决列太多宽度太长问题
        enableColumnResizing
        // 初始化状态
        initialState={{ columnVisibility: { createTime: false } }}
        muiToolbarAlertBannerProps={
          isError
            ? {
                color: "error",
                children: t("table.loading_error"),
              }
            : undefined
        }
        //行选中
        onRowSelectionChange={setRowSelection}
        muiTableBodyRowProps={({ row }) => ({
          onClick: row.getToggleSelectedHandler(),
          sx: { cursor: "pointer" },
        })}
        // 开启多选
        enableRowSelection
        // 列数
        rowCount={rowCount}
        // 固定头部
        enableStickyHeader
        // 处理表格高度
        muiTableContainerProps={{ sx: { maxHeight: "620px" } }}
        // 设置背景颜色
        muiTableBodyCellProps={{ sx: { backgroundColor: "white" } }}
        muiTableProps={{ sx: { backgroundColor: "white" } }}
        muiBottomToolbarProps={{ sx: { backgroundColor: "white" } }}
        muiTopToolbarProps={{ sx: { backgroundColor: "white" } }}
        // 分页回调函数
        onPaginationChange={setPagination}
        // 记得开启这些 tips:否则会出现useEffect 反作用函数发起多次请求点击下一页会出问题
        manualFiltering
        manualPagination
        manualSorting
        // 列过滤
        onColumnFiltersChange={setColumnFilters}
        // 全局过滤
        onGlobalFilterChange={setGlobalFilter}
        // 排序
        onSortingChange={setSorting}
        // 开启分页
        enablePagination
        // 列定义
        columns={columns}
        // 数据
        data={data}
        // 初始化国际化语言
        localization={tableI18n}
        // 自定义表头按钮
        renderTopToolbarCustomActions={({ table }) => {
          setTableObject(table.getSelectedRowModel().rows);
        }}
        // 多选底部提示
        positionToolbarAlertBanner="none"
      />
    </>
  );
});

export default AdvertiserTable;
