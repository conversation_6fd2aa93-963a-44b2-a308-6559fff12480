import request from "@/utils/request";

const baseProfixURI = "/location";

/**
 *  分页查询 
 * <AUTHOR>
 * @date 2023-12-11 11:30
 */
export const listByPage = (params) => {
  return request({
    url: `${baseProfixURI}/page`,
    method: "get",
    params: params,
  });
};

/**
 * 保存
 * <AUTHOR>
 * @date 2023-12-11 11:41
 */
export const save = (params) => {
  return request({
    url: `${baseProfixURI}/save`,
    method: "PUT",
    data: params,
  });
};

/**
 * 修改
 * <AUTHOR>
 * @date 2023-12-11 11:41
 */
export const update = (params) => {
  return request({
    url: `${baseProfixURI}/update`,
    method: "POST",
    data: params,
  });
};

/**
 * 删除
 * <AUTHOR>
 * @date 2023-03-28 11:51
 */
export const removeLocationType = (ids) => {
  return request({
    url: `${baseProfixURI}/delete/${ids}`,
    method: "delete",
  });
};


export const getLocationTypeOption = () => {
  return request({
    url: `${baseProfixURI}/option`,
    method: "get",
  });
};
