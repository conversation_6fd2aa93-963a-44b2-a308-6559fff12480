const agreeKey = 'ZK_isReadAgree';
/**
 *
 * @param {设置缓存的值} value
 */
export const setAgree = (value) => {
    window.localStorage.setItem(agreeKey, JSON.stringify(value));
};

/**
 *  获取阅读协议的缓存，如果没有则返回false
 * <AUTHOR>
 * @date 2023-01-04 09:22
 */
export const getAgree = () => {
    const agreeValue = JSON.parse(window.localStorage.getItem(agreeKey));
    console.log(typeof agreeValue);
    if (agreeValue && (agreeValue === false || agreeValue === true)) {
        return agreeValue;
    } else {
        return false;
    }
};

//清除看板中的缓存
export const clearDashboard = ()=>{
    localStorage.removeItem("mapSelectedRetailValue")
    localStorage.removeItem("mapSelectedRegionValue")
    localStorage.removeItem("outletSelectedRetailValue")
    localStorage.removeItem("summarySelectedRetailValue")
    localStorage.removeItem("summarySelectedRegionValue")
    localStorage.removeItem("outletSelectedRegionValue")
}