/* eslint-disable react/prop-types */
import React, { forwardRef, useState } from "react";
import { Stack, Typography, Button } from "@mui/material";
import {
  BootstrapDialog,
  BootstrapContent,
  BootstrapDialogTitle,
} from "@/components/dialog";

// i18n
import { useTranslation } from "react-i18next";
import { toast } from "react-toastify";
import LinkScreenTable from "./screenTable";

//拼接屏列表
const addLinkScreenList = forwardRef((props, ref) => {
  const { t } = useTranslation();
  const [open, setOpen] = React.useState(false);
  const [index, setIndex] = React.useState(0);

  React.useImperativeHandle(ref, () => ({
    handleClose,
    handleOpen,
    setIndex,
  }));

  //表单选择对象
  const [tableObject, setTableObject] = useState([]);

  const handleLinkScreen = (screen) => {
    if (screen.status != "1") {
      toast.error(t("ips.ips_offline_not_select"));
    } else {
      props.setFormValues(screen);
      handleClose();
    }
  };

  const handleClose = () => {
    setOpen(false);
  };
  const handleOpen = () => {
    setOpen(true);
  };

  return (
    <>
      <BootstrapDialog
        open={open}
        maxWidth="md"
        fullWidth={true}
        onClose={handleClose}
        aria-describedby="alert-dialog-slide-description">
        <BootstrapDialogTitle onClose={handleClose}>
          <Typography variant="h4" component="p">
            {t("ips.ips_signage_select")}
          </Typography>
        </BootstrapDialogTitle>
        <BootstrapContent>
          <LinkScreenTable
            setTableObject={setTableObject}
            getData={props.getData}
          />
        </BootstrapContent>
        <Stack
          direction="row"
          justifyContent="flex-end"
          sx={{ margin: "15px" }}>
          <Button
            variant="contained"
            color="primary"
            disabled={tableObject.length > 1 || tableObject.length === 0}
            onClick={() => {
              handleLinkScreen(tableObject[0].original);
            }}>
            {t("common.common_edit_ok")}
          </Button>
        </Stack>
      </BootstrapDialog>
    </>
  );
});

export default addLinkScreenList;
