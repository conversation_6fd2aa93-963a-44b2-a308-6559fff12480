/**
 * 百度地图API加载器
 * 解决document.write警告问题
 */

// 百度地图API配置
const BAIDU_MAP_CONFIG = {
  ak: 'hLoxcjPXor12FLim84yZ4Rgo4TqIqPx5', // 您的百度地图API密钥
  version: '3.0',
  type: 'webgl',
  services: ''
};

// 全局状态管理
let isLoading = false;
let isLoaded = false;
let loadPromise = null;
const callbacks = [];

/**
 * 动态加载百度地图API脚本
 * @returns {Promise} 加载完成的Promise
 */
export const loadBaiduMapAPI = () => {
  // 如果已经加载完成，直接返回resolved Promise
  if (isLoaded && window.BMapGL) {
    return Promise.resolve(window.BMapGL);
  }

  // 如果正在加载，返回现有的Promise
  if (isLoading && loadPromise) {
    return loadPromise;
  }

  // 开始加载
  isLoading = true;
  loadPromise = new Promise((resolve, reject) => {
    // 检查是否已经存在百度地图脚本
    const existingScript = document.querySelector('script[src*="api.map.baidu.com"]');
    if (existingScript) {
      // 如果脚本已存在但API未加载完成，等待加载
      const checkLoaded = () => {
        if (window.BMapGL) {
          isLoaded = true;
          isLoading = false;
          resolve(window.BMapGL);
        } else {
          setTimeout(checkLoaded, 100);
        }
      };
      checkLoaded();
      return;
    }

    // 创建script标签
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.async = true;
    script.defer = true;
    
    // 构建API URL
    const apiUrl = `https://api.map.baidu.com/api?v=${BAIDU_MAP_CONFIG.version}&type=${BAIDU_MAP_CONFIG.type}&ak=${BAIDU_MAP_CONFIG.ak}&services=${BAIDU_MAP_CONFIG.services}&t=${Date.now()}`;
    script.src = apiUrl;

    // 加载成功回调
    script.onload = () => {
      console.log('✅ 百度地图API加载成功');
      isLoaded = true;
      isLoading = false;
      
      // 等待API完全初始化
      const checkAPI = () => {
        if (window.BMapGL) {
          resolve(window.BMapGL);
          // 执行所有等待的回调
          callbacks.forEach(callback => callback(window.BMapGL));
          callbacks.length = 0;
        } else {
          setTimeout(checkAPI, 50);
        }
      };
      checkAPI();
    };

    // 加载失败回调
    script.onerror = (error) => {
      console.error('❌ 百度地图API加载失败:', error);
      isLoading = false;
      loadPromise = null;
      reject(new Error('百度地图API加载失败'));
    };

    // 添加到页面头部
    document.head.appendChild(script);
  });

  return loadPromise;
};

/**
 * 确保百度地图API已加载
 * @param {Function} callback 加载完成后的回调函数
 */
export const ensureBaiduMapAPI = (callback) => {
  if (isLoaded && window.BMapGL) {
    callback(window.BMapGL);
    return;
  }

  if (typeof callback === 'function') {
    callbacks.push(callback);
  }

  loadBaiduMapAPI().catch(error => {
    console.error('百度地图API加载失败:', error);
  });
};

/**
 * 检查百度地图API是否已加载
 * @returns {boolean}
 */
export const isBaiduMapAPILoaded = () => {
  return isLoaded && !!window.BMapGL;
};

/**
 * 预加载百度地图API（在应用启动时调用）
 */
export const preloadBaiduMapAPI = () => {
  // 只在中国版本中预加载
  if (window.localStorage.getItem("localVersion") === 'CN') {
    console.log('🚀 开始预加载百度地图API...');
    loadBaiduMapAPI().then(() => {
      console.log('✅ 百度地图API预加载完成');
    }).catch(error => {
      console.warn('⚠️ 百度地图API预加载失败:', error);
    });
  }
};

// 导出默认配置
export { BAIDU_MAP_CONFIG };
