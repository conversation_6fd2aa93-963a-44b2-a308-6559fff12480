/* eslint-disable no-undef */
/* eslint-disable react/prop-types */
import React, { useMemo, memo, useState, useRef } from "react";
import {
  Grid,
  Box,
  Button,
  OutlinedInput,
  Stack,
  InputAdornment,
  InputBase,
  FormHelperText,
  Typography,
  TextField,
  InputLabel,
} from "@mui/material";
import LoadingButton from "@mui/lab/LoadingButton";
import { toast } from "react-toastify";
import dayjs from "dayjs";
import Popover from "@mui/material/Popover";
import TimePickert from "@/components/zktime";
import { useFormik } from "formik";
import AdvancedDialog from "./AdvancedModal";
// api
import { saveCommonSchedule } from "@/service/api/schedule";

import "@amir04lm26/react-modern-calendar-date-picker/lib/DatePicker.css";
import { Calendar, utils } from "@amir04lm26/react-modern-calendar-date-picker";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import { getCalendarLocales } from "@/utils/calendarLocales";
// i18n
import { useTranslation } from "react-i18next";
import DateSvg from "./DateSvg";
import MainCard from "@/components/MainCard";
import { useNavigate } from "react-router-dom";
import moment from "moment";
import ScreenSelect from "./ScreenSelect";
import AddPlayList from "./AddPlayList";
import TimeButtonSelect from "./TimeButtonSelect";
import Container from "@mui/material/Container";
import { useForm, Controller, useFieldArray } from "react-hook-form";
import * as Yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";

import { getPlayListInfo } from "@/service/api/playList";
export default function AddCommonSchedule(props) {
  const [advancedOpen, setAdvancedOpen] = useState(false);
  const timeButtonSelectRef = useRef(null);
  const [advancedData, setAdvancedData] = useState([]);
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [publishLoading, setPublishLoading] = useState(false);
  const [publishDisable, setPublishDisable] = useState(false);
  const [saveDisable, setSaveDisable] = useState(false);
  //步骤
  const [activeStep, setActiveStep] = useState(0);
  const [tips, setTips] = useState(t("ips.ips_first_step"));
  const [anchorEl, setAnchorEl] = useState(null);
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const [tableObject, setTableObject] = useState([]);
  const statMaterialSizeRef = useRef(null);
  const open = Boolean(anchorEl);

  const handleResetLoading = () => {
    setSaveDisable(false);
    setPublishDisable(false);
    setLoading(false);
    setPublishLoading(false);
  };

  // 获取rowID
  const getRowId = async (selects) => {
    return await new Promise((reslove) => {
      const ids = [];
      selects.forEach((select) => {
        ids.push(select.original.id);
      });
      reslove(ids.toString());
    });
  };

  //上一步
  const previousStep = () => {
    if (activeStep === 0) {
      return;
    }
    setActiveStep(activeStep - 1);
    setTips(t("ips.ips_first_step"));
  };

  //下一步
  const nextStep = () => {
    if (tableObject.length === 0) {
      toast.error(t("ips.ips_at_least_one"));
      return;
    }
    let flag = false;
    for (let item of tableObject) {
      //判断是否存在不在线的设备
      if (item.original.status === "0") {
        flag = true;
        break;
      }
    }
    if (flag) {
      toast.error(t(t("ips.ips_no_select_offline")));
      return;
    }
    setActiveStep(activeStep + 1);
    setTips(t("ips.ips_second_step"));
  };
  const defaultRange = {
    from: null,
    to: null,
  };

  const [selectedDayRange, setSelectedDayRange] = useState(defaultRange);

  const schema = Yup.object().shape({
    name: Yup.string().required(t("common.common_input_scheduling_name")),
    startDate: Yup.string().required(t("common.common_select_dateInterval")),
    stopDate: Yup.string().required(t("common.common_select_dateInterval")),
  });
  // 新表单组件
  const {
    control,
    handleSubmit,
    setValue,
    register,
    reset,
    getValues,
    formState: { errors },
  } = useForm({
    mode: "all",
    resolver: yupResolver(schema),
  });

  // 初始化表单项数量
  const initialFieldsLength = React.useRef(0);
  // 清单列表，用于动态创建渲染
  const { fields, append, remove } = useFieldArray({
    control,
    name: "playLists",
  });

  React.useEffect(() => {
    // if (fields.length < 5) {
    const initialFormData = [
      { playListId: "", playListName: "", startTime: "", stopTime: "" },
      { playListId: "", playListName: "", startTime: "", stopTime: "" },
      { playListId: "", playListName: "", startTime: "", stopTime: "" },
      { playListId: "", playListName: "", startTime: "", stopTime: "" },
      { playListId: "", playListName: "", startTime: "", stopTime: "" },
    ];
    // fields([]);
    initialFormData.forEach((data) => {
      append(data);
    });
    // }
    // initialFieldsLength.current = fields.length;
  }, [append]);
  // 表单提交
  const onSubmit = async (data, event) => {
    event.preventDefault();
    // console.log(data);
    if (timeButtonSelectRef.current.monthsSelectList.length === 0) {
      toast.error(t("ips.ips_select_month"));
      return;
    }
    if (timeButtonSelectRef.current.daysSelectList.length === 0) {
      toast.error(t("ips.ips_select_date"));
      return;
    }
    //这是判断LCD-L101设备的校验 (无论保存还是发布)
    //1.判断设备中是否存在LCD-L101设备 2.是否存在1个以上的清单 3.清单中的素材是否超过60MB
    const isExistLCD = tableObject.some(
      (item) => item.original.screenModel === "LCD-L101"
    );

    const selectPlayList = data.playLists.filter(
      (item) =>
        item.playListId !== "" &&
        item.playListName !== "" &&
        item.startTime !== "" &&
        item.stopTime !== ""
    );
    if (isExistLCD) {
      if (selectPlayList.length == 1) {
        await getPlayListInfo(selectPlayList[0].playListId).then((res) => {
          const totalSize = res.data.playListMaterialList.reduce(
            (total, item) => total + (item.size || 0),
            0
          );
          statMaterialSizeRef.current = totalSize;
        });
        if (
          statMaterialSizeRef.current !== null &&
          statMaterialSizeRef.current > 60
        ) {
          toast.error(t("ips.ips_schedule_LCD_L101_valid"));
          return;
        }
      } else {
        toast.error(t("ips.ips_schedule_LCD_L101_valid"));
        return;
      }
    }
    //清除冗余播放清单对象
    removePlayListEmpty();
    const playLists = getValues().playLists;
    if (playLists.length === 0) {
      toast.error(t("ips.ips_please_select_playlist"));
      return;
    }
    let errorInventory = playLists.filter(
      (newInventory) =>
        newInventory.playListId == "" ||
        newInventory.startTime == "" ||
        newInventory.stopTime == ""
    );
    if (errorInventory.length > 0) {
      toast.error(t("ips.ips_playlist_exist"));
      return;
    }
    const ids = await getRowId(tableObject);
    const params = { ...data };

    params.inventoryList = params.playLists;
    params.screenIds = ids;

    params.playWeeks = timeButtonSelectRef.current.weeksSelectList;
    params.playDays = timeButtonSelectRef.current.daysSelectList;
    params.playMonths = timeButtonSelectRef.current.monthsSelectList;

    //分辨率
    params.resolution = "1920x1080";
    if (params.operateType == "1") {
      setPublishDisable(true);
      setLoading(true);
    }
    if (params.operateType === "0") {
      setSaveDisable(true);
      setPublishLoading(true);
    }
    // 删除对象属性
    delete params.playLists;

    //提交表单
    await saveCommonSchedule(params)
      .then((res) => {
        if (isExistLCD) {
          toast.success(t("ips.ips_schedule_LCD_L101_Invalid"));
        } else {
          toast.success(res.message);
        }

        // scheduleForm.setStatus({ success: true });
        // scheduleForm.setSubmitting(true);
        //跳转到投放排期界面
        navigate(-1);
      })
      .catch((error) => {
        handleResetLoading();
      });
  };

  const PlayListFromField = (props) => {
    // const { item, index } = props;
    const addPlayListRef = useRef(null);
    //设置清单id和名称
    const setPlayListFormValues = (playListId, playListName, index) => {
      setValue(`playLists[${index}].playListId`, playListId);
      setValue(`playLists[${index}].playListName`, playListName);
    };

    const [anchorTime, setAnchorTime] = useState(null);
    const [timeIndex, setTimeIndex] = useState();
    const handleTimeClick = (event, index) => {
      setTimeIndex(index);
      setAnchorTime(event.currentTarget);
    };
    const handleTimeClose = () => {
      setAnchorTime(null);
    };
    const openTimeRange = Boolean(anchorTime);
    const timeRangeId = openTimeRange ? "time-popover" : undefined;
    return (
      <>
        <Popover
          elevation={3}
          id={timeRangeId}
          open={openTimeRange}
          anchorEl={anchorTime}
          onClose={handleTimeClose}
          anchorOrigin={{
            vertical: "bottom",
            horizontal: "left",
          }}>
          <TimePickert
            endTimeText={t("common.common_endTime")}
            startTimeText={t("common.common_startTime")}
            confirmText={t("common.common_edit_ok")}
            onChange={(time) => {
              //时间段是否存在重叠标识
              let flag = false;
              //已选播放清单
              let timePeriodList = [...getValues().playLists];
              console.log(getValues().playLists, "getValues();");
              //开始时间
              let startTime = new Date("2022-06-01T" + time.startTime);
              //结束时间
              let endTime = new Date("2022-06-01T" + time.endTime);

              if (startTime.getTime() >= endTime.getTime()) {
                toast.error(t("ips.ips_starttime_greater_endtime"));
                return;
              }

              timePeriodList.forEach((timePeriod, index) => {
                //排除自身与其它时间段比较
                if (timeIndex != index) {
                  //排除未选择的播放时间段
                  if (timePeriod.startTime != "" && timePeriod.stopTime != "") {
                    //该时间段开始时间
                    let thisStartTime = new Date(
                      "2022-06-01T" + timePeriod.startTime
                    );
                    //该时间段结束时间
                    let thisStopTime = new Date(
                      "2022-06-01T" + timePeriod.stopTime
                    );
                    //交集判断结果
                    let result =
                      (startTime.getTime() < thisStartTime.getTime() &&
                        thisStartTime.getTime() < endTime.getTime()) ||
                      (startTime.getTime() < thisStopTime.getTime() &&
                        thisStopTime.getTime() < endTime.getTime()) ||
                      (startTime.getTime() >= thisStartTime.getTime() &&
                        endTime.getTime() <= thisStopTime.getTime()) ||
                      (startTime.getTime() <= thisStartTime.getTime() &&
                        endTime.getTime() >= thisStopTime.getTime());

                    if (result) {
                      //时间段存在交集
                      flag = true;
                      return;
                    }
                  }
                }
              });

              if (flag) {
                toast.error(t("ips.ips_overlap_play_time"));
                return;
              } else {
                setValue(`playLists[${timeIndex}].startTime`, time.startTime);
                setValue(`playLists[${timeIndex}].stopTime`, time.endTime);
                handleTimeClose();
              }
            }}
          />
        </Popover>
        <AddPlayList
          ref={addPlayListRef}
          setPlayListFormValues={setPlayListFormValues}
          playListType={"0"}
          isLayout={"1"}
        />
        {fields.map((field, index) => {
          return (
            <Grid
              key={field.id}
              container
              sx={{ marginBottom: "10px" }}
              justifyContent="flex-start"
              alignItems="center">
              <Grid item xs={5.5} sm={5.5} md={5.5}>
                <Controller
                  name={`playLists[${index}].playListName`}
                  control={control}
                  defaultValue={field.playListName}
                  render={({ field }) => (
                    <OutlinedInput
                      {...field}
                      id="schedule-playListName"
                      type="text"
                      name="playListName"
                      sx={{ width: "100%" }}
                      readOnly
                      // value={
                      //   scheduleForm.values.inventoryList[props.index]
                      //     .playListName
                      // }
                      // onBlur={scheduleForm.handleBlur}
                      // onChange={scheduleForm.handleChange}
                      placeholder={t("ips.ips_please_select_playlist")}
                      onClick={() => {
                        addPlayListRef.current.handleOpen();
                        addPlayListRef.current.setIndex(index);
                      }}></OutlinedInput>
                  )}
                />
              </Grid>
              <Grid item xs={1}>
                <Typography variant="body1" align="center">
                  {t("common.common_from")}
                </Typography>
              </Grid>
              <Grid item xs={5.5} sm={5.5} md={5.5}>
                <Controller
                  name={`playLists[${index}].stopTime`}
                  control={control}
                  defaultValue={field.stopTime}
                  render={({ field }) => (
                    <OutlinedInput
                      {...field}
                      onClick={(event) => {
                        handleTimeClick(event, index);
                      }}
                      id="infor-firstName"
                      type="text"
                      // readOnly
                      sx={{ width: "100%" }}
                      placeholder={t("common.common_endTime")}
                      startAdornment={
                        <InputAdornment position="start" sx={{ width: "120%" }}>
                          <Controller
                            name={`playLists[${index}].startTime`}
                            control={control}
                            defaultValue={field.startTime}
                            render={({ field }) => (
                              <InputBase
                                {...field}
                                // readOnly
                                endAdornment={<DateSvg />}
                                type="text"
                                placeholder={t("common.common_startTime")}
                                sx={{
                                  width: "100%",
                                }}
                              />
                            )}
                          />
                        </InputAdornment>
                      }
                    />
                  )}
                />
              </Grid>
            </Grid>
          );
        })}
      </>
    );
  };
  // 清除冗余动态表单
  const removePlayListEmpty = () => {
    const emptyIndexes = [];
    const playLists = getValues().playLists;
    playLists.forEach((item, index) => {
      if (
        item.playListId === "" ||
        item.playListName === "" ||
        item.startTime === "" ||
        item.stopTime === ""
      ) {
        emptyIndexes.push(index);
      }
    });
    console.log("emptyIndexes", emptyIndexes);
    emptyIndexes.reverse().forEach((index) => remove(index));
  };

  const handleOpenAdvanced = () => {
    const playLists = getValues().playLists;
    const tempData = [];
    playLists.forEach((item) => {
      if (
        item.playListId !== "" ||
        item.playListName !== "" ||
        item.startTime !== "" ||
        item.stopTime !== ""
      ) {
        tempData.push({
          id: item.playListId,
          name: item.playListName,
          time: {
            startTime: item.startTime,
            endTime: item?.stopTime,
          },
        });
      }
    });
    setAdvancedData(tempData);
    setAdvancedOpen(true);
  };

  const handleRestPlayListFormField = (values) => {
    return new Promise((reslove, reject) => {
      // const fields = [];
      // values.forEach((value) => {
      //     if (
      //         value?.id != "" ||
      //         value?.name != "" ||
      //         value?.time?.startTime != "" ||
      //         value?.time?.endTime != ""
      //     ) {
      //         fields.push({
      //             playListId: value?.id,
      //             playListName: value?.name,
      //             startTime: value.time?.startTime,
      //             stopTime: value?.time?.endTime,
      //         });
      //     }
      // });
      reset({ ...getValues(), playLists: values });
      reslove();
    });
  };

  return (
    <MainCard
      title={
        <MainCardTitle title={t("ips.ips_new_digital_signage")} tips={tips} />
      }
      border={false}
      contentSX={{ p: 0 }}>
      <form noValidate onSubmit={handleSubmit(onSubmit)}>
        {/* 排期form 修改为先选择设备 */}
        {activeStep === 0 && (
          <div style={{ padding: "8px 8px 8px 8px" }}>
            {/* 普通设备选择*/}
            <ScreenSelect setTableObject={setTableObject} />
            <Stack
              sx={{ marginTop: "20px" }}
              direction="row"
              justifyContent="flex-end"
              alignItems="center"
              spacing={2}>
              <Button
                variant="outlined"
                // fullWidth
                disableElevation
                size="large"
                color="info"
                onClick={() => navigate(-1)}>
                {t("common.common_close")}
              </Button>
              <Button
                variant="contained"
                // fullWidth
                disableElevation
                size="large"
                color="primary"
                onClick={() => {
                  //下一步选择清单和播放时间
                  nextStep();
                }}>
                {t("common.common_next")}
              </Button>
            </Stack>
          </div>
        )}
        {activeStep === 1 && (
          <>
            <Container maxWidth="sm" sx={{ padding: 3 }}>
              <Grid container spacing={1}>
                <Grid item xs={6}>
                  <Stack spacing={1}>
                    <InputLabel htmlFor="schedule-name">
                      {t("ips.ips_scheduling")}
                      <i style={{ color: "red" }}>*</i>
                    </InputLabel>
                    <Controller
                      name="name"
                      control={control}
                      defaultValue=""
                      render={({ field }) => (
                        <OutlinedInput
                          onBlur={field.onBlur}
                          {...field}
                          fullWidth
                          type="text"
                          error={Boolean(errors.name)}
                          placeholder={t("common.common_input_scheduling_name")}
                        />
                      )}
                    />
                    {errors.name?.message && (
                      <FormHelperText
                        error
                        id="standard-weight-helper-text-name-schedule">
                        {errors.name?.message}
                      </FormHelperText>
                    )}
                  </Stack>
                </Grid>
                <Grid item xs={6}>
                  <Stack spacing={1}>
                    <InputLabel htmlFor="infor-dateRange">
                      {t("ips.ips_scheduling_playTime")}
                      <i style={{ color: "red" }}>*</i>
                    </InputLabel>
                    <Controller
                      name="startDate"
                      control={control}
                      defaultValue=""
                      render={({ field }) => (
                        <OutlinedInput
                          {...field}
                          onBlur={field.onBlur}
                          onClick={handleClick}
                          startAdornment={
                            <InputAdornment position="start">
                              <CalendarMonthIcon />
                            </InputAdornment>
                          }
                          endAdornment={
                            <InputAdornment
                              position="end"
                              sx={{
                                width: "120%",
                              }}>
                              <Controller
                                name="stopDate"
                                control={control}
                                defaultValue=""
                                render={({ field }) => (
                                  <InputBase
                                    {...field}
                                    readOnly
                                    autoComplete="off"
                                    startAdornment={<DateSvg></DateSvg>}
                                    autoFocus={false}
                                    type="text"
                                    placeholder={t("common.common_end_date")}
                                  />
                                )}
                              />
                            </InputAdornment>
                          }
                          fullWidth
                          type="text"
                          error={Boolean(errors.startDate)}
                          placeholder={t("common.common_start_date")}
                        />
                      )}
                    />
                    {errors.startDate?.message || errors.stopDate?.message ? (
                      <FormHelperText
                        error
                        id="standard-weight-helper-text-name-schedule">
                        {errors.startDate?.message || errors.stopDate?.message}
                      </FormHelperText>
                    ) : null}
                    <Popover
                      elevation={3}
                      id="date"
                      open={open}
                      anchorEl={anchorEl}
                      onClose={handleClose}
                      anchorOrigin={{
                        vertical: "bottom",
                        horizontal: "center",
                      }}
                      transformOrigin={{
                        vertical: "top",
                        horizontal: "center",
                      }}>
                      <Calendar
                        locale={getCalendarLocales()}
                        colorPrimary="#7ac143" // added this
                        colorPrimaryLight="rgba(122, 193, 67, 0.1)"
                        minimumDate={utils().getToday()}
                        value={selectedDayRange}
                        onChange={(date) => {
                          setSelectedDayRange(date);
                          const startDate = date.from
                            ? dayjs(
                                date.from.year +
                                  `${
                                    date.from.month <= 9
                                      ? "0" + date.from.month
                                      : date.from.month
                                  }` +
                                  date.from.day
                              ).format("YYYY-MM-DD")
                            : "";
                          const endDate = date.to
                            ? dayjs(
                                date.to.year +
                                  `${
                                    date.to.month <= 9
                                      ? "0" + date.to.month
                                      : date.to.month
                                  }` +
                                  date.to.day
                              ).format("YYYY-MM-DD")
                            : "";
                          //计算开始时间和结束时间跨度不能超过一整年
                          let timeSpan = moment(new Date(startDate)).diff(
                            moment(new Date(endDate)),
                            "years"
                          );

                          if (timeSpan >= 1) {
                            toast.error(t("ips.ips_exceeds_one_year"));
                            return;
                          } else {
                            // console.log(startDate, endDate);
                            setValue("startDate", startDate);
                            setValue("stopDate", endDate);
                            // //将排期播放时间传给时间按钮选择组件进行计算
                            timeButtonSelectRef.current.getDaysWeeksAndMonthsDate(
                              startDate,
                              endDate
                            );
                          }
                        }}
                      />
                    </Popover>
                  </Stack>
                </Grid>
                <Grid
                  item
                  xs={12}
                  style={{
                    display: "flex",
                    justifyContent: "flex-end",
                  }}>
                  <Button
                    variant="contained"
                    onClick={() => {
                      handleOpenAdvanced();
                    }}>
                    {t("common.common_advanced_menu_title")}
                  </Button>
                </Grid>
              </Grid>
              <Grid
                container
                spacing={1}
                direction="row"
                justifyContent="center"
                alignItems="flex-start">
                <Grid
                  item
                  xs={12}
                  justifyContent="center"
                  alignItems="flex-start">
                  <InputLabel htmlFor="schedule-name">
                    {t("ips.ips_select_playlist")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                </Grid>
                <Grid
                  item
                  xs={12}
                  justifyContent="center"
                  alignItems="flex-start">
                  <Grid container>
                    <PlayListFromField />
                  </Grid>
                </Grid>
                <Grid
                  container
                  direction="row"
                  justifyContent="space-between"
                  item
                  xs={12}>
                  <InputLabel
                    sx={{
                      color: "grey",
                      fontSize: "0.8rem",
                    }}>
                    {t("common.common_time_not_repeat_annotation")}
                  </InputLabel>
                  <Stack spacing={1} direction="row">
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={() => {
                        append([
                          {
                            playListId: "",
                            playListName: "",
                            startTime: "",
                            stopTime: "",
                          },
                        ]);
                        // playList.push([
                        //   {
                        //     playListId: "",
                        //     playListName: "",
                        //     startTime: "",
                        //     stopTime: "",
                        //   },
                        // ]);
                      }}
                      // append({
                      //   title: "playListName1",
                      //   options: [
                      //     {
                      //       playListId: "",
                      //       startTime: "",
                      //       stopTime: "",
                      //     },
                      //   ],
                      // })
                      // }
                      // onClick={() => {
                      //   append({
                      //     playListId: "",
                      //     playListName: "",
                      //     startTime: "",
                      //     stopTime: "",
                      //   });
                      //   // let newList = scheduleForm.values.inventoryList;
                      //   // newList.push(inventoryObject);
                      //   // scheduleForm.setValues({
                      //   //   ...scheduleForm.values,
                      //   //   inventoryList: newList,
                      //   // });
                      // }}
                    >
                      {t("common.common_more")}
                    </Button>
                    <Button
                      variant="contained"
                      color="error"
                      onClick={() => {
                        remove(fields.length - 1);
                        // if (scheduleForm.values.inventoryList.length <= 1) {
                        //   return;
                        // }
                        // let newList = scheduleForm.values.inventoryList;
                        // //删除最后一个清单对象
                        // newList.pop();
                        // scheduleForm.setValues({
                        //   ...scheduleForm.values,
                        //   inventoryList: newList,
                        // });
                      }}>
                      {t("common.common_op_del")}
                    </Button>
                  </Stack>
                </Grid>
              </Grid>

              {/* 时间选择按钮组 */}
              <TimeButtonSelect ref={timeButtonSelectRef} />
              <Grid
                sx={{ marginTop: "10px" }}
                container
                justifyContent="center"
                alignItems="center">
                <Stack direction="row" spacing={2}>
                  <Button
                    size="large"
                    color="info"
                    variant="outlined"
                    onClick={() => navigate(-1)}>
                    {t("common.common_edit_cancel")}
                  </Button>
                  <Button
                    size="large"
                    variant="contained"
                    color="secondary"
                    onClick={() => {
                      //上一步 loading={loading}
                      previousStep();
                    }}>
                    {t("common.common_pre")}
                  </Button>
                  <LoadingButton
                    loading={loading}
                    disabled={saveDisable}
                    variant="contained"
                    size="large"
                    type="submit"
                    color="warning"
                    onClick={() => {
                      // removePlayListEmpty();
                      setValue("operateType", "1");
                      // scheduleForm.values.operateType = "1";
                    }}>
                    {t("common.common_edit_save")}
                  </LoadingButton>
                  <LoadingButton
                    loading={publishLoading}
                    disabled={publishDisable}
                    variant="contained"
                    size="large"
                    type="submit"
                    color="primary"
                    onClick={() => {
                      // removePlayListEmpty();
                      setValue("operateType", "0");
                    }}>
                    {t("common.common_op_publish")}
                  </LoadingButton>
                </Stack>
              </Grid>
            </Container>
          </>
        )}
      </form>
      <AdvancedDialog
        open={advancedOpen}
        onClose={() => {
          setAdvancedData([]);
          setAdvancedOpen(false);
        }}
        data={advancedData}
        onSubmit={handleRestPlayListFormField}
      />
    </MainCard>
  );
}

// 标题 提高性能
const MainCardTitle = memo((props) => {
  const { title, tips } = props;
  return (
    <>
      <Grid item container>
        <Grid item xs={5}>
          <Typography>{title}</Typography>
        </Grid>
        <Grid item xs={3}>
          <Typography color="#7AC143" sx={{ fontWeight: "bold" }}>
            {tips}
          </Typography>
        </Grid>
      </Grid>
    </>
  );
});
