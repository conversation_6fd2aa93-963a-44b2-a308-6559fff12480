import React, {
  useMemo,
  useState,
  useEffect,
  useRef,
  forwardRef,
  useImperativeHandle,
} from "react";
import MainCard from "@/components/MainCard";
import MaterialReactTable from "material-react-table";
import { tableI18n } from "@/utils/tableLang";
import {
  Stack,
  Grid,
  Button,
  Typography,
  TextField,
  Link,
} from "@mui/material";
import { useFormik } from "formik";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import { removeEmpty } from "@/utils/StringUtils";
import ZKSelect from "@/components/ZKSelect";
import DownloadIcon from "@mui/icons-material/Download";
import { listByPage, pullScreenLog } from "@/service/api/screenLog";
import { useTranslation } from "react-i18next";
import {
  BootstrapDialog,
  BootstrapContent,
  BootstrapDialogTitle,
} from "@/components/dialog";
import { screenLogStatus } from "@/dict/commonDict";
import BasicDateRangePicker from "@/components/datetimePicker";
// 消息提示
import { toast } from "react-toastify";
import { useConfirm } from "@/components/zkconfirm";
import DictTag from "@/components/DictTag";
const ScreenLog = forwardRef((props, ref) => {
  const { t } = useTranslation();
  const confirm = useConfirm();
  const [isError, setIsError] = useState(false);
  const { openScreenLog, onCancel, screenId } = props;
  const dateRangeRef = React.useRef(null);
  // 查询参数
  const requestParams = useRef(null);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  // 表格数据
  const [data, setData] = useState([]);
  const [currentData, setCurrentData] = useState([]);
  // 分页的参数
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);

  // 构建参数
  const buildParams = (type) => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      screenId: screenId,
      ...requestParams.current,
    };
    return params;
  };
  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "screenName",
        header: t("common.common_name"),
        size: 100,
      },
      {
        accessorKey: "time",
        header: t("common.common_operation_time"),
        size: 70,
      },
      {
        accessorKey: "state",
        header: t("common.common_login_status"),
        size: 120,
        Cell: ({ cell, row }) => {
          return (
            <DictTag
              dicts={screenLogStatus}
              fieldName={{
                value: "value",
                title: "label",
                color: "color",
              }}
              value={row.original.state}
            />
          );
        },
      },
      {
        accessorKey: "message",
        header: t("ips.ips_appLog_message"),
        size: 110,
      },
      {
        accessorKey: "logUrl",
        header: t("common.common_path"),
        size: 180,
      },
    ],
    []
  );

  useEffect(() => {
    if (openScreenLog) {
      handleScreenLogList();
    }
  }, [pagination, screenId]);
  //请求素材列表
  const handleScreenLogList = async (type) => {
    setIsLoading(true);
    await listByPage(buildParams(type)).then((res) => {
      setData(res.data.data);
      // 设置总记录数
      setRowCount(res.data.total);
      setIsLoading(false);
      setIsRefetching(false);
    });
  };
  useImperativeHandle(ref, () => ({
    handleClose,
    handleClickOpen,
  }));
  const handleClickOpen = () => {
    onCancel();
    setCurrentData([]);
    handleScreenLogList();
  };
  const handleClose = () => {
    onCancel();
    setPagination({
      pageIndex: 0,
      pageSize: 10,
    });
  };

  // 查询表单
  const queryFormik = useFormik({
    initialValues: {
      screenName: "",
      type: "",
      startTime: "",
      endTime: "",
    },
    onSubmit: async (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        // 拷贝value值
        const tempValue = { ...values };
        await removeEmpty(tempValue);
        requestParams.current = tempValue;
        handleScreenLogList();
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
  });
  // 重置查询
  const resetQuery = () => {
    // 重置表单
    dateRangeRef?.current.restInputValue();
    queryFormik.resetForm();
    requestParams.current = null;
    handleScreenLogList();
  };

  //拉取日志
  const handlePullLog = () => {
    confirm({
      title: t("message.messageBox_title"),
      confirmationText: t("common.common_edit_ok"),
      cancellationText: t("common.common_edit_cancel"),
      description: t("ips.ips_appLog_pull_log"),
    }).then(() => {
      pullScreenLog(screenId)
        .then((res) => {
          toast.success(res.message);
          // 重新请求数据
          handleScreenLogList();
        })
        .catch((err) => {});
    });
  };

  return (
    <div>
      <BootstrapDialog
        fullWidth
        maxWidth="xl"
        onClose={handleClose}
        aria-labelledby="customized-dialog-title"
        open={openScreenLog}>
        <BootstrapDialogTitle>
          <Typography variant="h4" component="p">
            {t("menu.media_deviceLog")}
          </Typography>
          <IconButton
            aria-label="close"
            onClick={handleClose}
            sx={{
              position: "absolute",
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500],
            }}>
            <CloseIcon />
          </IconButton>
        </BootstrapDialogTitle>
        <BootstrapContent>
          <Grid container spacing={7}>
            <Grid item xs={12}>
              <MainCard style={{ marginBottom: "10px" }}>
                <form noValidate onSubmit={queryFormik.handleSubmit}>
                  <Grid
                    container
                    direction="row"
                    justifyContent="flex-start"
                    alignItems="center"
                    spacing={2}>
                    {/* <Grid item xs={6} sm={4} md={2.8}>
                      <TextField
                        label={t("ips.ips_screen_name")}
                        value={queryFormik.values.screenName}
                        onChange={queryFormik.handleChange}
                        onBlur={queryFormik.handleBlur}
                        size="small"
                        type="text"
                        name="screenName"
                        fullWidth
                        placeholder={t("ips.ips_screen_name")}
                      />
                    </Grid> */}
                    {/* <Grid item xs={6} sm={4} md={2.8}>
                      <ZKSelect
                        name="type"
                        size="small"
                        value={queryFormik.values.type}
                        onChange={queryFormik.handleChange}
                        onBlur={queryFormik.handleBlur}
                        options={screenLogType}
                        onClear={() => {
                          queryFormik.setFieldValue("type", "");
                        }}
                        placeholder={t("common.common_select_type")}
                        menuWidth={200}
                      />
                    </Grid> */}
                    <Grid item xs={7} sm={5} md={4}>
                      <BasicDateRangePicker
                        ref={dateRangeRef}
                        onChange={(value) => {
                          if (value?.startTime && value?.endTime) {
                            if (
                              value.startTime === "Invalid Date" ||
                              value.endTime === "Invalid Date"
                            ) {
                              queryFormik.setValues({
                                startTime: "",
                                endTime: "",
                              });
                              return;
                            }
                            queryFormik.setValues(value);
                          }
                        }}
                      />
                    </Grid>
                    <Grid item xs={2}>
                      <Stack
                        direction="row"
                        justifyContent="flex-start"
                        alignItems="flex-start"
                        spacing={2}>
                        <Button
                          disableElevation
                          type="submit"
                          variant="contained"
                          size="small">
                          {t("common.common_table_query")}
                        </Button>
                        <Button
                          variant="outlined"
                          onClick={resetQuery}
                          color="info"
                          size="small"
                          sx={{
                            minWidth: "90px",
                          }}>
                          {t("common.common_op_reset")}
                        </Button>
                      </Stack>
                    </Grid>
                  </Grid>
                </form>
              </MainCard>

              <MaterialReactTable
                // table 状态
                state={{
                  // 加载状态
                  isLoading,
                  // 分页参数
                  pagination,
                  // 重新拉取
                  showProgressBars: isRefetching,
                  showAlertBanner: isError,
                  density: "compact",
                }}
                // enableRowSelection
                renderToolbarInternalActions={({ table }) => <></>}
                displayColumnDefOptions={{
                  "mrt-row-actions": {
                    header: t("common.common_relatedOp"), //change header text
                    size: 80,
                  },
                }}
                muiTablePaperProps={{
                  elevation: 0,
                  sx: {
                    border: "1px solid",
                    borderColor: "#e6ebf1",
                  },
                }}
                autoResetPageIndex={false}
                // 是否开启关闭头部底部工具类
                enableTopToolbar={true}
                enableColumnActions={false}
                enableBottomToolbar={true}
                // 关闭过滤搜素
                enableColumnFilters={false}
                // 关闭排序
                enableSorting={false}
                // 布局方式
                layoutMode="grid"
                // 开启列对齐
                muiTableHeadCellProps={{
                  sx: {
                    "& .Mui-TableHeadCell-Content": {
                      justifyContent: "space-between",
                    },
                  },
                }}
                // 解决列太多宽度太长问题
                enableColumnResizing
                // enablePinning
                // 初始化状态
                initialState={{ columnVisibility: { createTime: true } }}
                muiToolbarAlertBannerProps={
                  isError
                    ? {
                        color: "error",
                        children: "Error loading data",
                      }
                    : undefined
                }
                // 固定头部
                enableStickyHeader
                sx={{ boxShadow: "none" }}
                // 处理表格高度
                muiTableContainerProps={{ sx: { minHeight: "400px" } }}
                // 设置背景颜色
                muiTableBodyCellProps={({ row }) => ({
                  onDoubleClick: (event) => {
                    handleClickOpen();
                  },
                  sx: {
                    backgroundColor: "white",
                  },
                })}
                muiTableHeadRowProps={{ sx: { boxShadow: "none" } }}
                muiTableBodyProps={{
                  sx: {
                    backgroundColor: "white",
                    tableLayout: "fixed",
                    boxShadow: "none",
                  },
                }}
                muiTableProps={{
                  sx: {
                    backgroundColor: "white",
                    tableLayout: "fixed",
                    boxShadow: "none",
                  },
                }}
                muiBottomToolbarProps={{
                  sx: { backgroundColor: "white", boxShadow: "none" },
                }}
                muiTopToolbarProps={{
                  sx: { backgroundColor: "white", boxShadow: "none" },
                }}
                // 分页回调函数
                onPaginationChange={setPagination}
                // 记得开启这些 tips:否则会出现useEffect 反作用函数发起多次请求点击下一页会出问题
                manualFiltering
                manualPagination
                manualSorting
                // 列数
                rowCount={rowCount}
                // 开启分页
                enablePagination={true}
                // 列定义
                columns={columns}
                // 数据
                data={data}
                // 初始化国际化语言  --- todo: i18n key doc address: https://github.com/KevinVandy/material-react-table/blob/main/packages/material-react-table/src/_locales/en.ts
                localization={tableI18n}
                // 多选底部提示
                positionToolbarAlertBanner="none"
                // 开启action操作
                enableRowActions
                // action操作位置
                positionActionsColumn="last"
                // 自定义表头按钮
                renderTopToolbarCustomActions={({ table }) => {
                  return (
                    <div style={{ display: "flex", gap: "0.5rem" }}>
                      <Button
                        variant="contained"
                        color="primary"
                        onClick={() => {
                          handleScreenLogList();
                        }}>
                        {t("common.common_op_refresh")}
                      </Button>
                      <Button
                        variant="contained"
                        color="primary"
                        onClick={() => {
                          handlePullLog();
                        }}>
                        {t("ips.ips_appLog_log")}
                      </Button>
                    </div>
                  );
                }}
                // 自定义表头按钮
                renderRowActions={({ row, table }) => (
                  <Stack direction="row" alignItems="center">
                    <IconButton
                      color="primary"
                      disabled={row.original.state == "0"}
                      aria-label="cliear"
                      component="label"
                      title={t("common.common_download")}
                      onClick={() => window.open(row.original.logUrl)}>
                      <DownloadIcon />
                    </IconButton>
                  </Stack>
                )}
              />
            </Grid>
          </Grid>
        </BootstrapContent>
      </BootstrapDialog>
    </div>
  );
});
export default ScreenLog;
