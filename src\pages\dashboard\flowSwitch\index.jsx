import React, { useState } from "react";
import {
  Grid,
  Select,
  FormControl,
  MenuItem,
  InputLabel,
  TextField,
  OutlinedInput,
} from "@mui/material";
import DateRange from "@/components/dateRange/dateRange";
import { utils } from "@amir04lm26/react-modern-calendar-date-picker";
import { t } from "i18next";

const FlowSwitch = (props) => {
  const [day, setDay] = useState("0");
  const {
    switchHandle = () => {},
    switchDate = () => {},
    sx = {},
    rangeSx = 5,
    selectSx = 3,
  } = props;

  const handleChange = (event) => {
    setDay(event.target.value);
    switchHandle(event.target.value);
  };

  return (
    <>
      <Grid
        sx={sx}
        container
        spacing={2}
        direction="row"
        justifyContent="flex-end"
        alignItems="center"
      >
        <Grid item xs={rangeSx}>
          <DateRange
            maxDay={utils().getToday()}
            onClear={() => {
              switchHandle(day);
            }}
            onChange={(startDate, endDate) => {
              switchDate(startDate, endDate);
            }}
          />
        </Grid>

        <Grid sx={selectSx}>
          <FormControl sx={{ m: 1, minWidth: 40, height: 20 }}>
            <Select
              id="flowlineSelect"
              value={day}
              sx={{ height: 37 }}
              onChange={handleChange}
              input={<OutlinedInput />}
              autoWidth
              label="Age"
            >
              <MenuItem value={"0"}>{t("ips.ips_in_week")}</MenuItem>
              <MenuItem value={"1"}>{t("ips.ips_in_month")}</MenuItem>
            </Select>
          </FormControl>
        </Grid>
      </Grid>
    </>
  );
};
export default FlowSwitch;
