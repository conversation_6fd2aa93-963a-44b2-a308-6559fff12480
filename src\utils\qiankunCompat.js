/**
 * qiankun兼容性修复工具
 * 解决ES模块和React Refresh在qiankun环境中的兼容性问题
 */

/**
 * 检查是否在qiankun环境中
 */
export const isQiankunEnv = () => {
  return window.__POWERED_BY_QIANKUN__;
};

/**
 * 修复全局变量兼容性
 */
export const fixGlobalCompat = () => {
  if (isQiankunEnv()) {
    // 确保global变量存在
    if (typeof global === 'undefined') {
      window.global = window;
    }

    // 修复process.env
    if (typeof process === 'undefined') {
      window.process = {
        env: {
          NODE_ENV: 'development'
        }
      };
    }

    // 强制禁用React Refresh
    window.$RefreshReg$ = () => { };
    window.$RefreshSig$ = () => (type) => type;
    window.__vite_plugin_react_preamble_installed__ = false;

    // 拦截React Refresh的注入
    const originalDefineProperty = Object.defineProperty;
    Object.defineProperty = function (obj, prop, descriptor) {
      if (prop === '$RefreshReg$' || prop === '$RefreshSig$' || prop === '__vite_plugin_react_preamble_installed__') {
        console.warn(`⚠️ 阻止React Refresh属性注入: ${prop}`);
        return obj;
      }
      return originalDefineProperty.call(this, obj, prop, descriptor);
    };
  }
};

/**
 * 修复ES模块导入问题
 */
export const fixESModuleCompat = () => {
  if (isQiankunEnv()) {
    // 修复import语句在qiankun沙箱中的执行问题
    const originalEval = window.eval;
    window.eval = function (code) {
      // 检查是否包含import语句
      if (typeof code === 'string') {
        // 阻止React Refresh相关的代码执行
        if (code.includes('RefreshRuntime') ||
          code.includes('$RefreshReg$') ||
          code.includes('$RefreshSig$') ||
          code.includes('__vite_plugin_react_preamble_installed__')) {
          console.warn('⚠️ 阻止React Refresh代码执行');
          return;
        }

        // 处理ES模块导入
        if (code.includes('import ') && !code.includes('import(')) {
          console.warn('⚠️ 检测到ES模块导入，在qiankun环境中可能不兼容:', code.substring(0, 100));
          // 尝试转换为动态导入
          try {
            // 简单的import转换（仅用于演示，实际可能需要更复杂的处理）
            const transformedCode = code.replace(/import\s+(.+)\s+from\s+['"](.+)['"]/,
              'const $1 = await import("$2")');
            return originalEval.call(this, transformedCode);
          } catch (error) {
            console.warn('⚠️ ES模块转换失败:', error);
            return;
          }
        }
      }
      return originalEval.call(this, code);
    };
  }
};

/**
 * 修复模块热替换兼容性
 */
export const fixHMRCompat = () => {
  if (isQiankunEnv() && import.meta.hot) {
    // 在qiankun环境中禁用某些HMR功能
    const originalAccept = import.meta.hot.accept;
    import.meta.hot.accept = function (...args) {
      try {
        return originalAccept.apply(this, args);
      } catch (error) {
        console.warn('⚠️ HMR在qiankun环境中被限制:', error.message);
      }
    };
  }
};

/**
 * 初始化所有兼容性修复
 */
export const initQiankunCompat = () => {
  if (isQiankunEnv()) {
    console.log('🔧 初始化qiankun兼容性修复...');

    fixGlobalCompat();
    fixESModuleCompat();
    fixHMRCompat();

    console.log('✅ qiankun兼容性修复完成');
  }
};

/**
 * 安全的模块导入包装器
 */
export const safeImport = async (moduleSpecifier) => {
  try {
    return await import(moduleSpecifier);
  } catch (error) {
    if (isQiankunEnv()) {
      console.warn(`⚠️ 模块导入失败 (qiankun环境): ${moduleSpecifier}`, error);
      return {};
    }
    throw error;
  }
};

/**
 * 安全的动态脚本加载
 */
export const safeLoadScript = (src, options = {}) => {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.src = src;
    script.async = options.async !== false;
    script.defer = options.defer || false;

    // 在qiankun环境中添加额外的错误处理
    if (isQiankunEnv()) {
      script.crossOrigin = 'anonymous';
      script.onerror = (error) => {
        console.warn(`⚠️ 脚本加载失败 (qiankun环境): ${src}`, error);
        reject(error);
      };
    }

    script.onload = () => resolve(script);
    script.onerror = reject;

    document.head.appendChild(script);
  });
};

// 自动初始化（在模块加载时）
if (typeof window !== 'undefined') {
  // 延迟初始化，确保qiankun环境已经设置
  setTimeout(() => {
    initQiankunCompat();
  }, 0);
}
