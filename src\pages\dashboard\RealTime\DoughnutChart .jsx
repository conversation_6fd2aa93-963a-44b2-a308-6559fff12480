import React, { useEffect, useRef } from "react";
import * as echarts from "echarts";
import { Grid, Paper, Typography } from "@mui/material";
import { getAgeRange,getGenderRanger,getAgeGroupLine,getGenderGroupBar,getStayTime} from './chartOptions'
import { useTranslation } from "react-i18next";
const DoughnutChart = ({ data, type }) => {

  const { t } = useTranslation();

  const chartRef = useRef(null);
  const chartInstanceRef = useRef(null);
  useEffect(() => {
    const chart = echarts.init(chartRef.current);
    chartInstanceRef.current = chart;
    let option = {}

    let languageConfig = {
      RealTimeAge: t('RealTime.RealTimeAge'),
      RealTimePercentage: t('RealTime.RealTimePercentage'),
      RealTimeVisitors: t('RealTime.RealTimeVisitors'),
      RealTimeMale:t('RealTime.RealTimeMale'),
      RealTimeFemale: t('RealTime.RealTimeFemale'),
      RealTimeAgePercentageOf:  t('RealTime.RealTimeAgePercentageOf'),
      RealTimeAgePercentage: t('RealTime.RealTimeAgePercentage'),
      RealTimeGenderPercentageOf: t('RealTime.RealTimeGenderPercentageOf'),
      RealTimeGenderPercentage:  t('RealTime.RealTimeGenderPercentage'),
      RealTime_Entry_Flow: t('RealTime.RealTime_Entry_Flow'),
      RealTime_Trend: t('RealTime.RealTime_Trend'),
      RealTime_Gender:t('RealTime.RealTime_Gender'),
      RealTime_Distribution: t('RealTime.RealTime_Distribution'),
      duration:t('RealTime.duration'),
      Trend: t('RealTime.Trend')
    }

    if (type == 'ageRange') {
      option = getAgeRange(data,languageConfig)
    }

    if (type==='genderRanger') {
      option = getGenderRanger(data,languageConfig)
    }

    if (type === 'AgeGroupLineChart') {
      option = getAgeGroupLine(data,languageConfig)
    }
    if (type === 'GenderGroupBar') {
      option = getGenderGroupBar(data,languageConfig)
    }
    if (type === 'DurationTrend') {
      option = getStayTime(data,languageConfig)
    }




    chart.setOption(option);
    const handleResize = () => {
      setTimeout(() => {
        chart.resize();
      }, 600);
    };
    window.addEventListener('resize', handleResize);
    return () => {
      chart.dispose();
      window.removeEventListener('resize', handleResize);
    };
  }, [data]);

  return <div ref={chartRef} style={{ width: "100%", height: "100%" }} />;
};

export default DoughnutChart;
