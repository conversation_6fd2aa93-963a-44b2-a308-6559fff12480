import React, { useEffect, useState } from "react";
import { Grid, Typography, Stack, InputLabel } from "@mui/material";
import SummaryBottom from "./Summary/SummaryBottom";
import CustomizedList from "./Summary/CustomizedList";
import MerchantSelect from "@/components/MerchantSelect";
import Pie<PERSON>hart from "./Summary/PieChart";
import CurrentContent from "./Summary/CurrentContent";
import { getSummaryContent } from "@/service/api/summary";
import GradientBox from "@/components/GradientBox";
import GoogleMapChart from "./Summary/GoogleMapChart";
import BaiDuMapChart from "./Summary/BaiDuMapChart";
import { useTranslation } from "react-i18next";
import ZKSearchTree from "@/components/ZKSearchTree/ZKSearchTree.jsx";
const Summary = (props) => {
  const { t } = useTranslation();
  const [retailClientId, setRetailClientId] = useState("");
  const [areaId, setAreaId] = useState("");
  const [merchantError, setMerchantError] = useState("");
  const [regionError, setRegionError] = useState("");
  const [location, setLocation] = useState("");

  const [contentData, setContentData] = useState({});

  const [localVersion, setLocalVersion] = useState("EN");
  useEffect(() => {
    let version = localStorage.getItem("localVersion");
    if (version === "cn" || version === "CN") {
      setLocalVersion("CN");
    } else {
      setLocalVersion("EN");
    }
  }, []);

  const retailClear = () => {
    setRetailClientId("");
    setMerchantError("");
  };

  const regionClear = () => {
    setAreaId("");
    setRegionError("");
  };

  const regionChange = (e) => {
    setRegionError("");

    localStorage.setItem("summarySelectedRegionValue", JSON.stringify(e));
    setLocation(e.location);
    setAreaId(e.id);
  };

  const retailClientChange = (v) => {
    setRetailClientId(v);
    setMerchantError("");
  };

  useEffect(() => {
    let areaData = localStorage.getItem("summarySelectedRegionValue");
    if (areaData) {
      const area = JSON.parse(areaData);
      setRegionError("");
      setAreaId(area.id);
      setLocation(area.location);
    } else {
      setAreaId("");
    }
  }, []);

  useEffect(() => {
    let retailData = localStorage.getItem("summarySelectedRetailValue");
    if (retailData) {
      setRetailClientId(retailData);
    }
    if (retailClientId) {
      getSummaryContent({
        retailClientId: retailClientId,
      }).then((res) => {
        setContentData(res.data);
      });
    } else {
      setContentData({});
    }
  }, [retailClientId]);

  return (
    <React.Fragment>
      <Grid spacing={2} container>
        <Grid item xs={12} sm={12} md={12} lg={8}>
          <Grid sx={{ mt: 2 }}>
            <Grid container>
              <Grid xs={5} item>
                <div style={{ maxWidth: "200px" }}>
                  <MerchantSelect
                    retailKey="summarySelectedRetailValue"
                    label={t("dashboard.retail")}
                    onClear={retailClear}
                    error={merchantError}
                    onChange={retailClientChange}
                  />
                </div>
              </Grid>

              <Grid xs={5} item sx={{ ml: 2 }}>
                <div style={{ maxWidth: "300px" }}>
                  {/* <RegionSelect
                    regionKey="summarySelectedRegionValue"
                    style={{ maxWidth: "200px" }}
                    label={t("dashboard.region")}
                    onClear={regionClear}
                    error={regionError}
                    onChange={regionChange}
                  /> */}

                  <Stack spacing={1}>
                    <InputLabel htmlFor="sort-name">
                      {t("common.common_form_area_name")}
                      <i style={{ color: "red" }}>*</i>
                    </InputLabel>
                    <ZKSearchTree
                      optionValue="id"
                      isContainOldData="1"
                      regionKey="summarySelectedRegionValue"
                      optionLabel="name"
                      name="areaId"
                      onClear={regionClear}
                      error={regionError}
                      onChange={regionChange}
                      placeholder={t("common.common_select_area")}
                      disableParent={true}
                    />
                  </Stack>
                </div>
              </Grid>
            </Grid>

            <Grid container>
              <Grid
                item
                sx={{
                  display: "flex",
                  flexDirection: "column",
                }}
                xs={12}
                sm={5}
                md={5}
                lg={5}>
                <Grid sx={{ mt: 2, flexGrow: 1 }}>
                  <GradientBox
                    style={{
                      width: "100%",
                      height: "100%",
                      flexGrow: 1,
                      display: "flex",
                      padding: "2px",
                      margin: 2,
                    }}>
                    <Grid
                      style={{
                        display: "flex",
                        flexGrow: 1,
                        justifyContent: "center",
                        alignItems: "center",
                        minHeight: "200px",
                      }}>
                      <div
                        style={{
                          width: "100%",
                          height: "100%",
                          display: "flex",
                          backgroundImage: `url(${contentData.logo})`,
                          justifyContent: "center",
                          alignItems: "center",
                          backgroundSize: "contain",
                          backgroundPosition: "center",
                          backgroundRepeat: "no-repeat",
                        }}>
                        {contentData.logo ? "" : t("summary.noDataTip")}
                      </div>
                    </Grid>
                  </GradientBox>
                </Grid>
              </Grid>
              <Grid
                item
                sx={{
                  display: "flex",
                  flexDirection: "column",
                }}
                xs={12}
                sm={7}
                md={7}
                lg={7}>
                <Grid
                  sx={{ ml: 2, mt: 2 }}
                  style={{ minHeight: 300, display: "flex", flexGrow: 1 }}>
                  {/* {localVersion === "EN" && (
                    <GoogleMapChart
                      center={location}
                      retailClientId={retailClientId}
                      areaId={areaId}></GoogleMapChart>
                  )}
                  {localVersion === "CN" && (
                    <BaiDuMapChart
                      center={location}
                      retailClientId={retailClientId}
                      areaId={areaId}></BaiDuMapChart>
                  )} */}

                  <BaiDuMapChart
                    center={location}
                    retailClientId={retailClientId}
                    areaId={areaId}></BaiDuMapChart>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
          <SummaryBottom
            retailClientId={retailClientId}
            areaId={areaId}></SummaryBottom>
        </Grid>
        <Grid style={{ display: "flex" }} item xs={12} sm={12} md={12} lg={4}>
          <Grid
            style={{
              flexDirection: "column",
              display: "flex",
              flexGrow: 1,
            }}>
            <Grid>{t("summary.current_content")} </Grid>
            <Grid
              style={{
                display: "flex",
                flexGrow: 1,
              }}
              sx={{ mt: 2 }}>
              {" "}
              <CurrentContent contentData={contentData.content} />
            </Grid>
          </Grid>
        </Grid>
      </Grid>

      <Grid sx={{ mt: 1, mb: 4 }} spacing={2} container>
        <Grid item xs={12} sm={12} md={8} lg={8}>
          <CustomizedList retailClientId={retailClientId} areaId={areaId} />
        </Grid>
        <Grid
          item
          xs={12}
          sm={12}
          md={4}
          lg={4}
          style={{
            display: "flex",
            flexDirection: "column",
            paddingRight: "10px",
          }}>
          <Typography variant="h6">
            {t("summary.total_digital_signage")}
          </Typography>
          <Grid style={{ minHeight: "300px" }} sx={{ mt: 1 }}>
            <PieChart retailClientId={retailClientId} areaId={areaId} />
          </Grid>
        </Grid>
      </Grid>
    </React.Fragment>
  );
};
export default Summary;
