/**
 * HMR 辅助工具
 * 用于在qiankun微前端环境中改善热更新体验
 */


// 检查是否在qiankun环境中
export const isQiankunEnv = () => {
  return window.__POWERED_BY_QIANKUN__;
};




// 检查是否在开发环境
export const isDev = () => {
  return import.meta.env.DEV;
};

// HMR状态管理
let hmrEnabled = false;

// 启用HMR监听
export const enableHMR = () => {
  if (!isDev() || hmrEnabled) return;

  hmrEnabled = true;

  // 监听Vite HMR事件
  if (import.meta.hot) {
    import.meta.hot.on('vite:beforeUpdate', () => {
      console.log('[HMR] 准备更新...');
    });

    import.meta.hot.on('vite:afterUpdate', () => {

      console.log('[HMR] 更新完成');

      // 在qiankun环境中，可能需要手动触发一些更新
      if (isQiankunEnv()) {
        // 触发自定义事件通知主应用
        window.dispatchEvent(new CustomEvent('micro-app-updated', {
          detail: { appName: 'cms-app' }
        }));
      }
    });

    import.meta.hot.on('vite:error', (err) => {
      console.error('[HMR] 更新失败:', err);
    });
  }
};

// 禁用HMR监听
export const disableHMR = () => {
  hmrEnabled = false;
};

// 强制刷新页面（在HMR失效时使用）
export const forceRefresh = () => {


  if (isQiankunEnv()) {
    // 在qiankun环境中，通知主应用刷新子应用
    window.dispatchEvent(new CustomEvent('micro-app-refresh', {
      detail: { appName: 'cms-app' }
    }));
  } else {
    // 独立运行时直接刷新
    window.location.reload();
  }
};

// 开发环境下的调试工具
export const devTools = {
  // 显示当前环境信息
  showEnvInfo: () => {
    console.group('🔧 开发环境信息');
    console.log('是否为开发环境:', isDev());
    console.log('是否在qiankun中:', isQiankunEnv());
    console.log('HMR是否启用:', hmrEnabled);
    console.log('当前URL:', window.location.href);
    console.groupEnd();
  },

  // 测试HMR连接
  testHMR: () => {
    if (import.meta.hot) {
      console.log('✅ HMR连接正常');
      return true;
    } else {
      console.log('❌ HMR连接异常');
      return false;
    }
  },

  // 手动触发更新
  triggerUpdate: () => {
    if (import.meta.hot) {
      import.meta.hot.send('vite:invalidate', { path: window.location.pathname });
    }
  }
};

// 在开发环境下自动启用
if (isDev()) {
  enableHMR();

  // 添加全局调试工具
  window.__CMS_APP_DEV_TOOLS__ = devTools;

}
