/**
 *  用户操作接口 --- 废弃
 * <AUTHOR>
 * @date 2022-11-29 14:36
 */
import request from "@/utils/request";

// 邮箱注册
export const registerByEmail = (params) => {
  return request({
    url: "/user/create/email",
    method: "POST",
    headers: {
      isToken: false,
    },
    data: params,
  });
};

/**
 * 获取验证码
 * <AUTHOR>
 * @date 2022-12-07 10:36
 */
export const sendCode = (accountType, account) => {
  return request({
    url: `/forget/${accountType}/${account}`,
    method: "GET",
    headers: {
      isToken: false,
    },
  });
};

/**
 * 忘记密码
 * <AUTHOR>
 * @date 2022-12-07 14:36
 */
export const resetPassword = (params) => {
  return request({
    url: "/user/reset/password",
    method: "POST",
    headers: {
      isToken: false,
    },
    data: params,
  });
};
/**
 *  重置密码
 * <AUTHOR>
 * @date 2023-07-24 16:58
 */
export const restPassword = (params) => {
  return request({
    url: "/user/password/rest",
    method: "POST",
    data: params,
  });
};

/**
 *  发送注册验证码
 * <AUTHOR>
 * @date 2022-12-09 15:27
 */
export const getSignupCode = (username, areaCode) => {
  return request({
    url: `/getSignupCode/${username}/${areaCode}`,
    method: "GET",
    headers: {
      isToken: false,
    },
  });
};
/**
 *  注册
 * <AUTHOR>
 * @date 2022-12-09 15:52
 */
export const signup = (params) => {
  return request({
    url: `/user/signup`,
    method: "POST",
    headers: {
      isToken: false,
    },
    data: params,
  });
};

/**
 * 获取登录的用户信息
 * @param {} params
 */
export const getLoginInfor = () => {
  return request({
    url: `/user/infor`,
    method: "GET",
  });
};

/**
 *  修改个人信息
 * <AUTHOR>
 * @date 2023-01-07 09:42
 */
export const updateProfile = (params) => {
  return request({
    url: "/user/profile",
    method: "PUT",
    data: params,
  });
};

export const updatePassword = (params) => {
  return request({
    url: "/user/password",
    method: "PUT",
    data: params,
  });
};
/**
 *  修改头像
 * <AUTHOR>
 * @date 2023-02-13 10:50
 */
export const uploadAvatar = (params) => {
  return request({
    url: "/user/avatar",
    method: "POST",
    data: params,
  });
};

export const listByPage = (params) => {
  return request({
    url: "/user/listPage",
    method: "GET",
    params: params,
  });
};
export const listUserPageByMerchant = (params) => {
  return request({
    url: "/user/listUserPageByMerchant",
    method: "GET",
    params: params,
  });
};
export const saveUser = (params) => {
  return request({
    url: "/user/createUser",
    method: "POST",
    data: params,
  });
};

export const saveTenantUser = (params)  =>{
  return request({
    method: "POST",
    data: params,
    url:"/user/tenant/create"
  })
}

export const updateTenantUser = (params) =>{
  return request({
    method: "PUT",
    data: params,
    url:"/user/tenant/update"
  })
}

export const updateStore = (params) => {
  return request({
    url: "/user/avatar",
    method: "POST",
    data: params,
  });
};

export const getStore = (params) => {
  return request({
    url: "/user/avatar",
    method: "POST",
    data: params,
  });
};

/**
 *根据角色code获取角色部分信息
 * <AUTHOR>
 * @returns
 */
export const getRoleInfoByCode = (code) => {
  return request({
    url: `/user/getRoleInfoByCode/${code}`,
    method: "get",
  });
};

/**
 *  根据ID获取联屏信息
 * <AUTHOR> yu
 * @date 2023-05-09 11:18
 */
export const getUserById = (id) => {
  return request({
    url: `/user/getUserById/${id}`,
    method: "get",
  });
};

/**
 *  修改用户信息
 *
 * <AUTHOR> yu
 * @date 2023-05-09 18:12
 */
export const updateUserInfo = (params) => {
  return request({
    url: "/user/update",
    method: "POST",
    data: params,
  });
};

/**
 *  删除用户
 *
 * <AUTHOR> yu
 * @date 2023-05-09 18:13
 */
export const removeUser = (ids) => {
  return request({
    url: `/user/delete/${ids}`,
    method: "delete",
  });
};

/**
 *  获取最新的token信息
 *
 * <AUTHOR>
 */
export const doRefreshToken = () => {
  return request({
    url: `/user/doRefreshToken`,
    method: "get",
  });
};
//用户订阅信息
export const userSubscribeInfo = () =>{
  return request({
    url: `/user/subscribe`,
    method: "get",
  })
}

export const userBanned = (id)=>{
  return request({
    url: `/user/banned/${id}`,
    method: "get",
  })
}

export const changeMerchant = ()=>{
   return request({
    url: `/user/changeMerchant`,
    method: "get",
  })
}
