/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/prop-types */
import { useEffect, useRef } from 'react';
import { Grid, Card, Stack, Typography, Box } from '@mui/material';
import * as echarts from 'echarts';
import 'echarts-liquidfill';

//水波图
const Instrument = (props) => {
    const chartRef = useRef(null);

    useEffect(() => {
        initEchart();
    }, []);

    const initEchart = () => {
        let myEcharts = echarts.init(chartRef.current);

        let option = {
            title: {
                // 标题
                // text: '100',
                textStyle: {
                    // 标题的样式
                    color: '#21992A', // 字体颜色
                    fontFamily: 'Microsoft YaHei', // 字体
                    fontSize: 24,
                    fontWeight: 'bold',
                    align: 'center', // 文字的水平方式
                    baseline: 'middle',
                    position: 'inside',
                    verticalAlign: 'middle' // 文字的垂直方式
                },
                left: 'center', // 定位
                top: '30%'
            },
            polar: {
                radius: ['65%', '60%'],
                center: ['50%', '50%']
            },
            angleAxis: {
                max: 100,
                clockwise: false, //刻度增长是否按顺时针，默认顺时针(true)。
                axisLine: {
                    show: false
                },
                axisTick: {
                    show: false
                },
                axisLabel: {
                    show: false
                },
                splitLine: {
                    show: false
                }
            },
            radiusAxis: {
                type: 'category',
                show: true,
                axisLabel: {
                    show: false
                },
                axisLine: {
                    show: false
                },
                axisTick: {
                    show: false
                }
            },
            series: [
                {
                    type: 'liquidFill', //定义类型
                    radius: '55%',
                    waveAnimation: true,
                    z: 1,
                    data: [
                        {
                            value: 0.7,
                            direction: 'left', //移动方向
                            amplitude: 10, // 水波纹的振幅
                            label: {
                                formatter: `${props.data}`,
                                fontSize: 30,
                                fontWeight: 'normal',
                                color: '#7AC143', //在背景上显示时的文本颜色。
                                insideColor: '#7AC143' //在 wave 上显示时的文本颜色。
                            },
                            itemStyle: {
                                //一个波浪设置透明度
                                color: '#D7F6BF'
                            },
                            // 悬浮后的样式
                            emphasis: {
                                itemStyle: {
                                    opacity: 0.9
                                }
                            }
                        },
                        {
                            value: 0.5,
                            direction: 'right',
                            amplitude: 10, // 水波纹的振幅
                            itemStyle: {
                                color: '#B8E992'
                            },
                            // 悬浮后的样式
                            emphasis: {
                                itemStyle: {
                                    opacity: 0.9
                                }
                            }
                        }
                    ],
                    // 改变水球图的形状，比如 'circle', 'rect', 'roundRect', 'triangle', 'diamond', 'pin', 'arrow'(默认为circle)
                    shape: 'circle',
                    //水球图边框属性设定
                    outline: {
                        show: true,
                        borderDistance: 0, // 设置和外层轮廓的间距
                        itemStyle: {
                            borderWidth: 0 // 设置外层边框宽度
                            // borderColor: 'red', // 设置外层边框颜色
                            //  shadowBlur: 'none' // 消除外层边框阴影
                        }
                    },
                    itemStyle: {
                        opacity: 0.9, // 波浪的透明度
                        shadowBlur: 0 // 波浪的阴影范围
                    },
                    backgroundStyle: {
                        //  borderWidth: 4, // 修改背景边框宽度
                        //  borderColor: 'green' // 修改背景边框的颜色
                        color: '#F1F1F1' // 球内背景色
                    }
                    // label: {
                    //     // 数据展示样式
                    //     show: false,
                    //     color: '#000',
                    //     insideColor: '#fff',
                    //     fontSize: 24,
                    //     fontWeight: 400,
                    //     align: 'center',
                    //     baseline: 'middle',
                    //     position: 'inside'
                    // }
                },
                {
                    //外圈
                    name: '',
                    type: 'bar',
                    roundCap: true,
                    z: 2,
                    showBackground: true,
                    backgroundStyle: {
                        color: '#A1F584'
                    },
                    data: [52],
                    coordinateSystem: 'polar',
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0.5, 1, [
                            {
                                offset: 0,
                                color: '#E6E3E3'
                            },
                            {
                                offset: 0.7,
                                color: '#A1F584'
                            },
                            {
                                offset: 1,
                                color: '#A1F584'
                            }
                        ])
                    }
                }
            ]
        };

        myEcharts.setOption(option);
        myEcharts.on('finished', () => {
            myEcharts.resize();
        });
    };
    return (
        <>
            {/* <div style={{ width: '200px' }}> */}
            <Box ref={chartRef} sx={{ width: '100%', height: '150px' }}></Box>
            {/* <Typography>12312</Typography> */}
            {/* </div> */}
        </>
    );
};

const LiquidChart = (props) => {
    const { totalStoreNum, totalScreenNum, onlineNum, offlineNum } = props.storeScreenData;
    return (
        <>
            <Card
                sx={{
                    width: '100%',
                    height: '387px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'cneter',
                    border: '1px solid #eaf0f5'
                }}
                variant="outlined"
            >
                <Grid container direction="row" justifyContent="flex-start" alignItems="center">
                    <Grid container item xs={6} md={6} direction="column" justifyContent="center" alignItems="center">
                        <Instrument data={totalStoreNum} />
                        <Typography sx={{ color: '#848f99' }}>门店数量</Typography>
                    </Grid>
                    <Grid container item xs={6} md={6} direction="column" justifyContent="center" alignItems="center">
                        <Instrument data={totalScreenNum} />
                        <Typography sx={{ color: '#848f99' }}>数字标牌总数</Typography>
                    </Grid>
                    <Grid container item xs={6} md={6} direction="column" justifyContent="center" alignItems="center">
                        <Instrument data={onlineNum} />
                        <Typography sx={{ color: '#848f99' }}>数字标牌在线数</Typography>
                    </Grid>
                    <Grid container item xs={6} md={6} direction="column" justifyContent="center" alignItems="center">
                        <Instrument data={offlineNum} />
                        <Typography sx={{ color: '#848f99' }}>数字标牌离线数</Typography>
                    </Grid>
                    {/* <Instrument />
                    <Instrument />
                    <Instrument /> */}
                </Grid>
            </Card>
        </>
    );
};

export default LiquidChart;
