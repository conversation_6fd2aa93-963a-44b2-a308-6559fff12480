import React from "react";
import { forwardRef } from "react";
import {
  Stack,
  InputLabel,
  OutlinedInput,
  Select,
  MenuItem,
} from "@mui/material";
import ListSubheader from "@mui/material/ListSubheader";

const RendItems = (items) => {
  let temp = [];
  items.forEach((info) => {
    temp.push(
      <ListSubheader key={info.label + "Subheader"}>{info.label}</ListSubheader>
    );
    info.options.forEach((item) => {
      temp.push(
        <MenuItem key={item.label} value={item.value}>
          {item.label}
        </MenuItem>
      );
    });
  });
  return temp;
};

const CustomGroupSelect = forwardRef((props, ref) => {
  const { onChange, value, label, required, items, ...rest } = props;
  const handleChangeSelect = (event) => {
    if (onChange) {
      onChange(event);
    }
  };

  return (
    <Stack
      sx={{
        mb: 2,
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        width: "100%",
      }}
      spacing={1}>
      {label && (
        <InputLabel sx={{ color: "#707070", fontSize: "14px", mr: 2 }}>
          {label} {required && <i style={{ color: "red" }}>*</i>}
        </InputLabel>
      )}
      <Select
        sx={{
          minWidth: 80,
        }}
        style={{
          marginTop: "0px",
          marginLeft: "10px",
        }}
        displayEmpty
        {...rest}
        value={props.value}
        onChange={handleChangeSelect}
        input={<OutlinedInput />}
        inputProps={{ "aria-label": "Without label" }}>
        <MenuItem value="">
          <em>None</em>
        </MenuItem>
        {RendItems(items)}
      </Select>
    </Stack>
  );
});

export default CustomGroupSelect;
