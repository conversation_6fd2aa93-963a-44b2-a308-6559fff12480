import React from 'react'

import Moveable from "react-moveable";
import { useEffect, useState, useRef } from "react";
import { useBoolean } from "ahooks";
import <PERSON><PERSON>elper from "@/pages/program/material/components/AiHelper";

const AiButton = () => {
  const [floatTarget, setFloatTarget] = useState(null);
  useEffect(() => {
    setFloatTarget(document.querySelector(".moveable_flow_target"));
  }, []);
  function handleFlowButtonDragStart(e) {
    e.set(flowFrame.translate);
  }
  const handleFlowButtonMove = (e) => {
    flowFrame.translate = e.beforeTranslate;
    e.target.style.transform = `translate(${e.beforeTranslate[0]}px, ${e.beforeTranslate[1]}px)`;
  };
  const [flowFrame, setFlowFrame] = useState({
    translate: [0, 0],
  });

  const [aiOpen, { setTrue: handleOpenAi, setFalse: handleCloseAi }] =
    useBoolean(false);
  const handleClickBtn = () => {
    console.log("aa");

    handleOpenAi();
  };
  return (
    <>
      <div
        className="moveable_flow_target"
        onClick={() => {
          handleClickBtn();
        }}
      >
        <div className="flow-button" onClick={() => {
          handleClickBtn();
        }}>
          <svg
            t="1728971337804"
            className="icon"
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            p-id="88785"
            width="22"
            height="22"
          >
            <path
              d="M496.85086 0C285.377049 0 112.594962 154.562175 112.594962 347.406637c0 25.180328 3.070772 50.155938 8.802879 74.517393l-54.455018 121.807277c-4.29908 9.826469-3.889644 21.085966 1.228309 30.502999 5.117953 9.417033 14.534986 15.763295 24.975609 17.196322 57.935226 8.188725 81.682527 33.164334 81.682527 82.501399v118.941224c0 18.015194 7.165134 35.416234 20.062375 48.313474 12.897241 12.897241 30.093563 20.062375 48.313475 20.062375h22.109556c37.668133 0 68.171132 30.502999 68.171132 68.171132v60.391843c0 18.834066 15.353858 34.187925 34.187925 34.187925h368.492602c18.834066 0 34.187925-15.353858 34.187925-34.187925 0-18.834066-15.353858-34.187925-34.187925-34.187925H402.066373V929.420232c0-75.336265-61.005998-136.546981-136.546981-136.546981h-22.109556v-118.941224c0-70.013595-34.392643-118.122351-97.241103-139.822471l42.581367-95.193922c3.27549-7.369852 3.889644-15.558577 1.637745-23.337865-6.346261-22.109556-9.417033-45.037985-9.417033-68.171132 0-153.129148 140.641343-279.030788 315.880048-279.030787s315.880048 125.901639 315.880048 279.030787c0 18.834066 15.353858 34.187925 34.187924 34.187925 18.834066 0 34.187925-15.353858 34.187925-34.187925C881.106757 154.562175 708.32467 0 496.85086 0z m428.884446 443.62415c-18.834066 0-34.187925 15.353858-34.187925 34.187925v383.846462c0 18.834066 15.353858 34.187925 34.187925 34.187924 18.834066 0 34.187925-15.353858 34.187925-34.187924V477.607357a34.187925 34.187925 0 0 0-34.187925-33.983207z m0 0"
              p-id="88786"
            ></path>
            <path
              d="M680.278289 463.481807c-12.487805-27.227509-51.588964-26.203918-62.643743 1.637745L475.969612 820.919632c-6.960416 17.605758 1.637745 37.463415 19.038785 44.423831 17.605758 6.960416 37.463415-1.637745 44.42383-19.038785l34.802079-87.414634c0.614154 0 1.228309 0.409436 1.842463 0.409436h165.207517l40.738905 88.642943c5.117953 11.054778 15.763295 18.629348 27.841663 19.652939 12.078369 1.228309 23.952019-4.29908 30.912435-14.330268 6.960416-10.031188 8.188725-22.928429 3.070772-33.983207l-163.569772-355.80008z m-79.021192 227.441824l50.360656-126.515794 58.139944 126.515794h-108.5006z m0 0"
              p-id="88787"
            ></path>
          </svg>
        </div>
      </div>
      <Moveable
        target={floatTarget} // moveable的对象
        draggable // 是否可以拖拽
        padding={{ left: 0, top: 0, right: 0, bottom: 0 }} // padding距离
        zoom={1} // 缩放包裹的moveable
        origin={false} // 显示中心点
        className="flow_Moveable"
        throttleDrag={0} // 拖拽阈值 达到这个值才执行拖拽
        onDragStart={handleFlowButtonDragStart} // 拖动开始执行
        onDrag={handleFlowButtonMove} // 拖动中
      />
      <AiHelper open={aiOpen} onClose={handleCloseAi} />
    </>
  );
};

export default AiButton;
