import React from "react";
import { useState, useRef, useEffect } from "react";
import {
  <PERSON><PERSON>,
  Stack,
  Typography,
  Grid,
  InputLabel,
  TextField,
  FormHelperText,
} from "@mui/material";
import LoadingButton from "@mui/lab/LoadingButton";
import {
  BootstrapActions,
  BootstrapContent,
  BootstrapDialog,
  BootstrapDialogTitle,
} from "@/components/dialog";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import * as Yup from "yup";
import { useFormik } from "formik";
import ZKSelect from "@/components/ZKSelect";
import { useStateUserInfo } from "@/hooks/user";
import AdvertiserSelect from "./advertiserSelect";
import { existName } from "@/service/api/layout";
import { debounce } from "lodash-es";
import { getTreeSelect } from "@/service/api/materialGroup";
import Treeselect from "@/components/zktreeselect";
import { getPrincipaList } from "@/service/api/L3Sevice.js";

const ASYNC_VALIDATION_TIMEOUT_IN_MS = 1000;
const validationFunction = async (value, context, resolve) => {
  if (value) {
    let result = await existName({ name: value });
    if (result.data) {
      resolve(false);
    } else {
      resolve(true);
    }
  } else {
    resolve(false);
  }
};

const validationDebounced = debounce(
  validationFunction,
  ASYNC_VALIDATION_TIMEOUT_IN_MS
);

// eslint-disable-next-line react-refresh/only-export-components
const AddNewLayout = (props, ref) => {
  const { t } = useTranslation();
  const { open, onCancel, userTemp, tempInfo, isMobile } = props;
  const [saveLoading, setSaveLoding] = useState(false);
  const [resolution, setResolution] = useState("");
  const [type, setType] = useState("");
  const navigate = useNavigate();
  const advertiserSelect = useRef(null);
  const userInfor = useStateUserInfo();
  const [groups, setGroups] = useState([]);
  const treeSelectRef = React.useRef(null);
  let templateJson = {};
  if (tempInfo) {
    templateJson = JSON.parse(tempInfo.templateJson);
  }

  const resolutionOptions = [
    { label: "1920*1080", value: "1920*1080" },
    { label: "1080*1920", value: "1080*1920" },
    { label: "1366*768", value: "1366*768" },
    { label: "1280*720", value: "1280*720" },
    { label: "720*1280", value: "720*1280" },
    { label: t("layout.custom"), value: "custom" },
  ];

  const [merchants, setMerchants] = useState([]);

  const getMerchant = () => {
    //如果是使用模版新增布局资源，那么选择的商户为该模版的商户

    getPrincipaList({ type: "2" }).then((res) => {
      setMerchants(res?.data || []);
    });
  };

  useEffect(() => {
    getMerchant();
  }, []);

  let validationSchema =
    localStorage.getItem("showAudit") === "Y" && userInfor.roleCode != "admin"
      ? {
          name: Yup.string()
            .required(t("layout.input_name"))
            .max(50, t("layout.name_max"))
            .test(
              "Name exists",
              t("layout.name_exit"),
              (val, context) =>
                new Promise((resolve) =>
                  validationDebounced(val, context, resolve)
                )
            ),
          resolution: Yup.string().required(
            t("layout.input_select_resolution")
          ),
          customWidth: Yup.number(t("layout.input_number"))
            .required(t("layout.input_width"))
            .positive(t("layout.input_positive_number"))
            .integer(t("layout.input_integer")),
          customHeight: Yup.number(t("layout.input_number"))
            .required(t("layout.input_height"))
            .positive(t("layout.input_positive_number"))
            .integer(t("layout.input_positive_number")),
          // advertiserType: Yup.string().required(t('layout.please_select')),
          //   advertiserName: Yup.string().required(t("layout.select_retail")),
          departmentId: Yup.string().required(t("layout.select_retail")),
          groupId: Yup.string().required(
            t("common.common_material_category_please")
          ),
        }
      : {
          name: Yup.string()
            .required(t("layout.input_name"))
            .max(50, t("layout.name_max"))
            .test(
              "Name exists",
              t("layout.name_exit"),
              (val, context) =>
                new Promise((resolve) =>
                  validationDebounced(val, context, resolve)
                )
            ),
          resolution: Yup.string().required(
            t("layout.input_select_resolution")
          ),
          customWidth: Yup.number(t("layout.input_number"))
            .required(t("layout.input_width"))
            .positive(t("layout.input_positive_number"))
            .integer(t("layout.input_integer")),
          customHeight: Yup.number(t("layout.input_number"))
            .required(t("layout.input_height"))
            .positive(t("layout.input_positive_number"))
            .integer(t("layout.input_integer")),
          //    advertiserType: Yup.string().required(t('layout.please_select')),
          departmentId: Yup.string().required(t("layout.select_retail")),
          groupId: Yup.string().required(
            t("common.common_material_category_please")
          ),
        };
  const newLayoutFormik = useFormik({
    initialValues: {
      name: "",
      resolution: userTemp
        ? `${templateJson.width}*${templateJson.height}`
        : "1920*1080",
      customWidth: 200,
      customHeight: 100,
      // advertiserType: '',
      //   advertiserName: "",
      departmentId: "",
      audit: "",
      groupId: undefined,
    },
    onSubmit: (values, { setErrors, setStatus, setSubmitting }) => {
      if (userTemp) {
        sessionStorage.setItem("isUserTemp", true);
        sessionStorage.setItem("userTemplateJson", tempInfo.templateJson);
      } else {
        sessionStorage.setItem("isUserTemp", false);
        sessionStorage.removeItem("userTemplateJson");
      }
      values.audit = userInfor.roleCode === "admin" ? "1" : "0";
      sessionStorage.setItem("PREVIOUS_ROUTE", "program");
      sessionStorage.setItem("PROGRAM_INFO", JSON.stringify(values));
      sessionStorage.setItem("isAdd", true);
      sessionStorage.setItem("isMobile", isMobile);
      sessionStorage.setItem("templateLayout", false);
      navigate("/neweditor");
    },
    validationSchema: Yup.object().shape(validationSchema),
  });

  //   //选择商户回调数值
  //   const setFormValues = (id, name) => {
  //     newLayoutFormik.setFieldValue("departmentId", id);
  //     newLayoutFormik.setFieldValue("advertiserName", name);
  //   };

  const handleClose = () => {
    setGroups([]);
    onCancel();
    newLayoutFormik.handleReset();
  };
  const getOption = (merchantId) => {
    getTreeSelect({ departmentId: merchantId }).then((res) => {
      setGroups(res.data);
    });
  };
  const handleClearGroup = () => {
    newLayoutFormik.setFieldValue("groupId", "");
    treeSelectRef?.current?.clear();
  };
  return (
    <>
      <BootstrapDialog
        open={open}
        maxWidth={"xs"}
        fullWidth
        onClose={handleClose}>
        <form noValidate onSubmit={newLayoutFormik.handleSubmit}>
          <BootstrapDialogTitle onClose={handleClose}>
            <Typography variant="h4" component="p">
              {isMobile
                ? t("template.addMobileTemTitle")
                : userTemp
                ? t("template.useTitle")
                : t("layout.add_new_layout")}
            </Typography>
          </BootstrapDialogTitle>
          <BootstrapContent>
            <Stack
              justifyContent="flex-start"
              alignItems="flex-start"
              spacing={1}
              sx={{ marginBottom: 2 }}>
              <InputLabel htmlFor="name">
                {t("layout.program")}
                <span style={{ color: "red" }}>*</span>
              </InputLabel>
              <TextField
                id="name"
                fullWidth
                type="text"
                placeholder={t("layout.input_name")}
                variant="outlined"
                name="name"
                onBlur={newLayoutFormik.handleBlur}
                onChange={newLayoutFormik.handleChange}
                value={newLayoutFormik.values.name}
                error={Boolean(
                  newLayoutFormik.touched.name && newLayoutFormik.errors.name
                )}
              />
              {newLayoutFormik.touched.name && newLayoutFormik.errors.name && (
                <FormHelperText error id="name-error">
                  {newLayoutFormik.errors.name}
                </FormHelperText>
              )}
            </Stack>

            {!userTemp && (
              <Stack
                justifyContent="flex-start"
                alignItems="flex-start"
                spacing={1}
                sx={{ marginBottom: 2 }}>
                <InputLabel htmlFor="resolution">
                  {t("layout.resolution")}
                  <span style={{ color: "red" }}>*</span>
                </InputLabel>
                <ZKSelect
                  id="resolution"
                  fullWidth
                  name="resolution"
                  value={newLayoutFormik.values.resolution}
                  onBlur={newLayoutFormik.handleBlur}
                  onClear={() => {
                    setResolution("");
                    newLayoutFormik.setFieldValue("resolution", undefined);
                  }}
                  onChange={(e) => {
                    setResolution(e.target.value);
                    newLayoutFormik.handleChange(e);
                  }}
                  placeholder={t("layout.input_select_resolution")}
                  options={resolutionOptions}
                  error={Boolean(
                    newLayoutFormik.touched.resolution &&
                      newLayoutFormik.errors.resolution
                  )}
                />
                {newLayoutFormik.touched.resolution &&
                  newLayoutFormik.errors.resolution && (
                    <FormHelperText error id="type-resolution">
                      {newLayoutFormik.errors.resolution}
                    </FormHelperText>
                  )}
              </Stack>
            )}

            {resolution === "custom" && (
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  height: "80px",
                }}>
                <Stack
                  justifyContent="flex-start"
                  alignItems="flex-start"
                  spacing={1}
                  sx={{ marginBottom: 2 }}>
                  <InputLabel htmlFor="customWidth">
                    {t("layout.width")}
                    <span style={{ color: "red" }}>*</span>
                  </InputLabel>
                  <TextField
                    id="customWidth"
                    fullWidth
                    type="number"
                    placeholder={t("layout.input_width")}
                    variant="outlined"
                    name="customWidth"
                    onBlur={newLayoutFormik.handleBlur}
                    onChange={newLayoutFormik.handleChange}
                    value={newLayoutFormik.values.customWidth}
                    error={Boolean(
                      newLayoutFormik.touched.customWidth &&
                        newLayoutFormik.errors.customWidth
                    )}
                  />
                  {newLayoutFormik.touched.customWidth &&
                    newLayoutFormik.errors.customWidth && (
                      <FormHelperText error id="name-customWidth">
                        {newLayoutFormik.errors.customWidth}
                      </FormHelperText>
                    )}
                </Stack>

                <Stack
                  justifyContent="flex-start"
                  alignItems="flex-start"
                  spacing={1}
                  sx={{ marginBottom: 2 }}>
                  <InputLabel htmlFor="customHeight">
                    {t("layout.height")}
                    <span style={{ color: "red" }}>*</span>
                  </InputLabel>
                  <TextField
                    id="customHeight"
                    fullWidth
                    type="number"
                    placeholder={t("layout.input_height")}
                    variant="outlined"
                    name="customHeight"
                    onBlur={newLayoutFormik.handleBlur}
                    onChange={newLayoutFormik.handleChange}
                    value={newLayoutFormik.values.customHeight}
                    error={Boolean(
                      newLayoutFormik.touched.customHeight &&
                        newLayoutFormik.errors.customHeight
                    )}
                  />
                  {newLayoutFormik.touched.customHeight &&
                    newLayoutFormik.errors.customHeight && (
                      <FormHelperText error id="customHeight-error">
                        {newLayoutFormik.errors.customHeight}
                      </FormHelperText>
                    )}
                </Stack>
              </div>
            )}

            {/*
                        <Stack
                            justifyContent="flex-start"
                            alignItems="flex-start"
                            spacing={1}
                            sx={{ marginBottom: 2 }}
                        >
                            <InputLabel htmlFor="advertiserType">
                                {t("ips.ips_merchant")}
                                <span style={{ color: "red" }}>*</span>
                            </InputLabel>
                            <ZKSelect
                                id="advertiserType"
                                fullWidth
                                name="advertiserType"
                                value={newLayoutFormik.values.advertiserType}
                                onBlur={newLayoutFormik.handleBlur}
                                onClear={() => {
                                    setType(undefined);
                                    newLayoutFormik.setFieldValue("advertiserType", undefined);

                                }}
                                onChange={(e) => {
                                    setType(e.target.value);
                                    newLayoutFormik.handleChange(e);

                                }}
                                placeholder={t("ips.ips_select_merchant_type")}
                                options={merchantTypes}
                                error={Boolean(
                                    newLayoutFormik.touched.advertiserType && newLayoutFormik.errors.advertiserType
                                )}
                            />
                            {newLayoutFormik.touched.advertiserType && newLayoutFormik.errors.advertiserType && (
                                <FormHelperText error id="type-error">
                                    {newLayoutFormik.errors.advertiserType}
                                </FormHelperText>
                            )}
                        </Stack> */}

            <Grid item xs={6}>
              <Stack spacing={1} sx={{ marginBottom: 2 }}>
                <InputLabel htmlFor="brandCooperate">
                  {t("ips.ips_store_brand")}
                  <i style={{ color: "red" }}>*</i>
                </InputLabel>
                <ZKSelect
                  name="departmentId"
                  value={newLayoutFormik.values.departmentId}
                  onBlur={newLayoutFormik.handleBlur}
                  options={merchants}
                  labelOptions={{
                    label: "label",
                    value: "value",
                  }}
                  onClear={() => {
                    newLayoutFormik.setFieldValue("departmentId", "");
                    setGroups([]);
                  }}
                  onChange={(e) => {
                    getOption(e.target.value);
                    newLayoutFormik.handleChange(e);
                  }}
                  placeholder={t("common.common_please_select_retail")}
                  error={Boolean(
                    newLayoutFormik.touched.departmentId &&
                      newLayoutFormik.errors.departmentId
                  )}
                />
                {!newLayoutFormik.values.departmentId &&
                  newLayoutFormik.touched.departmentId &&
                  newLayoutFormik.errors.departmentId && (
                    <FormHelperText error id="departmentId-error">
                      {newLayoutFormik.errors.departmentId}
                    </FormHelperText>
                  )}
              </Stack>
            </Grid>
            <Grid item xs={6}>
              <Stack spacing={1}>
                <InputLabel htmlFor="store-id">
                  {t("common.common_material_category")}
                  <i style={{ color: "red" }}>*</i>
                </InputLabel>
                <Treeselect
                  ref={treeSelectRef}
                  data={groups}
                  isClear={true}
                  optionValue="id"
                  optionLabel="name"
                  placeholder={t("common.common_material_category_please")}
                  onChange={(valuas) => {
                    newLayoutFormik.setFieldValue("groupId", valuas.id);
                  }}
                  onClear={handleClearGroup}
                  disableParent={true}
                  error={Boolean(
                    newLayoutFormik.touched.groupId &&
                      newLayoutFormik.errors.groupId
                  )}
                />
                {!newLayoutFormik.values.groupId &&
                  newLayoutFormik.touched.groupId &&
                  newLayoutFormik.errors.groupId && (
                    <FormHelperText error id="groupId-error">
                      {newLayoutFormik.errors.groupId}
                    </FormHelperText>
                  )}
              </Stack>
            </Grid>
          </BootstrapContent>
          <BootstrapActions>
            <Stack spacing={1} direction="row">
              <Button color="info" variant="outlined" onClick={handleClose}>
                {t("common.common_edit_cancel")}
              </Button>
              <LoadingButton
                loading={saveLoading}
                variant="contained"
                color="primary"
                disableElevation
                disabled={saveLoading}
                type="submit">
                {t("common.common_edit_ok")}
              </LoadingButton>
            </Stack>
          </BootstrapActions>
        </form>
      </BootstrapDialog>

      {/* <AdvertiserSelect
        ref={advertiserSelect}
        type={type}
        setFormValues={setFormValues}
      ></AdvertiserSelect> */}
    </>
  );
};

// eslint-disable-next-line react-refresh/only-export-components
export default React.forwardRef(AddNewLayout);
