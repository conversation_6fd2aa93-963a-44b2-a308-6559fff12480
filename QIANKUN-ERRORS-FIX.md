# qiankun错误修复方案

## 问题描述

在qiankun微前端环境中出现以下两个错误：

### 1. React Refresh错误
```
[import-html-entry]: error occurs while executing normal script <script type="module">
import RefreshRuntime from "/@react-refresh"
RefreshRuntime.injectIntoGlobalHook(window)
window.$RefreshReg$ = () => {}
window.$RefreshSig$ = () => (type) => type
window.__vite_plugin_react_preamble_installed__ = true
</script>
```

### 2. ES模块导入错误
```
qiankun.js:2621 Uncaught SyntaxError: Cannot use import statement outside a module
```

## 问题原因

1. **React Refresh冲突**: Vite的React插件在qiankun环境中注入React Refresh代码，与qiankun的沙箱机制冲突
2. **ES模块兼容性**: qiankun的模块加载机制与现代ES模块语法不完全兼容
3. **全局变量污染**: React Refresh需要的全局变量在qiankun沙箱中被隔离

## 解决方案

### 1. Vite配置修复

**文件**: `vite.config.js`

- 在qiankun模式下禁用React插件
- 添加qiankun特定的构建配置
- 设置全局变量定义

```javascript
// 检查是否在qiankun环境中
const isQiankunMode = process.env.QIANKUN === '1';

plugins: [
  // 在qiankun模式下禁用React插件
  !isQiankunMode && react({
    fastRefresh: true,
  }),
  // ... 其他插件
  qiankun("cms-app", {
    useDevMode: true,
    devSandbox: false, // 禁用开发沙箱
  }),
].filter(Boolean),

// 修复qiankun环境下的模块兼容性
define: isQiankunMode ? {
  'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
  global: 'globalThis',
} : {},
```

### 2. HTML预修复

**文件**: `index.html`

在HTML头部添加预修复脚本，在qiankun环境中预先设置必要的全局变量：

```html
<script>
  // 在qiankun环境中预先设置React Refresh相关变量
  if (window.__POWERED_BY_QIANKUN__) {
    window.$RefreshReg$ = function() {};
    window.$RefreshSig$ = function() { return function(type) { return type; }; };
    window.__vite_plugin_react_preamble_installed__ = false;
    
    // 确保global变量存在
    if (typeof global === 'undefined') {
      window.global = window;
    }
    
    // 设置process.env
    if (typeof process === 'undefined') {
      window.process = { env: { NODE_ENV: 'development' } };
    }
  }
</script>
```

### 3. 兼容性工具

**文件**: `src/utils/qiankunCompat.js`

创建了完整的兼容性修复工具，包括：
- 全局变量兼容性修复
- ES模块导入问题修复
- HMR兼容性修复
- 安全的模块导入包装器

### 4. 应用启动修复

**文件**: `src/main.jsx`

在应用启动时初始化兼容性修复：

```javascript
import { initQiankunCompat } from "@/utils/qiankunCompat";

// 初始化qiankun兼容性修复
initQiankunCompat();
```

## 修复效果

### ✅ 解决的问题

1. **React Refresh错误**: 通过禁用React插件和预设全局变量解决
2. **ES模块错误**: 通过沙箱配置和eval拦截解决
3. **全局变量问题**: 通过预修复和兼容性工具解决

### 🚀 性能优化

1. **减少错误日志**: 消除控制台中的错误和警告
2. **提高稳定性**: 避免因兼容性问题导致的功能异常
3. **更好的开发体验**: 在qiankun环境中提供更稳定的开发体验

## 使用指南

### 开发模式选择

1. **qiankun集成模式**: `npm run dev`
   - React Refresh被禁用
   - 完整的qiankun兼容性
   - 适合测试与主应用的集成

2. **独立开发模式**: `npm run dev:standalone`
   - React Refresh启用
   - 完整的热更新体验
   - 适合单独开发功能

### 验证修复

启动应用后检查：

1. **控制台日志**: 应该看到"qiankun兼容性预修复完成"
2. **错误消失**: 不应该再出现React Refresh和ES模块相关错误
3. **功能正常**: 所有应用功能应该正常工作

## 注意事项

1. **开发体验**: 在qiankun模式下热更新功能受限，建议使用独立模式开发
2. **构建配置**: 确保生产构建时使用正确的qiankun配置
3. **主应用配置**: 确保主应用正确配置了子应用的加载参数

## 故障排除

### 如果错误仍然存在

1. **清除缓存**: 清除浏览器缓存和Vite缓存
2. **重启服务**: 完全重启开发服务器
3. **检查环境变量**: 确认`QIANKUN=1`环境变量正确设置
4. **检查主应用**: 确认主应用的qiankun配置正确

### 调试工具

在浏览器控制台中检查：

```javascript
// 检查qiankun环境
console.log('qiankun环境:', window.__POWERED_BY_QIANKUN__);

// 检查React Refresh状态
console.log('React Refresh状态:', {
  $RefreshReg$: typeof window.$RefreshReg$,
  $RefreshSig$: typeof window.$RefreshSig$,
  __vite_plugin_react_preamble_installed__: window.__vite_plugin_react_preamble_installed__
});
```
