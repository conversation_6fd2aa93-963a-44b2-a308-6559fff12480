/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/prop-types */
import React, { useEffect, useMemo, useState, useRef } from "react";
import {
  But<PERSON>,
  Stack,
  Typography,
  Alert,
  Tooltip,
  Link,
  Grid,
  IconButton,
  TextField,
} from "@mui/material";
import TipsAndUpdatesIcon from "@mui/icons-material/TipsAndUpdates";
import { CloseOutlined } from "@material-ui/icons";
import CancelIcon from "@mui/icons-material/Cancel";
import InfoIcon from "@mui/icons-material/Info";
import SyncIcon from "@mui/icons-material/Sync";
import DictTag from "@/components/DictTag";
// api
import { screenAlert } from "@/service/api/screen";
import { useNavigate, useLocation } from "react-router-dom";
import { tableI18n } from "@/utils/tableLang";
import ExportDialog from "@/components/ExportDialog";
// 消息提示
import { toast } from "react-toastify";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import { useTranslation } from "react-i18next";
import { useConfirm } from "@/components/zkconfirm";
import MaterialReactTable from "material-react-table";
import UploadUpgrade from "./UploadUpgrade";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import { useFormik } from "formik";
import { removeEmpty } from "@/utils/StringUtils";
import MainCard from "@/components/MainCard";
import ZKSelect from "@/components/ZKSelect";
import AuthButton from "@/components/AuthButton";
import {
  screenStatus,
  screendirections,
  deviceTypies,
} from "@/dict/commonDict";
import { download } from "@/utils/downloadFile";
import DropdownMenu from "@/components/dropdownMenu";
import CloudUploadOutlinedIcon from "@mui/icons-material/CloudUploadOutlined";
import SyncAltOutlinedIcon from "@mui/icons-material/SyncAltOutlined";
import EditIcon from "@mui/icons-material/Edit";
import RestartAltIcon from "@mui/icons-material/RestartAlt";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
import ContentPasteSearchOutlinedIcon from "@mui/icons-material/ContentPasteSearchOutlined";
import ScreenLogList from "./screenLogList";
import ToastContent from "@/components/@extended/ToastContent";
import UpgradeIcon from "@mui/icons-material/Upgrade";
// import TimedShutdown from './TimedShutdown';
import HighlightOffIcon from "@mui/icons-material/HighlightOff";
import Shutdown from "./Shutdown";
import UpgradeList from "./upgradeList";
import { useBoolean } from "ahooks";

import dayjs from "dayjs";
import timezone from "dayjs/plugin/timezone"; // 引入时区插件
import utc from "dayjs/plugin/utc"; // 引入 UTC 插件
import { eventlevels } from "@/dict/commonDict";
// 使用插件
dayjs.extend(timezone);
dayjs.extend(utc);
const AlertTable = ({ screenId }) => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const confirm = useConfirm();
  const [rowSelection, setRowSelection] = useState([]);
  const [isError, setIsError] = useState(false);
  const upload = useRef(null);
  const screenLogRef = useRef(null);
  const [timedShutdownOpen, setTimedShutdownOpen] = useState(false);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  // 表格数据
  const [data, setData] = useState([]);
  // 分页的参数
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);
  // 显示搜索
  const [showSearch, setShowSearch] = useState(true);
  // 查询参数
  const requestParams = useRef(null);
  // 构建参数
  const buildParams = () => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      id: screenId,
    };
    return params;
  };

  // 获取数据
  const getTableData = async () => {
    if (!data.length) {
      setIsLoading(true);
    } else {
      setIsRefetching(true);
    }
    // // 开启加载
    setIsLoading(true);
    // setIsRefetching(true);
    await screenAlert(buildParams())
      .then((res) => {
        // 设置数据
        setData(res.data.data);
        // 设置总记录数
        setRowCount(res.data.total);
        setIsLoading(false);
        setIsRefetching(false);
      })
      .catch((err) => {
        setIsError(true);
        setIsLoading(false);
        setIsRefetching(false);
      });
  };
  useEffect(() => {
    if (screenId) {
      getTableData();
      setRowSelection([]);
    }
  }, [pagination.pageIndex, pagination.pageSize, screenId]);
  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "eventName",
        header: t("event.column_event_name"),
        size: 180,
        Cell: ({ cell, row }) => {
          return (
            <Tooltip title={row.original.eventName} placement="top">
              <Typography className="textSpace">
                {t(`event.${row.original.eventName}`)}
              </Typography>
            </Tooltip>
          );
        },
      },
      {
        accessorKey: "eventLevel",
        header: t("event.column_event_level"),
        size: 180,
        Cell: ({ cell, row }) => {
          return (
            <DictTag
              dicts={eventlevels}
              fieldName={{ title: "label", color: "color" }}
              value={row.original.eventLevel}
            />
          );
        },
      },
      {
        accessorKey: "eventPushTime",
        header: t("event.column_event_alert_time"),
        size: 180,
        Cell: ({ cell, row }) => {
          const browserTimeZone =
            Intl.DateTimeFormat().resolvedOptions().timeZone;
          const formattedDate = dayjs(row.original.eventPushTime)
            .tz(browserTimeZone)
            .format("YYYY-MM-DD HH:mm:ss");

          return (
            <Tooltip title={formattedDate} placement="top">
              <Typography className="textSpace">{formattedDate}</Typography>
            </Tooltip>
          );
        },
      },
    ],
    []
  );

  return (
    <div>
      <Alert sx={{ mb: 1 }} severity="info">
        {t("event.event_table_tips", {
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        })}
        {/* 当前浏览器时区为：{Intl.DateTimeFormat().resolvedOptions().timeZone}
        ，已将事件告警时间转为浏览器时区. */}
      </Alert>
      <MaterialReactTable
        // table 状态
        state={{
          // 加载状态
          isLoading,
          // 分页参数
          pagination,
          // 重新拉取
          showProgressBars: isRefetching,
          columnPinning: { right: ["mrt-row-actions"] },
          showAlertBanner: isError,
          // columnPinning: { right: ["action"] },
          rowSelection,
        }}
        enableTopToolbar={false}
        enableToolbarInternalActions={false}
        muiTablePaperProps={{
          elevation: 0,
          sx: {
            borderRadius: "5px",
            // border: "1px solid #f0f0f0",
          },
        }}
        muiTableHeadRowProps={{
          sx: { backgroundColor: "#fafafa", boxShadow: "none" },
        }}
        // 关闭过滤搜素
        enableColumnFilters={true}
        // 关闭排序
        enableSorting={false}
        // 布局方式
        layoutMode="grid"
        enableColumnActions={false}
        // 开启列对齐
        muiTableHeadCellProps={{
          sx: {
            "& .Mui-TableHeadCell-Content": {
              justifyContent: "space-between",
            },
          },
        }}
        // 解决列太多宽度太长问题
        enableColumnResizing
        // enablePinning
        // 初始化状态

        initialState={{
          columnVisibility: { createTime: true },
          columnPinning: { right: ["action"] },
        }}
        muiToolbarAlertBannerProps={
          isError
            ? {
                color: "error",
                children: "Error loading data",
              }
            : undefined
        }
        //行选中
        onRowSelectionChange={setRowSelection}
        muiTableBodyRowProps={({ row }) => ({
          onClick: row.getToggleSelectedHandler(),
          sx: { cursor: "pointer" },
        })}
        // 开启多选
        // enableRowSelection
        // 列数
        rowCount={rowCount}
        // 固定头部
        enableStickyHeader
        // 处理表格高度
        muiTableContainerProps={{ sx: { maxHeight: "420px" } }}
        // 设置背景颜色
        muiTableBodyCellProps={{ sx: { backgroundColor: "white" } }}
        muiTableProps={{
          sx: { backgroundColor: "white", tableLayout: "fixed" },
        }}
        muiBottomToolbarProps={{ sx: { backgroundColor: "white" } }}
        muiTopToolbarProps={{ sx: { backgroundColor: "white" } }}
        // 分页回调函数
        onPaginationChange={setPagination}
        // 记得开启这些 tips:否则会出现useEffect 反作用函数发起多次请求点击下一页会出问题
        manualFiltering
        manualPagination
        manualSorting
        // 开启分页
        enablePagination
        // 列定义
        columns={columns}
        // 数据
        data={data}
        localization={tableI18n}
        // 多选底部提示
        positionToolbarAlertBanner="none"
        // 开启action操作
        // enableRowActions
        // action操作位置
        positionActionsColumn="last"
        displayColumnDefOptions={{
          "mrt-row-actions": {
            size: 180,
          },
        }}
      />
    </div>
  );
};
export default AlertTable;
