import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App";
import { setToken } from "@/utils/auth";

import "@/lang/index";
import { Provider as ReduxProvider } from "react-redux";
import {
  renderWithQiankun,
  qiankunWindow,
} from "vite-plugin-qiankun/dist/helper";
import { store } from "@/store";
import { useDispatchUser } from "@/hooks/user";
import { BrowserRouter as Router } from "react-router-dom";
import "@/assets/css/global.less";
import "virtual:svg-icons-register";
import "@/utils/hmr"; // 引入HMR辅助工具

const initQianKun = () => {
  renderWithQiankun({
    // 当前应用在主应用中的生命周期
    // 文档 https://qiankun.umijs.org/zh/guide/getting-started#
    mount(props) {
      try {
        render(props.container);

        props.onGlobalStateChange((state) => {
          if (state?.token) {
            setToken(state.token);
            sessionStorage.setItem(
              "USER_INFO",
              JSON.stringify(state?.userInfo)
            );
          }
        }, true);
      } catch (error) {
        console.error("子应用挂载时发生错误:", error);
      }
    },
    bootstrap() {},
    unmount(props) {
      console.log("子应用正在卸载");
      // 清理root实例，为下次挂载做准备
      if (root) {
        root.unmount();
        root = null;
      }
    },
  });
};

// 在其他地方监听挂载完成事件
window.addEventListener("cms-app", () => {
  console.log("子应用挂载完成");
  // 执行挂载完成后的操作
});

let root = null;

const render = (container) => {
  // 如果是在主应用的环境下就挂载主应用的节点，否则挂载到本地
  const appDom = container ? container : document.getElementById("root");

  // 避免重复创建root，支持HMR
  if (!root) {
    root = ReactDOM.createRoot(appDom);
  }

  root.render(
    <ReduxProvider store={store}>
      <Router basename="/cms-app">
        <App />
      </Router>
    </ReduxProvider>
  );
};

if (!qiankunWindow.__POWERED_BY_QIANKUN__) {
  // 独立运行模式，HMR 直接生效
  render();
} else {
  initQianKun();
}

// 判断当前应用是否在主应用中
// qiankunWindow.__POWERED_BY_QIANKUN__ ? initQianKun() : render();
