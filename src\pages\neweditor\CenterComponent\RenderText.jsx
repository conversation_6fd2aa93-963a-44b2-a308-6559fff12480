import React from 'react'
import { Grid } from "@mui/material";
import RenderCommon from "./RenderCommon";
const RenderText = (props) => {
  const info = props.info;
  if (info.hide) {
    return "";
  }

  const getAnimationDuration = (speed) => {
    let baseNumber = 1;
    if (info.scrollDirection === "left" || info.scrollDirection === "right") {
      baseNumber = info.width / 200;
    } else {
      baseNumber = info.height / 200;
    }
    let speedTime = baseNumber * 3;
    switch (speed) {
      case 20:
        speedTime = baseNumber * 8;
        break;
      case 60:
        speedTime = baseNumber * 5;
        break;
      case 120:
        speedTime = baseNumber * 3;
        break;
      case 200:
        speedTime = baseNumber * 2;
        break;

      default:
        speedTime = baseNumber * 3;
    }
    return speedTime;
  };

  const getAnimation = (animation) => {
    let animationName = "";
    switch (animation) {
      case "up":
        animationName = "bottom-to-top-wrap";
        break;
      case "down":
        animationName = "top-to-bottom-wrap";
        break;
      case "left":
        animationName = "right-to-left-wrap";
        break;
      case "right":
        animationName = "left-to-right-wrap";
        break;
      default:
        animationName = "";
    }
    return animationName;
  };

  return (
    <RenderCommon {...props}>
      <Grid
        className={`animated ${info.anim}`}
        sx={{
          width: "100%",
          height: "100%",
          backgroundColor: info.bgColor,
          overflow: "hidden",
        }}
      >
        <Grid
          className={getAnimation(info.scrollDirection)}
          style={{
            width: "100%",
            height: "100%",
            fontSize: info.fontSize,
            fontFamily: info.font,
            color: info.fontColor,
            fontWeight: info.isBold ? "bold" : "normal",
            fontStyle: info.isItaly ? "italic" : "normal",
            textDecoration: info.isUnderline ? "underline" : "none",
            textAlign: info.textAlign,
            lineHeight: info.lineHeight + "px",
            animationDuration: getAnimationDuration(info.speed) + "s",
            // wordBreak: "break-all",
            whiteSpace: "pre-wrap",
          }}
        >
          {info.text}
        </Grid>
      </Grid>
    </RenderCommon>
  );
};

export default RenderText;
