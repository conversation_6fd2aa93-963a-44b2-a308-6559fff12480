import React from 'react'
import { useRef, useEffect, useState, memo } from "react";

// eslint-disable-next-line react-refresh/only-export-components
const VideoPlay = (props) => {
  const videoRef = useRef(false);
  const [timeOut, setTimeOut] = useState(null);
  const initPlay = () => {
    if (videoRef.current) {
      videoRef.current.src = props.currentUrl + "?_=" + new Date().getTime();
      videoRef.current.load();
      videoRef.current.play();
    }
  };

  useEffect(() => {
    if (props.currentUrl) {
      initPlay();
      if (props.playEnd) {
        if (timeOut) {
          clearTimeout(timeOut);
        }
        let time = setTimeout(() => {
          props.playEnd();
        }, props.duration * 1000);
        setTimeOut(time);
      }
    }
    return () => {
      if (timeOut) {
        clearTimeout(timeOut);
      }
    };
  }, [props.currentUrl, props.playIndex, props.videoSize, props.duration]);

  if (!props.currentUrl) {
    return "";
  }
  return (
    <video
      ref={videoRef}
      poster={props.posterUrl}
      autoPlay
      crossOrigin="anonymous"
      muted="muted"
      width="100%"
      height="100%"
      loop={true}
      style={{
        objectFit: "fill",
        borderRadius: "10px",
      }}
    ></video>
  );
};

const areEqual = (prevProps, nextProps) => {
  if (
    prevProps.currentUrl === nextProps.currentUrl &&
    prevProps.playIndex === nextProps.playIndex &&
    prevProps.videoSize === nextProps.videoSize &&
    prevProps.duration === nextProps.duration
  ) {
    return true;
  } else {
    return false;
  }
};

// eslint-disable-next-line react-refresh/only-export-components
export default memo(VideoPlay, areEqual);
