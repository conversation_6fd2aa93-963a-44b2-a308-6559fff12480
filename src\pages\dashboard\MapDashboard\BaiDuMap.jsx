/* eslint-disable no-undef */
import { useEffect, useState } from "react"
import {
    Grid,
    Card,
    Typography,
} from "@mui/material";
import pin_orange from "@/assets/images/icons/pin_orange.svg";
import pin_green from "@/assets/images/icons/pin_green.svg";
import pin_blue from "@/assets/images/icons/pin_blue.svg";
import { useTranslation } from "react-i18next";

const BaiDuMap = (props) => {

    let center = props.center
    let position = { lat: 24.618211693728895, lng: 118.06088011225243 };

    const [map, setMap] = useState(null);

    const [dataList, setDataList] = useState([]);

    const [showType, setShowType] = useState(['1', '2', '3']);

    const [showData, setShowData] = useState([]);

    const [markers, setMarkers] = useState([]);
    const [zoom, setZoom] = useState(1);

    const { t } = useTranslation();

    const getSvgIcon = (Leve = '1') => {
        let fillColor = ""
        if (Leve === '1') {
            fillColor = "rgb(86,166,59)"
        } else if (Leve === '2') {
            fillColor = "rgb(107,149,238)"
        } else if (Leve === '3') {
            fillColor = "rgb(234,143,64)"
        }

        let icons = new BMap.Symbol("M0 0 C6.85580569 4.2076404 11.26833084 10.1340168 13.19140625 17.921875 C14.30442032 25.65439383 12.65614117 31.93420041 8.5625 38.5 C6.22259831 41.41280605 3.67502836 44.08167006 1.0625 46.75 C0.39863281 47.44351563 -0.26523437 48.13703125 -0.94921875 48.8515625 C-4.07317781 52.10646596 -7.2310006 55.32621998 -10.4375 58.5 C-13.82416654 57.03169836 -16.05414718 55.14074491 -18.66015625 52.54296875 C-19.83868164 51.36831055 -19.83868164 51.36831055 -21.04101562 50.16992188 C-21.85248047 49.35072266 -22.66394531 48.53152344 -23.5 47.6875 C-24.31533203 46.87990234 -25.13066406 46.07230469 -25.97070312 45.24023438 C-26.75509766 44.45326172 -27.53949219 43.66628906 -28.34765625 42.85546875 C-29.41705444 41.78268677 -29.41705444 41.78268677 -30.50805664 40.68823242 C-36.24409544 34.18284007 -36.86254633 28.32958628 -36.77734375 19.89453125 C-36.00567055 12.18666904 -32.25404279 6.5262427 -26.37890625 1.65625 C-18.97059305 -2.75594458 -7.92679982 -3.50578622 0 0 Z", {
            fillColor: fillColor,
            fillOpacity: 1,
            scale: 0.8,
            rotation: 0,
            strokeWeight: 0,
            strokeOpacity: 0.1,
            anchor: new BMapGL.Size(-10, 60)
        })
        return icons
    }

    const getLabel = (number = 0) => {
        var offsetSize = new BMap.Size(10, 10);
        var labelStyle = {
            color: "#fff",
            backgroundColor: "0.05",
            border: "0",
            fontSize: "24px"
        };
        switch ((number + '').length) {
            case 1:
                labelStyle.fontSize = "24px";
                offsetSize = new BMap.Size(12, 10);
                break;
            case 2:
                labelStyle.fontSize = "18px";
                offsetSize = new BMap.Size(10, 12);
                break;
            case 3:
                labelStyle.fontSize = "16px";
                offsetSize = new BMap.Size(7, 11);
                break;
            case 4:
                labelStyle.fontSize = "14px";
                offsetSize = new BMap.Size(5, 11);
                break;
            default:
                break;
        }
        var label = new BMap.Label(number, {
            offset: offsetSize
        });
        label.setStyle(labelStyle)
        return label;
    }

    const addMarker = (position, element) => {
        const markerPoint = new BMap.Point(position.lng, position.lat);
        let icons = getSvgIcon(element.yearsType)
        const marker = new BMap.Marker(markerPoint, {
            icon: icons,
            title: element.address + ' ' + element.storeName
        });

        marker.addEventListener("click", () => {
            map.setZoom(18);
            map.setCenter(marker.getPosition());
        });


        marker.setLabel(getLabel(element.total));
        if (map) {
            map.addOverlay(marker);
            return marker;
        } else {
            return null
        }
    }


    const renderMakrk = async () => {
        if (showData.length > 0) {
            markers.map((item) => {
                item?.remove();
            })
            setMarkers([])
            let markerList = showData.map((element, index) => {
                let local = element.location.split(',')
                let location = { lng: Number(local[0]), lat: Number(local[1]) };
                return addMarker(location, element)
            });
            setMarkers(markerList)
        } else {
            markers.map((item) => {
                item?.remove();
            })
            setMarkers([])
        }
    }


    useEffect(() => {
        renderMakrk()
    }, [showData])


    const filterData = (list) => {
        let temp = {}
        list.forEach((item) => {
            let id = item.id
            let obj = temp[id]
            if (!obj) {
                temp[id] = item
            } else {
                let current = Number(item.yearsType)
                let tempValue = Number(obj.yearsType)
                let total = item.total + obj.total
                if (current > tempValue) {
                    item.total = total
                    temp[id] = item
                }
            }
        })
        let result = Object.keys(temp).map((key) => {
            return temp[key]
        })
        return result;
    }

    useEffect(() => {
        let dataList = [...(props.mapData?.zeroToThreeYears || []), ...(props.mapData?.threeToSixYears || []), ...(props.mapData?.sixPlusYears || [])]
        let result = filterData(dataList)
        setDataList(result)
        let list = dataList?.filter((item) => {
            let index = showType.indexOf(item.yearsType)
            if (index > -1) {
                return true
            } else {
                return false
            }
        })
        setShowData(list)
    }, [props.mapData])


    useEffect(() => {
        let list = dataList?.filter((item) => {
            let index = showType.indexOf(item.yearsType)
            if (index > -1) {
                return true
            } else {
                return false
            }
        })
        setShowData(list)
        sessionStorage.setItem('showType', showType.toString())
    }, [showType])



    const fitBounds = (bPoints) => {
      if (map) {
            /* eslint-disable no-eval */
            const { zoom, center } = map.getViewport(eval(bPoints))
            setZoom(zoom)
            map.centerAndZoom(center, zoom)
        }
    }


    useEffect(() => {
        if (showData.length > 1) {
            let resultPoints = showData.map((item) => {
                let location = item.location.split(',')
                if (location.length === 2) {
                    let p = { lng: Number(location[0]), lat: Number(location[1]) };
                    let markerPoint = new BMap.Point(p.lng, p.lat);
                    return markerPoint
                } else {
                    return null
                }
            })
            if (map) {
                fitBounds(resultPoints)
            }
        } else if (showData.length === 1) {
            try {
                let location = showData[0].location.split(',')
                if (location.length === 2) {
                    let p = { lng: Number(location[0]), lat: Number(location[1]) };
                    const point = new BMap.Point(p.lng, p.lat);
                    map?.panTo(point)
                }
            } catch (e) {
                console.log(e)
            }
        }
    }, [showData])

    useEffect(() => {
        if (center) {
            let location = center.split(',')
            if (location.length === 2) {
                position = { lng: Number(location[0]), lat: Number(location[1]) };
                const point = new BMap.Point(position.lng, position.lat);
                map.setZoom(1)
                map?.panTo(point)
            }
        }
    }, [center])

    const initMap = () => {
        const map = new BMap.Map("mapContainer");
        map.centerAndZoom(new BMap.Point(118.06088011225243, 24.618211693728895), zoom);
        map.enableScrollWheelZoom(true)
        var zoomCtrl = new BMap.NavigationControl()  // 添加缩放控件
        map.addControl(zoomCtrl);

        var fullscreenControl = new FullscreenControl();
        //添加到地图中
        map.addControl(fullscreenControl);

        var yearControl = new YearControl();
        //添加到地图中
        map.addControl(yearControl);

        setMap(map)
    }

    useEffect(() => {
        if (map === null) {
            initMap()
        }
    }, [])


    const showTypeClick = (type) => {
        let showTypeStr = sessionStorage.getItem('showType')
        let tempArray = []
        if (showTypeStr) {
            tempArray = showTypeStr.split(',')
        }
        let index = tempArray.indexOf(type)
        if (index > -1) {
            tempArray.splice(index, 1)
            setShowType(tempArray)
        } else {
            let temp = [...tempArray, type]
            setShowType(temp)
        }
    }


    // 添加自定义全屏控件  定义 一个控件类
    function FullscreenControl () {
        this.defaultAnchor = BMAP_ANCHOR_TOP_RIGHT;
        this.defaultOffset = new BMap.Size(10, 10)
    }
    //通过JavaScript的prototype属性继承于BMap.Control
    FullscreenControl.prototype = new BMap.Control();

    //自定义控件必须实现自己的initialize方法，并且将控件的DOM元素返回
    FullscreenControl.prototype.initialize = function (map) {
        //创建一个dom元素
        var div = document.createElement('div');
        div.innerHTML = '<svg t="1705989135739" style="width:30px;height:30px;cursor: pointer;"  viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4170" width="200" height="200"><path d="M285.866667 810.666667H384v42.666666H213.333333v-170.666666h42.666667v98.133333l128-128 29.866667 29.866667-128 128z m494.933333 0l-128-128 29.866667-29.866667 128 128V682.666667h42.666666v170.666666h-170.666666v-42.666666h98.133333zM285.866667 256l128 128-29.866667 29.866667-128-128V384H213.333333V213.333333h170.666667v42.666667H285.866667z m494.933333 0H682.666667V213.333333h170.666666v170.666667h-42.666666V285.866667l-128 128-29.866667-29.866667 128-128z" fill="#444444" p-id="4171"></path></svg>'
        // 设置样式
        div.style.cursor = "pointer";
        div.style.padding = "8px 13px";
        div.style.margin = "20px 0px";
        div.style.boxShadow = "0 2px 6px 0 rgba(27, 142, 236, 0.5)";
        div.style.borderRadius = "5px";
        div.style.backgroundColor = "white";
        // 绑定事件-全屏
        div.onclick = function (e) {
            if (document.fullscreenElement === null) {
                document.getElementById("mapContainer").requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        document.addEventListener("fullscreenchange", () => {
            if (document.fullscreenElement === null) {
                console.log("Exited fullscreen");
                div.innerHTML = '<svg t="1705989135739" style="width:30px;height:30px;cursor: pointer;" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4170" width="200" height="200"><path d="M285.866667 810.666667H384v42.666666H213.333333v-170.666666h42.666667v98.133333l128-128 29.866667 29.866667-128 128z m494.933333 0l-128-128 29.866667-29.866667 128 128V682.666667h42.666666v170.666666h-170.666666v-42.666666h98.133333zM285.866667 256l128 128-29.866667 29.866667-128-128V384H213.333333V213.333333h170.666667v42.666667H285.866667z m494.933333 0H682.666667V213.333333h170.666666v170.666667h-42.666666V285.866667l-128 128-29.866667-29.866667 128-128z" fill="#444444" p-id="4171"></path></svg>'
            } else {
                div.innerHTML = '<svg t="1705989240172" style="width:30px;height:30px;cursor: pointer;"  viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4314" width="200" height="200"><path d="M354.133333 682.666667H256v-42.666667h170.666667v170.666667H384v-98.133334L243.2 853.333333l-29.866667-29.866666L354.133333 682.666667z m358.4 0l140.8 140.8-29.866666 29.866666-140.8-140.8V810.666667h-42.666667v-170.666667h170.666667v42.666667h-98.133334zM354.133333 384L213.333333 243.2l29.866667-29.866667L384 354.133333V256h42.666667v170.666667H256V384h98.133333z m358.4 0H810.666667v42.666667h-170.666667V256h42.666667v98.133333L823.466667 213.333333l29.866666 29.866667L712.533333 384z" fill="#444444" p-id="4315"></path></svg>';
                console.log("Entered fullscreen");
            }
        });
        // 添加DOM元素到地图中
        map.getContainer().appendChild(div);
        // 将DOM元素返回
        return div;
    }


    const createCenterControl = (yearsType) => {
        const controlButton = document.createElement('div');
        controlButton.style.display = 'flex'
        controlButton.style.alignItems = 'center'
        controlButton.style.marginRight = '10px'
        let icon = ''
        if (yearsType === '1') {
            icon = pin_green
        } else if (yearsType === '2') {
            icon = pin_blue
        } else if (yearsType === '3') {
            icon = pin_orange
        }
        const img = document.createElement('img');
        img.src = icon
        img.style.width = "20px"
        img.style.height = "20px"

        let label = ''
        if (yearsType === '1') {
            label = '0-3Yr'
        } else if (yearsType === '2') {
            label = '3-6Yr'
        } else if (yearsType === '3') {
            label = '6+Yr'
        }
        const pNode = document.createElement('p');
        pNode.textContent = label
        pNode.style.fontSize = "15px"
        pNode.style.marginLeft = '5px'

        controlButton.appendChild(img);
        controlButton.appendChild(pNode);
        controlButton.addEventListener('click', (e) => {
            if (img.style.opacity === '0.5') {
                img.style.opacity = 1
            } else {
                img.style.opacity = 0.5
            }
            showTypeClick(yearsType)
        });
        return controlButton;

    }



    function YearControl () {
        this.defaultAnchor = BMAP_ANCHOR_BOTTOM_RIGHT;
        this.defaultOffset = new BMap.Size(120, 10)
    }


    //通过JavaScript的prototype属性继承于BMap.Control
    YearControl.prototype = new BMap.Control();


    //自定义控件必须实现自己的initialize方法，并且将控件的DOM元素返回
    YearControl.prototype.initialize = function (map) {

        const controlBox = document.createElement('div');
        controlBox.style.boxShadow = "0px 2px 1px -1px rgba(0,0,0,0.2), 0px 1px 1px 0px rgba(0,0,0,0.14), 0px 1px 3px 0px rgba(0,0,0,0.12)"
        controlBox.style.padding = "10px 15px"
        controlBox.style.borderRadius = '5px'
        controlBox.style.background = '#ffffff'
        controlBox.style.marginBottom = '20px'
        const legendBox = document.createElement('div');
        legendBox.style.display = "flex"

        // Create the control.
        const control1 = createCenterControl('1');
        // Append the control to the DIV.
        legendBox.appendChild(control1);

        const control2 = createCenterControl('2');
        legendBox.appendChild(control2);

        const control3 = createCenterControl('3');
        legendBox.appendChild(control3);

        const title1 = document.createElement('div');
        title1.textContent = t('mapDashboard.additional_information')//  'Additional Information'
        title1.style.fontWeight = 600;
        title1.style.fontSize = '18px'

        const title2 = document.createElement('div');
        title2.textContent = t('mapDashboard.digital_signage_usage_duration')
        title2.style.fontWeight = 400;
        title2.style.fontSize = '14px'
        title2.style.marginTop = '10px'

        const title3 = document.createElement('div');
        title3.textContent = t('mapDashboard.markers_number_tip')
        title3.style.fontWeight = 400;
        title3.style.fontSize = '12px'
        title3.style.marginTop = '10px'
        title3.style.marginBottom = '10px'

        controlBox.appendChild(title1);
        controlBox.appendChild(title2);
        controlBox.appendChild(title3);
        controlBox.appendChild(legendBox);
        // 添加DOM元素到地图中
        map.getContainer().appendChild(controlBox);
        // 将DOM元素返回
        return controlBox;
    }

    return <div style={{ width: '100%', height: '70vh', position: 'relative' }} >
        <div style={{ width: '100%', height: '100%' }} id="mapContainer"></div>
    </div>
}

export default BaiDuMap
