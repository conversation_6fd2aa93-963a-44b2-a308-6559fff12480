import { useEffect, useRef, useState } from "react"
import * as echarts from "echarts";
const BarChart = (props) => {
    const chartRef = useRef(null);
    const [myEcharts, setMyEcharts] = useState(null);
    const initChart = () => {
        let chart = echarts.init(chartRef.current, null, { renderer: "svg" });
        setMyEcharts(chart)
        // 设置初始大小
        chart.resize();
        // 监听窗口大小变化，自动调整图表大小
        window.addEventListener("resize", handleResize);
        const options = getOptions(props.MonthTrendData);
        chart.setOption(options);
    }

    const handleResize = () => {
        if (myEcharts) {
            myEcharts.resize();
        }
    };

    useEffect(() => {
        // 在组件挂载时进行初始化
        initChart();
        return () => {
            window.removeEventListener("resize", handleResize);
            if (myEcharts) {
                myEcharts.dispose();
                setMyEcharts(null)
            }
        };
    }, [])

    useEffect(() => {
        if (myEcharts === null) {
            initChart()
        } else {
            const options = getOptions(props.MonthTrendData);
            myEcharts.setOption(options);
        }
    }, [props.MonthTrendData])

    const getOptions = (data) => {
        let yAxis = []
        let seriesData = []
        data.forEach(element => {
            yAxis.push(element.fieldName)
            seriesData.push(element.total)
        });
        return {
            grid: {
                left: '20%',
                top: '0',
                right: '20%',
                bottom: '0',
                containLabel: false//grid 区域是否包含坐标轴的刻度标签。
            },
            xAxis: {
                type: 'value',
                show: false
            },
            yAxis: {
                type: 'category',
                data: yAxis,
                axisTick: {
                    // 轴刻度
                    show: false,
                },
                axisLabel: {
                    // 轴文字
                    show: true,
                    color: "#000000",
                    fontSize: 12,
                },
                axisLine: {
                    // 轴线
                    show: false,
                    color: '#268C8C',
                }
            },
            series: [{
                data: seriesData,
                type: 'bar',
                barWidth: 15,
                itemStyle: {
                    normal: {
                        color: '#7ac143',
                        barBorderRadius: [0, 20, 20, 0], // 左上，右上，右下，左下
                    }
                },
                label: {
                    normal: {
                        show: true,
                        position: 'right'
                    }
                },
            }]
        };
    }
    return <div style={{ width: '200px', height: '100px' }} ref={chartRef}></div>
}

export default BarChart