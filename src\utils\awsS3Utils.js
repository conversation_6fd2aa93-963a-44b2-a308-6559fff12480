
import { S3Client ,ChecksumAlgorithm } from "@aws-sdk/client-s3";
import { Upload } from "@aws-sdk/lib-storage";
export const buildS3Client = (tokenConfig)=> {
  const endpoint =
  window.location.protocol + "//s3-accelerate.amazonaws.com";
  return new S3Client({
    ...tokenConfig,
    endpoint,
    requestChecksumCalculation: "WHEN_REQUIRED",
    responseChecksumValidation:"WHEN_REQUIRED"
  });
}

// 上传文件
export async function uploadFile(s3Client,file, bucketName, key,callback) {
  try {
    const upload = new Upload({
      client: s3Client,
      params: {
        Bucket: bucketName,
        Key: key,
        Body: file, // 直接传入 File 对象
        // ChecksumAlgorithm: ChecksumAlgorithm.CRC32,
      },
      partSize: 5 * 1024 * 1024,
      leavePartsOnError: false, // 是否在出错时保留分片
      // checksumAlgorithm: 'CRC32', // 指定校验和算法
      // checksum: (part) => {
      //   return new Promise((resolve, reject) => {
      //     const hash = createHash('crc32');
      //     hash.update(part);
      //     resolve(hash.digest().toString('hex'));
      //   });
      // }
    });

    upload.on("httpUploadProgress", (progress) => {
      const percentage = Math.round((progress.loaded / progress.total) * 100);
      console.log(`Upload progress: ${percentage}%`);
      if(callback){
        callback(percentage)
      }
    });

    const data = await upload.done();
    console.log("Upload completed successfully", data);
    return data;
  } catch (error) {
    console.error("Error uploading file:", error);
    throw Error(error);
  }
}
