import i18n from "i18next";
const screenStatus = [
  {
    label: i18n.t("common.common_online"),
    value: "1",
    color: "success",
  },
  {
    label: i18n.t("common.common_offline"),
    value: "0",
    color: "error",
  },
];
const reourceType = [
  {
    label: i18n.t("common.common_all"),
    value: "all",
  },
  {
    label: i18n.t("common.common_picture"),
    value: "image",
  },
  {
    label: i18n.t("common.common_video"),
    value: "media",
  },
  {
    label: i18n.t("common.common_layout"),
    value: "layout",
  },
  {
    label: i18n.t("template.mobileTemp"),
    value: "mobile",
  },
];
const fileTypes = [
  {
    value: "media",
    title: i18n.t("common.common_video"),
  },
  {
    value: "image",
    title: i18n.t("common.common_picture"),
  },
  {
    value: "audio",
    title: i18n.t("common.common_audio"),
  },
  {
    value: "layout",
    title: i18n.t("common.common_layout"),
  },
  {
    value: "mobile",
    title: i18n.t("template.mobileTemp"),
  },
];
const screendirections = [
  { label: i18n.t("ips.ips_material_landscape"), value: "0" },
  { label: i18n.t("ips.ips_material_portrait_screen"), value: "1" },
];
const fileResourceStatus = [
  {
    title: i18n.t("dictData.dict_convert"),
    color: "warning",
    value: "0",
  },
  {
    title: i18n.t("dictData.dict_convert_success"),
    color: "success",
    value: "2",
  },
  {
    title: i18n.t("dictData.dict_convert_failed"),
    color: "error",
    value: "4",
  },
  {
    title: i18n.t("dictData.dict_not_complete"),
    color: "error",
    value: "5",
  },
];

const directions = [
  {
    label: i18n.t("ips.ips_material_landscape"),
    value: "0",
  },
  {
    label: i18n.t("ips.ips_material_portrait_screen"),
    value: "1",
  },
];

const playListType = [
  { label: i18n.t("common.common_number_screen_list"), value: "0" },
  { label: i18n.t("common.common_link_screen_list"), value: "1" },
  { label: i18n.t("common.common_play_list_type_layout"), value: "2" },
];

const merchantTypes = [
  { label: i18n.t("common.common_brand"), value: "1" },
  { label: i18n.t("common.common_advertiser"), value: "0" },
];
const scheduleType = [
  { label: i18n.t("ips.ips_digital_signage"), value: "0" },
  { label: i18n.t("ips.ips_play_link_schedule"), value: "1" },
];
const LoginStatus = [
  {
    label: i18n.t("system.system_login_success"),
    value: "0",
    color: "success",
  },
  { label: i18n.t("system.system_login_error"), value: "1", color: "error" },
];
const operLogStatus = [
  {
    label: i18n.t("common.common_op_success"),
    value: "0",
    color: "success",
  },
  { label: i18n.t("common.common_op_fail"), value: "1", color: "error" },
];

const androidVersionType = [
  { label: i18n.t("ips.ips_app_prod_version"), value: "prod" },
  { label: i18n.t("ips.ips_app_dev_version"), value: "dev" },
];

const publishStatus = [
  { label: i18n.t("ips.ips_publish_finish"), value: "0" },
  { label: i18n.t("ips.ips_no_publish"), value: "1" },
  { label: i18n.t("ips.ips_publishing"), value: "2" },
  { label: i18n.t("ips.ips_publish_fail"), value: "3" },
];

const scheduleStatus = [
  { label: i18n.t("ips.ips_downloading"), value: "0" },
  { label: i18n.t("ips.ips_wait_download"), value: "1" },
  { label: i18n.t("ips.ips_download_fail"), value: "2" },
  { label: i18n.t("ips.ips_download_success"), value: "3" },
];

const materialAudit = [
  { label: i18n.t("dictData.dict_pend_audit"), value: "0" },
  { label: i18n.t("dictData.dict_audit_pass"), value: "1" },
];
const screenLogType = [
  { label: "debug", value: "0" },
  { label: "normal", value: "1" },
  { label: "warn", value: "2" },
  { label: "error", value: "3" },
];
const menuType = [
  {
    label: i18n.t("permissions.permission_menu_catalogue"),
    value: "M",
  },
  {
    label: i18n.t("permissions.permission_menu"),
    value: "C",
  },
  {
    label: i18n.t("permissions.permission_menu_btn"),
    value: "F",
  },
  {
    label: i18n.t("permissions.permission_menu_api"),
    value: "D",
  },
];
const menuStatus = [
  {
    label: i18n.t("common.common_enable"),
    value: "0",
    color: "success",
  },
  {
    label: i18n.t("common.common_stop"),
    value: "1",
    color: "error",
  },
];
const accountStatus = [
  {
    label: i18n.t("common.common_enable"),
    value: "0",
    color: "success",
  },
  {
    label: i18n.t("common.common_stop"),
    value: "1",
    color: "error",
  },
];
const deviceTypies = [
  {
    label: "LCD",
    value: "0",
    color: "success",
  },
  {
    label: "LED",
    value: "1",
    color: "error",
  },
];
class Flag {
  static Y = "Y";
  static N = "N";
}
const weeks = [
  {
    label: i18n.t("common.common_monday"),
    value: 1,
  },
  {
    label: i18n.t("common.common_tuesday"),
    value: 2,
  },
  {
    label: i18n.t("common.common_wednesday"),
    value: 3,
  },
  {
    label: i18n.t("common.common_thursday"),
    value: 4,
  },
  {
    label: i18n.t("common.common_friday"),
    value: 5,
  },
  {
    label: i18n.t("common.common_saturday"),
    value: 6,
  },
  {
    label: i18n.t("common.common_sunday"),
    value: 7,
  },
];
const currentScheduleStatus = [
  {
    label: i18n.t("detail.current_schedule_status_not"),
    value: "0",
  },
  {
    label: i18n.t("detail.current_schedule_status_normal"),
    value: "1",
  },
];
const screenLogStatus = [
  {
    label: i18n.t("common.common_normal"),
    value: "1",
    color: "success",
  },
  {
    label: i18n.t("common.common_exception"),
    value: "0",
    color: "error",
  },
];
const subPayStatus = [
  { label: i18n.t("subscription.unpaid"), value: "0" },
  { label: i18n.t("subscription.paid"), value: "1" },
];
const eventlevels = [
  { label: i18n.t("event.eventlevels_5"), value: 5 ,color: "warning",}, // 非常低
  { label: i18n.t("event.eventlevels_4"), value: 4 ,color: "warning"}, // 低
  { label: i18n.t("event.eventlevels_3"), value: 3 ,color: "warning"}, // 中
  { label: i18n.t("event.eventlevels_2"), value: 2 ,color: "error"}, // 高
  { label: i18n.t("event.eventlevels_1"), value: 1 ,color: "error"}, // 严重
];
const adbSwitchTab = [
  {
    label: i18n.t("common.event_enable"),
    value: "1"
  },
  {
    label: i18n.t("common.event_disable"),
    value: "0"
  },
];
export {
  screenStatus,
  screendirections,
  reourceType,
  directions,
  fileTypes,
  fileResourceStatus,
  playListType,
  merchantTypes,
  scheduleType,
  LoginStatus,
  operLogStatus,
  androidVersionType,
  publishStatus,
  scheduleStatus,
  materialAudit,
  screenLogType,
  menuType,
  menuStatus,
  accountStatus,
  Flag,
  deviceTypies,
  weeks,
  currentScheduleStatus,
  eventlevels,
  screenLogStatus,
  subPayStatus,
  adbSwitchTab,
};
