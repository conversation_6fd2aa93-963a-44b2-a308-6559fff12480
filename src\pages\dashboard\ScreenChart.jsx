import { useEffect, useRef, useState } from "react";
import * as echarts from "echarts";
import { t } from "i18next";
const ScreenChart = (props) => {
  const chartRef = useRef();
  const { data } = props;
  // const [data, setData] = useState({});
  // const handleListScreenStatistics = () => {
  // let x = [];
  // let y1 = [];
  // let y2 = [];
  // let y3 = [];
  // listScreenStatistics().then((res) => {
  //   res.data.forEach((item) => {
  //     x.push(item.statisticsDay.slice(5));
  //     y1.push(item.onlineNum);
  //     y2.push(item.offlineNum);
  //     y3.push(item.totalNum);
  //   });
  //   setData({ x: x, y1: y1, y2: y2, y3: y3 });
  // });
  // };

  // useEffect(() => {
  //   handleListScreenStatistics();
  // }, []);

  useEffect(() => {
    let myEcharts = null;
    const initChart = () => {
      // 初始化Echarts图表
      myEcharts = echarts.init(chartRef.current, null, { renderer: "svg" });

      // 设置初始大小
      myEcharts.resize();

      // 监听窗口大小变化，自动调整图表大小
      window.addEventListener("resize", handleResize);
      const options = getOptions(data);
      myEcharts.setOption(options);
    };

    const handleResize = () => {
      myEcharts.resize();
    };

    // 在组件挂载时进行初始化
    initChart();

    // 在组件卸载时移除事件监听
    return () => {
      window.removeEventListener("resize", handleResize);
      myEcharts.dispose();
    };
  }, [data]);

  const getOptions = (data) => {
    let option = {
      legend: {
        // bottom: '13%',
        bottom: "5%",
        left: "center",
        // selectedMode: false
      },
      tooltip: {
        trigger: "axis",
        //自定义提示框
        // formatter: function (params) {
        //     console.log(params);
        // }
      },
      xAxis: {
        data: data.x,
        type: "category",
        boundaryGap: false,
      },
      yAxis: {
        // name: '台',
        type: "value",
      },
      grid: { left: "6%", right: "6%", top: "5%", containLabel: true },
      series: [
        {
          name: t("ips.ips_online_number"),
          type: "line",
          data: data.y1,
          smooth: true,
          lineStyle: {
            color: "#7AC143",
            width: 2,
          },
          itemStyle: {
            color: "#7AC143",
            borderColor: "#7AC143",
          },
        },
        {
          name: t("ips.ips_offline_number"),
          type: "line",
          data: data.y2,
          smooth: true,
          lineStyle: {
            color: "#F12435",
            width: 2,
          },
          itemStyle: {
            color: "#F12435",
            borderColor: "#F12435",
          },
        },
        {
          name: t("ips.ips_total_number"),
          type: "line",
          data: data.y3,
          smooth: true,
          lineStyle: {
            color: "#C8C9E9",
            width: 2,
          },
          itemStyle: {
            color: "#C8C9E9",
            borderColor: "#C8C9E9",
          },
        },
      ],
    };
    return option;
  };
  return <div ref={chartRef} style={{ width: "100%", height: "350px" }}></div>;
};
export default ScreenChart;
