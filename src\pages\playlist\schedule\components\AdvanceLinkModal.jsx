/* eslint-disable no-undef */
import React, { useRef, useState, useEffect, useMemo, forwardRef } from "react";
import LoadingButton from "@mui/lab/LoadingButton";
import {
  Stack,
  Alert,
  Grid,
  Checkbox,
  InputBase,
  Button,
  Popover,
  IconButton,
  RadioGroup,
  InputAdornment,
  OutlinedInput,
  FormControlLabel,
  FormGroup,
  TextField,
  Paper,
  Radio,
  Typography,
  InputLabel,
} from "@mui/material";
import DateSvg from "./DateSvg";
import { toast } from "react-toastify";
import TimePickert from "@/components/zktime";
import dayjs from "dayjs";
import isBetween from "dayjs/plugin/isBetween";
import ClearIcon from "@mui/icons-material/Clear";
import { tableI18n } from "@/utils/tableLang";
import AddPlayList from "./AddPlayList";
import { useConfirm } from "@/components/zkconfirm";
import { useForm, Controller, useFieldArray } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import AddCircleIcon from "@mui/icons-material/AddCircle";
import * as Yup from "yup";
import { useFormik } from "formik";
import AddLinkPlayList from "./AddLinkPlayList";
import {
  BootstrapDialog,
  BootstrapDialogTitle,
  BootstrapContent,
  BootstrapActions,
} from "@/components/dialog";
import { useTranslation } from "react-i18next";
dayjs.extend(isBetween);
const useStyles = makeStyles({
  hidden: { display: "none" },
  show: {},
  big: { fontSize: "3rem" },
});
import { makeStyles } from "@material-ui/core";
const AdvancedDialog = forwardRef((props, ref) => {
  const [screenNumber, setScreenNumber] = useState(1);
  const confirm = useConfirm();
  const classes = useStyles();
  const { t } = useTranslation();
  const {
    open,
    onClose,
    inventoryObject,
    tableObject,
    onImport,
    advancedData,
  } = props;
  const [copyOpen, setCopyOpen] = useState(false);
  //设备的所在元素的宽度
  const [xsValue, setXsValue] = useState(8);
  const addLinkPlayListRef = useRef(null);
  // 总数
  const [loading, setLoading] = useState(false);
  const handleClose = () => {
    onClose();
  };
  const [copyList, setCopyList] = useState([]);

  //屏幕组清单对象
  const screenGroupPlayListObject = {
    startTime: "",
    checkBox: false,
    stopTime: "",
    areaScreenPlayList: [],
  };

  // 新表单组件
  const { control, handleSubmit, setValue, register, getValues, watch, reset } =
    useForm({
      mode: "all",
    });

  //表单
  const scheduleForm = useFormik({
    initialValues: {
      name: "",
      playDirection: "0",
      startDate: "",
      stopDate: "",
      screenGroupIds: "",
      //集合内部的集合对象对应为区域设备对象
      screenGroupPlayList: [
        {
          startTime: "",
          stopTime: "",
          areaScreenPlayList: [
            { playListId: "", playListName: "" },
            { playListId: "", playListName: "" },
            { playListId: "", playListName: "" },
            { playListId: "", playListName: "" },
          ],
        },
      ],
    },
    onSubmit: async (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        // handelTableSubmit(values);
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
    validationSchema: Yup.object().shape({
      name: Yup.string().required(t("common.common_input_scheduling_name")),
      startDate: Yup.date().required(t("common.common_select_dateInterval")),
      stopDate: Yup.date().required(t("common.common_select_dateInterval")),
    }),
  });

  //清单对象
  // 清单列表，用于动态创建渲染
  const { fields, append, remove } = useFieldArray({
    control,
    name: "linkPlayLists",
  });
  useEffect(() => {
    handeOpenChange();
  }, [advancedData]);
  const handeOpenChange = () => {
    reset({ linkPlayLists: [] });
    const tempOpject = [];
    advancedData.forEach((item) => {
      if (item?.startTime && item.stopTime) {
        tempOpject.push(item);
        return;
      }
      for (let i = 0; i < item?.areaScreenPlayList.length; i++) {
        if (
          item?.areaScreenPlayList[0].playListId &&
          item?.areaScreenPlayList[0].playListName
        ) {
          tempOpject.push(item);
          return;
        }
      }
    });
    let oneItem = tableObject[0];
    if (tempOpject?.length > 0) {
      // remove all
      tempOpject.forEach((data) => {
        append(data);
      });
    } else {
      let tempScreenNumber =
        oneItem?.original?.line * oneItem?.original?.columns;
      let newAreaScreenObject = [];
      let newnewscreenGroupPlayLists = [];
      setScreenNumber(tempScreenNumber);
      for (let i = 0; i < tempScreenNumber; i++) {
        let newInventoryObject = { ...inventoryObject };
        newAreaScreenObject.push(newInventoryObject);
      }
      let newscreenGroupPlayListObject = {};
      newscreenGroupPlayListObject = {
        startTime: "",
        stopTime: "",
        checkBox: false,
        areaScreenPlayList: newAreaScreenObject,
      };

      newnewscreenGroupPlayLists.push(newscreenGroupPlayListObject);

      newnewscreenGroupPlayLists.forEach((data) => {
        append(data);
      });
    }
    handleCalculateXS(
      parseInt(oneItem.original.columns),
      parseInt(oneItem.original.line)
    );
  };
  // 重置表单
  const handelReset = () => {
    reset([]);
  };
  // 暴露给父组件
  React.useImperativeHandle(ref, () => ({
    handeOpenChange,
    handelReset,
  }));

  //计算出每个设备占的宽度
  const handleCalculateXS = (cloumnNum, lineNum) => {
    const xs = 12 / cloumnNum;
    setXsValue(xs);
  };

  const handleCheckboxChange = (index, e) => {
    const { checked } = e.target;
    const fieldName = `linkPlayLists[${index}].checkBox`;
    setValue(fieldName, checked);
    const allChecked = watchedChecked.every((item) => item?.checkBox);
    console.log("allChecked", getValues());
    setSelectAll(allChecked);
  };

  const watchedChecked = watch("linkPlayLists", []);
  const [selectAll, setSelectAll] = useState(
    watchedChecked.length > 0 && watchedChecked.every((item) => item?.checkBox)
  );
  const handleSelectAll = (e) => {
    const valueData = getValues().linkPlayLists;
    let data = valueData.map((item) => {
      item.checkBox = e.target.checked;
      return item;
    });
    setSelectAll(e.target.checked);
    console.log(data);
    reset({ linkPlayLists: data });
  };
  // 动态创建联屏form表单
  const CreateLinkScreenFormField = (props) => {
    const { setValue } = props;
    const selectPlayListObject = (screenIndex, playListIndex) => {
      addLinkPlayListRef.current.handleOpen();
      addLinkPlayListRef.current.setPlayListIndex(playListIndex);
      addLinkPlayListRef.current.setScreenIndex(screenIndex);
    };
    // 设置更新动态表单值
    const setDynameFormFiledValue = (
      playListId,
      playListName,
      playListIndex,
      screenIndex
    ) => {
      const updatedValue = [
        ...watch(`linkPlayLists[${screenIndex}].areaScreenPlayList`),
      ];
      updatedValue[playListIndex] = {
        playListId: playListId,
        playListName: playListName,
      };
      setValue(
        `linkPlayLists[${screenIndex}].areaScreenPlayList`,
        updatedValue
      );
    };
    // 时间选择组件相关
    const [anchorTime, setAnchorTime] = useState(null);
    const openTimeRange = Boolean(anchorTime);
    // 所选输入框的下标
    const [currentIndex, setCurrentIndex] = useState(null);
    const timeRangeId = openTimeRange ? "time-popover" : undefined;
    const handleTimeClose = () => {
      setAnchorTime(null);
    };
    const handleTimeClick = (event, index) => {
      setAnchorTime(event.currentTarget);
      setCurrentIndex(index);
    };

    return (
      <React.Fragment key={"ADVANCEl"}>
        <AddLinkPlayList
          ref={addLinkPlayListRef}
          setPlayListFormValues={setDynameFormFiledValue}
          playListType={"1"}
        />
        <Popover
          id={timeRangeId}
          open={openTimeRange}
          anchorEl={anchorTime}
          onClose={handleTimeClose}
          anchorOrigin={{
            vertical: "bottom",
            horizontal: "left",
          }}>
          <TimePickert
            endTimeText={t("common.common_endTime")}
            startTimeText={t("common.common_startTime")}
            confirmText={t("common.common_edit_ok")}
            onChange={(time) => {
              //时间段是否存在重叠标识
              let flag = false;
              //已选播放清单
              // let timePeriodList = [...scheduleForm.values.screenGroupPlayList];
              let timePeriodList = [...getValues().linkPlayLists];
              console.log(timePeriodList);
              //开始时间
              let startTime = new Date("2022-06-01T" + time.startTime);
              //结束时间
              let endTime = new Date("2022-06-01T" + time.endTime);

              if (startTime.getTime() >= endTime.getTime()) {
                toast.error(t("ips.ips_starttime_greater_endtime"));
                return;
              }

              timePeriodList.forEach((timePeriod, index) => {
                //排除自身与其它时间段比较
                if (currentIndex != index) {
                  //排除未选择的播放时间段
                  if (timePeriod.startTime != "" && timePeriod.stopTime != "") {
                    //该时间段开始时间
                    let thisStartTime = new Date(
                      "2022-06-01T" + timePeriod.startTime
                    );
                    //该时间段结束时间
                    let thisStopTime = new Date(
                      "2022-06-01T" + timePeriod.stopTime
                    );
                    //交集判断结果
                    let result =
                      (startTime.getTime() < thisStartTime.getTime() &&
                        thisStartTime.getTime() < endTime.getTime()) ||
                      (startTime.getTime() < thisStopTime.getTime() &&
                        thisStopTime.getTime() < endTime.getTime()) ||
                      (startTime.getTime() >= thisStartTime.getTime() &&
                        endTime.getTime() <= thisStopTime.getTime()) ||
                      (startTime.getTime() <= thisStartTime.getTime() &&
                        endTime.getTime() >= thisStopTime.getTime());

                    if (result) {
                      //时间段存在交集
                      flag = true;
                      return;
                    }
                  }
                }
              });

              if (flag) {
                toast.error(t("ips.ips_overlap_play_time"));
                return;
              } else {
                console.log("AAA", time.startTime);
                // 表单赋值
                // linkPlayLists[${screenIndex}]
                setValue(
                  `linkPlayLists[${currentIndex}].startTime`,
                  time.startTime
                );
                setValue(
                  `linkPlayLists[${currentIndex}].stopTime`,
                  time.endTime
                );
                setCurrentIndex(null);
                handleTimeClose();
              }
            }}
          />
        </Popover>
        {fields.map((field, index) => {
          return (
            <React.Fragment key={index}>
              <Grid container spacing={1} key={field}>
                <Grid item xs={6}>
                  <Stack spacing={1}>
                    <InputLabel htmlFor="listrangeTime">
                      <Stack direction="row" spacing={0.1} alignItems="center">
                        <Controller
                          name={`linkPlayLists[${index}].checkBox`}
                          control={control}
                          defaultValue={field.checkBox}
                          render={({ field }) => {
                            return (
                              <Checkbox
                                checked={field?.value || false}
                                onClick={(e) => handleCheckboxChange(index, e)}
                                {...field}
                              />
                            );
                          }}
                        />
                        <Typography>
                          {t("ips.ips_playlist_play_time")}
                        </Typography>
                        <i style={{ color: "red" }}>*</i>
                      </Stack>
                    </InputLabel>
                    <Controller
                      name={`linkPlayLists[${index}].stopTime`}
                      control={control}
                      defaultValue={field.stopTime}
                      render={({ field }) => (
                        <OutlinedInput
                          id="stopTime"
                          {...field}
                          // name={``}
                          type="text"
                          onClick={(event) => {
                            handleTimeClick(event, index);
                          }}
                          placeholder={t("common.common_endTime")}
                          startAdornment={
                            <InputAdornment
                              position="start"
                              sx={{
                                width: "100%",
                              }}>
                              <Controller
                                name={`linkPlayLists[${index}].startTime`}
                                control={control}
                                defaultValue={field.startTime}
                                render={({ field }) => (
                                  <InputBase
                                    {...field}
                                    endAdornment={<DateSvg />}
                                    onClick={(event) => {
                                      handleTimeClick(event, index);
                                    }}
                                    type="text"
                                    placeholder={t("common.common_startTime")}
                                    sx={{
                                      width: "100%",
                                    }}
                                  />
                                )}
                              />
                            </InputAdornment>
                          }
                        />
                      )}
                    />
                  </Stack>
                </Grid>
              </Grid>
              <Grid
                container
                spacing={1}
                justifyContent="flex-start"
                sx={{
                  marginTop: "10px",
                  width: "100%",
                  maxHeight: "270px",
                  padding: "20px 0px 30px 0px",
                  backgroundColor: "#f9f9f9",
                  overflow: "auto",
                  marginBottom: "10px",
                }}>
                {field.areaScreenPlayList.map((item, playListIndex) => {
                  return (
                    <React.Fragment key={playListIndex}>
                      <Grid
                        item
                        container={
                          tableObject[0].original.columns > 4 ? true : false
                        }
                        xs={xsValue}
                        key={playListIndex}
                        sx={{ paddingRight: "8px" }}
                        align="center">
                        <Paper
                          className={"box"}
                          elevation={0}
                          style={{
                            border: "1px solid rgba(120, 189, 66,0.7)",
                            height: "100px",
                            width: "200px",
                            boxShadow: "0px 5px 10px 2px rgba(0, 0, 0, 0.1)",
                          }}>
                          {watch(
                            `linkPlayLists[${index}].areaScreenPlayList[${playListIndex}].playListName`
                          ) !== "" && (
                            <>
                              <div className="ovrly"></div>
                              <div className="btn">
                                <Button
                                  className="bt1"
                                  onClick={() => {
                                    setDynameFormFiledValue(
                                      "",
                                      "",
                                      playListIndex,
                                      index
                                    );
                                  }}
                                  style={{
                                    fontSize: "14px",
                                  }}>
                                  {t("ips.ips_reset")}
                                </Button>
                              </div>
                            </>
                          )}
                          <Stack
                            spacing={1}
                            sx={{
                              height: "100%",
                              width: "100%",
                            }}
                            justifyContent="center"
                            alignItems="center">
                            {watch(
                              `linkPlayLists[${index}].areaScreenPlayList[${playListIndex}].playListName`
                            ) == "" ? (
                              <>
                                <IconButton
                                  color="primary"
                                  aria-label="add playList"
                                  className={`
                                                                    ${
                                                                      watch(
                                                                        `linkPlayLists[${index}].areaScreenPlayList[${playListIndex}].playListName`
                                                                      ) == ""
                                                                        ? classes.show
                                                                        : classes.hidden
                                                                    }`}
                                  variant="contained"
                                  onClick={() => {
                                    selectPlayListObject(index, playListIndex);
                                  }}>
                                  <AddCircleIcon className={classes.big} />
                                </IconButton>
                                <Typography
                                  sx={{
                                    color: "rgba(0, 0, 0,.3)",
                                    lineHeight: "22px",
                                    fontSize: "14px",
                                  }}>
                                  {t("ips.ips_add_playlist")}
                                  <span
                                    style={{
                                      color: "red",
                                    }}>
                                    *
                                  </span>
                                </Typography>
                              </>
                            ) : (
                              <Typography
                                sx={{
                                  color: "rgba(0, 0, 0, 0.3)",
                                  wordBreak: "break-all",
                                  padding: "5px 10px 5px 10px",
                                }}>
                                {watch(
                                  `linkPlayLists[${index}].areaScreenPlayList[${playListIndex}].playListName`
                                )}
                              </Typography>
                            )}
                          </Stack>
                        </Paper>
                      </Grid>
                    </React.Fragment>
                  );
                })}
              </Grid>
            </React.Fragment>
          );
        })}
      </React.Fragment>
    );
  };
  // 添加新的一行
  const handleAddRow = () => {
    const newList = [];
    //对象中的areaScreenPlayList集合中对象的个数对应屏幕组中的设备数量
    let newAreaScreenObject = [];
    for (let i = 0; i < screenNumber; i++) {
      console.log(inventoryObject);
      let newInventoryObject = {
        ...inventoryObject,
      };
      newAreaScreenObject.push(newInventoryObject);
    }
    let newscreenGroupPlayListObject = {
      ...screenGroupPlayListObject,
      checkBox: true,
    };
    newscreenGroupPlayListObject.areaScreenPlayList = newAreaScreenObject;
    console.log(newscreenGroupPlayListObject);
    newList.push(newscreenGroupPlayListObject);
    append(newList);
  };
  const handleCopyRow = () => {
    const linkPlayLiss = getValues()?.linkPlayLists;
    //查询是否有勾选
    let checkBoxs = linkPlayLiss.filter((item) => item?.checkBox === true);
    if (checkBoxs.length === 0) {
      toast.error(t("common.common_please_select_row_copy"));
      return;
    }
    //校验时间
    let flag = false;
    checkBoxs.forEach((item) => {
      if (!item?.stopTime || !item?.startTime) {
        flag = true;
        return;
      }
      item?.areaScreenPlayList.forEach((areaPlayList) => {
        if (!areaPlayList?.playListId || !areaPlayList?.playListName) {
          flag = true;
        }
      });
    });
    if (flag) {
      toast.error(t("common.common_copy_row_null_playlist"));
      return;
    }
    setCopyList(checkBoxs);
    setCopyOpen(true);
    console.log(getValues());
  };
  const handleCopySubmit = (value) => {
    const data = [];
    let copyStartTime = dayjs(
      "2022-06-01T" + copyList[copyList.length - 1]?.stopTime,
      "YYYY-MM-DDTHH:mm:ss"
    );
    copyList.forEach((item) => {
      data.push({
        checkBox: true,
        startTime: copyStartTime.format("HH:mm:ss"),
        stopTime: copyStartTime.add(value, "minute").format("HH:mm:ss"),
        areaScreenPlayList: item.areaScreenPlayList,
      });
      copyStartTime = copyStartTime.add(value, "minute");
    });
    let formData = getValues()?.linkPlayLists;
    const temps = [...formData, ...data];
    setValue("linkPlayLists", temps);
  };

  // // 判断两个时间段是否有重叠
  // function isTimeOverlap(time1, time2) {
  //     const start1 = new Date(`2000-01-01T${time1.startTime}`);
  //     const end1 = new Date(`2000-01-01T${time1.stopTime}`);
  //     const start2 = new Date(`2000-01-01T${time2.startTime}`);
  //     const end2 = new Date(`2000-01-01T${time2.stopTime}`);

  //     // 如果有跨越一天的时间段，则需要进行特殊处理
  //     if (start1 > end1) {
  //         end1.setDate(end1.getDate() + 1);
  //     }

  //     if (start2 > end2) {
  //         end2.setDate(end2.getDate() + 1);
  //     }

  //     if (start1 < end2 && end1 > start2) {
  //         return true; // 存在重叠时间段
  //     }

  //     return false; // 不存在重叠时间段
  // }

  // // 校验时间列表中是否存在重叠时间段
  // function hasOverlapTime(times) {
  //     for (let i = 0; i < times.length - 1; i++) {
  //         const time1 = times[i];

  //         for (let j = i + 1; j < times.length; j++) {
  //             const time2 = times[j];

  //             if (isTimeOverlap(time1, time2)) {
  //                 return true; // 存在重叠时间段
  //             }
  //         }
  //     }

  //     return false; // 不存在重叠时间段
  // }

  function isOverlap(startTime1, stopTime1, startTime2, stopTime2) {
    const start1 = Date.parse(`2000-01-01T${startTime1}Z`);
    let stop1 = Date.parse(`2000-01-01T${stopTime1}Z`);
    const start2 = Date.parse(`2000-01-01T${startTime2}Z`);
    let stop2 = Date.parse(`2000-01-01T${stopTime2}Z`);

    // 处理第一个时间段结束时间在第二天的情况
    if (stop1 < start1) stop1 += 24 * 60 * 60 * 1000;
    // 处理第二个时间段结束时间在第二天的情况
    if (stop2 < start2) stop2 += 24 * 60 * 60 * 1000;

    return (
      Math.max(start1, start2) < Math.min(stop1, stop2) ||
      Math.max(start1, start2 - 24 * 60 * 60 * 1000) <
        Math.min(stop1, stop2 - 24 * 60 * 60 * 1000)
    );
  }

  function hasOverlapTime(times) {
    for (let i = 0; i < times.length - 1; i++) {
      const time1 = times[i];

      for (let j = i + 1; j < times.length; j++) {
        const time2 = times[j];

        if (
          isOverlap(
            time1.startTime,
            time1.stopTime,
            time2.startTime,
            time2.stopTime
          )
        ) {
          return true; // 存在重叠时间段
        }
      }
    }

    return false; // 不存在重叠时间段
  }
  const onSubmit = async (data, event) => {
    if (data?.linkPlayLists.length === 0) {
      toast.error(t("common.common_copy_erro_null"));
      return;
    }
    //校验时间
    let flag = false;
    data?.linkPlayLists.forEach((item) => {
      if (!item?.stopTime || !item?.startTime) {
        flag = true;
        return;
      }
      item?.areaScreenPlayList.forEach((areaPlayList) => {
        if (!areaPlayList?.playListId || !areaPlayList?.playListName) {
          flag = true;
        }
      });
    });
    if (flag) {
      toast.error(t("common.common_copy_row_null_playlist"));
      return;
    }
    console.log(JSON.stringify(data?.linkPlayLists));
    if (hasOverlapTime(data?.linkPlayLists)) {
      toast.error(t("ips.ips_overlap_play_time"));
      return;
    }
    confirm({
      title: t("message.messageBox_title"),
      confirmationText: t("common.common_edit_ok"),
      cancellationText: t("common.common_edit_cancel"),
      description: t("common.common_copy_playlist_tips"),
    }).then(() => {
      setLoading(true);
      //导入到表单中
      onImport(data?.linkPlayLists)
        .then(() => {
          setLoading(false);
          onClose();
        })
        .catch((err) => {
          setLoading(false);
        });
    });
  };

  const handleDelRow = () => {
    const valueData = getValues().linkPlayLists;
    const flag = valueData.some((item) => item?.checkBox);
    if (!flag) {
      toast.error(t("common.common_screen_copy_del_error"));
      return;
    }
    // valueData.forEach((item, index) => {
    //     if (item.checkBox) {
    //         remove(index);
    //         return;
    //     }
    // })
    for (let i = getValues().linkPlayLists.length - 1; i >= 0; i--) {
      if (getValues().linkPlayLists[i].checkBox) {
        remove(i);
      }
    }
  };
  return (
    <>
      <BootstrapDialog
        BackdropClick={false}
        fullWidth
        maxWidth="lg"
        onClose={handleClose}
        aria-labelledby="customized-dialog-title"
        open={open}>
        <BootstrapDialogTitle
          onClose={handleClose}
          dividers
          style={{
            position: "sticky",
            top: 0,
            background: "white",
            zIndex: "1000",
          }}>
          <Typography variant="h4" component="p">
            {t("common.common_advanced_menu_title")}
          </Typography>
        </BootstrapDialogTitle>
        <form noValidate onSubmit={handleSubmit(onSubmit)}>
          <BootstrapContent
            dividers
            style={{ minHeight: "30vh", overflowY: "auto " }}>
            <Grid container>
              <Grid item xs={12} sx={{ padding: "10px 30px 10px 30px" }}>
                <Stack direction="row" spacing={1} alignItems="center">
                  <Button variant="contained" onClick={handleCopyRow}>
                    {t("common.common_copy_row")}
                  </Button>
                  <Button variant="contained" onClick={handleAddRow}>
                    {t("common.common_please_add_new_row")}
                  </Button>
                  <Button
                    variant="contained"
                    color="info"
                    onClick={handleDelRow}>
                    {t("common.common_op_batch_del")}
                  </Button>
                </Stack>
              </Grid>
              <Grid item xs={12} sx={{ paddingLeft: "30px" }}>
                <FormControlLabel
                  control={<Checkbox value={selectAll} checked={selectAll} />}
                  label={t("ips.ips_all")}
                  onChange={handleSelectAll}
                />
              </Grid>
              <Grid item xs={12} sx={{ padding: "10px 30px 10px 30px" }}>
                <CreateLinkScreenFormField setValue={setValue} />
              </Grid>
            </Grid>
          </BootstrapContent>

          <BootstrapActions
            style={{
              position: "sticky",
              bottom: "0",
              background: "white",
              zIndex: "1000",
            }}>
            <Button color="info" variant="outlined" onClick={handleClose}>
              {t("common.common_edit_cancel")}
            </Button>
            <LoadingButton
              disableElevation
              loading={loading}
              type="submit"
              variant="contained"
              color="primary">
              {t("common.common_edit_ok")}
            </LoadingButton>
          </BootstrapActions>
        </form>
      </BootstrapDialog>
      <CopyPlayListTimeRange
        open={copyOpen}
        onClose={() => {
          setCopyList([]);
          setCopyOpen(false);
        }}
        onSubmit={handleCopySubmit}
      />
    </>
  );
});

export const CopyPlayListTimeRange = ({ open, onClose, onSubmit }) => {
  const [value, setValue] = React.useState("15");
  const [customerValue, setCustomerValue] = React.useState("");
  const { t } = useTranslation();
  const handleSubmit = () => {
    let duration = 0;
    if (value === "customer") {
      if (!customerValue) {
        toast.error(t("common.common_input_interval_time"));
        return;
      }
      duration = Number(customerValue);
    } else {
      duration = Number(value);
    }

    onSubmit(duration);
    onClose();
  };

  const handleChange = (event) => {
    setValue(event.target.value);
  };

  return (
    <BootstrapDialog open={open} width="xs" fullWidth>
      <BootstrapDialogTitle textAlign="center">
        <Typography variant="h5" component="p">
          {t("common.common_copy_input_info")}
        </Typography>
      </BootstrapDialogTitle>
      <BootstrapContent>
        <Stack spacing={2}>
          <Alert severity="warning">{t("common.common_copy_tips_info")}</Alert>
          <form onSubmit={(e) => e.preventDefault()}>
            <Stack spacing={1}>
              <InputLabel htmlFor="infor-dateRange">
                {t("common.common_interval_time")}
                <i style={{ color: "red" }}>*</i>
              </InputLabel>
              <RadioGroup
                row
                value={value}
                onChange={handleChange}
                aria-labelledby="demo-controlled-radio-buttons-group"
                name="controlled-radio-buttons-group">
                <FormControlLabel
                  value="15"
                  control={<Radio />}
                  label={t("common.common_interval_time_15_minute")}
                />
                <FormControlLabel
                  value="30"
                  control={<Radio />}
                  label={t("common.common_interval_time_30_minute")}
                />
                <FormControlLabel
                  value="60"
                  control={<Radio />}
                  label={t("common.common_interval_time_60_minute")}
                />
                <FormControlLabel
                  value="customer"
                  control={<Radio />}
                  label={t("common.common_interval_time_customer_minute")}
                />
              </RadioGroup>
            </Stack>
            {value === "customer" && (
              <Stack spacing={2}>
                <InputLabel htmlFor="infor-dateRange">
                  {t("common.common_duration_minute")}
                  <i style={{ color: "red" }}>*</i>
                </InputLabel>
                <OutlinedInput
                  value={customerValue}
                  onChange={(e) => setCustomerValue(e.target.value)}
                  sx={{ width: "300px" }}
                  type="number"
                />
              </Stack>
            )}
          </form>
        </Stack>
      </BootstrapContent>
      <BootstrapActions sx={{ p: "1.25rem" }}>
        <Button onClick={onClose} variant="outlined" color="info">
          {t("common.common_edit_cancel")}
        </Button>
        <Button color="primary" onClick={handleSubmit} variant="contained">
          {t("common.common_copy_ok")}
        </Button>
      </BootstrapActions>
    </BootstrapDialog>
  );
};

export default AdvancedDialog;
