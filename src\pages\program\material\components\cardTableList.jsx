import React from 'react'
/* eslint-disable react-hooks/rules-of-hooks */

import MediaCard from "@/components/mediaCard";
import { Grid, Box } from "@mui/material";
import TablePagination from "@mui/material/TablePagination";
import MainCard from "@/components/MainCard";
export default function cardTableList() {
  const [page, setPage] = React.useState(2);
  const [rowsPerPage, setRowsPerPage] = React.useState(10);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  return (
    <MainCard border={false}>
      <Box sx={{ widht: "100%", height: "100%" }}>
        <Grid
          container
          justifyContent="flex-start"
          alignItems="flex-start"
          spacing={2}
        >
          {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map((index) => {
            return (
              <Grid key={index} item xs={6} sm={4} md={4} lg={2}>
                <MediaCard />
              </Grid>
            );
          })}
        </Grid>
        <TablePagination
          sx={{ bottom: "5px", right: "10px" }}
          component="div"
          count={100}
          page={page}
          onPageChange={handleChangePage}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage={<p>{message("editor_rowsPerPage")}:</p>}
        />
      </Box>
    </MainCard>
  );
}
