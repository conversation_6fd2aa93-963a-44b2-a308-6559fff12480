import React from 'react'
/* eslint-disable react-hooks/rules-of-hooks */

import { Link, Box, Stack } from "@mui/material";
import ImgSrc from "@/assets/images/error/ic_404.png";
import { useNavigate } from "react-router-dom";
export default function noPage() {
  const navigate = useNavigate();
  return (
    <Stack
      direction="column"
      justifyContent="center"
      alignItems="center"
      spacing={10}
      sx={{ background: "white" }}
      style={{ width: "100%", height: "100vh" }}
    >
      <Box>
        <img src={ImgSrc} />
      </Box>
      <Box
        onClick={() => {
          navigate(-1);
        }}
        sx={{ fontSize: "20px", cursor: "pointer" }}
      >
        返回主页
      </Box>
    </Stack>
  );
}
