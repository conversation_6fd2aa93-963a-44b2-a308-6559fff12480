import { Grid } from "@mui/material";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
const Visitors = (props) => {
  const { VisitorCount = 0 } = props;
  const { t } = useTranslation();
  const [defaultNumber, setDefaultNumber] = useState("0000000");

  const getNumberOfDigits = (num) => {
    return num.toString().length;
  };

  useEffect(() => {
    const numberOfDigits = getNumberOfDigits(VisitorCount);
    const numberOfZeros = 7 - numberOfDigits;
    const zeros = "0".repeat(numberOfZeros);
    setDefaultNumber(zeros);
  }, [VisitorCount]);

  return (
    <Grid>
      <Grid
        sx={{
          textAlign: "right",
          font: "normal normal bold 20px/24px Roboto",
          letterSpacing: "0px",
          color: "#474B4F",
        }}
      >
        {t('RealTime.RealTimeVisitors') }
      </Grid>
      <Grid
        sx={{
          display: "flex",
          flexDirection: "row",
          justifyContent: "flex-end",
          alignItems: "center",
        }}
      >
        <Grid sx={{
          font:'normal normal bold 50px/61px Roboto',
          letterSpacing:'2px',
          color: '#D5D8DB',
          fontSize:'50px',
          lineHeight:'61px',
          textAlign:'right',
        }}>{defaultNumber}</Grid>
        <Grid sx={{
          font:'normal normal bold 50px/61px Roboto',
          letterSpacing:'2px',
          color: '#474B4F',
          fontSize:'50px',
          lineHeight:'61px',
          textAlign:'right',
        }}>{VisitorCount?VisitorCount:''}</Grid>
      </Grid>
    </Grid>
  );
};

export default Visitors;
