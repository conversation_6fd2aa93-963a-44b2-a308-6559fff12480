import React from 'react'
/* eslint-disable react/prop-types */
import  {
  forwardRef,
  useRef,
  useState,
  useImperativeHandle,
} from "react";
import {
  Stack,
  Button,
  Typography,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
// i18n
import { useTranslation } from "react-i18next";
import AdvertiserTable from "./advertiserTable";
import AddMerchant from "./addMerchant";
import {
  BootstrapDialog,
  BootstrapDialogTitle,
  BootstrapContent,
  BootstrapActions,
} from "@/components/dialog";
import AuthButton from "@/components/AuthButton";
const AdvertiserSelect = forwardRef((props, ref) => {
  const { type, setFormValues = () => { } } = props;
  const { t } = useTranslation();
  const addMerchantRef = useRef(null);
  const advertiserTableRef = useRef(null);
  // 广告商选择弹窗
  const [open, setOpen] = React.useState(false);

  useImperativeHandle(ref, () => ({
    handleClose,
    handleOpen,
  }));

  const handleAdvertiser = (id, name) => {
    setFormValues(id, name);
  };
  //表单选择对象
  const [tableObject, setTableObject] = useState([]);

  //打开新增广告商
  const handleOpenAddMerchant = () => {
    addMerchantRef.current.handleOpen();
  };
  //重新加载广告商列表
  const reloadMerchantList = () => {
    advertiserTableRef.current.getTableData();
  };
  const handleClose = () => {
    setOpen(false);
  };
  const handleOpen = () => {
    setOpen(true);
  };

  return (
    <>
      <BootstrapDialog
        open={open}
        fullWidth
        onClose={handleClose}
        aria-describedby="alert-dialog-slide-description"
      >
        <BootstrapDialogTitle onClose={handleClose}>
          <Typography variant="h4" component="p">
            {type === "0" && t("common.common_select_advertiser")}
            {type === "1" && t("common.common_select_retail")}
          </Typography>
        </BootstrapDialogTitle>
        <BootstrapContent dividers>
          <AdvertiserTable
            ref={advertiserTableRef}
            type={type}
            setTableObject={setTableObject}
          />
        </BootstrapContent>
        <Stack
          direction="row"
          justifyContent="flex-end"
          sx={{ margin: "15px" }}
          spacing={2}
        >
          {/* {type === "0" ? (
            <AuthButton button="merchant:add:button">
              <Button
                variant="contained"
                color="warning"
                onClick={() => handleOpenAddMerchant()}
              >
                {t("common.common_add_create_advertiser")}
              </Button>
            </AuthButton>
          ) : (
            <AuthButton button="merchant:add:button">
              <Button
                variant="contained"
                color="warning"
                onClick={() => handleOpenAddMerchant()}
              >
                {t("common.common_add_create_retailer")}
              </Button>
            </AuthButton>
          )
          } */}
          <Button
            variant="contained"
            color="primary"
            disabled={tableObject.length > 1 || tableObject.length === 0}
            onClick={() => {
              handleAdvertiser(
                tableObject[0].original.id,
                tableObject[0].original.name
              );
              handleClose();
            }}
          >
            {t("common.common_edit_ok")}
          </Button>
        </Stack>
      </BootstrapDialog>
      <AddMerchant
        type={type}
        ref={addMerchantRef}
        getAdvertiserList={reloadMerchantList}
      />
    </>
  );
});

export default AdvertiserSelect;
