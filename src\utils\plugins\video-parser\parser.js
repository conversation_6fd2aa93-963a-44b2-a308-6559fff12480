import SparkMD5 from "spark-md5";
export const extractVideoInfo = (file) => {
  return new Promise((resolve, reject) => {
    const video = document.createElement("video");
    const url = URL.createObjectURL(file); // 使用URL.createObjectURL将文件转换为URL

    // 设定video元素的源
    video.src = url;

    video.addEventListener("loadedmetadata", () => {
      // 获取视频的时长和分辨率
      const duration = Math.floor(video.duration); // 视频时长（秒）
      const width = video.videoWidth; // 视频宽度
      const height = video.videoHeight; // 视频高度

      // 创建canvas以生成缩略图
      const canvas = document.createElement("canvas");
      canvas.width = width;
      canvas.height = height;

      const context = canvas.getContext("2d");

      // 在视频的特定时间绘制缩略图，这里选择视频的开始时间
      video.currentTime = 0;

      video.addEventListener("seeked", function () {
        context.drawImage(video, 0, 0, width, height);
        const thumbnailBase64 = canvas.toDataURL("image/jpeg"); // 获取缩略图的Base64

        // 释放创建的对象URL
        URL.revokeObjectURL(url);

        // 计算文件的MD5值
        const md5Promise = calculateFileMD5(file);
        // 获取文件大小
        const fileSize = file.size;

        md5Promise
          .then((md5) => {
            // 释放创建的对象URL
            URL.revokeObjectURL(url);

            resolve({
              duration: duration, // 视频时长
              resolution: `${width}x${height}`, // 分辨率
              thumbnail: thumbnailBase64, // 缩略图的Base64
              md5: md5,
              fileSize: fileSize, // 文件的MD5值
            });
          })
          .catch((err) => {
            URL.revokeObjectURL(url);
            reject(err); // 处理MD5计算错误
          });
        // resolve({
        //     duration: duration,          // 视频时长
        //     resolution: `${width}x${height}`, // 分辨率
        //     thumbnail: thumbnailBase64 ,   // 缩略图的Base64
        //     md5: md5
        //   });
      });
    });

    video.addEventListener("error", (err) => {
      // 释放创建的对象URL
      URL.revokeObjectURL(url);
      reject(err); // 处理视频加载错误
    });
  });
};

// 计算文件的MD5值
async function calculateFileMD5(file) {
  const spark = new SparkMD5.ArrayBuffer();
  const fileReader = new FileReader();

  return new Promise((resolve, reject) => {
    fileReader.onload = function (e) {
      spark.append(e.target.result); // 使用ArrayBuffer追加数据
      const md5 = spark.end(); // 获取最终的MD5值
      resolve(md5);
    };

    fileReader.onerror = function () {
      reject(fileReader.error);
    };

    fileReader.readAsArrayBuffer(file);
  });
}
