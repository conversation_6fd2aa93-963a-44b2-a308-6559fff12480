import React from "react";
import { Grid, TextField } from "@mui/material";
import { Stack, InputLabel, OutlinedInput } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import { getComponentId } from "../common/utils";
import { useState } from "react";
import {
  AntTab,
  AntTabs,
  FormLabel,
  PrettoSlider,
} from "./PropertiesComponent";
import ColorPick from "../components/ColorPick";
import CustomInput from "../components/CustomInput";
import CustomSelect from "../components/CustomSelect";
import CustomGroupSelect from "../components/CustomGroupSelect";
import { useEffect } from "react";
import Autocomplete, { createFilterOptions } from "@mui/material/Autocomplete";

import {
  fontSizeList,
  fontList,
  animationList,
  dictScrollDirection,
  dictScrollSpeed,
} from "../common/utils";
import Button from "@mui/material/Button";
import ButtonGroup from "@mui/material/ButtonGroup";
import { message } from "../common/i18n";
import { toast } from "react-toastify";
import { getOptionByType } from "@/service/api/layout";

const filter = createFilterOptions();

let initproperties = {
  title: "editor_text", //标题
  name: "editor_text", //名称
  type: "ZKTecoText", //组件类型
  tag: "",
  left: 0,
  top: 0,
  width: 200,
  height: 50,
  zIndex: 50,
  hide: false, //是否隐藏
  anim: "pulse", //动画样式名称
  text: "ZKTeco", //文字
  fontColor: "#262626", //颜色
  bgColor: "#ffffff", //背景颜色
  font: "MyriadPro-Light", //字体
  fontSize: 32, //字体大小
  isBold: false, //是否加粗
  isItaly: true, //是否斜体
  isUnderline: true, //下划线
  textAlign: "center", //字体位置（居中还是左对齐右对齐等）
  lineHeight: 30, //行高
  rotate: 0, //宣传
  isScroll: true, //是否滚动
  duration: 60, //默认时长
  scrollDirection: "left", //滚动方向
  speed: 60, //滚动速度
  componentId: "",
};

const TextComponent = (props) => {
  const currentPageIndex = props.currentPageIndex;
  const isMobile = props.isMobile;
  const currentComponentId = props.currentComponentId;
  const activeTempIndex = props.activeTempIndex;
  const setCurrentComponentId = props.setCurrentComponentId;
  const setCurrentComponentIndex = props.setCurrentComponentIndex;
  const currentIndex = props.currentComponentIndex;
  const pages = props.pages;
  const setPages = props.setPages;
  const [properties, setProperties] = useState({
    ...initproperties,
  });

  const [textLabelList, setTextLabelList] = useState([]);

  const [selectResult, setSelectResult] = useState([]);

  useEffect(() => {
    if (isMobile) {
      getOptionByType("mobile_layout_text_option").then((res) => {
        if (res.code === 0) {
          setTextLabelList(res.data);
        } else {
          setTextLabelList([]);
        }
      });
    }
  }, [isMobile]);

  useEffect(() => {
    if (isMobile) {
      let tagList = pages[currentPageIndex].componentList
        .filter((item) => {
          if (item.type === "ZKTecoText" && item.tag) {
            return true;
          } else {
            return false;
          }
        })
        .map((item) => {
          return item.tag;
        });
      setSelectResult(tagList);
    }
  }, [
    currentPageIndex,
    currentIndex,
    activeTempIndex,
    properties,
    pages,
    textLabelList,
  ]);

  useEffect(() => {
    if (currentIndex !== "") {
      const curretnPage = pages[currentPageIndex];
      if (curretnPage.isTemplate) {
        let componentInfo =
          curretnPage.tempLayout[activeTempIndex]?.componentList[currentIndex];
        setProperties({
          ...componentInfo,
        });
      } else {
        let componentInfo = pages[currentPageIndex].componentList[currentIndex];
        setProperties({
          ...componentInfo,
        });
      }
    }
  }, [currentPageIndex, currentIndex, activeTempIndex, pages]);

  const setComponentInfo = (baseInfo) => {
    const newPages = JSON.parse(JSON.stringify(props.pages));
    let currentPage = newPages[currentPageIndex];
    if (currentPage.isTemplate) {
      let oldInfo =
        currentPage.tempLayout[activeTempIndex].componentList[currentIndex];
      let newInfo = {
        ...oldInfo,
        ...baseInfo,
      };
      currentPage.tempLayout[activeTempIndex].componentList[currentIndex] =
        newInfo;
    } else {
      let oldInfo = newPages[currentPageIndex].componentList[currentIndex];
      let newInfo = {
        ...oldInfo,
        ...baseInfo,
      };
      newPages[currentPageIndex].componentList[currentIndex] = newInfo;
    }

    if (props.setPages) {
      props.setPages(newPages);
    }
  };

  const addText = () => {
    let currentPage = pages[currentPageIndex];
    if (currentPage.isTemplate) {
      if (activeTempIndex === "") {
        toast.success(message("editor_select_template_tip"));
      } else {
        let ComponentId = getComponentId(pages);
        let initValue = {
          ...initproperties,
          componentId: ComponentId,
        };

        currentPage.tempLayout[activeTempIndex].componentList.push(initValue);
        setProperties({
          ...initValue,
        });
        setPages([...pages]);
        setCurrentComponentId(ComponentId);
        let index =
          currentPage.tempLayout[activeTempIndex].componentList.length - 1;
        setCurrentComponentIndex(index);
      }
    } else {
      let ComponentId = getComponentId(pages);

      let initValue = {
        ...initproperties,
        componentId: ComponentId,
      };

      pages[currentPageIndex].componentList.push(initValue);
      setProperties({
        ...initValue,
      });
      setPages([...pages]);
      setCurrentComponentId(ComponentId);
      let index = pages[currentPageIndex].componentList.length - 1;
      setCurrentComponentIndex(index);
    }
  };

  const [value, setValue] = useState("style");
  const handleChange = (event, newValue) => {
    setValue(newValue);
  };
  const changeProperties = (event) => {
    const name = event.target.name;
    let newInfo = {
      ...properties,
      [name]: event.target.value,
    };
    if (newInfo.text === "") {
      toast.error(message("editor_textContentNoEmpty"));
    }
    setComponentInfo(newInfo);
  };

  const changeConvertProperties = (event) => {
    const name = event.target.name;
    let newInfo = {
      ...properties,
      [name]: Math.floor(event.target.value),
    };
    if (newInfo.text === "") {
      toast.error(message("editor_textContentNoEmpty"));
    }
    setComponentInfo(newInfo);
  };

  const changeTagProperties = (value) => {
    let newInfo = {
      ...properties,
      tag: value,
    };
    setComponentInfo(newInfo);
  };

  const handleClickStyle = (name) => {
    let newInfo = {
      ...properties,
      [name]: !properties[name],
    };
    setComponentInfo(newInfo);
  };

  const setTextAlign = (textAlign) => {
    let newInfo = {
      ...properties,
      textAlign: textAlign,
    };
    setComponentInfo(newInfo);
  };

  const handleSliderChange = (event, newValue) => {
    let newInfo = {
      ...properties,
      lineHeight: newValue,
    };
    setComponentInfo(newInfo);
  };

  const handleRotationChange = (event, newValue) => {
    let newInfo = {
      ...properties,
      rotate: newValue,
    };
    setComponentInfo(newInfo);
  };

  return (
    <Grid
      sx={{
        display: "flex",
        justifyContent: "center",
        height: "100%",
      }}>
      {currentComponentId ? (
        <Grid
          sx={{
            width: "100%",
            boxShadow: "0px 0px 6px #00000029",
            borderRadius: "10px",
            backgroundColor: "#ffffff",
            overflow: "hidden",
            display: "flex",
            flexDirection: "column",
          }}>
          <AntTabs
            value={value}
            onChange={handleChange}
            aria-label="ant example">
            <AntTab value="style" label={message("editor_style")} />
            <AntTab value="animation" label={message("editor_animation")} />
          </AntTabs>
          <Grid
            sx={{
              overflow: "auto",
            }}>
            {value === "style" && (
              <Grid sx={{ p: 2, pt: 1 }}>
                {isMobile && (
                  <Grid
                    sx={{
                      mt: 1,
                    }}>
                    <Stack
                      sx={{
                        marginBottom: "5px",
                        display: "flex",
                        flexDirection: "row",
                        alignItems: "center",
                      }}
                      spacing={0}>
                      <InputLabel
                        sx={{
                          color: "#707070",
                          fontSize: "14px",
                          mr: 2,
                          flexShrink: 0,
                          whiteSpace: "nowrap",
                        }}>
                        {message("textLabel")}
                      </InputLabel>

                      <Autocomplete
                        value={properties.tag}
                        disableClearable={!properties.tag}
                        style={{
                          width: "100%",
                          position: "relative",
                        }}
                        sx={{
                          ".MuiAutocomplete-endAdornment": {
                            top: 0,
                            transform: "none",
                          },
                        }}
                        size="small"
                        options={textLabelList}
                        onChange={(event, newValue) => {
                          if (newValue === null) {
                            changeTagProperties("");
                          } else {
                            changeTagProperties(newValue.value);
                          }
                        }}
                        filterOptions={(options, params) => {
                          const filtered = filter(options, params);
                          const { inputValue } = params;
                          const isExisting = options.some(
                            (option) => inputValue === option.title
                          );
                          if (inputValue !== "" && !isExisting) {
                            filtered.push({
                              value: inputValue,
                              label: inputValue,
                            });
                          }
                          return filtered;
                        }}
                        getOptionDisabled={(option) => {
                          if (selectResult.includes(option.value)) {
                            return true;
                          }
                          return false;
                        }}
                        renderInput={(params) => {
                          const { inputProps } = params;
                          const value = inputProps.value;
                          let label = value;
                          const list = textLabelList.filter(
                            (option) => value === option.value
                          );
                          if (list && list.length === 1) {
                            label = list[0].label;
                          }
                          params.inputProps.value = label;
                          return (
                            <TextField
                              {...params}
                              label=""
                              InputProps={{
                                ...params.InputProps,
                                // type: "search",
                              }}
                            />
                          );
                        }}
                        renderOption={(props, option) => {
                          const { key, ...optionProps } = props;
                          return (
                            <li key={key} {...optionProps}>
                              {option.label}
                            </li>
                          );
                        }}
                      />
                    </Stack>
                  </Grid>
                )}

                <CustomInput
                  label={message("editor_layerName") + ":"}
                  value={properties.name}
                  onChange={changeProperties}
                  name="name"></CustomInput>
                <CustomInput
                  label={message("editor_textInfo") + ":"}
                  value={properties.text}
                  onChange={changeProperties}
                  name="text"></CustomInput>
                <Grid
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    mt: 1,
                  }}>
                  <FormLabel sx={{ mr: 2 }}>
                    {message("editor_fontColor")}:
                  </FormLabel>
                  <ColorPick
                    value={properties.fontColor}
                    name="fontColor"
                    onChange={changeProperties}></ColorPick>
                </Grid>
                <Grid
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    mt: 1,
                  }}>
                  <FormLabel sx={{ mr: 2 }}>
                    {message("editor_bgColor")}:
                  </FormLabel>
                  <ColorPick
                    value={properties.bgColor}
                    name="bgColor"
                    onChange={changeProperties}></ColorPick>
                </Grid>

                <Grid
                  sx={{
                    mt: 1,
                  }}>
                  <CustomSelect
                    label={message("editor_scrollDirection") + ":"}
                    name="scrollDirection"
                    onChange={changeProperties}
                    value={properties.scrollDirection}
                    items={dictScrollDirection}></CustomSelect>
                </Grid>

                <Grid>
                  <CustomSelect
                    label={message("editor_scrollSpeed") + ":"}
                    name="speed"
                    onChange={changeProperties}
                    value={properties.speed}
                    items={dictScrollSpeed}></CustomSelect>
                </Grid>

                <Grid>
                  <CustomSelect
                    label={message("editor_fontSize") + ":"}
                    name="fontSize"
                    onChange={changeProperties}
                    value={properties.fontSize}
                    items={fontSizeList}></CustomSelect>
                </Grid>

                <Grid>
                  <CustomSelect
                    label={message("editor_font") + ":"}
                    name="font"
                    onChange={changeProperties}
                    value={properties.font}
                    items={fontList}></CustomSelect>
                </Grid>

                <Grid>
                  <ButtonGroup
                    size="small"
                    variant="outlined"
                    aria-label="outlined button group">
                    <Button
                      variant={properties.isBold ? "contained" : "outlined"}
                      onClick={() => {
                        handleClickStyle("isBold");
                      }}>
                      B
                    </Button>
                    <Button
                      variant={properties.isItaly ? "contained" : "outlined"}
                      onClick={() => {
                        handleClickStyle("isItaly");
                      }}>
                      I
                    </Button>
                    <Button
                      variant={
                        properties.isUnderline ? "contained" : "outlined"
                      }
                      onClick={() => {
                        handleClickStyle("isUnderline");
                      }}
                      sx={{
                        textDecoration: "underline",
                      }}>
                      U
                    </Button>
                  </ButtonGroup>
                </Grid>

                <Grid sx={{ mt: 2 }}>
                  <ButtonGroup
                    size="small"
                    variant="outlined"
                    aria-label="outlined button group">
                    <Button
                      variant={
                        properties.textAlign === "left"
                          ? "contained"
                          : "outlined"
                      }
                      onClick={() => {
                        setTextAlign("left");
                      }}>
                      <svg
                        t="1582712720542"
                        className="icon"
                        viewBox="0 0 1024 1024"
                        version="1.1"
                        xmlns="http://www.w3.org/2000/svg"
                        p-id="1710"
                        width="16"
                        height="16">
                        <path
                          d="M624 119.6H64v112h560v-112z m0 448H64v112h560v-112z m224-224H64v112h784v-112z m-784 448v112h896v-112H64z"
                          p-id="1711"
                        />
                      </svg>
                    </Button>
                    <Button
                      variant={
                        properties.textAlign === "center"
                          ? "contained"
                          : "outlined"
                      }
                      onClick={() => {
                        setTextAlign("center");
                      }}>
                      <svg
                        t="1582712739697"
                        className="icon"
                        viewBox="0 0 1024 1024"
                        version="1.1"
                        xmlns="http://www.w3.org/2000/svg"
                        p-id="2052"
                        width="16"
                        height="16">
                        <path
                          d="M733.9 120.6H286.4v111.9h447.5V120.6zM789.8 680V568.1H230.5V680h559.3z m56-335.6H174.5v111.9h671.3V344.4zM62.6 791.9v111.9h895V791.9h-895z"
                          p-id="2053"
                        />
                      </svg>
                    </Button>
                    <Button
                      variant={
                        properties.textAlign === "right"
                          ? "contained"
                          : "outlined"
                      }
                      onClick={() => {
                        setTextAlign("right");
                      }}>
                      <svg
                        t="1582712868266"
                        className="icon"
                        viewBox="0 0 1024 1024"
                        version="1.1"
                        xmlns="http://www.w3.org/2000/svg"
                        p-id="2426"
                        width="16"
                        height="16">
                        <path
                          d="M400 231.6h560v-112H400v112z m0 448h560v-112H400v112z m-224-224h784v-112H176v112z m-112 336v112h896v-112H64z"
                          p-id="2427"
                        />
                      </svg>
                    </Button>

                    <Button
                      sx={{ p: 1 }}
                      variant={
                        properties.textAlign === "justify"
                          ? "contained"
                          : "outlined"
                      }
                      onClick={() => {
                        setTextAlign("justify");
                      }}>
                      <svg
                        t="1582712760866"
                        className="icon"
                        viewBox="0 0 1024 1024"
                        version="1.1"
                        xmlns="http://www.w3.org/2000/svg"
                        p-id="2239"
                        width="16"
                        height="16">
                        <path
                          d="M64 120.4v112h896.2v-112H64z m0 560.1h896.2v-112H64v112z m0-224h896.2v-112H64v112z m0 448.1h896.2v-112H64v112z"
                          p-id="2240"
                        />
                      </svg>
                    </Button>
                  </ButtonGroup>
                </Grid>

                <Grid
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    mt: 5,
                  }}>
                  <FormLabel sx={{ mr: 2 }}>
                    {message("editor_lineHeight")}:
                  </FormLabel>
                  <PrettoSlider
                    onChange={handleSliderChange}
                    size="small"
                    min={10}
                    max={300}
                    step={1}
                    color="secondary"
                    value={properties.lineHeight}
                    aria-label="Small"
                    valueLabelDisplay="on"></PrettoSlider>
                </Grid>

                <Grid
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    mt: 4,
                  }}>
                  <FormLabel sx={{ mr: 2 }}>
                    {message("editor_rotate")}:
                  </FormLabel>
                  <PrettoSlider
                    onChange={handleRotationChange}
                    size="small"
                    min={0}
                    max={360}
                    step={1}
                    color="secondary"
                    value={properties.rotate}
                    aria-label="Small"
                    valueLabelDisplay="on"></PrettoSlider>
                </Grid>

                <CustomInput
                  label={message("editor_abscissa") + ":"}
                  value={properties.left}
                  onChange={changeConvertProperties}
                  name="left"></CustomInput>

                <CustomInput
                  label={message("editor_ordinate") + ":"}
                  value={properties.top}
                  onChange={changeConvertProperties}
                  name="top"></CustomInput>

                <CustomInput
                  label={message("editor_width") + ":"}
                  value={properties.width}
                  onChange={changeConvertProperties}
                  name="width"></CustomInput>

                <CustomInput
                  label={message("editor_height") + ":"}
                  value={properties.height}
                  onChange={changeConvertProperties}
                  name="height"></CustomInput>
              </Grid>
            )}
            {value === "animation" && (
              <Grid sx={{ p: 2, pt: 1 }}>
                <CustomGroupSelect
                  label={message("editor_animation") + ":"}
                  name="anim"
                  onChange={changeProperties}
                  value={properties.anim}
                  items={animationList}></CustomGroupSelect>
              </Grid>
            )}
          </Grid>
        </Grid>
      ) : (
        <Grid
          sx={{
            width: "100%",
            height: "80px",
            background: "#ffffff",
            borderRadius: "10px",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            cursor: "pointer",
          }}
          onClick={addText}>
          <span style={{ marginRight: "10px" }}>
            {message("editor_addText")}
          </span>
          <AddIcon></AddIcon>
        </Grid>
      )}
    </Grid>
  );
};

export default TextComponent;
