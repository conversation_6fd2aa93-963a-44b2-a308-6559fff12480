import React from "react";
import { Grid } from "@mui/material";
import { styled } from "@mui/material/styles";
import { useEffect, useState } from "react";
import RenderText from "./CenterComponent/RenderText";
import RenderPhotos from "./CenterComponent/RenderPhotos";
import RenderVideo from "./CenterComponent/RenderVideo";
import RenderAudio from "./CenterComponent/RenderAudio";
import RenderTime from "./CenterComponent/RenderTime";
import RenderNews from "./CenterComponent/RenderNews";
import RenderLive from "./CenterComponent/RenderLive";
import RenderWeather from "./CenterComponent/RenderWeather";
import Moveable from "react-moveable";
import { toast } from "react-toastify";
import { message } from "./common/i18n";
import CustomTemplate from "./CustomTemplate";
import { pageDuration } from "./common/utils";
const RenderCompon = (props) => {
  let info = props.info;
  let type = info.type;
  let component = null;
  switch (type) {
    case "ZKTecoText":
      component = <RenderText {...props}></RenderText>;
      break;
    case "ZKTecoSlideShowImg":
      component = <RenderPhotos {...props}></RenderPhotos>;
      break;
    case "ZKTecoVideo":
      component = <RenderVideo {...props}></RenderVideo>;
      break;
    case "ZKTecoMusic":
      component = <RenderAudio {...props}></RenderAudio>;
      break;
    case "ZKTecoWeather":
      component = <RenderWeather {...props}></RenderWeather>;
      break;

    case "ZKTecoTime":
      component = <RenderTime {...props}></RenderTime>;
      break;

    case "ZKTecoNews":
      component = <RenderNews {...props}></RenderNews>;
      break;

    case "ZKTecoLive":
      component = <RenderLive {...props}></RenderLive>;
      break;
    default:
      component = null;
      break;
  }

  return component;
};

const CustomButton = styled(Grid)(({ theme }) => ({
  margin: "10px 0px",
  "&:hover": {
    color: "#7ac143",
    cursor: "pointer",
  },
}));

const CenterArea = (props) => {
  const currentPageIndex = props.currentPageIndex;
  const currentPage = props.pages[currentPageIndex] || [];
  const contextMenu = props.contextMenu;
  const setContextMenu = props.setContextMenu;

  const activeTempIndex = props.activeTempIndex;
  const scale = props.scale;
  const width = props.width;
  const height = props.height;
  const clickCenter = () => {
    if (props.setCurrentComponentId) {
      props.setCurrentComponentId("");
      props.setCurrentComponentIndex("");
    }
    if (props.setCurrentType) {
      props.setCurrentType("scene");
    }
    setContextMenu({
      pageX: 0,
      pageY: 0,
      show: false,
      component: null,
    });
  };

  const [frame, setFrame] = useState({
    translate: [0, 0],
  });

  const [target, setTarget] = useState(null);
  useEffect(() => {
    setTarget(document.querySelector(".centerArea"));
  }, []);

  function handleDragStart(e) {
    e.set(frame.translate);
  }
  function handleDrag(e) {
    frame.translate = e.beforeTranslate;
    e.target.style.transform = `translate(${e.beforeTranslate[0]}px, ${e.beforeTranslate[1]}px) scale(${scale})`;
  }

  useEffect(() => {
    let centerTag = document.querySelector(".centerArea");
    centerTag.style.transform = `translate(0px, 0px) scale(${scale})`;
  }, [scale]);

  const deleteCom = (event) => {
    event.preventDefault();
    event.stopPropagation();

    const newPages = JSON.parse(JSON.stringify(props.pages));
    let currentPage = newPages[currentPageIndex];
    let componentList = [];

    if (currentPage.isTemplate) {
      componentList = currentPage.tempLayout[activeTempIndex].componentList;
    } else {
      componentList = newPages[currentPageIndex].componentList;
    }

    let newComponentList = componentList.filter((item) => {
      if (contextMenu?.component?.componentId === item.componentId) {
        return false;
      } else {
        return true;
      }
    });

    if (currentPage.isTemplate) {
      currentPage.tempLayout[activeTempIndex].componentList = newComponentList;
    } else {
      newPages[currentPageIndex].componentList = newComponentList;
    }

    setContextMenu({
      pageX: 0,
      pageY: 0,
      show: false,
      component: null,
    });

    if (props.setPages) {
      props.setPages(pageDuration(newPages));
    }
    if (props.setCurrentType) {
      props.setCurrentType(null);
    }
    if (props.setCurrentComponentId) {
      props.setCurrentComponentId(null);
      props.setCurrentComponentIndex(null);
    }
  };

  //上移一层
  const upCom = (event) => {
    event.preventDefault();
    event.stopPropagation();
    const newPages = JSON.parse(JSON.stringify(props.pages));
    let currentPage = newPages[currentPageIndex];
    let componentList = [];
    if (currentPage.isTemplate) {
      componentList = currentPage.tempLayout[activeTempIndex].componentList;
    } else {
      componentList = newPages[currentPageIndex].componentList;
    }

    let tempList = componentList
      .filter((item) => {
        if (item.zIndex >= contextMenu?.component.zIndex) {
          return true;
        } else {
          return false;
        }
      })
      .sort((a, b) => {
        return a.zIndex - b.zIndex;
      });

    if (tempList.length > 1) {
      componentList.forEach((item) => {
        if (contextMenu?.component?.componentId === item.componentId) {
          item.zIndex = tempList[1].zIndex + 1;
        }
      });
    } else {
      toast.success(message("editor_move_top_message"));
    }

    if (props.setPages) {
      props.setPages(newPages);
    }
    setContextMenu({
      pageX: 0,
      pageY: 0,
      show: false,
      component: null,
    });
  };

  //下移一层
  const downCom = (event) => {
    event.preventDefault();
    event.stopPropagation();

    const newPages = JSON.parse(JSON.stringify(props.pages));
    let currentPage = newPages[currentPageIndex];
    let componentList = [];
    if (currentPage.isTemplate) {
      componentList = currentPage.tempLayout[activeTempIndex].componentList;
    } else {
      componentList = newPages[currentPageIndex].componentList;
    }

    let tempList = componentList
      .filter((item) => {
        if (item.zIndex <= contextMenu?.component.zIndex) {
          return true;
        } else {
          return false;
        }
      })
      .sort((a, b) => {
        return b.zIndex - a.zIndex;
      });

    if (tempList.length > 1) {
      componentList.forEach((item) => {
        if (contextMenu?.component?.componentId === item.componentId) {
          item.zIndex = tempList[1].zIndex - 1;
        }
      });
    } else {
      toast.success(message("editor_move_bottom_message"));
    }

    if (props.setPages) {
      props.setPages(newPages);
    }
    setContextMenu({
      pageX: 0,
      pageY: 0,
      show: false,
      component: null,
    });
  };

  const upTop = (event) => {
    event.preventDefault();
    event.stopPropagation();
    const newPages = JSON.parse(JSON.stringify(props.pages));
    let currentPage = newPages[currentPageIndex];
    let componentList = [];
    if (currentPage.isTemplate) {
      componentList = currentPage.tempLayout[activeTempIndex].componentList;
    } else {
      componentList = newPages[currentPageIndex].componentList;
    }

    let tempList = componentList
      .filter((item) => {
        if (item.zIndex >= contextMenu?.component.zIndex) {
          return true;
        } else {
          return false;
        }
      })
      .sort((a, b) => {
        return a.zIndex - b.zIndex;
      });

    if (tempList.length > 1) {
      const max = Math.max(
        ...componentList.map((item) => {
          return item.zIndex;
        })
      );
      componentList.forEach((item) => {
        if (contextMenu?.component?.componentId === item.componentId) {
          item.zIndex = max + 1;
        }
      });

      componentList.forEach((item) => {
        if (contextMenu?.component?.componentId === item.componentId) {
          item.zIndex = tempList[tempList.length - 1].zIndex + 1;
        }
      });
    } else {
      toast.success(message("editor_move_top_message"));
    }

    if (props.setPages) {
      props.setPages(newPages);
    }
    setContextMenu({
      pageX: 0,
      pageY: 0,
      show: false,
      component: null,
    });
  };

  const downBottom = (event) => {
    event.preventDefault();
    event.stopPropagation();

    const newPages = JSON.parse(JSON.stringify(props.pages));
    let currentPage = newPages[currentPageIndex];
    let componentList = [];
    if (currentPage.isTemplate) {
      componentList = currentPage.tempLayout[activeTempIndex].componentList;
    } else {
      componentList = newPages[currentPageIndex].componentList;
    }

    let tempList = componentList
      .filter((item) => {
        if (item.zIndex <= contextMenu?.component.zIndex) {
          return true;
        } else {
          return false;
        }
      })
      .sort((a, b) => {
        return b.zIndex - a.zIndex;
      });

    if (tempList.length > 1) {
      const min = Math.min(
        ...componentList.map((item) => {
          return item.zIndex;
        })
      );
      componentList.forEach((item) => {
        if (contextMenu?.component?.componentId === item.componentId) {
          item.zIndex = min - 1;
        }
      });
    } else {
      toast.success(message("editor_move_bottom_message"));
    }

    if (props.setPages) {
      props.setPages(newPages);
    }
    setContextMenu({
      pageX: 0,
      pageY: 0,
      show: false,
      component: null,
    });
  };

  const clickTemp = (event, index) => {
    event.preventDefault();
    event.stopPropagation();
    if (props.setActiveTempIndex) {
      props.setActiveTempIndex(index);
    }

    if (props.setCurrentComponentId) {
      props.setCurrentComponentId("");
      props.setCurrentComponentIndex("");
    }

    if (props.setCurrentType) {
      props.setCurrentType("templateSetting");
    }

    setContextMenu({
      pageX: 0,
      pageY: 0,
      show: false,
      component: null,
    });
  };

  return (
    <Grid
      sx={{
        width: "100%",
        height: "100%",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        position: "relative",
        overflow: "hidden",
      }}>
      {contextMenu.show && (
        <div
          style={{
            backgroundColor: "#ffffff",
            borderRadius: "10px",
            position: "fixed",
            padding: "10px",
            top: contextMenu.pageY + "px",
            left: contextMenu.pageX + "px",
            zIndex: 10000,
            boxShadow: "0px 0px 6px #00000029",
          }}>
          <Grid>
            <CustomButton onClick={deleteCom}>
              {message("editor_delete_component")}
            </CustomButton>
            <CustomButton onClick={upCom}>
              {message("editor_bring_forward")}
            </CustomButton>
            <CustomButton onClick={downCom}>
              {message("editor_sent_backward")}
            </CustomButton>
            <CustomButton onClick={upTop}>
              {message("editor_move_top")}
            </CustomButton>
            <CustomButton onClick={downBottom}>
              {message("editor_move_bottom")}
            </CustomButton>
          </Grid>
        </div>
      )}

      <Grid
        className="centerArea"
        id="htmlcanvas"
        onClick={clickCenter}
        sx={{
          position: "absolute",
          transform: `scale(${scale})`,
          backgroundColor: currentPage.bgColor
            ? currentPage.bgColor
            : "#ffffff",
          width: width + "px",
          height: height + "px",
          overflow: "hidden",
        }}>
        {currentPage.isTemplate && (
          <Grid
            id="htmlcanvasTemplate"
            sx={{
              width: "100%",
              height: "100%",
              zIndex: 10,
              position: "absolute",
              backgroundColor: currentPage.bgColor
                ? currentPage.bgColor
                : "#ffffff",
            }}>
            {currentPage.bgImg && (
              <img
                style={{
                  zIndex: 0,
                  position: "absolute",
                  width: "100%",
                  height: "100%",
                }}
                crossOrigin="anonymous"
                src={currentPage.bgImg + "?_aa=" + new Date().getTime()}></img>
            )}

            {!currentPage.customTemplate &&
              currentPage.tempLayout.map((item, index) => {
                return (
                  <Grid
                    key={index}
                    onClick={(e) => {
                      clickTemp(e, index);
                    }}
                    sx={{
                      width: item.width + "px",
                      height: item.height + "px",
                      position: "absolute",
                      top: item.top + "px",
                      left: item.left + "px",
                      overflow: "hidden",
                      backgroundColor:
                        item.bgColor === "transparent" ||
                        item.bgColor === "#ffffff"
                          ? "#ffffff"
                          : item.bgColor,
                      boxShadow:
                        props.activeTempIndex === index
                          ? "inset 0px 0px 5px #7ac143"
                          : "inset 0px 0px 0px 1px #000000bf",
                      border:
                        props.activeTempIndex === index
                          ? "1px solid #7ac143"
                          : "",
                    }}>
                    {item.bgImg && (
                      <img
                        style={{
                          zIndex: 0,
                          position: "absolute",
                          width: "100%",
                          height: "100%",
                        }}
                        crossOrigin="anonymous"
                        src={item.bgImg + "?_aa=" + new Date().getTime()}></img>
                    )}

                    {item.componentList.map((comItem, componentIndex) => {
                      if (comItem.hide) {
                        return "";
                      } else {
                        return (
                          <RenderCompon
                            centerWidth={width}
                            centerHeight={height}
                            scale={scale}
                            key={comItem.componentId}
                            setContextMenu={setContextMenu}
                            componentIndex={componentIndex}
                            info={comItem}
                            tempIndex={index}
                            {...props}></RenderCompon>
                        );
                      }
                    })}
                  </Grid>
                );
              })}

            {currentPage.customTemplate &&
              currentPage.tempLayout.map((item, index) => {
                return (
                  <CustomTemplate
                    item={item}
                    tempIndex={index}
                    clickTemp={clickTemp}
                    key={index}
                    {...props}>
                    <Grid
                      key={index}
                      onClick={(e) => {
                        clickTemp(e, index);
                      }}
                      sx={{
                        width: item.width + "px",
                        height: item.height + "px",
                        position: "absolute",
                        overflow: "hidden",
                        backgroundColor:
                          item.bgColor === "transparent" ||
                          item.bgColor === "#ffffff"
                            ? "#ffffff"
                            : item.bgColor,
                        boxShadow:
                          props.activeTempIndex === index
                            ? "inset 0px 0px 5px #7ac143"
                            : "inset 0px 0px 0px 1px #000000bf",
                        border:
                          props.activeTempIndex === index
                            ? "1px solid #7ac143"
                            : "",
                      }}>
                      {item.bgImg && (
                        <img
                          style={{
                            zIndex: 0,
                            position: "absolute",
                            width: "100%",
                            height: "100%",
                          }}
                          crossOrigin="anonymous"
                          src={
                            item.bgImg + "?_aa=" + new Date().getTime()
                          }></img>
                      )}

                      {item.componentList.map((comItem, componentIndex) => {
                        if (comItem.hide) {
                          return "";
                        } else {
                          return (
                            <RenderCompon
                              centerWidth={width}
                              centerHeight={height}
                              scale={scale}
                              key={comItem.componentId}
                              setContextMenu={setContextMenu}
                              componentIndex={componentIndex}
                              info={comItem}
                              tempIndex={index}
                              {...props}></RenderCompon>
                          );
                        }
                      })}
                    </Grid>
                  </CustomTemplate>
                );
              })}
          </Grid>
        )}

        {currentPage.bgImg && (
          <img
            style={{
              zIndex: 0,
              position: "absolute",
              width: "100%",
              height: "100%",
            }}
            crossOrigin="anonymous"
            src={currentPage.bgImg + "?_aa=" + new Date().getTime()}></img>
        )}

        {currentPage?.componentList?.map((item, componentIndex) => {
          if (item.hide) {
            return "";
          } else {
            return (
              <RenderCompon
                centerWidth={width}
                centerHeight={height}
                scale={scale}
                key={item.componentId}
                setContextMenu={setContextMenu}
                componentIndex={componentIndex}
                info={item}
                {...props}></RenderCompon>
            );
          }
        })}
      </Grid>
      {/* draggable={currentComponentId === ""} // 是否可以拖拽 */}
      <Moveable
        className="center_Moveable"
        target={target} // moveable的对象
        draggable={false} // 是否可以拖拽
        padding={{ left: 0, top: 0, right: 0, bottom: 0 }} // padding距离
        zoom={1} // 缩放包裹的moveable
        origin={false} // 显示中心点
        throttleDrag={0} // 拖拽阈值 达到这个值才执行拖拽
        onDragStart={handleDragStart} // 拖动开始执行
        onDrag={handleDrag} // 拖动中
      />
    </Grid>
  );
};

export default CenterArea;
