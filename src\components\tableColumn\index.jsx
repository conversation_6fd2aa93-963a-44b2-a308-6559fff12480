import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Tooltip,
  Checkbox,
  FormControlLabel,
  Link,
  Grid,
  IconButton,
  TextField,
} from "@mui/material";
import FilterAltOutlinedIcon from "@mui/icons-material/FilterAltOutlined";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { toast } from "react-toastify";

export default function FilterColumn({ columns, columnVisibility, table }) {
  const [anchorEl, setAnchorEl] = React.useState(null);
  const key = window.location.hash + "/columnVisibility";
  const { t } = useTranslation();

  const validColumns = columns.filter(
    (col) =>
      col.columnDef.accessorKey && // 必须有 accessorKey
      !["mrt-row-actions", "mrt-row-select"].includes(col.id)
  );
  const handleOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleToggleColumn = (accessorKey) => {
    table.setColumnVisibility((prev) => {
      return {
        ...prev,
        [accessorKey]: !prev[accessorKey],
      };
    });
  };

  const allVisible = validColumns.every(
    (col) => columnVisibility[col.columnDef.accessorKey] !== false
  );
  const someVisible = validColumns.some(
    (col) => columnVisibility[col.columnDef.accessorKey] !== false
  );
  const isAllSelected = allVisible && someVisible;

  const handleSelectAll = () => {
    const newVisibility = validColumns.reduce((acc, col) => {
      acc[col.columnDef.accessorKey] = !isAllSelected;
      return acc;
    }, {});
    table.setColumnVisibility(newVisibility);
  };

  const handleReset = () => {
    const resetVisibility = validColumns.reduce((acc, col) => {
      acc[col.columnDef.accessorKey] = true;
      return acc;
    }, {});
    table.setColumnVisibility(resetVisibility);
    window.localStorage.removeItem(key);
    toast.success(t("table.table_column_filter_reset"));
  };

  const handleOnOk = () => {
    const columnVisibility = table.getState().columnVisibility;
    window.localStorage.setItem(key, JSON.stringify(columnVisibility));
    handleClose();
    toast.success(t("table.table_column_filter_save"));
  };
  return (
    <>
      <Tooltip arrow placement="top" title={t("table.table_column_filter")}>
        <IconButton onClick={handleOpen} aria-label="column-filter">
          <FilterAltOutlinedIcon />
        </IconButton>
      </Tooltip>

      <Popover
        open={Boolean(anchorEl)}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
        sx={{ width: "300px", padding: "10px 10px" }}
        transformOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
      >
        <div
          style={{ padding: "10px", display: "flex", flexDirection: "column" }}
        >
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              gap: 1,
              maxWidth: "100%",
            }}
          >
            <Checkbox
              sx={{ padding: "6px" }}
              size="small"
              checked={isAllSelected}
              indeterminate={someVisible && !allVisible}
              onChange={handleSelectAll}
            />
            <Tooltip title= {t('table.table_column_filter_all')} placement="right">
              <Typography
                sx={{
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  flexGrow: 1,
                }}
              >
                {t('table.table_column_filter_all')}
              </Typography>
            </Tooltip>
          </Box>
          {validColumns.map((col) => {
            const accessorKey = col.columnDef.accessorKey;
            const header = col.columnDef.header;
            return (
              <Box
                key={col.id}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: 1,
                  maxWidth: "100%",
                }}
              >
                <Checkbox
                  size="small"
                  sx={{ padding: "6px" }}
                  checked={columnVisibility[accessorKey] ?? true}
                  onChange={() => handleToggleColumn(accessorKey)}
                />
                <Tooltip title={col.header} placement="right">
                  <Typography
                    sx={{
                      whiteSpace: "nowrap",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      flexGrow: 1,
                    }}
                  >
                    {header}
                  </Typography>
                </Tooltip>
              </Box>
            );
          })}
          <Stack spacing={2} sx={{ mt: 1 }} direction={"row"}>
            <Button variant="contained" onClick={handleOnOk} size="small">
              {t("common.common_edit_ok")}
            </Button>
            <Button
              variant="outlined"
              color="info"
              size="small"
              onClick={handleReset}
            >
              {t("common.common_op_reset")}
            </Button>
          </Stack>
        </div>
      </Popover>
    </>
  );
}
