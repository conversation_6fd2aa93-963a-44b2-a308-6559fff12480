<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>微应用-react</title>

    <!-- qiankun兼容性修复 -->
    <script>
      // 在qiankun环境中预先设置React Refresh相关变量
      if (window.__POWERED_BY_QIANKUN__) {
        window.$RefreshReg$ = function () {};
        window.$RefreshSig$ = function () {
          return function (type) {
            return type;
          };
        };
        window.__vite_plugin_react_preamble_installed__ = false;

        // 确保global变量存在
        if (typeof global === "undefined") {
          window.global = window;
        }

        // 设置process.env
        if (typeof process === "undefined") {
          window.process = { env: { NODE_ENV: "development" } };
        }
      }
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
