export const getAgeRange = (data, languageConfig) => {
  let colors = {
    "0-11": "#78BC27",
    "12-17": "#9FD85B",
    "18-34": "#A7DFF8",
    "35-54": "#94BCFF",
    "55+": "#C2C2FF",
  };

  let seriesData = data
    .filter((item) => {
      return item.viewCount > 0;
    })
    .map((item) => {
      return {
        value: item.viewCount,
        name: languageConfig.RealTimeAge + " " + item.ageGroup,
        itemStyle: {
          color: colors[item.ageGroup],
        },
      };
    });

  const option = {
    title: [
      {
        text: languageConfig.RealTimeAgePercentageOf,
        left: "0%",
        top: "5%",
        textStyle: {
          fontSize: 25,
          fontWeight: "bold",
          color: "#000000",
        },
      },
      {
        text: languageConfig.RealTimeAgePercentage,
        left: "0%",
        top: "20%",
        textStyle: {
          fontSize: 25,
          fontWeight: "bold",
          color: "#000000",
        },
      },
    ],

    graphic: [
      {
        type: "group",
        left: "0%",
        top: "35%",
        children: [
          {
            type: "line",
            shape: {
              x1: 0,
              y1: 0,
              x2: 130,
              y2: 0,
            },
            style: {
              stroke: "#D5D5D5",
              lineWidth: 1,
            },
          },
        ],
      },
    ],

    tooltip: {
      trigger: "item",
      formatter: function (params) {
        return params.value + " " + languageConfig.RealTimeVisitors;
      },
    },

    legend: {
      orient: "horizontal",
      left: "0%",
      bottom: "5%",
      type: "plain",
      width: "50%",
      itemWidth: 20, // 设置图例宽度为30像素
      itemHeight: 20, // 设置图例高度为30像素
      icon: "rect", // 将图例改为方块形状
      itemStyle: {
        borderRadius: 5, // 设置图例圆角
      },
      textStyle: {
        overflow: "breakAll",
        width: 100,
      },
    },
    series: [
      {
        name: "",
        type: "pie",
        radius: ["40%", "75%"],
        center: ["75%", "50%"],
        avoidLabelOverlap: false,
        padAngle: 5,
        itemStyle: {
          borderRadius: 2,
          borderColor: "#fff",
          borderWidth: 6,
        },
        label: {
          show: true,
          position: "inside",
          formatter: "{d}%",
          fontSize: 9,
          fontWeight: "normal",
          color: "#303030",
        },
        // emphasis: {
        //   label: {
        //     show: true,
        //     position: "inside",
        //     formatter: "{d}%",
        //     fontSize: 9,
        //     fontWeight: "normal",
        //     color: "#303030",
        //   },
        // },
        labelLine: {
          show: false,
        },
        data: seriesData,
      },
    ],
  };
  return option;
};

export const getGenderRanger = (data, languageConfig) => {
  let colors = {
    1: "#A7DFF8", // 男性颜色
    2: "#FF9FB2", // 女性颜色
  };

  let seriesData = data
    .filter((item) => {
      return item.viewCount > 0;
    })
    .map((item) => {
      return {
        value: item.viewCount,
        name:
          item.gender === "1"
            ? languageConfig.RealTimeMale
            : languageConfig.RealTimeFemale,
        itemStyle: {
          color: colors[item.gender],
        },
      };
    });

  const option = {
    title: [
      {
        text: languageConfig.RealTimeGenderPercentageOf,
        left: "0%",
        top: "5%",
        textStyle: {
          fontSize: 25,
          fontWeight: "bold",
          color: "#000000",
        },
      },
      {
        text: languageConfig.RealTimeGenderPercentage,
        left: "0%",
        top: "20%",
        textStyle: {
          fontSize: 25,
          fontWeight: "bold",
          color: "#000000",
        },
      },
    ],
    graphic: [
      {
        type: "group",
        left: "0%",
        top: "35%",
        children: [
          {
            type: "line",
            shape: {
              x1: 0,
              y1: 0,
              x2: 130,
              y2: 0,
            },
            style: {
              stroke: "#D5D5D5",
              lineWidth: 1,
            },
          },
        ],
      },
    ],

    tooltip: {
      trigger: "item",
      formatter: function (params) {
        return params.value + " " + languageConfig.RealTimeVisitors;
      },
    },

    legend: {
      orient: "horizontal",
      left: "0%",
      bottom: "5%",
      type: "plain",
      width: "50%",

      itemWidth: 20, // 设置图例宽度为30像素
      itemHeight: 20, // 设置图例高度为30像素
      icon: "rect", // 将图例改为方块形状
      itemStyle: {
        borderRadius: 5, // 设置图例圆角
      },

      textStyle: {
        overflow: "breakAll",
        width: 100,
      },
    },
    series: [
      {
        name: "",
        type: "pie",
        radius: ["40%", "75%"],
        center: ["75%", "50%"],
        avoidLabelOverlap: false,
        padAngle: 5,
        itemStyle: {
          borderRadius: 2,
          borderColor: "#fff",
          borderWidth: 6,
        },
        label: {
          show: true,
          position: "inside",
          formatter: "{d}%",
          fontSize: 9,
          fontWeight: "normal",
          color: "#303030",
        },
        // emphasis: {
        //   label: {
        //     show: true,
        //     position: "inside",
        //     formatter: "{d}%",
        //     fontSize: 9,
        //     fontWeight: "normal",
        //     color: "#303030",
        //   },
        // },
        labelLine: {
          show: false,
        },
        data: seriesData,
      },
    ],
  };

  return option;
};

export const getAgeGroupLine = (data, languageConfig) => {
  let colors = {
    "0-11": "#78BC27",
    "12-17": "#9FD85B",
    "18-34": "#A7DFF8",
    "35-54": "#94BCFF",
    "55+": "#C2C2FF",
  };

  let key = ["0-11", "12-17", "18-34", "35-54", "55+"];

  let seriesData = key
    .filter((key) => {
      let value = data[key];
      if (value) {
        return true;
      } else {
        return false;
      }
    })
    .map((key) => {
      return {
        name: languageConfig.RealTimeAge + " " + key,
        type: "line",
        data: data[key],
        smooth: true, // 设置折线为平滑
        symbol: "none", // 不显示折线上的点
        lineStyle: {
          color: colors[key],
          width: 3,
        },
        itemStyle: {
          color: colors[key],
        },
      };
    });

  const option = {
    title: {
      text: `{bold|${languageConfig.RealTime_Entry_Flow}}{tt|${languageConfig.RealTime_Trend}}`,
      left: "left",
      top: "2px",
      fontWeight: 500,
      textStyle: {
        rich: {
          bold: {
            fontWeight: "bold",
              color:'#000000',
            fontSize: 20,
          },
          tt: {
            fontWeight: "bold",
            color:'#000000',
            fontSize: 20,
          },
        },
      },
    },
    tooltip: {
      trigger: "axis",
    },
    legend: {
      bottom: "0%",
      left: "0%",
      itemWidth: 20, // 设置图例宽度为30像素
      itemHeight: 20, // 设置图例高度为30像素
      icon: "rect", // 将图例改为方块形状
      itemStyle: {
        borderRadius: 5, // 设置图例圆角
      },
    },

    grid: {
      left: "3%", // 将左边距缩小到3%
      right: "3%", // 将右边距缩小到3%
      bottom: "16%", // 保留足够的底部空间给图例
      containLabel: true, // 确保刻度标签在图表区域内
    },

    xAxis: {
      type: "category",
      data: data.times,
    },
    yAxis: {
      type: "value",
    },
    series: seriesData,
  };

  return option;
};

export const getGenderGroupBar = (data, languageConfig) => {
  let colors = {
    1: "#A7DFF8", // 男性颜色
    0: "#FF9FB2", // 女性颜色
  };

  // data = [{ time: '00:00', male: 10, female: 15 }]
  // const times = data.map(item => item.time);
  // const maleData = data.map(item => item.male);
  // const femaleData = data.map(item => item.female);

  const option = {
    title: {
      text: `{bold|${languageConfig.RealTime_Gender}}{tt|${languageConfig.RealTime_Distribution}}`,
      left: "left",
      top: "2px",
      fontWeight: 500,
      textStyle: {
        rich: {
          bold: {
            fontWeight: "bold",
            fontSize: 20,
              color:'#000000'
          },
          tt: {
            fontWeight: "bold",
            fontSize: 20,
              color:'#000000'
          },
        },
      },
    },

    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      left: "3%", // 将左边距缩小到3%
      right: "3%", // 将右边距缩小到3%
      bottom: "16%", // 保留足够的底部空间给图例
      containLabel: true, // 确保刻度标签在图表区域内
    },
    legend: {
      bottom: "0%",
      left: "0%",
      itemWidth: 20, // 设置图例宽度为30像素
      itemHeight: 20, // 设置图例高度为30像素
      icon: "rect", // 将图例改为方块形状
      itemStyle: {
        borderRadius: 5, // 设置图例圆角
      },
    },
    xAxis: {
      type: "category",
      data: data.times || [],
    },
    yAxis: {
      type: "value",
    },
    series: [
      {
        name: languageConfig.RealTimeMale,
        type: "bar",
        stack: "total",
        data: data[1] || [],
        itemStyle: {
          color: colors["1"],
        },
      },
      {
        name: languageConfig.RealTimeFemale,
        type: "bar",
        stack: "total",
        data: data[2] || [],
        itemStyle: {
          color: colors["0"],
        },
      },
    ],
  };
  return option;
};

export const getStayTime = (data, languageConfig) => {
  var option = {
    title: {
      text: `{tt|${languageConfig.duration}}{bold|${languageConfig.Trend}}`,
      left: "left",
      top: "2px",
      fontWeight: 500,
      textStyle: {
        rich: {
          bold: {
            fontWeight: "bold",
            fontSize: 20,
            color:'#000000'
          },
          tt: {
            fontWeight: "bold",
            fontSize: 20,
            color:'#000000'
          },
        },
      },
    },
    tooltip: {
      trigger: "axis",
    },
    legend: {
      bottom: "0%",
      left: "0%",
      itemWidth: 20, // 设置图例宽度为30像素
      itemHeight: 20, // 设置图例高度为30像素
      icon: "rect", // 将图例改为方块形状
      itemStyle: {
        borderRadius: 5, // 设置图例圆角
      },
    },
    grid: {
      left: "3%", // 将左边距缩小到3%
      right: "3%", // 将右边距缩小到3%
      bottom: "16%", // 保留足够的底部空间给图例
      containLabel: true, // 确保刻度标签在图表区域内
    },
    xAxis: {
      type: "category",
      data: data.times || [],
      axisLabel: {
        formatter: function(value) {
          return value + 's';  // 在每个标签后添加 's'
        }
      }
    },
    yAxis: {
      type: "value",
    },
    series: [
      {
        name: languageConfig.Trend,
        type: "line",
        data:  data.values||[] ,
        smooth: true, // 设置折线为平滑
        symbol: "none", // 不显示折线上的点
        lineStyle: {
          color:'#60B5E8',
          width: 3,
        },
        itemStyle: {
          color: '#60B5E8',
        },
      }
    ],
  };

  return option;
};
