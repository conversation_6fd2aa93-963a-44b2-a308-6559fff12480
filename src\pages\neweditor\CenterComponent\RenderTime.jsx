import React from 'react'
import { Grid } from "@mui/material";
import Render<PERSON>om<PERSON> from "./RenderCommon";
import { useEffect, useState } from "react";
 import { message } from '../common/i18n.js'

const RenderTime = (props) => {
  const info = props.info;
  const [timerId, setTimerId] = useState(null);
  const [y, setTimeInfoy] = useState("");
  const [M, setTimeInfoM] = useState("");
  const [d, setTimeInfod] = useState("");
  const [H, setTimeInfoH] = useState("");
  const [m, setTimeInfom] = useState("");
  const [s, setTimeInfos] = useState("");
  const [whichDay, setWhichDTimeInfoa] = useState("");

  const setVal = () => {
    const now = new Date();
    let y = now.getFullYear();
    let M =
      now.getMonth() + 1 > 9 ? now.getMonth() + 1 : "0" + (now.getMonth() + 1);
    let d = now.getDate() > 9 ? now.getDate() : "0" + now.getDate();
    let H = now.getHours() > 9 ? now.getHours() : "0" + now.getHours();
    let m = now.getMinutes() > 9 ? now.getMinutes() : "0" + now.getMinutes();
    let s = now.getSeconds() > 9 ? now.getSeconds() : "0" + now.getSeconds();
    let whichDay = "7123456".charAt(new Date().getDay());
    setTimeInfoy(y);
    setTimeInfoM(M);
    setTimeInfod(d);
    setTimeInfoH(H);
    setTimeInfom(m);
    setTimeInfos(s);
    setWhichDTimeInfoa(whichDay);
  };

  useEffect(() => {
    if (timerId) {
      clearInterval(timerId);
    }
    let time = setInterval(setVal, 1000);
    setTimerId(time);
    return () => {
      if (timerId) {
        clearInterval(timerId);
      }
    };
  }, []);

  if (info.hide) {
    return "";
  }

  return (
    <RenderCommon {...props}>
      <Grid
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100%",
        }}
      >
        <Grid
          className={`animated ${info.anim}`}
          sx={{
            width: "100%",
            height: "100%",
            backgroundColor: info.bgColor,
            opacity: info.transparency,
            overflow: "hidden",
          }}
        >
          <Grid
            style={{
              width: "100%",
              height: "100%",
              fontSize: info.fontSize,
              lineHeight:info.fontSize+'px',
              fontFamily: info.font,
              color: info.fontColor,
              fontWeight: info.isBold ? "bold" : "normal",
              // fontStyle: info.isItaly ? "italic" : "normal",
              textAlign: info.textAlign,
              alignItems: "center",
              display: "flex",
              justifyContent: "center",
            }}
          >
            {(info.format === "" ||
              info.format === undefined ||
              info.format === null) && (
              <span>
                {`${y}-${M}-${d} ${H}:${m}:${s} `}
                {message("editor_week_" + `${whichDay}`)}
              </span>
            )}
            {info.format === "0" && (
              <span>
                {`${y}-${M}-${d} ${H}:${m}:${s} `}
                {message("editor_week_" + `${whichDay}`)}
              </span>
            )}
            {info.format === "1" && <span>{`${y}`}</span>}
            {info.format === "2" && <span>{`${y}-${M}`}</span>}
            {info.format === "3" && <span>{`${y}-${M}-${d}`}</span>}
            {info.format === "4" && <span>{`${y}-${M}-${d} ${H}`}</span>}
            {info.format === "5" && <span>{`${y}-${M}-${d} ${H}:${m}`}</span>}
            {info.format === "6" && (
              <span>{`${y}-${M}-${d} ${H}:${m}:${s}`}</span>
            )}
            {info.format === "7" && (
              <span>
                {`${y}-${M}-${d} `}
                {message("editor_week_" + `${whichDay}`)}
              </span>
            )}
            {info.format === "8" && <span>{`${M}-${d} ${H}:${m}`}</span>}
            {info.format === "9" && <span>{`${M}-${d}`}</span>}
            {info.format === "10" && <span>{`${H}:${m}`}</span>}
          </Grid>
        </Grid>
      </Grid>
    </RenderCommon>
  );
};

export default RenderTime;
