.container {
  position: relative;
  display: inline-block; /* 使容器根据内容自动调整尺寸 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.text {
  position: absolute;
  top: 44%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  width: 100%; /* 让文本宽度占满容器 */
}

.text:before {
  content: ""; /* 创建一个伪元素 */
  display: inline-block;
  vertical-align: middle;
  height: 100%;
}

.text span {
  display: inline-block;
  vertical-align: middle;
  font-size: 16px;
  color: #fff;
}

.flow {
  position: relative;
  padding-left: 12px;
}

.flow::before {
  content: "";
  position: absolute;
  top: 30%;
  left: 0;
  height: 40%;
  width: 1px;
  background-color: #eceff4;
}
