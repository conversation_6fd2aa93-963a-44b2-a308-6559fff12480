import React, {
    useState,
    useEffect,
    forwardRef,
} from "react";
import LoadingButton from "@mui/lab/LoadingButton";
import {
    Stack,
    Alert,
    Grid,
    Checkbox,
    Button,
    RadioGroup,
    FormGroup,
    FormControlLabel,
    Radio,
    Typography,
    InputLabel,
} from "@mui/material";
import {
    BootstrapDialog,
    BootstrapDialogTitle,
    BootstrapContent,
    BootstrapActions,
} from "@/components/dialog";
import { useTranslation } from "react-i18next";
const ExportDialog = forwardRef((props, ref) => {
    const { t } = useTranslation();
    const { open, onClose, columns, onExport,title } = props;
    const [selectAll, setSelectAll] = useState(false);
    const [selectedColumns, setSelectedColumns] = useState([]);
    const [formateColumns, setFormateColumns] = useState([]);
    const [exportType, setExportType] = useState("0");
    const [loading, setLoading] = useState(false);
    const handleClose = () => {
        onClose();
    };
    const handleSelectAllChange = (event) => {
        setSelectAll(event.target.checked);
        if (event.target.checked) {
            setSelectedColumns(formateColumns.map((column) => column.value));
        } else {
            setSelectedColumns([]);
        }
    };

    const handleColumnChange = (event) => {
        const columnValue = event.target.value;
        const isColumnSelected = selectedColumns.includes(columnValue);

        let updatedSelectedColumns;

        if (isColumnSelected) {
            updatedSelectedColumns = selectedColumns.filter(
                (value) => value !== columnValue
            );
        } else {
            updatedSelectedColumns = [...selectedColumns, columnValue];
        }

        setSelectedColumns(updatedSelectedColumns);
        setSelectAll(updatedSelectedColumns.length === formateColumns.length);
    };

    useEffect(() => {
        if (open && columns) {
            console.log(columns);
            const dataColumns = [];
            columns?.forEach((column) => {
                dataColumns.push({
                    value: column?.accessorKey,
                    label: column?.header,
                });
            });
            setFormateColumns(dataColumns);
        }
    }, [open, columns]);

    const handleExport = () => {
        // 从selectedColumns中获取所选字段
        const selectedFields = formateColumns.filter((column) =>
            selectedColumns.includes(column.value)
        );
        // 获取勾选的类型
        console.log(selectedFields, exportType, selectAll);
        const selecteds = [];
        selectedFields?.forEach(item=>{
            selecteds.push(item?.value)
        })
        // 构造数据返回
        const exportRequestData = {
            exportType: exportType,
            exportFields: selectAll ? "" : selecteds.join(","),
        };
        setLoading(true);
        onExport(exportRequestData)
            .then(() => {
                setLoading(false);
            })
            .catch(() => {
                setLoading(false);
            });
    };

    return (
        <>
            <BootstrapDialog
                fullWidth
                onClose={handleClose}
                aria-labelledby="customized-dialog-title"
                open={open}
            >
                <BootstrapDialogTitle>
                    <Typography variant="h4" component="p">
                        {title}
                    </Typography>
                </BootstrapDialogTitle>
                <BootstrapContent dividers>
                    <Grid container spacing={3}>
                        <Grid item xs={6}>
                            <Stack spacing={1}>
                                <InputLabel htmlFor="store-name">
                                    {t('common.common_export_type')}
                                    <i style={{ color: "red" }}>*</i>
                                </InputLabel>
                                <RadioGroup
                                    row
                                    value={exportType}
                                    onChange={(event) =>
                                        setExportType(event.target.value)
                                    }
                                    aria-labelledby="demo-row-radio-buttons-group-label"
                                    name="row-radio-buttons-group"
                                >
                                    <FormControlLabel
                                        value="1"
                                        control={<Radio />}
                                        label= {t('common.common_export_type_one')}
                                    />
                                    <FormControlLabel
                                        value="0"
                                        control={<Radio />}
                                        label={t('common.common_export_type_zero')}
                                    />
                                </RadioGroup>
                            </Stack>
                        </Grid>
                    </Grid>
                    <Grid container spacing={3}>
                        <Grid item xs={12}>
                            <Stack spacing={1}>
                                <InputLabel htmlFor="store-name">
                                {t('common.common_export_field')}
                                    <i style={{ color: "red" }}>*</i>
                                </InputLabel>
                                <div>
                                    <FormControlLabel
                                        control={
                                            <Checkbox
                                                checked={selectAll}
                                                onChange={handleSelectAllChange}
                                                defaultChecked
                                            />
                                        }
                                        label={t('ips.ips_all')}
                                    />
                                </div>
                                <FormGroup row={true}>
                                    {formateColumns?.map((column, index) => {
                                        return (
                                            <FormControlLabel
                                                key={index}
                                                control={
                                                    <Checkbox
                                                        checked={selectedColumns.includes(
                                                            column.value
                                                        )}
                                                        onChange={
                                                            handleColumnChange
                                                        }
                                                        value={column.value}
                                                    />
                                                }
                                                label={column?.label}
                                            />
                                        );
                                    })}
                                </FormGroup>
                            </Stack>
                        </Grid>
                    </Grid>
                    <br />
                    <Grid>
                        <Alert severity="warning">
                            {t('common.common_export_tips')}
                        </Alert>
                    </Grid>
                </BootstrapContent>
                <BootstrapActions>
                    <Button
                        color="info"
                        variant="outlined"
                        onClick={handleClose}
                    >
                        {t("common.common_edit_cancel")}
                    </Button>
                    <LoadingButton
                        disableElevation
                        loading={loading}
                        type="submit"
                        variant="contained"
                        color="primary"
                        onClick={handleExport}
                    >
                        {t('common.common_export_btn')}
                    </LoadingButton>
                </BootstrapActions>
            </BootstrapDialog>
        </>
    );
});

export default ExportDialog;
