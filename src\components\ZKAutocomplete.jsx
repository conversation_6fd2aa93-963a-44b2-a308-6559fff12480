import * as React from "react";
import OutlinedInput from "@mui/material/OutlinedInput";
import { useEffect, useState } from "react";
import Autocomplete from "@mui/material/Autocomplete";
import { TextField } from "@mui/material";
import { makeStyles } from "@material-ui/core/styles";
import { FixedSizeList } from "react-window";
const useStyles = makeStyles({
  inputRoot: {
    display: "flex",
    alignItems: "center",
    "& .MuiAutocomplete-endAdornment": {
      top: 0,
      transform: "none",
    },
  },
});
export default function ZKAutocomplete({
  fullWidth = true,
  name,
  value = "",
  id = "ZKAutocomplete",
  data = [],
  onClear = () => {},
  onChange = (event, newValue) => {},
  onBlur = (event) => {},
  error,
  placeholder = "",
  sx,
  noOptionsText = "No options",
  labelField = "label", // 动态标签字段，默认为 "name"
  valueField = "value", // 动态值字段，默认为 "value"
}) {
  const classes = useStyles();

  const [selectedValue, setSelectedValue] = useState(null);

  useEffect(() => {
    if (data) {
      const selectedOption = data?.find((option) => option.value === value);

      setSelectedValue(selectedOption || null);
    }
  }, [value, data]);

  return (
    <Autocomplete
      disablePortal
      id={id}
      value={selectedValue}
      // value={selectedValue ? selectedValue.label : ''}
      options={data}
      filterOptions={(options, { inputValue }) => {
        let result = options.filter((option) => {
          let ra = option?.label?.indexOf(inputValue) !== -1;
          return ra;
        });
        return result;
      }}
      onChange={(event, newValue) => {
        if (newValue) {
          setSelectedValue(newValue);
          onChange(event, newValue);
        } else {
          onClear();
          setSelectedValue(null);
        }
      }}
      onBlur={onBlur}
      // onInputChange={handleInputChange}
      name={name}
      noOptionsText={noOptionsText}
      getOptionLabel={(option) => option[labelField] || ""}
      isOptionEqualToValue={(option, value) =>
        option[valueField] === value[valueField]
      }
      renderOption={(props, option) => {
        return (
          <li {...props} key={option[valueField]}>
            {option[labelField]}
          </li>
        );
      }}
      renderInput={(params) => (
        <OutlinedInput
          error={error}
          size="small"
          sx={{ ...sx }}
          placeholder={placeholder}
          classes={{ root: classes.inputRoot }}
          fullWidth={fullWidth}
          {...params.InputProps}
          inputProps={params.inputProps}
          inputComponent={params.inputComponent}
        />
      )}
    />
  );
}
