/* eslint-disable no-undef */
/* eslint-disable react/prop-types */
/* eslint-disable react-hooks/rules-of-hooks */
import { useEffect, useState, useRef } from "react";
// import { useLocation } from "react-router-dom";
import {
  Grid,
  Card,
  InputAdornment,
  IconButton,
  TextField,
  Typography,
  Stack,
  CircularProgress,
  Box,
} from "@mui/material";
import { Search } from "@mui/icons-material";
import "../store/components/del.less";
// import ChangePasswordTip from "../authentication/auth-forms/ChangePasswordTip";
import ResoureChart from "./ResoureChart";
import ScreenChart from "./ScreenChart";
import RetailerList from "./RetailerTableList";

import MapChart from "./googleMap/Map"; //谷歌地图组件
import CitySelect from "./googleMap/CitySelect"; //谷歌地图 城市选择
// import MapInput from "./googleMap/mapTextAuto"; //谷歌地图 搜索框
import MapInput from "./googleMap/GoogleMaps"; //谷歌地图 搜索框

import BaiduMapChart from "./baiduMap/MapChart"; //百度地图
import BaiduCitySelect from "./baiduMap/CitySelect"; //百度地图城市选择
import MapTextAutoComplete from "./baiduMap/mapTextAutoComplete"; //百度地图搜索框
import Dot from "@/components/@extended/Dot";
import {
  countMaterialStatistics,
  getMaterialDownloadStatistics,
} from "@/service/api/material";
import FlowPie from "./FlowPie";
import FlowLine from "./FlowLine";
import flowIcon from "@/assets/images/icons/ic_yuanxingtu .svg";
import "./index.less";
import FlowSwitch from "./flowSwitch";
import useLoading from "@/hooks/useLoading";
import { listScreenStatistics } from "@/service/api/dashboard";
import { useTranslation } from "react-i18next";
const DashboardDefault = () => {
  const { t } = useTranslation();
  const [loading, toggleLoading] = useLoading();
  //禁止双击左侧菜单栏重新获取地图数据
  // const { state } = useLocation();
  const mapRef = useRef(null);
  const searchRef = useRef(null); //百度地图引用
  //下拉框选中的地址
  const [selectAddress, setSelectAddress] = useState("");
  //输入框选择的地址
  const [inputAddress, setInputAddress] = useState("");
  const [materialData, setMaterialData] = useState({});
  const [materialDownloadData, setMaterialDownloadData] = useState({});
  const [screenStatusList, setScreenStatusList] = useState({});
  const [cash, setCash] = useState(""); //流量使用情况
  const [detailAddress, setDetailAddress] = useState("");
  const [addressData, setAddressData] = useState("");
  const [zoom, setZoom] = useState(4); //谷歌地图画面比例
  // const [zoom, setZoom] = useState(5); //百度地图画面比例
  const [center, setCenter] = useState({});
  const [code, setCode] = useState(undefined);
  const [centerPosition, setCenterPosition] = useState([103.73, 36.03]);
  const [centerData, setCenterData] = useState(null);
  const [coordinates, setCoordinates] = useState({
    lat: 34.932032320983154,
    lng: 103.8510534864727,
  }); //初始化经纬度

  /**
   * @param {*} detailsAddress 省+市+区/县
   * @param {*} zoom  区域的邮政编码
   * @param {*} address 省/市/区
   * 将选中的地址转换为经纬度坐标
   */
  //谷歌地图搜索方法
  const handleSearchGoogle = async (detailsAddress, zoom, address) => {
    setDetailAddress(detailsAddress);
    // 调用百度地图 API 的 geocoder.geocode 方法将地址信息转换为经纬度坐标
    const geocoder = new google.maps.Geocoder();
    geocoder.geocode({ address: address }, function (results, status) {
      if (results) {
        // 获取第一个结果的经纬度坐标
        var location = results[0].geometry.location;
        const selectedLatLng = {
          lat: location.lat(), // 纬度
          lng: location.lng(), // 经度
        };
        setCoordinates(selectedLatLng);
      } else {
        console.error("地理编码失败：" + status);
      }
    });
  };
  // googleMap 处理从搜索框组件获取的经纬度
  const handleCoordinatesChange = (newCoordinates) => {
    setCenterData(newCoordinates);
  };
  //获取项目地址名称
  const handlerDetailAddress = (newDetailAddress) => {
    setAddressData(newDetailAddress);
  };

  //获取当前位置
  const getNowLocation = () => {
    return new Promise((resolve, reject) => {
      const geolocation = new BMapGL.Geolocation();
      geolocation.getCurrentPosition(function (r) {
        if (this.getStatus() === BMAP_STATUS_SUCCESS) {
          setCode(r.address.city);
          resolve(r.point);
        } else {
          toast.error(t("common.common_current_location_error"));
          reject(t("common.common_current_location_error"));
        }
      });
    });
  };
  // 根据坐标得到地址描述
  const Geolocation = async (lng, lat) => {
    return new Promise((resolve, reject) => {
      const myGeo = new BMapGL.Geocoder();
      // 根据坐标得到地址描述
      myGeo.getLocation(new BMapGL.Point(lng, lat), function (result) {
        if (result) {
          resolve(result);
        } else {
          reject(null);
        }
      });
    });
  };

  //百度地图搜索方法
  const handleSearch = (detailsAddress, zoom, address) => {
    // 调用百度地图 API 的 geocoder.geocode 方法将地址信息转换为经纬度坐标
    const geocoder = new BMapGL.Geocoder();
    geocoder.getPoint(
      detailsAddress,
      (point) => {
        if (point) {
          setCenter({
            lng: point.lng,
            lat: point.lat,
          });
          let zoom = 8;
          // console.log(address.indexOf("区"));
          if (address.indexOf("市") > 0) {
            zoom = 10;
          } else if (address.indexOf("区") > 0 || address.indexOf("县") > 0) {
            zoom = 14;
          }
          setCenterPosition([point.lng, point.lat]);
          setZoom(zoom);
          // mapRef.current.setCenterPositionRef([point.lng, point.lat], zoom);
          if (!(address.indexOf("区") > 0 || address.indexOf("县") > 0)) {
            setCode(address);
          }
        }
      },
      detailsAddress
    );
  };

  //选中下拉框选中的地址
  const handleChangeSelectAddress = (address) => {
    console.log("address6666", address);
    setSelectAddress(address);
  };

  //修改输入框选择的地址
  const handleTextOnChange = () => {
    setInputAddress(searchRef.current.value);
  };
  // 流量统计时间范围搜索
  const getDownloadStatistics = (type, startData, endData) => {
    getMaterialDownloadStatistics(type, startData, endData).then((res) => {
      // console.log("修改输入框中的地址", res);
      setMaterialDownloadData(res.data);
      // console.log(99999999999, res.data);
      setCash(res.data.downloadDataList);
    });
  };
  // 鼠标数量统计
  const getListScreenStatistics = (type, startData, endData) => {
    let x = [];
    let y1 = [];
    let y2 = [];
    let y3 = [];
    listScreenStatistics(type, startData, endData).then((res) => {
      res.data.forEach((item) => {
        x.push(item.statisticsDay.slice(5));
        y1.push(item.onlineNum);
        y2.push(item.offlineNum);
        y3.push(item.totalNum);
      });
      setScreenStatusList({ x: x, y1: y1, y2: y2, y3: y3 });
      // setData({ x: x, y1: y1, y2: y2, y3: y3 });
    });
  };

  //百度地图模糊搜索
  const handleChildValueChange = (newValue) => {
    // getNowLocation(newValue).then((point) => {
    //   setCenterPosition(point);
    // });

    // 在子组件的回调函数中获取子组件的值
    const geocoder = new BMapGL.Geocoder();
    geocoder.getPoint(newValue, (point) => {
      if (point) {
        // mapRef.current.setCenterPositionRef([point.lng, point.lat], zoom);
        setCenterPosition([point.lng, point.lat]);
        setZoom(16);
      }
    });
  };
  useEffect(() => {
    const fetchDataAndCloseLoading = async () => {
      countMaterialStatistics().then((res) => {
        setMaterialData(res.data);
      });
      getDownloadStatistics(0, "", "");
      getListScreenStatistics(0, "", "");
      // 假设数据渲染需要一秒钟
      setTimeout(() => {
        toggleLoading(); // 关闭加载状态
      }, 1000);
    };
    fetchDataAndCloseLoading();
  }, []);

  // 加载图标的样式
  const loadingStyle = {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    height: "calc(100vh - 200px)",
  };
  return (
    <div style={loading ? loadingStyle : null}>
      {loading ? (
        <CircularProgress />
      ) : (
        <>
          <Grid item xs={12}>

            {/* 百度地图集成 */}
            {window.localStorage.getItem("localVersion")
              === 'CN' && <Card sx={{ mt: -1 }}>
                <Grid container columnSpacing={2.75}>
                  <Grid item xs={6.3}></Grid>
                  <Grid
                    item
                    xs={12}
                    container
                    direction="row"
                    justifyContent="flex-end"
                    alignItems="flex-end"
                  >
                    <BaiduCitySelect
                      search={handleSearch}
                      changeSelectAddress={handleChangeSelectAddress}
                    />

                    <MapTextAutoComplete
                      code={code}
                      onChildValueChange={handleChildValueChange}
                      detailAddress={detailAddress}
                      placeholder={t("common.common_input_location_search")}
                      sx={{
                        height: "41px",
                        width: "100%",
                        ".MuiAutocomplete-endAdornment": { top: 0 },
                      }}
                    />
                  </Grid>
                </Grid>
                <BaiduMapChart
                  ref={mapRef}
                  centerPosition={centerPosition}
                  setCenterPosition={setCenterPosition}
                  zoom={zoom}
                  setZoom={setZoom}
                />
              </Card>}
            {/* 谷歌地图集成 */}

            {
              window.localStorage.getItem('localVersion') === 'EN' && <Card sx={{ mt: -1 }}>
                <Grid container>
                  <Grid
                    item
                    xs={12}
                    container
                    direction="row"
                    justifyContent="flex-end"
                    alignItems="flex-end"
                  >
                    <CitySelect
                      setZoom={setZoom}
                      search={handleSearchGoogle}
                      changeSelectAddress={handleChangeSelectAddress}
                    ></CitySelect>

                    <MapInput
                      coordinates={coordinates}
                      setCoordinates={setCoordinates}
                      setZoom={setZoom}
                      detailAddress={detailAddress}
                      onCoordinatesChange={handleCoordinatesChange}
                      onDetailAddress={handlerDetailAddress}
                    ></MapInput>
                  </Grid>
                </Grid>
                <MapChart
                  ref={mapRef}
                  centerData={centerData}
                  coordinates={coordinates}
                  setCoordinates={setCoordinates}
                  zoom={zoom}
                  setZoom={setZoom}
                  addressData={addressData}
                />
              </Card>
            }
          </Grid>
          <Grid
            container
            direction="row"
            justifyContent="flex-start"
            alignItems="flex-start"
            columnSpacing={3}
          >
            <Grid item xs={12}>
              <h3>{t("ips.ips_status_monitoring")}</h3>
              <RetailerList />
            </Grid>
            <Grid item xs={12} sm={6} md={6}>
              <h3>{t("ips.ips_signage_number")}</h3>
              <Grid
                item
                xs={12}
                sm={12}
                md={12}
                container
                sx={{
                  border: "1px solid #eaf0f5",
                  background: "white",
                  paddingTop: 2,
                }}
                direction="column"
                justifyContent="center"
              // alignItems="center"
              >
                <FlowSwitch
                  selectSx={4}
                  rangeSx={5}
                  sx={{ paddingRight: "5%" }}
                  // defaultHandle={() => {
                  //   getDownloadStatistics(value, "", "");
                  // }}
                  switchHandle={(value) => {
                    getListScreenStatistics(value, "", "");
                  }}
                  switchDate={(startDate, endDate) => {
                    getListScreenStatistics("", startDate, endDate);
                  }}
                />
                <ScreenChart data={screenStatusList} />
              </Grid>
            </Grid>

            <Grid item xs={12} sm={6} md={6}>
              <h3>{t("menu.media_advertising_resources")}</h3>
              <Card
                variant="outlined"
                sx={{
                  border: "1px solid #eaf0f5",
                  paddingTop: 2,
                }}
              >
                <Grid container>
                  <Grid
                    item
                    xs={12}
                    sm={6}
                    md={6}
                    sx={{
                      position: "relative",
                      paddingRight: "30px",
                    }}
                  >
                    <ResoureChart
                      data={[
                        {
                          value: materialData.image ? materialData.image : 0,
                          name: t("ips.ips_picture"),
                          type: "image",
                        },
                        {
                          value: materialData.media ? materialData.media : 0,
                          name: t("ips.ips_media"),
                          type: "media",
                        },
                      ]}
                    />

                    <span
                      style={{
                        content: "",
                        position: "absolute",
                        right: "15px", // 调整位置
                        top: "60px",
                        bottom: 0,
                        height: "250px",
                        width: "2px", // 线的宽度
                        background: "#f0f0f0", // 线的颜色
                      }}
                    ></span>
                  </Grid>
                  <Grid item style={{ display: "flex" }} xs={12} sm={6} md={6}>
                    <FlowPie materialData={materialData} />
                    <Grid item direction="column" alignItems="center">
                      <Box
                        style={{
                          width: "10vw",
                          height: "10vw",
                          marginLeft: "10px",
                        }}
                      >
                        <Stack
                          direction="row"
                          alignItems="center"
                          spacing={1}
                          style={{ marginTop: "140px" }}
                        >
                          <Dot color="#F12435" />
                          <Typography style={{ fontSize: "12px" }}>
                            {t("ips.ips_sys_total_storage")} :
                            {materialData.sysTotalStorageStr}
                          </Typography>
                        </Stack>
                        <Stack
                          direction="row"
                          spacing={1}
                          alignItems="center"
                          paddingTop={0.5}
                        >
                          <Dot color="#73d13d" />
                          <Typography style={{ fontSize: "12px" }}>
                            {t("ips.ips_sys_used_storage")} :
                            {materialData.sysUsedStorageStr}
                          </Typography>
                        </Stack>
                        <Stack
                          direction="row"
                          spacing={1}
                          alignItems="center"
                          paddingTop={0.5}
                        >
                          <Dot color="#d9f7be" />
                          <Typography style={{ fontSize: "12px" }}>
                            {t("ips.ips_system_last")} :
                            {materialData.sysSurplusStorageStr}
                          </Typography>
                        </Stack>
                        <Stack
                          direction="row"
                          spacing={1}
                          alignItems="center"
                          paddingTop={0.5}
                        >
                          <Dot color="#C8C9E9" />
                          <Typography style={{ fontSize: "12px" }}>
                            {t("ips.ips_self_use")} :
                            {materialData.currentSizeStr}
                          </Typography>
                        </Stack>
                      </Box>
                    </Grid>
                  </Grid>
                </Grid>
              </Card>
            </Grid>
            <Grid item xs={12} sm={12} md={12}>
              <h3>{t("ips.ips_traffic_statistics")}</h3>
              <Card
                variant="outlined"
                sx={{ border: "1px solid #eaf0f5", paddingTop: 2 }}
              >
                <Grid
                  sx={{ width: "100%" }}
                  container
                  direction="row"
                  justifyContent="flex-start"
                  alignItems="center"
                >
                  <Grid
                    item
                    xs={12}
                    sm={8}
                    md={8}
                    container
                    direction="column"
                    justifyContent="flex-end"
                    alignItems="left"
                  >
                    <FlowSwitch
                      sx={{ paddingRight: "5%" }}
                      switchHandle={(value) => {
                        getDownloadStatistics(value, "", "");
                      }}
                      switchDate={(startDate, endDate) => {
                        getDownloadStatistics("", startDate, endDate);
                      }}
                    />
                    <FlowLine materialDownload={materialDownloadData} />
                  </Grid>
                  <Grid item xs={8} sm={6} md={4}>
                    <div className="container">
                      <img src={flowIcon}></img>
                      <div className="text">
                        <Typography
                          variant="h5"
                          sx={{
                            fontSize: "1.25rem",
                            color: "#7AC143",
                            fontWeight: "bold",
                            lineHeight: "20px",
                          }}
                        >
                          {materialDownloadData.usedData}
                        </Typography>
                        <Typography
                          sx={{
                            fontSize: "0.8rem",
                            fontFamily: "Source Han Sans CN",
                            fontWeight: "400",
                            color: "#92A5B7",
                            lineHeight: "24px",
                            marginTop: "1%",
                          }}
                        >
                          {t("ips.ips_system_used")}
                        </Typography>
                      </div>
                    </div>
                    <Box
                      item
                      xs={12}
                      sm={2}
                      md={2}
                      direction="row"
                      alignItems="center"
                      justifyContent="center"
                      style={{ display: "flex", marginTop: "30px" }}
                    >
                      <Stack
                        direction="row"
                        spacing={1}
                        alignItems="center"
                        justifyContent="center"
                        style={{ marginRight: "20px" }}
                      >
                        <Dot color="#F12435" />
                        <Typography style={{ fontSize: "12px" }}>
                          {t("common.common_system_flow")} :
                          {materialDownloadData.totalDownload}
                        </Typography>
                      </Stack>

                      <Stack
                        direction="row"
                        spacing={1}
                        alignItems="center"
                        justifyContent="center"
                        style={{ marginRight: "20px" }}
                      >
                        <Dot color="#d9f7be" />
                        <Typography style={{ fontSize: "12px" }}>
                          {t("ips.ips_system_last")} :
                          {materialDownloadData.surplusData}
                        </Typography>
                      </Stack>
                      <Stack
                        direction="row"
                        justifyContent="center"
                        spacing={1}
                        alignItems="center"
                      >
                        <Dot color="#C8C9E9" />
                        <Typography style={{ fontSize: "12px" }}>
                          {t("ips.ips_self_use")} :
                          {materialDownloadData.personUsed}
                        </Typography>
                      </Stack>
                    </Box>
                  </Grid>
                </Grid>
              </Card>
            </Grid>
          </Grid>
        </>
      )}
      {/* {null === state ? null : <ChangePasswordTip from={state.from} />} */}
    </div>
  );
};

export default DashboardDefault;
