import React from 'react'
import {  Grid } from "@mui/material";
import Render<PERSON><PERSON><PERSON> from "./RenderCommon";
import VideoPlay from "./VideoPlay";
import {  useEffect, useState } from "react";
const RenderVideo = (props) => {
  let info = props.info;
  const [currentUrl, setCurrentUrl] = useState("");
  const [posterUrl, setPosterUrl] = useState("");
  const [playIndex, setPlayIndex] = useState(0);
  const [duration, setDuration] = useState(0);

  const [videoList, setVideoList] = useState([]);

  const initPlayInfo = () => {
    let len = videoList.length;
    if (len > 0 && playIndex < len) {
      let videoInfo = videoList[playIndex];
      let url = videoInfo.url;
      let poster = videoInfo.coverImage;
      setCurrentUrl(url);
      setPosterUrl(poster);
      if (videoInfo.duration) {
        setDuration(parseInt(videoInfo.duration));
      }
    } else {
      setCurrentUrl("");
      setPosterUrl("");
    }
  };

  useEffect(() => {
    initPlayInfo();
  }, [playIndex]);

  useEffect(() => {
    if (videoList.length > 0 && playIndex >= videoList.length) {
      setPlayIndex(videoList.length - 1);
    } else if (videoList.length === 0) {
      setPlayIndex(0);
    }
    initPlayInfo();
  }, [videoList]);

  useEffect(() => {
    setVideoList([...info.videoList]);
  }, [info]);

  const playEnd = () => {
    let len = videoList.length;
    let newIndex = playIndex + 1;
    if (newIndex >= len) {
      setPlayIndex(0);
    } else {
      setPlayIndex(newIndex);
    }
  };

  return (
    <RenderCommon {...props}>
      {
        <Grid
          className={`animated ${info.anim}`}
          sx={{
            width: "100%",
            height: "100%",
            backgroundColor: info.bgColor,
            overflow: "hidden",
            opacity: info.transparency,
          }}
        >
          <VideoPlay
            playIndex={playIndex}
            duration={duration}
            videoSize = {videoList.length}
            playEnd={playEnd}
            currentUrl={currentUrl}
            posterUrl={posterUrl}
          />
        </Grid>
      }
    </RenderCommon>
  );
};

export default RenderVideo;
