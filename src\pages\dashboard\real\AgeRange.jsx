import { useEffect, useRef, useState } from "react";
import * as echarts from "echarts";
import { useTranslation } from "react-i18next";
const AgeRange = (props) => {
  const { t } = useTranslation();
  const chartRef = useRef(null);
  const myChartRef = useRef(null);

  const initChart = () => {
    let chart = echarts.init(chartRef.current, null, { renderer: "svg" });
    myChartRef.value = chart;
    // 设置初始大小
    chart.resize();
    // 监听窗口大小变化，自动调整图表大小
    window.addEventListener("resize", handleResize);
    const options = getOptions(props.ageRange);
    chart.setOption(options);
  };

  const handleResize = () => {
    if (myChartRef.value !== null) {
      myChartRef.value.resize();
    }
  };

  useEffect(() => {
    // 在组件挂载时进行初始化
    initChart();
    return () => {
      window.removeEventListener("resize", handleResize);
      if (myChartRef.value) {
        myChartRef.value.dispose();
        myChartRef.value = null;
      }
    };
  }, []);

  useEffect(() => {
    if (myChartRef.value === null) {
      initChart();
    } else {
      const options = getOptions(props.ageRange);
      myChartRef.value.setOption(options);
    }
  }, [props.ageRange]);

  const getOptions = (data) => {
    var colors = {
      "0-11": '#5470c6',
      "12-17":'#91cc75', 
      "18-34":'#fac858',  
      "35-54":'#ee6666', 
      "55+":  '#73c0de', 
    };

    let result = data
      ?.filter((item) => {
        if (item.number > 0) {
          return true;
        } else {
          return false;
        }
      })
      ?.map((item) => {
        let color =  colors[item.fieldName]
        return {
          name: item.fieldName,
          value: item.number,
          itemStyle: {
            color:color
          },
        };
      });
    if (!result) {
      result = [];
    }
    return {
      title: {
        text: t("realTime.ageTitle"),
        textStyle: {
          color: "#86909c",
          fontStyle: "normal",
          fontWeight: "normal",
          fontFamily: "Arial",
          fontSize: 14,
        },
      },
      tooltip: {
        trigger: "item",
        formatter: t("realTime.ageTooltip"),
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "5%",
        containLabel: true,
      },
      legend: {
        orient: "vertical",
        left: "left",
        top: "middle",
        itemGap: 15,
        formatter: function (name) {
          return t("realTime.ageLgen", {
            name,
          });
        },
      },
      series: [
        {
          name: data.length > 0 ? "AgeRange" : "",
          type: "pie",
          radius: "90%",
          center: ["50%", "50%"],
          labelLine: {
            show: false, // 不显示标签的连线
          },
          label: {
            show: true,
            position: "inside", // 设置标签位置为饼图内部
            formatter: "{d}%", // 标签文本格式器，这里显示名称、数值和百分比
          },
          data: result,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: "rgba(0, 0, 0, 0.5)",
            },
          },
        },
      ],
    };
  };
  return (
    <div
      style={{
        width: "100%",
        boxSizing: "border-box",
        position: "relative",
        border: "1px solid #e5e6eb",
        borderRadius: "20px",
        height: "100%",
        padding: "10px",
        backgroundColor: "#ffffff",
      }}
    >
      <div
        style={{
          width: "100%",
          height: "100%",
        }}
        ref={chartRef}
      ></div>
    </div>
  );
};

export default AgeRange;
