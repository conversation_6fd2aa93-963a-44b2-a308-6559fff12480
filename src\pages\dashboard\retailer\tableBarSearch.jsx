/* eslint-disable react/prop-types */
import React, { useState, useEffect, useRef } from "react";
import { Stack, Button, Box, FormControl, TextField } from "@mui/material";
import { useTranslation } from "react-i18next";
import Treeselect from "@/components/zktreeselect";
import { getTreeSelect } from "@/service/api/L3Sevice";
import RegionZKSelect from "../RegionZKSelect";

const TableBarSearch = (props) => {
  const { t } = useTranslation();
  //是否显示区域选择
  const [loadingArea, setLoadingArea] = useState(props.loadingArea);
  const regionRef = useRef(null);
  const storeRef = useRef(null);
  //区域列表
  const [areaList, setAreaList] = useState([]);
  //选中的区域值
  const [area, setArea] = useState("");
  //调用搜索
  const { handleGetTableData } = props;
  const [storeName, setStoreName] = useState("");
  //获取区域列表
  const handleListAreaByUserTree = () => {
    getTreeSelect("1").then((res) => {
      setAreaList(res.data);
    });
  };
  const storeNameOnChange = () => {
    setStoreName(storeRef.current.value);
  };
  useEffect(() => {
    if (loadingArea) {
      handleListAreaByUserTree();
    }
  }, []);
  const handleClear = () => {
    regionRef.current.handleClear();
    setStoreName("");
    handleGetTableData({});
  };
  return (
    <Box sx={{ paddingRight: "10px" }}>
      <Stack
        spacing={2}
        direction="row"
        justifyContent="flex-start"
        alignItems="center"
      >
        <RegionZKSelect ref={regionRef} />
        {loadingArea && (
          <FormControl sx={{ m: 1, minWidth: 200, maxWidth: 250 }} size="small">
            <Treeselect
              data={areaList}
              optionValue="id"
              optionLabel="name"
              name="areaId"
              onClear={() => {}}
              placeholder={t("ips.ips_enter_region")}
              onChange={(valuas) => {
                setArea(valuas.id);
              }}
              disableParent={true}
            />
          </FormControl>
        )}

        <TextField
          label={t("ips.ips_store_outlet")}
          size="small"
          type="text"
          name="storeName"
          fullWidth
          placeholder={t("common.common_please_input_store_name")}
          value={storeName}
          onChange={storeNameOnChange}
          inputRef={storeRef}
        />
        <Button
          disableElevation
          type="submit"
          variant="contained"
          size="small"
          onClick={() => {
            const regionInfo = regionRef.current.handleGetRegionInfo();
            const searchData = { ...regionInfo, storeName, areaId: area };
            handleGetTableData(searchData);
          }}
        >
          {t("common.common_table_query")}
        </Button>
        <Button
          variant="outlined"
          color="info"
          size="small"
          sx={{
            minWidth:'90px'
          }}
          onClick={() => {
            handleClear();
          }}
        >
          {t("common.common_op_reset")}
        </Button>
      </Stack>
    </Box>
  );
};
export default TableBarSearch;
