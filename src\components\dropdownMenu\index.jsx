/* eslint-disable react/jsx-key */
import React, { useState } from "react";
import { Button, Link, Menu, MenuItem, Stack } from "@mui/material";
import AuthButton from "@/components/AuthButton";
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import PropTypes from 'prop-types';
const DropdownMenu = ({
  btnText = "操作",
  tagger = "link",
  options = [
    {
      text: "",
      icon: undefined,
      key: "",
      authCode: "",
    },
  ],
  onActions = () => { },
}) => {
  const [anchorEl, setAnchorEl] = useState(null);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };
  const handleOptionClick = (option) => {
    onActions(option.key);
    setAnchorEl(null);
  };
  // 获取options的方法
  const optionItems = typeof options === 'function' ? options() : options;



  return (
    <div>
      {
        tagger === 'button' ? <Button type="submit"
          endIcon={<ArrowDropDownIcon />}
          variant="contained" onClick={handleClick}>
            {btnText}
        </Button> : <Link component="button" underline="none" onClick={handleClick}>
          {btnText}
        </Link>
      }

      <Menu anchorEl={anchorEl} open={Boolean(anchorEl)} onClose={handleClose}>
        {optionItems.map((option, index) => {
          if (option?.authCode) {
            return (
              <AuthButton key={option.key + index} button={option.authCode}>
                <MenuItem onClick={() => handleOptionClick(option)}>
                  <Stack
                    direction="row"
                    spacing={1}
                    alignItems={"center"}
                    justifyContent={"center"}
                  >
                    {option?.icon && option.icon}
                    <div>{option.text}</div>
                  </Stack>
                </MenuItem>
              </AuthButton>
            );
          } else {
            return (
              <MenuItem
                key={option.key + index}
                onClick={() => handleOptionClick(option)}
              >
                <Stack
                  direction="row"
                  spacing={1}
                  alignItems={"center"}
                  justifyContent={"center"}
                >
                  {option?.icon && option.icon}
                  <div> {option.text}</div>
                </Stack>
              </MenuItem>
            );
          }
        })}
      </Menu>
    </div>
  );
};

// PropTypes定义选项的类型
DropdownMenu.propTypes = {
  btnText: PropTypes.string,
  tagger: PropTypes.string,
  options: PropTypes.oneOfType([
    PropTypes.arrayOf(
      PropTypes.shape({
        text: PropTypes.string.isRequired,
        icon: PropTypes.node,
        key: PropTypes.string.isRequired,
        authCode: PropTypes.string,
      })
    ),
    PropTypes.func,
  ]),
  onActions: PropTypes.func,
};

export default DropdownMenu;
