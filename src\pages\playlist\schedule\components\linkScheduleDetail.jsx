/* eslint-disable react/prop-types */
import React, {
  useState,
  useRef,
  forwardRef,
  useImperativeHandle,
} from "react";
import {
  Grid,
  Paper,
  Button,
  InputLabel,
  OutlinedInput,
  Stack,
  InputAdornment,
  InputBase,
  Typography,
  Container,
} from "@mui/material";
import "./link.less";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import { useFormik } from "formik";
// api
import { getScheduleInfo } from "@/service/api/schedule";
import "@amir04lm26/react-modern-calendar-date-picker/lib/DatePicker.css";
// i18n
import { useTranslation } from "react-i18next";
import TimeButtonSelect from "./TimeButtonSelect";
import DateSvg from "./DateSvg";
import {
  BootstrapDialog,
  BootstrapContent,
  BootstrapDialogTitle,
} from "@/components/dialog";
const LinkScheduleDetail = forwardRef((props, ref) => {
  const timeButtonSelectRef = useRef(null);
  const { t } = useTranslation();
  const [detailOpen, setDetailOpen] = useState(false);
  const [xsValue, setXsValue] = useState(3);
  const [heightValue, setHeightValue] = useState(160);

  const handleClose = () => {
    setDetailOpen(false);
  };

  useImperativeHandle(ref, () => ({
    handleOpen,
  }));

  const handleOpen = (id) => {
    handleGetScheduleInfo(id);
    setDetailOpen(true);
  };

  //获取排期数据
  const handleGetScheduleInfo = async (id) => {
    await getScheduleInfo(id).then((res) => {
      const result = res.data;
      setFormData(result);
      const monthWeekDayMap =
        timeButtonSelectRef.current.getDaysWeeksAndMonthsDate(
          result.startDate,
          result.stopDate
        );
      timeButtonSelectRef.current.handleSelectedDate(result);
      timeButtonSelectRef.current.handleChangeDaysAble(
        result.playWeeksNum,
        result.playMonths,
        monthWeekDayMap
      );
      handleCalculateXS(
        parseInt(parseInt(result.columns)),
        parseInt(parseInt(result.line))
      );
    });
  };
  //计算出每个设备所在元素的宽度，设备所在父级元素的高度
  const handleCalculateXS = (cloumnNum, lineNum) => {
    const newCloumnNum = cloumnNum > 4 ? 4 : cloumnNum;
    const xs = 12 / newCloumnNum;
    setXsValue(xs);
    //计算总高度
    const expandRow = cloumnNum > 4 ? 1 : 0;
    const newRowNum = lineNum + expandRow;
    setHeightValue(newRowNum * 160);
  };

  // 表单赋值
  const setFormData = (data) => {
    scheduleForm.setValues(
      {
        id: data.id,
        name: data.name,
        inventoryList: data.inventoryList,
        startDate: data.startDate,
        stopDate: data.stopDate,
        playWeeks: data.playWeeksNum,
        playDays: data.playDays,
        playMonths: data.playMonths,
        screenIds: data.screenIds,
        screenGroupPlayList: data.screenGroupPlayList,
      },
      true
    );
  };

  //表单
  const scheduleForm = useFormik({
    initialValues: {
      name: "",
      startDate: "",
      stopDate: "",
      screenGroupIds: "",
      //集合内部的集合对象对应为区域设备对象
      screenGroupPlayList: [],
    },
  });

  //联屏清单选择组件
  function LinkPlayListSelect(props) {
    const playListIndex = props.playListIndex;
    const { xsValue, heightValue } = props;
    return (
      <>
        <Grid item xs={6}>
          <Stack spacing={1}>
            <InputLabel htmlFor="infor-firstName">
              {t("ips.ips_playlist_play_time")}
              <i style={{ color: "red" }}>*</i>
            </InputLabel>
            <OutlinedInput
              value={
                scheduleForm.values.screenGroupPlayList[playListIndex].stopTime
              }
              id="infor-firstName"
              type="text"
              name="stopTime"
              sx={{ width: "100%" }}
              startAdornment={
                <InputAdornment position="start" sx={{ width: "120%" }}>
                  <InputBase
                    endAdornment={
                      <Stack
                        direction="row"
                        justifyContent="flex-end"
                        alignItems="center"
                      >
                        <DateSvg />
                      </Stack>
                    }
                    startAdornment={
                      <InputAdornment position="start">
                        <AccessTimeIcon />
                      </InputAdornment>
                    }
                    value={
                      scheduleForm.values.screenGroupPlayList[playListIndex]
                        .startTime
                    }
                    name="startTime"
                    // id="quick-area"
                    type="text"
                    sx={{ width: "100%" }}
                  />
                </InputAdornment>
              }
            />
          </Stack>
        </Grid>
        <Grid
          container
          spacing={1}
          justifyContent={
            // scheduleForm.values.screenGroupPlayList[playListIndex]
            //   .areaScreenPlayList.length > 4
            //   ? "flex-start"
            //   : "space-evenly"
            "flex-start"
          }
          // alignItems="center"
          sx={{
            marginTop: "10px",
            height: `${heightValue}px`,
            padding: "20px 0px 30px 0px",
            backgroundColor: "#f9f9f9",
            overflow: "auto",
            overflowX: "hidden",
            overflowY: "auto",
            marginBottom: "10px",
          }}
        >
          {scheduleForm.values.screenGroupPlayList[playListIndex] &&
            scheduleForm.values.screenGroupPlayList[
              playListIndex
            ].areaScreenPlayList.map(
              (screenGroupPlayListScreenObject, screenIndex) => {
                return (
                  <Grid item xs={xsValue} key={screenIndex} align="center">
                    <Paper
                      className={
                        scheduleForm.values.screenGroupPlayList[playListIndex]
                          .areaScreenPlayList[screenIndex].playListName == ""
                          ? "box"
                          : "box"
                      }
                      elevation={0}
                      sx={{
                        border: "1px solid rgba(120, 189, 66,0.7)",
                        height: "100px",
                        width: "200px",
                        boxShadow: "0px 5px 10px 2px rgba(0, 0, 0, 0.1)",
                      }}
                    >
                      <Stack
                        spacing={1}
                        sx={{ height: "100%", width: "100%" }}
                        justifyContent="center"
                        alignItems="center"
                      >
                        {
                          <Typography
                            sx={{
                              color: "rgba(0, 0, 0, 0.3)",
                              wordBreak: "break-all",
                              padding: "5px 10px 5px 10px",
                            }}
                          >
                            {
                              scheduleForm.values.screenGroupPlayList[
                                playListIndex
                              ].areaScreenPlayList[screenIndex].playListName
                            }
                          </Typography>
                        }
                      </Stack>
                    </Paper>
                  </Grid>
                );
              }
            )}
        </Grid>
      </>
    );
  }

  return (
    <BootstrapDialog
      fullWidth
      maxWidth="lg"
      onClose={handleClose}
      aria-labelledby="customized-dialog-title"
      open={detailOpen}
    >
      <BootstrapDialogTitle id="customized-dialog-title" onClose={handleClose}>
        <Typography variant="h4" component="p">
          {t("ips.ips_link_schedule_detail")}
        </Typography>
      </BootstrapDialogTitle>
      <BootstrapContent>
        <form noValidate>
          <Container maxWidth="md" sx={{ padding: 3 }}>
            <Grid container spacing={1} direction="row">
              <Grid item xs={5.5}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="schedule-name" sx={{ width: 100 }}>
                    {t("ips.ips_scheduling")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <OutlinedInput
                    fullWidth={true}
                    id="schedule-name"
                    type="text"
                    name="name"
                    value={scheduleForm.values.name}
                  />
                </Stack>
              </Grid>
              <Grid item xs={5.5}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="infor-firstName" sx={{ width: 250 }}>
                    {t("ips.ips_startDate_endDate")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <OutlinedInput
                    readOnly
                    autoFocus={false}
                    autoComplete="off"
                    name="startDate"
                    type="text"
                    value={scheduleForm.values.stopDate}
                    fullWidth
                    startAdornment={
                      <InputAdornment position="start" sx={{ width: "120%" }}>
                        <InputBase
                          readOnly
                          autoComplete="off"
                          autoFocus={false}
                          name="stopDate"
                          startAdornment={
                            <InputAdornment position="start">
                              <CalendarMonthIcon />
                            </InputAdornment>
                          }
                          onBlur={scheduleForm.handleBlur}
                          endAdornment={
                            <Stack
                              direction="row"
                              justifyContent="flex-end"
                              sx={{ paddingRight: "3px" }}
                              alignItems="center"
                            >
                              <DateSvg />
                            </Stack>
                          }
                          sx={{
                            width: "100%",
                          }}
                          type="text"
                          value={scheduleForm.values.startDate}
                        />
                      </InputAdornment>
                    }
                    sx={{
                      width: "100%",
                    }}
                  />
                </Stack>
              </Grid>
              <Grid item sx={{ marginBottom: "10px" }} xs={11}>
                <Stack spacing={2}>
                  {scheduleForm.values.screenGroupPlayList.map(
                    (playListObject, playListIndex) => (
                      <LinkPlayListSelect
                        key={playListIndex}
                        playListIndex={playListIndex}
                        xsValue={xsValue}
                        heightValue={heightValue}
                      />
                    )
                  )}
                </Stack>
              </Grid>

              <Grid item xs={12}>
                <TimeButtonSelect ref={timeButtonSelectRef} />
              </Grid>
            </Grid>
          </Container>
        </form>
      </BootstrapContent>
    </BootstrapDialog>
  );
});

export default LinkScheduleDetail;
