import { useEffect, useRef, useState } from "react";
import * as echarts from "echarts";
import { useTranslation } from "react-i18next";
const AgeRange = (props) => {
  const chartRef = useRef(null);
  const { t } = useTranslation();
  const myChartRef = useRef(null);

  const initChart = () => {
    let chart = echarts.init(chartRef.current, null, { renderer: "svg" });
    myChartRef.value = chart;
    // 设置初始大小
    chart.resize();
    // 监听窗口大小变化，自动调整图表大小
    window.addEventListener("resize", handleResize);
    const options = getOptions(props.genderDistribution);
    chart.setOption(options);
  };

  const handleResize = () => {
    if (myChartRef.value) {
      myChartRef.value.resize();
    }
  };

  useEffect(() => {
    // 在组件挂载时进行初始化
    initChart();
    return () => {
      window.removeEventListener("resize", handleResize);
      if (myChartRef.value) {
        myChartRef.value.dispose();
        myChartRef.value = null;
      }
    };
  }, []);

  useEffect(() => {
    if (myChartRef.value === null) {
      initChart();
    } else {
      const options = getOptions(props.genderDistribution);
      myChartRef.value.setOption(options);
    }
  }, [props.genderDistribution]);

  const getOptions = (data) => {
    let xAxis = [];
    let dataObj = {};
    data?.forEach((element) => {
      xAxis.push(element.filedName);
      element?.genders?.forEach((item) => {
        if (!dataObj[item.fieldName]) {
          dataObj[item.fieldName] = [];
        }
        dataObj[item.fieldName].push(item);
      });
    });

    let series = Object.keys(dataObj).map((item) => {
      let arry = dataObj[item];
      let fieldName = arry[0]?.fieldName;
      return {
        name: t("realTime." + fieldName),
        type: "bar",
        stack: "stack",
        barWidth: 40,
        symbol: "none",
        itemStyle: {
          // 设置柱状图的颜色
          color: fieldName === "female" ? "rgb(244,164,218)" : "rgb(0,180,255)",
        },
        data: arry?.map((valueItem) => {
          return valueItem.filedValue;
        }),
      };
    });

    let legend = series?.map((item) => {
      return item?.name;
    });

    return {
      title: {
        text: t("realTime.genderDistriBution"),
        textStyle: {
          color: "#86909c",
          fontStyle: "normal",
          fontWeight: "normal",
          fontFamily: "Arial",
          fontSize: 14,
        },
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "shadow",
        },
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "10%",
        containLabel: true,
      },
      legend: {
        data: legend,
        top: 15,
        left: "center",
        align: "right",
        icon: "rect",
        itemWidth: 30,
        itemHeight: 15,
        itemGap: 30,
        right: "4%",
        textStyle: {
          fontSize: 12,
          color: "#333333",
        },
      },
      xAxis: {
        type: "category",
        data: xAxis,
        // 自定义轴线样式
        axisLine: {
          // 延伸轴线到最大最小值之外
          extend: "both",
        },
        // 扩展X轴数据范围
        min: "dataMin",
        max: "dataMax",
        // 设置边界空白
        boundaryGap: false, // 关闭边界空白
      },
      yAxis: {
        type: "value",
      },
      series: series,
    };
  };
  return (
    <div
      style={{
        width: "100%",
        boxSizing: "border-box",
        position: "relative",
        height: "100%",
        padding: "10px",
        border: "1px solid #e5e6eb",
        borderRadius: "20px",
        backgroundColor: "#ffffff",
      }}
    >
      <div
        style={{
          width: "100%",
          height: "100%",
        }}
        ref={chartRef}
      ></div>
    </div>
  );
};

export default AgeRange;
