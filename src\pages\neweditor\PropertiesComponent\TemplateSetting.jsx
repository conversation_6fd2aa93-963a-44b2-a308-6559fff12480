import React from "react";
import { Grid, TablePagination, Button } from "@mui/material";
import { FormLabel } from "./PropertiesComponent";
import CustomInput from "../components/CustomInput";
import { isPositiveInteger } from "../common/utils";
import { toast } from "react-toastify";
import { message } from "../common/i18n";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import ImageIcon from "@mui/icons-material/Image";
import ColorPick from "../components/ColorPick";
import ClearIcon from "@mui/icons-material/Clear";
import { useEffect, useState } from "react";
import { styled } from "@mui/material/styles";
import { useConfirm } from "@/components/zkconfirm";
import { getResource } from "@/service/api/layout";
const BootstrapDialog = styled(Dialog)(({ theme }) => ({
  "& .MuiDialogContent-root": {
    padding: theme.spacing(2),
  },
  "& .MuiDialogActions-root": {
    padding: theme.spacing(1),
  },
}));

const TemplateSetting = (props) => {
  const currentPageIndex = props.currentPageIndex;
  const activeTempIndex = props.activeTempIndex;
  const pages = props.pages;
  const confirmFn = useConfirm();

  const [properties, setProperties] = useState({
    duration: 10,
    bgColor: "#ffffff",
    bgImg: "",
    checksum: "",
  });

  useEffect(() => {
    if (activeTempIndex !== "") {
      const curretnPage = pages[currentPageIndex];
      let componentInfo = curretnPage.tempLayout[activeTempIndex];
      setProperties({
        duration: componentInfo.duration,
        bgColor: componentInfo.bgColor,
        bgImg: componentInfo.bgImg,
        checksum: componentInfo.checksum,
      });
    }
  }, [pages, currentPageIndex, activeTempIndex]);

  const [imageList, setImageList] = useState([]);
  const [open, setOpen] = useState(false);
  const [imgOptions, setImgOptions] = useState({
    current: 0,
    size: 6,
    total: 0,
    sizes: [6, 10, 20, 40, 100],
  });

  const [pageNumber, setPageNumber] = useState(0);
  const [pageSize, setPageSize] = useState(10);

  const getImageList = (page) => {
    let currentPage = page === undefined ? imgOptions.current + 1 : page + 1;
    getResource({
      page: currentPage,
      pageSize: imgOptions.size,
      showAudited: true,
      type: "image",
      status: 2,
    }).then((res) => {
      let resData = res.data;
      let list = resData.data.map((it) => {
        it.url = it.downloadUrl;
        return it;
      });
      setImageList(list);
      setImgOptions({
        ...imgOptions,
        current: currentPage - 1,
        total: resData.total,
      });
    });
  };

  useEffect(() => {
    getImageList();
  }, [pageSize, pageNumber]);

  const setComponentInfo = (baseInfo) => {
    const newPages = JSON.parse(JSON.stringify(props.pages));
    const curretnPage = newPages[currentPageIndex];
    let componentInfo = curretnPage.tempLayout[activeTempIndex];

    let newInfo = {
      ...componentInfo,
      ...baseInfo,
    };

    curretnPage.tempLayout[activeTempIndex] = newInfo;
    if (props.setPages) {
      props.setPages(newPages);
    }
  };

  const changeProperties = (event) => {
    const name = event.target.name;
    let newInfo = {
      ...properties,
      [name]: event.target.value,
    };

    setComponentInfo(newInfo);
  };

  const clearBg = () => {
    setProperties({
      ...properties,
      imgId: "",
      bgImg: "",
      checksum: "",
    });
    setComponentInfo({
      imgId: "",
      bgImg: "",
      checksum: "",
    });
  };

  const changeDuration = (event) => {
    let value = event.target.value;

    if (value === "" || isPositiveInteger(value)) {
      if (value > 999999) {
        value = 999999;
      }
      if (value < 1) {
        value = 1;
      }

      setProperties({
        ...properties,
        duration: value,
      });
      setComponentInfo({
        duration: value,
      });
    } else {
      setProperties({
        ...properties,
        duration: 10,
      });
      setComponentInfo({
        duration: 10,
      });
      toast.error(message("editor_number_error_message"));
    }
  };

  const addImage = (item) => {
    setProperties({
      ...properties,
      imgId: item.id,
      bgImg: item.downloadUrl,
      checksum: item.checksum,
    });
    setComponentInfo({
      imgId: item.id,
      bgImg: item.downloadUrl,
      checksum: item.checksum,
    });
    setOpen(false);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleChangePage = (e, v) => {
    setImgOptions({
      ...imgOptions,
      current: v,
    });
  };

  useEffect(() => {
    setPageNumber(imgOptions.current);
    setPageSize(imgOptions.size);
  }, [imgOptions]);

  const handleChangeRowsPerPage = (e, v) => {
    let value = e.target.value;
    setImgOptions({
      ...imgOptions,
      current: 0,
      size: value,
    });
  };

  const onBlurFn = (event) => {
    let value = event.target.value;
    if (value === "") {
      setProperties({
        ...properties,
        duration: 10,
      });
      setComponentInfo({
        duration: 10,
      });
      toast.error(message("editor_number_error_message"));
    }
  };

  const deleteTemp = () => {
    confirmFn({
      title: message("delete_cmp_tip"),
      confirmationText: message("editor_edit_ok"),
      cancellationText: message("editor_edit_cancel"),
      description: message("editor_template_delete_template_tip"),
    }).then(() => {
      const newPages = JSON.parse(JSON.stringify(props.pages));
      const curretnPage = newPages[currentPageIndex];
      curretnPage.isTemplate = false;
      curretnPage.tempLayout = [];
      if (props.setPages) {
        props.setPages(newPages);
      }
      if (props.setCurrentType) {
        props.setCurrentType("scene");
      }
    });
  };

  return (
    <Grid
      sx={{
        width: "100%",
        boxShadow: "0px 0px 6px #00000029",
        borderRadius: "10px",
        backgroundColor: "#ffffff",
        overflow: "hidden",
        minHeight: "200px",
      }}>
      <Grid
        sx={{
          p: 2,
        }}>
        {activeTempIndex === "" ? (
          <Grid>{message("editor_template_select_properties")}</Grid>
        ) : (
          <Grid>
            <Grid
              sx={{
                display: "flex",
                alignItems: "center",
                mt: 1,
              }}>
              <FormLabel sx={{ mr: 2 }}>{message("editor_bgColor")}:</FormLabel>
              <ColorPick
                value={properties.bgColor}
                name="bgColor"
                onChange={changeProperties}></ColorPick>
            </Grid>
            <Grid
              sx={{
                display: "flex",
                alignItems: "center",
                mt: 2,
              }}>
              <FormLabel sx={{ mr: 2 }}>{message("editor_bgImage")}:</FormLabel>

              {properties.bgImg ? (
                <img
                  style={{
                    height: "30px",
                    width: "30px",
                  }}
                  src={properties.bgImg}></img>
              ) : (
                <ImageIcon
                  onClick={() => {
                    setOpen(true);
                  }}></ImageIcon>
              )}

              {properties.bgImg && (
                <ClearIcon
                  sx={{
                    "&:hover": {
                      color: "red",
                      cursor: "pointer",
                    },
                  }}
                  onClick={clearBg}></ClearIcon>
              )}
            </Grid>
            <Grid
              sx={{
                mb: 1,
                mt: 2,
                display: "flex",
                alignItems: "center",
              }}>
              <FormLabel sx={{ mr: 2 }}>{message("editor_duration")}</FormLabel>
              <Grid>
                <CustomInput
                  sx={{
                    width: "60px",
                    marginTop: "0px",
                    input: {
                      padding: "5px",
                    },
                  }}
                  type="number"
                  value={properties.duration}
                  onChange={(e) => {
                    changeDuration(e);
                  }}
                  onBlur={(e) => {
                    onBlurFn(e);
                  }}
                  name="duration"></CustomInput>
              </Grid>
              <FormLabel sx={{ ml: 2 }}>{message("editor_second")}</FormLabel>
            </Grid>

            <Button
              onClick={() => {
                deleteTemp();
              }}>
              {message("editor_template_delete")}
            </Button>
          </Grid>
        )}

        <BootstrapDialog
          open={open}
          onClose={handleClose}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description">
          <DialogTitle sx={{ m: 0, p: 2 }} id="customized-dialog-title">
            {message("editor_select_image")}
          </DialogTitle>
          <IconButton
            aria-label="close"
            onClick={handleClose}
            sx={{
              position: "absolute",
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500],
            }}>
            <CloseIcon />
          </IconButton>
          <DialogContent dividers>
            <Grid
              sx={{
                minWidth: "700px",
              }}>
              <Grid
                sx={{
                  pb: 2,
                }}
                container
                rowSpacing={1}
                columnSpacing={1}>
                {imageList.map((item) => (
                  <Grid
                    onClick={() => {
                      addImage(item);
                    }}
                    item
                    xs={4}
                    sx={{
                      height: "100px",
                    }}
                    key={item.id}>
                    <img
                      srcSet={`${item.downloadUrl}`}
                      src={`${item.downloadUrl}`}
                      style={{
                        height: "100%",
                        width: "100%",
                      }}
                      loading="lazy"
                    />
                  </Grid>
                ))}
              </Grid>
              <TablePagination
                component="div"
                count={imgOptions.total}
                page={imgOptions.current}
                onPageChange={handleChangePage}
                rowsPerPage={imgOptions.size}
                onRowsPerPageChange={handleChangeRowsPerPage}
                rowsPerPageOptions={imgOptions.sizes}
                labelRowsPerPage={<p>{message("editor_rowsPerPage")}:</p>}
              />
            </Grid>
          </DialogContent>
        </BootstrapDialog>
      </Grid>
    </Grid>
  );
};

export default TemplateSetting;
