/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable react/prop-types */
import { useState, forwardRef, useImperativeHandle, useEffect } from "react";
import { FormControl } from "@mui/material";
import {
  getCounty,
  getGoogleProvince,
  getGoogleCity,
} from "@/service/api/bmap";
import ZKSelect from "@/components/ZKSelect";
import { useTranslation } from "react-i18next";

const TableBarSearch = forwardRef((props, ref) => {
  const { t } = useTranslation();
  //存储数据国家、省份、城市数据
  const [countyArray, setCountyArray] = useState([]);
  const [provinceArray, setProvinceArray] = useState([]);
  const [cityArray, setCityArray] = useState([]);
  //国家、省份、城市选中的名称
  const [country, setCounty] = useState("");
  const [province, setProvince] = useState("");
  const [city, setCity] = useState("");
  //国家、省份、城市选中的value值
  const [selectCountyValue, setSelectCountyValue] = useState(undefined);
  const [selectProvinceValue, setSelectProvinceValue] = useState(undefined);
  const [selectCityValue, setSelectCityValue] = useState(undefined);
  useEffect(() => {
    getCounty().then((res) => setCountyArray(res.data));
    getGoogleProvince(selectCountyValue).then((res) =>
      setProvinceArray(res.data)
    );
    getGoogleCity(selectProvinceValue).then((res) => setCityArray(res.data));
  }, [selectCountyValue, selectProvinceValue]);

  useImperativeHandle(ref, () => ({
    handleGetRegionInfo,
    handleClear,
  }));

  const handleGetRegionInfo = () => {
    return { country, province, city };
  };

  const handleClear = () => {
    setSelectCountyValue("");
    setSelectProvinceValue("");
    setSelectCityValue("");
    setProvinceArray([]);
    setCityArray([]);
  };
  return (
    <>
      <FormControl sx={{ m: 1, minWidth: 200, maxWidth: 200 }} size="small">
        <ZKSelect
          placeholder={t("ips.ips_store_country")}
          labelOptions={{ label: "label", value: "value" }}
          value={selectCountyValue}
          onChange={(event, child) => {
            const { children, value } = child.props;
            setSelectCountyValue(value);
            setCounty(children);
          }}
          options={countyArray}
          menuWidth={200}
          onClear={() => {
            setSelectCountyValue("");
            setSelectProvinceValue("");
            setSelectCityValue("");
            setProvinceArray([]);
            setCityArray([]);
          }}
        />
      </FormControl>
      <FormControl sx={{ m: 1, minWidth: 200, maxWidth: 200 }} size="small">
        <ZKSelect
          placeholder={t("ips.ips_store_province")}
          labelOptions={{ label: "label", value: "value" }}
          value={selectProvinceValue}
          onChange={(event, child) => {
            const { children, value } = child.props;
            setSelectProvinceValue(value);
            setProvince(children);
          }}
          options={provinceArray}
          menuWidth={200}
          onClear={() => {
            setSelectProvinceValue("");
            setSelectCityValue("");
            setCityArray([]);
          }}
        />
      </FormControl>
      <FormControl sx={{ m: 1, minWidth: 200, maxWidth: 200 }} size="small">
        <ZKSelect
          placeholder={t("ips.ips_store_city")}
          labelOptions={{ label: "label", value: "value" }}
          value={selectCityValue}
          onChange={(event, child) => {
            const { children, value } = child.props;
            setSelectCityValue(value);
            setCity(children);
          }}
          options={cityArray}
          menuWidth={200}
          onClear={() => {
            setSelectCityValue("");
          }}
        />
      </FormControl>
    </>
  );
});

export default TableBarSearch;
