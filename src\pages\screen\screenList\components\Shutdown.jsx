/* eslint-disable react-refresh/only-export-components */
/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/prop-types */
import React, { useEffect, useState, useRef } from "react";
import {
  BootstrapActions,
  BootstrapContent,
  BootstrapDialog,
  BootstrapDialogTitle,
} from "@/components/dialog";
import PropTypes from "prop-types";
import {
  Button,
  Stack,
  Typography,
  MenuItem,
  Grid,
  Chip,
  Tooltip,
  Alert,
  Tab,
  Tabs,
  Select,
  InputLabel,
  Box,
  FormHelperText,
  AlertTitle,
  TextField,
  OutlinedInput,
  IconButton,
} from "@mui/material";
import { toast } from "react-toastify";
import i18n from "i18next";
import { useTranslation } from "react-i18next";
import ZKSelect from "@/components/ZKSelect";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import "dayjs/locale/es";
import "dayjs/locale/en";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { TimePicker } from "@mui/x-date-pickers/TimePicker";
import { LoadingButton } from "@mui/lab";
const CornTabPanel = (props) => {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}>
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};
const DateTimePicker = ({ timeValue, onChange }) => {
  const handleChangeTime = (value) => {
    onChange(value);
  };
  const pickerRef = useRef();

  useEffect(() => {
    if (pickerRef.current) {
      pickerRef.current.value = timeValue;
    }
  }, [timeValue]);

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <TimePicker
        ref={pickerRef}
        timeSteps={{
          minutes: 1,
        }}
        okText="a"
        value={null}
        onChange={handleChangeTime}
        ampm={false}
        renderInput={(props) => <OutlinedInput {...props} />}
      />
    </LocalizationProvider>
  );
};
const weeks = [
  {
    label: i18n.t("cron.week_1"),
    value: "1",
  },
  {
    label: i18n.t("cron.week_2"),
    value: "2",
  },
  {
    label: i18n.t("cron.week_3"),
    value: "3",
  },
  {
    label: i18n.t("cron.week_4"),
    value: "4",
  },
  {
    label: i18n.t("cron.week_5"),
    value: "5",
  },
  {
    label: i18n.t("cron.week_6"),
    value: "6",
  },
  {
    label: i18n.t("cron.week_7"),
    value: "0",
  },
];
//月份
function getDayOfTheMonthOption() {
  const days = [];
  for (let i = 1; i < 31; i += 1) {
    days.push({ value: i, label: i.toString().concat(i18n.t("cron.hao")) });
  }
  return days;
}
//小时
function getHourOption() {
  const hours = [];
  for (let i = 0; i < 24; i += 1) {
    hours.push({ value: i.toString(), label: i.toString() });
  }
  return hours;
}
//分
function getMinuteOption() {
  const hours = [];
  for (let i = 0; i < 60; i += 1) {
    hours.push({ value: i.toString(), label: i.toString() });
  }
  return hours;
}

//定时关机
const shutdown = (props) => {
  const { t } = useTranslation();
  const [cornTabValue, setCornTabValue] = useState(0);
  const { open, onCancel, onSubmit, onNowShutodwn } = props;
  const [expression, setExpression] = useState("");
  const [loading, setLoading] = useState(false);
  const [nowLoading, setNowLoading] = useState(false);
  const handleClose = () => {
    //清除表达式
    setExpression("");
    onCancel();
  };
  const switchCornTab = (event, newValue) => {
    setExpression("");
    setCornTabValue(newValue);
  };
  //拷贝CRON表达式
  async function copyTextToClipboard(text) {
    try {
      await navigator.clipboard.writeText(text);
    } catch (err) {
      console.error("Failed to copy: ", err);
    }
  }
  const handleSave = async () => {
    setLoading(true);
    await onSubmit(expression)
      .then(() => {
        setLoading(false);
        setExpression("");
      })
      .catch(() => {
        setLoading(false);
      });
  };

  const handleNowShutdown = async () => {
    setNowLoading(true);
    await onNowShutodwn()
      .then(() => {
        setNowLoading(false);
        setExpression("");
      })
      .catch(() => {
        setNowLoading(false);
      });
  };

  return (
    <BootstrapDialog open={open} fullWidth maxWidth={"xs"}>
      <BootstrapDialogTitle onClose={handleClose}>
        <Typography variant="h4" component="p">
          {t("screen.time_shutdown")}
        </Typography>
      </BootstrapDialogTitle>
      <BootstrapContent>
        <Box>
          <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
            <Tabs
              value={cornTabValue}
              onChange={switchCornTab}
              aria-label="basic tabs example">
              <Tab label={t("cron.everyday")} {...a11yProps(0)} />
              <Tab label={t("cron.everyweek")} {...a11yProps(1)} />
              <Tab label={t("cron.everymonth")} {...a11yProps(2)} />
              {/* <Tab label={t("cron.customer")} {...a11yProps(3)} /> */}
            </Tabs>
          </Box>
          <CornTabPanel value={cornTabValue} index={0}>
            <Stack spacing={2}>
              <CornTimePicker
                expression={expression}
                defaultTimeType="0"
                onResult={(val) => {
                  setExpression(val);
                }}
              />
            </Stack>
          </CornTabPanel>
          <CornTabPanel value={cornTabValue} index={1}>
            <CornWeekSelectTimePicker
              onResult={(value) => {
                setExpression(value);
              }}
            />
          </CornTabPanel>
          <CornTabPanel value={cornTabValue} index={2}>
            <CornMonthSelectTimePicker
              onResult={(value) => {
                setExpression(value);
              }}
            />
          </CornTabPanel>
          <CornTabPanel value={cornTabValue} index={3}>
            <CornCustomize
              onResult={(value) => {
                setExpression(value);
              }}
            />
          </CornTabPanel>
        </Box>
      </BootstrapContent>
      <BootstrapActions>
        <Grid
          container
          direction="row"
          justifyContent={"space-between"}
          alignItems="center">
          {/* {expression && (
            <Box sx={{ pl: 2, width: "65%" }}>
              <Stack direction={"row"} alignItems={"center"} spacing={2}>
                <Tooltip title={expression} placement="top">
                  <Typography
                    sx={{
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap",
                    }}
                  >
                    <b>{t("cron.corn_expression")}</b>
                    {expression}
                  </Typography>
                </Tooltip>
                <IconButton
                  size="small"
                  onClick={async () => {
                    copyTextToClipboard(expression).then((res) => {
                      toast.success(t("cron.copy_success"));
                    });
                  }}
                >
                  <ContentCopyIcon />
                </IconButton>
              </Stack>
            </Box>
          )} */}
          <Grid item>
            <LoadingButton
              variant="contained"
              color="warning"
              loading={nowLoading}
              onClick={handleNowShutdown}>
              {t("screen.screen_shutdown_now")}
            </LoadingButton>
          </Grid>
          <Grid item>
            <Stack spacing={1} direction="row" justifyContent={"end"}>
              <Button color="info" variant="outlined" onClick={handleClose}>
                {t("common.common_edit_cancel")}
              </Button>
              <LoadingButton
                variant="contained"
                color="primary"
                loading={loading}
                disabled={!expression}
                onClick={() => handleSave()}>
                {t("common.common_edit_ok")}
              </LoadingButton>
            </Stack>
          </Grid>
        </Grid>
      </BootstrapActions>
    </BootstrapDialog>
  );
};

function a11yProps(index) {
  return {
    id: `simple-tab-${index}`,
    "aria-controls": `simple-tabpanel-${index}`,
  };
}
const CornTimePicker = ({ expression, defaultTimeType, onResult }) => {
  const [value, setValue] = useState(undefined);
  const handleChangeTime = (value) => {
    // console.log("value",dayjs(value).format("HH:mm"));
    const currentCron = expression ? expression.split(" ") : [];
    const [seconds, , , dayOfMonth, month1, dayOfWeek] = currentCron;
    const timeStr = dayjs(value).format("H:m");
    const hours = timeStr.split(":")[0];
    const minutes = timeStr.split(":")[1];
    // console.log(minutes,hours);
    let result = null;
    if (!Number.isNaN(Number(hours)) && !Number.isNaN(Number(minutes))) {
      result = `${minutes} ${hours} * * *`;
    }
    if (result !== null) {
      onResult(result);
    }
  };
  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <TimePicker
        timeSteps={{
          minutes: 1,
        }}
        value={value}
        onChange={handleChangeTime}
        ampm={false}
        renderInput={(props) => <OutlinedInput {...props} />}
      />
    </LocalizationProvider>
  );
};

const CornWeekSelectTimePicker = ({ onResult }) => {
  const { t } = useTranslation();
  const [everyWeek, setEveryWeek] = useState("");
  const [timeValue, setTimeValue] = useState(undefined);
  function generateWeeklyCronExpression(daysOfWeek, hour, minute) {
    // 构建 cron 表达式的各个部分
    const daysOfWeekStr = daysOfWeek.join(",");

    // 生成 cron 表达式
    const cronExpression = `${minute} ${hour} * * ${daysOfWeekStr}`;
    return cronExpression;
  }
  const handleTimeOnChange = (value) => {
    if (value && everyWeek) {
      setTimeValue(value);
      const timeStr = dayjs(value).format("H:m");
      const hours = timeStr.split(":")[0];
      const minutes = timeStr.split(":")[1];
      // 构建 cron 表达式的各个部分
      onResult(generateWeeklyCronExpression([everyWeek], hours, minutes));
    }
  };
  useEffect(() => {
    if (everyWeek && timeValue) {
      const timeStr = dayjs(timeValue).format("H:m");
      const hours = timeStr.split(":")[0];
      const minutes = timeStr.split(":")[1];
      // 构建 cron 表达式的各个部分
      onResult(generateWeeklyCronExpression([everyWeek], hours, minutes));
    }
  }, [everyWeek]);
  return (
    <Stack direction="row" spacing={2}>
      <ZKSelect
        placeholder={t("cron.please_select_week")}
        labelOptions={{ label: "label", value: "value" }}
        value={everyWeek}
        onChange={(event) => {
          const { value } = event.target;
          setEveryWeek(value);
          handleTimeOnChange();
        }}
        options={weeks}
        menuWidth={200}
        onClear={() => {
          setTimeValue(null);
          setEveryWeek("");
          onResult("");
        }}
      />
      <DateTimePicker timeValue={timeValue} onChange={handleTimeOnChange} />
    </Stack>
  );
};

const CornMonthSelectTimePicker = ({ onResult }) => {
  const { t } = useTranslation();
  const [everyMonth, setEveryMonth] = useState("");
  const [timeValue, setTimeValue] = useState(undefined);
  //带有31号的月份
  const selectedMonths = [1, 3, 5, 7, 8, 10, 12]; // 用户选择的月份
  //带有31号的月份

  // function generateMonthlyCronExpression(dayOfMonth, hour, minute,currentMonth) {
  //   if(dayOfMonth){
  //       return `${minute} ${hour} ${dayOfMonth} ${selectedMonths} *`;
  //   }
  //   const cronExpression = `${minute} ${hour} ${dayOfMonth} * *`;
  //   // 生成 cron 表达式
  //   return cronExpression;
  // }
  function generateMonthlyCronExpression(dayOfMonth, hour, minute) {
    if (dayOfMonth) {
      return `${minute} ${hour} ${dayOfMonth} ${selectedMonths} *`;
    }
    const cronExpression = `${minute} ${hour} ${dayOfMonth} * *`;
    // 生成 cron 表达式
    return cronExpression;
  }
  useEffect(() => {
    if (everyMonth && timeValue) {
      const timeStr = dayjs(timeValue).format("H:m");
      const hours = timeStr.split(":")[0];
      const minutes = timeStr.split(":")[1];
      // 构建 cron 表达式的各个部分
      onResult(generateMonthlyCronExpression(everyMonth, hours, minutes));
    }
  }, [everyMonth]);
  const handleTimeOnChange = (value) => {
    if (value && everyMonth) {
      setTimeValue(value);
      // console.log(everyMonth);
      const timeStr = dayjs(value).format("H:m");
      const hours = timeStr.split(":")[0];
      const minutes = timeStr.split(":")[1];
      // 构建 cron 表达式的各个部分
      onResult(generateMonthlyCronExpression(everyMonth, hours, minutes));
    }
  };
  return (
    <Stack direction="row" spacing={2}>
      <ZKSelect
        placeholder={t("cron.please_select_day")}
        labelOptions={{ label: "label", value: "value" }}
        value={everyMonth}
        onChange={(event) => {
          const { value } = event.target;
          setEveryMonth(value);
        }}
        options={getDayOfTheMonthOption()}
        menuWidth={200}
        onClear={() => {
          setEveryMonth("");
        }}
      />
      <DateTimePicker timeValue={timeValue} onChange={handleTimeOnChange} />
    </Stack>
  );
};
//数据
const monthOption = [
  { value: "1", label: i18n.t("cron.month_1") },
  { value: "2", label: i18n.t("cron.month_2") },
  { value: "3", label: i18n.t("cron.month_3") },
  { value: "4", label: i18n.t("cron.month_4") },
  { value: "5", label: i18n.t("cron.month_5") },
  { value: "6", label: i18n.t("cron.month_6") },
  { value: "7", label: i18n.t("cron.month_7") },
  { value: "8", label: i18n.t("cron.month_8") },
  { value: "9", label: i18n.t("cron.month_9") },
  { value: "10", label: i18n.t("cron.month_10") },
  { value: "11", label: i18n.t("cron.month_11") },
  { value: "12", label: i18n.t("cron.month_12") },
];
const days = getDayOfTheMonthOption();
const hours = getHourOption();
const minutes = getMinuteOption();
const CornCustomize = ({ onResult }) => {
  const [month, setMonth] = useState([]);
  const { t } = useTranslation();
  const [week, setWeek] = useState([]);
  const [day, setDay] = useState([]);
  const [hour, setHour] = useState([]);
  const [minute, setMinute] = useState([]);
  const handleChangeTime = (value, type = "month") => {
    let cronExpression = "";
    if (type === "month") {
      const selectedMonths =
        value && value.length > 0 ? value.map((v) => v.value).join(",") : "*";
      const selectedWeek =
        week && week.length > 0 ? week.map((w) => w.value).join(",") : "*";
      const selectedDay =
        day && day.length > 0 ? day.map((d) => d.value).join(",") : "*";
      const selectedHour =
        hour && hour.length > 0 ? hour.map((h) => h.value).join(",") : "*";
      const selectedMinute =
        minute && minute.length > 0
          ? minute.map((m) => m.value).join(",")
          : "*";
      cronExpression = `${selectedMinute} ${selectedHour} ${selectedDay} ${selectedMonths} ${selectedWeek}`;
    } else if (type === "week") {
      const selectedWeeks =
        value && value.length > 0 ? value.map((v) => v.value).join(",") : "*";
      const selectedMonth =
        month && month.length > 0 ? month.map((m) => m.value).join(",") : "*";
      const selectedDay =
        day && day.length > 0 ? day.map((d) => d.value).join(",") : "*";
      const selectedHour =
        hour && hour.length > 0 ? hour.map((h) => h.value).join(",") : "*";
      const selectedMinute =
        minute && minute.length > 0
          ? minute.map((m) => m.value).join(",")
          : "*";
      cronExpression = `${selectedMinute} ${selectedHour} ${selectedDay} ${selectedMonth} ${selectedWeeks}`;
    } else if (type === "day") {
      const selectedDay =
        value && value.length > 0 ? value.map((v) => v.value).join(",") : "*";
      const selectedWeeks =
        week && week.length > 0 ? week.map((w) => w.value).join(",") : "*";
      const selectedMonth =
        month && month.length > 0 ? month.map((m) => m.value).join(",") : "*";
      const selectedHour =
        hour && hour.length > 0 ? hour.map((h) => h.value).join(",") : "*";
      const selectedMinute =
        minute && minute.length > 0
          ? minute.map((m) => m.value).join(",")
          : "*";
      cronExpression = `${selectedMinute} ${selectedHour} ${selectedDay} ${selectedMonth} ${selectedWeeks}`;
    } else if (type === "hour") {
      const selectedHour =
        value && value.length > 0 ? value.map((v) => v.value).join(",") : "*";
      const selectedWeeks =
        week && week.length > 0 ? week.map((w) => w.value).join(",") : "*";
      const selectedMonth =
        month && month.length > 0 ? month.map((m) => m.value).join(",") : "*";
      const selectedDay =
        day && day.length > 0 ? day.map((d) => d.value).join(",") : "*";
      const selectedMinute =
        minute && minute.length > 0
          ? minute.map((m) => m.value).join(",")
          : "*";
      cronExpression = `${selectedMinute} ${selectedHour} ${selectedDay} ${selectedMonth} ${selectedWeeks}`;
    } else if (type === "minute") {
      const selectedMinute =
        value && value.length > 0 ? value.map((v) => v.value).join(",") : "*";
      const selectedWeeks =
        week && week.length > 0 ? week.map((w) => w.value).join(",") : "*";
      const selectedMonth =
        month && month.length > 0 ? month.map((m) => m.value).join(",") : "*";
      const selectedDay =
        day && day.length > 0 ? day.map((d) => d.value).join(",") : "*";
      const selectedHour =
        hour && hour.length > 0 ? hour.map((h) => h.value).join(",") : "*";
      cronExpression = `${selectedMinute} ${selectedHour} ${selectedDay} ${selectedMonth} ${selectedWeeks}`;
    }
    onResult(cronExpression);
  };

  return (
    <Grid container spacing={1}>
      <Grid item xs={6}>
        <Stack spacing={1}>
          <InputLabel>{t("cron.month")}</InputLabel>
          <MultipleSelect
            value={month}
            options={monthOption}
            placeholder={t("cron.please_select_month")}
            onChange={(event) => {
              const {
                target: { value },
              } = event;
              setMonth(value);
              handleChangeTime(value, "month");
            }}
          />
        </Stack>
      </Grid>
      <Grid item xs={6}>
        <Stack spacing={1}>
          <InputLabel htmlFor="area-name">{t("cron.weeks")}</InputLabel>
          <MultipleSelect
            value={week}
            placeholder={t("cron.please_select_week")}
            options={weeks}
            onChange={(event) => {
              const {
                target: { value },
              } = event;
              setWeek(value);
              handleChangeTime(value, "week");
            }}
          />
        </Stack>
      </Grid>
      <Grid item xs={6}>
        <Stack spacing={1}>
          <InputLabel htmlFor="area-name">{t("cron.day")}</InputLabel>
          <MultipleSelect
            placeholder={t("cron.please_select_day")}
            value={day}
            options={days}
            onChange={(event) => {
              const {
                target: { value },
              } = event;
              setDay(value);
              handleChangeTime(value, "day");
            }}
          />
        </Stack>
      </Grid>
      <Grid item xs={6}>
        <Stack spacing={1}>
          <InputLabel htmlFor="area-name">{t("cron.hour")}</InputLabel>
          <MultipleSelect
            placeholder={t("cron.please_select_hour")}
            value={hour}
            options={hours}
            onChange={(event) => {
              const {
                target: { value },
              } = event;
              setHour(value);
              handleChangeTime(value, "hour");
            }}
          />
        </Stack>
      </Grid>
      <Grid item xs={6}>
        <Stack spacing={1}>
          <InputLabel htmlFor="area-name">{t("cron.minute")}</InputLabel>
          <MultipleSelect
            value={minute}
            placeholder={t("cron.please_select_minute")}
            options={minutes}
            onChange={(event) => {
              const {
                target: { value },
              } = event;
              setMinute(value);
              handleChangeTime(value, "minute");
            }}
          />
        </Stack>
      </Grid>
    </Grid>
  );
};
const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};
const MultipleSelect = ({
  onChange = () => {},
  value,
  placeholder = "Placeholder",
  options = [],
}) => {
  return (
    <Select
      size="small"
      multiple
      displayEmpty
      value={value}
      onChange={onChange}
      input={<OutlinedInput />}
      renderValue={(selected) => {
        if (selected.length === 0) {
          return <span>{placeholder}</span>;
        }
        return (
          <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
            {selected.map((item) => {
              return (
                <Chip
                  color="primary"
                  variant="outlined"
                  key={item?.value}
                  label={item?.label}
                />
              );
            })}
          </Box>
        );
      }}
      sx={{ color: value.length == 0 ? "#757575" : "black" }}
      MenuProps={MenuProps}>
      <MenuItem disabled value="">
        <span>{placeholder}</span>
      </MenuItem>
      {options.length !== 0 &&
        options?.map((item) => (
          <MenuItem key={item.value} value={item}>
            {item.label}
          </MenuItem>
        ))}
    </Select>
  );
};

CornTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired,
};
export default shutdown;
