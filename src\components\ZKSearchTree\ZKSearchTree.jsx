import React, { useState, forwardRef, useEffect } from "react";
import SearchTree from "@/components/ZKSearchTree/index";
import { Grid } from "@mui/material";
import { treeList, subTreeList } from "@/service/api/L3Sevice.js";

const ZKSearchTree = forwardRef((props, ref) => {
  const {
    onChange,
    placeholder,
    isContainOldData = "0",
    regionKey,
    onClear,
  } = props;
  const [treeData, setTreeData] = useState([]);

  // 从 localStorage 获取持久化的值
  const getStoredValue = () => {
    if (!regionKey) return { id: "", name: "" };
    try {
      const stored = localStorage.getItem(regionKey);
      return stored ? JSON.parse(stored) : { id: "", name: "" };
    } catch (error) {
      return { id: "", name: "" };
    }
  };

  const storedValue = getStoredValue();
  const [valueId, setValueId] = useState(storedValue.id);
  const [valueName, setValueName] = useState(storedValue.name);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [loadingKeys, setLoadingKeys] = useState([]);

  // 保存值到 localStorage
  const saveToStorage = (id, name) => {
    if (!regionKey) return;
    try {
      const valueToStore = { id, name };
      localStorage.setItem(regionKey, JSON.stringify(valueToStore));
    } catch (error) {}
  };

  // 清除 localStorage 中的值
  const clearStorage = () => {
    if (!regionKey) return;
    try {
      localStorage.removeItem(regionKey);
    } catch (error) {}
  };

  useEffect(() => {
    loadInitialData();
  }, []);

  // 当树数据加载完成且有持久化值时，触发 onChange 回调
  useEffect(() => {
    if (treeData.length > 0 && valueId && valueName) {
      // 通知父组件恢复的值
      onChange?.({
        id: valueId,
        name: valueName,
      });
    }
  }, [treeData, valueId, valueName]);

  const loadInitialData = () => {
    treeList({ isContainOldData: isContainOldData }).then((res) => {
      if (res?.code == "00000000") {
        // 为初始数据设置 isLeaf 属性，假设所有一级节点都可能有子节点
        const processedData = res.data.map((node) => ({
          ...node,
          isLeaf: false, // 一级节点默认不是叶子节点，显示折叠图标
          subRows: node.subRows || [], // 确保有 subRows 属性
        }));
        setTreeData(processedData);
      } else {
        setTreeData([]);
      }
    });
  };

  const loadData = async (node) => {
    // 防止重复加载
    if (loadingKeys.includes(node.id)) {
      return Promise.resolve();
    }

    // 检查节点是否已经有子数据，如果有则不需要重新加载
    if (node.subRows && node.subRows.length > 0) {
      return Promise.resolve();
    }

    setLoadingKeys((prev) => [...prev, node.id]);

    return new Promise((resolve) => {
      subTreeList(node.id)
        .then((res) => {
          if (res?.code == "00000000") {
            setTreeData((prevTreeData) =>
              updateTreeData(prevTreeData, node.id, res.data, false)
            );
          } else {
            setTreeData((prevTreeData) =>
              updateTreeData(prevTreeData, node.id, [], false)
            );
          }
        })
        .catch(() => {
          setTreeData((prevTreeData) =>
            updateTreeData(prevTreeData, node.id, [], false)
          );
        })
        .finally(() => {
          setLoadingKeys((prev) => prev.filter((key) => key !== node.id));
          resolve();
        });
    });
  };

  const updateTreeData = (list, key, children, forceLeaf = true) => {
    return list.map((node) => {
      if (node.id === key) {
        // 为新加载的子节点设置 isLeaf 属性
        const processedChildren = children.map((child) => ({
          ...child,
          isLeaf: false, // 假设子节点也可能有子节点，显示折叠图标
          subRows: child.subRows || [],
        }));

        return {
          ...node,
          subRows: processedChildren,
          isLeaf: forceLeaf ? processedChildren.length === 0 : false,
        };
      }
      if (node.subRows) {
        return {
          ...node,
          subRows: updateTreeData(node.subRows, key, children, forceLeaf),
        };
      }
      return node;
    });
  };

  // 清除选中的值
  const clearItem = () => {
    setValueId("");
    setValueName("");
    clearStorage();
    onClear?.(); // 调用外部传入的清除回调
  };

  React.useImperativeHandle(ref, () => ({
    setItem,
    clearItem,
  }));

  const setItem = (item) => {
    const id = item?.id || "";
    const name = item?.name || "";

    setValueId(id);
    setValueName(name);

    // 保存到 localStorage
    if (id && name) {
      saveToStorage(id, name);
    } else {
      clearStorage();
    }
  };

  return (
    <SearchTree
      treeData={treeData}
      fieldNames={{
        label: "name",
        value: "id",
        children: "subRows",
      }}
      virtual={true}
      showSearch={true}
      allowClear={!!(valueId && valueName)} // 只有当有值时才显示清除图标
      treeLine={true}
      treeDefaultExpandAll={false}
      size="medium"
      virtualThreshold={100}
      virtualItemSize={40}
      maxTagCount={3}
      loadData={loadData} // 恢复 loadData 以显示折叠图标
      // 尝试不同的value格式
      value={{ value: valueId, label: valueName }} // 使用对象格式
      labelInValue={true}
      placeholder={placeholder}
      onTreeExpand={(newExpandedKeys, info) => {
        console.log("onTreeExpand 被调用:", { newExpandedKeys, info });

        // 更新展开状态
        setExpandedKeys(newExpandedKeys);

        // 如果有 info 参数且包含节点信息，说明是从展开图标点击触发的
        if (info && info.node && info.expanded) {
          const node = info.node;
          console.log("展开节点:", node.name, "节点数据:", node);

          // 检查节点是否需要加载数据
          if (!node.subRows || node.subRows.length === 0) {
            console.log("🚀 调用 subTreeList 接口 - 节点:", node.name);
            // 这里的 loadData 调用已经在 SearchTree 组件的 handleExpandClick 中处理了
            // 不需要在这里重复调用
          } else {
            console.log("节点已有子数据，跳过接口调用:", node.name);
          }
        } else {
          // 兼容旧的调用方式，检查新展开的节点
          const newKey = newExpandedKeys.find(
            (key) => !expandedKeys.includes(key)
          );

          if (newKey) {
            console.log("新展开的节点 key:", newKey);

            // 查找对应的节点数据
            const findNode = (data, id) => {
              for (const node of data) {
                if (node.id === id) return node;
                if (node.subRows && node.subRows.length > 0) {
                  const found = findNode(node.subRows, id);
                  if (found) return found;
                }
              }
              return null;
            };

            const node = findNode(treeData, newKey);
            console.log("找到的节点数据:", node);

            // 只有当节点确实被展开且没有子数据时才加载
            if (node && (!node.subRows || node.subRows.length === 0)) {
              console.log("🚀 调用 subTreeList 接口 - 节点:", node.name);
              loadData(node);
            } else if (node && node.subRows && node.subRows.length > 0) {
              console.log("节点已有子数据，跳过接口调用:", node.name);
            }
          }
        }
      }}
      treeExpandedKeys={expandedKeys}
      onChange={(valObj, node) => {
        if (valObj?.value && valObj?.label) {
          setValueId(valObj.value);
          setValueName(valObj.label);

          // 保存到 localStorage
          saveToStorage(valObj.value, valObj.label);

          onChange?.({
            id: valObj.value,
            name: valObj.label,
            location: valObj?.location,
            ...node,
          });
        } else {
          setValueId("");
          setValueName("");

          // 清除 localStorage
          clearStorage();

          onChange?.(undefined);
        }
      }}
      onSelect={(value, node) => {
        if (value && node) {
          setValueId(value);
          setValueName(node.name);

          // 保存到 localStorage
          saveToStorage(value, node.name);

          // 手动关闭下拉框
          setTimeout(() => {
            const event = new MouseEvent("click", {
              bubbles: true,
              cancelable: true,
              view: window,
            });
            document.body.dispatchEvent(event);
          }, 100);

          onChange?.({
            id: value,
            name: node.name,
            ...node,
          });
        }
      }}
      // 确保选择后关闭下拉框
      dropdownStyle={{ maxHeight: 400, overflow: "auto" }}
      // 添加更多属性尝试解决问题
      showArrow={true}
      treeNodeFilterProp="name"
      treeNodeLabelProp="name"
    />
  );
});

export default ZKSearchTree;
