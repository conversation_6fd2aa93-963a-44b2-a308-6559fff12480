import React from 'react'
import  { useRef, useState } from "react";
import { Grid } from "@mui/material";
import Render<PERSON>ommon from "./RenderCommon";
import { useEffect } from "react";
import { getNewsByPage } from "@/service/api/editorApi";
import newTemp from "./newsList";

const RenderNews = (props) => {
  const elementRef = useRef(null);
  const info = props.info;
  const [animationDuration, setAnimationDuration] = useState(30);
  const [newsList, setNewsList] = useState([...newTemp]);
  const [categories, seCategories] = useState("");
  const [language, setLanguage] = useState("");
  const [locale, setLocale] = useState("");

  const getAnimationDuration = (speed, direction) => {
    let width = 100;
    let height = 100;
    if (elementRef.current) {
      width = elementRef.current.offsetWidth;
      height = elementRef.current.offsetHeight;
    }
    let speedTime = 3;
    switch (speed) {
      case 20:
        speedTime = 5;
        break;
      case 60:
        speedTime = 2;
        break;
      case 120:
        speedTime = 1.5;
        break;
      case 200:
        speedTime = 0.8;
        break;
      default:
        speedTime = 3;
    }
    if (direction === "up" || direction === "down") {
      setAnimationDuration(speedTime * (height / 100));
    } else {
      setAnimationDuration(speedTime * (width / 100));
    }
  };

  const getAnimation = (animation) => {
    let animationName = "";
    switch (animation) {
      case "up":
        animationName = "news-bottom-to-top-wrap";
        break;
      case "down":
        animationName = "news-top-to-bottom-wrap";
        break;
      case "left":
        animationName = "news-right-to-left-wrap";
        break;
      case "right":
        animationName = "news-left-to-right-wrap";
        break;
      default:
        animationName = "";
    }
    return animationName;
  };

  const getAlignItems = (direction) => {
    if (direction === "up" || direction === "down") {
      return "";
    } else {
      return "center";
    }
  };

  useEffect(() => {
    setTimeout(() => {
      getAnimationDuration(info.speed, info.scrollDirection);
    }, 300);
    seCategories(info.newsType);
    setLanguage(info.languageType);
    setLocale(info.newsArea);
  }, [info]);

  useEffect(() => {
    setTimeout(() => {
      getAnimationDuration(info.speed, info.scrollDirection);
    }, 300);
  }, [newsList]);

  const loadNews = () => {
    if (categories && language && locale) {
      getNewsByPage({
        categories,
        language,
        locale,
      }).then((res) => {
        let list = res.data;
        setNewsList(list);
      });
    } else {
      setNewsList([...newTemp]);
    }
  };

  useEffect(() => {
    loadNews();
  }, [categories, language, locale]);

  if (info.hide) {
    return "";
  }

  return (
    <RenderCommon {...props}>
      <Grid
        sx={{
          width: "100%",
          height: "100%",
          display: "flex",
          backgroundColor: info.bgColor,
          overflow: "hidden",
          opacity: info.transparency,
          alignItems: getAlignItems(info.scrollDirection),
        }}
      >
        <Grid
          ref={elementRef}
          style={{
            animationDuration: animationDuration + "s",
          }}
          className={getAnimation(info.scrollDirection)}
        >
          {newsList.map((item, index) => {
            return (
              <Grid
                sx={{
                  fontSize: info.fontSize,
                  fontFamily: info.font,
                  color: info.fontColor,
                  fontWeight: info.isBold ? "bold" : "normal",
                  fontStyle: info.isItaly ? "italic" : "normal",
                  textDecoration: info.isUnderline ? "underline" : "none",
                  textAlign: info.textAlign,
                  lineHeight: info.lineHeight,
                  // wordBreak: "break-all",
                  // whiteSpace: "pre-wrap",
                }}
                key={item.title}
              >
                <span style={{ margin: "0px 10px" }}>{index + 1}、</span>
                {item.title}
                {index !== newsList.length - 1 && (
                  <span style={{ width: "20px" }}></span>
                )}
              </Grid>
            );
          })}
        </Grid>
      </Grid>
    </RenderCommon>
  );
};

export default RenderNews;
