import { getFileSize } from "@/utils/zkUtils";
import { getTreeSelect } from "@/service/api/materialGroup";
import {
    uploadFile,
    getS3Token,
    uploadFileUrl,
    uploadVideoUrl,
    checkUploadFileName,
    saveFileToUploadAws,
} from "@/service/api/material";
import { extractVideoInfo } from "@/utils/plugins/video-parser/parser";
import { buildS3Client, uploadFile as s3Upload } from "@/utils/awsS3Utils";
import ChunkUploader from "@/utils/uploadUtils";
import { md5 } from "hash-wasm";
import axios from 'axios'
import { getToken } from '@/utils/auth'
import { toast } from "react-toastify";
export const controlAddLabelIsShow = (userInfor, setControlLabelShow) => {
    //超管和租户不受限，没有权限的不显示
    if (
        userInfor.roleKey != "superAdmin" &&
        userInfor.roleKey != "admin" &&
        !userInfor?.permission?.includes("resource:material:addLabel")
    ) {
        setControlLabelShow(false);
    }
};

/**
 * 验证上传文件的有效性
 * @param {File} file - 要验证的文件对象
 * @returns {Object} 包含验证结果的对象
 * @property {boolean} valid - 文件是否有效
 * @property {string[]} [errors] - 如果文件无效,包含错误信息的数组
 */
export const validatorFile = (file, files) => {
    // 获取文件名(不包含扩展名)
    let fileName = file.name.substring(0, file.name.lastIndexOf("."));

    // 检查是否存在同名文件
    for (let i = 0; i < files.length; i++) {
        let filestemName = files[i].name.substring(0, file.name.lastIndexOf("."));
        if (fileName === filestemName) {
            // 如果存在同名文件,设置错误信息并返回无效结果
            setErrorMsg(
                t("common.common_upload_name_exist", {
                    name: file.name,
                })
            );
            return {
                valid: false,
                errors: [
                    t("common.common_upload_name_exist", {
                        name: file.name,
                    }),
                ],
            };
        }
    }

    // 获取文件大小(单位:MB)
    const fileSize = getFileSize(file.size);
    const suffix = `(jpg|png|jpeg|mp4)`;
    // 创建文件扩展名正则表达式
    // eslint-disable-next-line no-useless-escape
    let regular = new RegExp(`.*\.${suffix}`);

    // 检查文件类型
    if (!regular.test(file.name.toLocaleLowerCase())) {
        // 如果文件类型不支持,设置错误信息并返回无效结果
        setErrorMsg(
            t("common.common_upload_type_not_support", {
                type: file.type,
            })
        );
        return {
            valid: false,
            errors: [
                t("common.common_upload_type_not_support", {
                    type: file.type,
                }),
            ],
        };
    } else if (fileSize > 1024) {
        // 如果文件大小超过1GB,设置错误信息并返回无效结果
        setErrorMsg(
            t("common.common_upload_file_size_max_1G", {
                fileSize: fileSize,
            })
        );
        return {
            valid: false,
            errors: [
                t("common.common_upload_file_size_max_1G", {
                    fileSize: fileSize,
                }),
            ],
        };
    } else {
        // 文件验证通过,返回有效结果
        return { valid: true };
    }
};

export const updateFiles = (incommingFiles, setIsFilesError, setFiles, files) => {
    setIsFilesError(false);
    if (files.length + incommingFiles.length > 6) {
        if (incommingFiles.length == 6) {
            setFiles(incommingFiles);
        } else {
            const tempFiles = [...files];
            const addFileLen = 6 - files.length;
            const skliceFile = incommingFiles.slice(0, addFileLen);
            setFiles([...tempFiles, ...skliceFile]);
        }
    } else {
        const tempdataFileList = [...files];
        incommingFiles.forEach((element) => {
            tempdataFileList.push(element);
        });
        setFiles(tempdataFileList);
    }
};

export const getOption = (merchantId, setGroups) => {
    getTreeSelect({
        departmentId: merchantId,
    }).then((res) => {
        setGroups(res.data);
    });
};

const getAwsS3Client = async () => {
    let s3Client;
    let updateConfig;
    try {
        const tokenRes = await getS3Token();
        updateConfig = tokenRes.data;
        s3Client = await buildS3Client(updateConfig);
        updateConfig.credentials.expiration = new Date(
            updateConfig.credentials.expiration
        );
        return Promise.resolve({
            s3Client: s3Client,
            updateConfig: updateConfig,
        });
    } catch (e) {
        console.error(e);
        throw new Error("获取令牌失败", e);
    }
};

//分片上传代码
const chuckUpload = async (file, fileStatus) => {
    return new Promise((reslove, reject) => {
        // 初始化上传状态
        fileStatus.progress = 0; // 进度归零
        fileStatus.uploadStatus = "uploading"; // 设置状态为上传中

        // 创建分片上传器实例
        const uploader = new ChunkUploader({
            uploadUrl: "/sd/v1/material/s3/multipart", // 分片上传接口地址
            // mergeUrl: "/v1/material/s3/multipart", // 分片合并接口地址
            chunkSize: 5 * 1024 * 1024, // 设置分片大小为5MB（常见分片大小）
        });

        // 将上传器实例保存到ref（可能是为了在React组件中控制上传行为）
        uploaderRef.current = uploader;

        // 执行上传操作
        uploader
            .upload(
                file, // 要上传的文件对象
                (progress) => {
                    // 进度回调函数
                    // 防止上传完成后的重复更新（当进度已达100%时不再更新）
                    if (fileStatus.progress == 100 && progress == 100) {
                        return;
                    }
                    fileStatus.progress = progress; // 实时更新上传进度
                }
            )
            .then((res) => {
                reslove(res); // 上传成功时解析Promise
            })
            .catch((err) => {
                reject(err); // 上传失败时拒绝Promise
            });
    });
};

//校验文件名称
const chuckUploadCheckFileName = async (fileName) => {
    return new Promise((reslove, reject) => {
        checkUploadFileName({
            fileName: fileName,
        })
            .then((res) => {
                reslove(res?.data);
            })
            .catch((err) => {
                reject(err);
            });
    });
};

//提交表单
const uploadMaterial = async (md5, token, groupId, merchantId) => {
    return new Promise((reslove, reject) => {
        saveFileToUploadAws(groupId, merchantId, {
            hash: token,
            md5: md5,
        })
            .then((res) => {
                reslove(res);
            })
            .catch((err) => {
                reject(err);
            });
    });
};

//表单提交
export const handelSaveSubmit = async (
    formValues,
    files,
    setIsFilesError,
    setUploadLoding,
    setUploadBtnDisable,
    isAccelerate,
    setFiles, onCancel, t
) => {
    // 校验文件
    if (files.length === 0) {
        setIsFilesError(true); // 如果没有文件，设置文件错误状态为true
        return; // 直接返回，不继续执行
    }
    setIsFilesError(false); // 如果有文件，设置文件错误状态为false
    // setIsAvartiserError(false); // 注释掉的代码，可能是之前用于设置广告商错误状态的

    // 设置按钮为加载状态
    setUploadLoding(true);
    // 设置按钮为禁用状态
    setUploadBtnDisable(true);

    // 从formValues中提取需要的值
    const values = {
        departmentId: formValues.advertiserId,
        groupId: formValues.groupId,
        ageRange: formValues.ageRange,
        gender: formValues.gender,
    };

    let updateConfig, s3Client;
    if (isAccelerate) {
        // 如果需要加速，获取AWS S3客户端配置
        const clientData = await getAwsS3Client(isAccelerate);
        updateConfig = clientData.updateConfig; // 更新配置
        s3Client = clientData.s3Client; // S3客户端
    }

    const errorFiles = []; // 用于存储上传失败的文件

    // 遍历所有文件
    for (let i = 0; i < files.length; i++) {
        if (isAccelerate) {
            // 如果需要加速上传
            try {
                files[i].uploadStatus = "preparing"; // 设置文件上传状态为准备中
                const file = files[i].file; // 获取当前文件
                const fileSuffix = file.name.substring(file.name.lastIndexOf(".")); // 获取文件后缀

                // 生成文件路径
                const filePath =
                    updateConfig?.dirPath +
                    userInfor.id +
                    new Date().getTime() +
                    Math.floor(Math.random() * 10000) +
                    fileSuffix;

                files[i].uploadStatus = "uploading"; // 设置文件上传状态为上传中
                const reponse = await s3Upload(
                    s3Client,
                    file,
                    updateConfig?.bucketName,
                    filePath,
                    (progress) => {
                        console.log(progress); // 打印上传进度
                        files[i].progress = progress; // 更新文件上传进度
                        if (progress == 100) {
                            files[i].uploadStatus = "success"; // 如果进度为100%，设置上传状态为成功
                        }
                    }
                );

                // 获取ETag并去除双引号
                let eTag = reponse.ETag.replace(/"/g, "");
                eTag = eTag.replace(/-\d+$/, "");
                values.eTag = eTag; // 设置ETag
                values.fileUrl =
                    "https://" +
                    updateConfig?.bucketName +
                    ".s3." +
                    updateConfig.region +
                    ".amazonaws.com/" +
                    filePath; // 设置文件URL
                values.name = file.name.substring(0, file.name.lastIndexOf(".")); // 设置文件名（不含后缀）

                const videoExtensions = [".mp4"]; // 视频文件扩展名
                if (videoExtensions.includes(fileSuffix)) {
                    // 如果是视频文件
                    try {
                        const data = await extractVideoInfo(file); // 提取视频信息
                        await uploadVideoUrl({
                            ...values,
                            duration: data.duration, // 视频时长
                            resolution: data.resolution, // 视频分辨率
                            thumbnail: data.thumbnail, // 视频缩略图
                            eTag: data.md5, // 视频MD5
                            fileSize: data.fileSize, // 视频文件大小
                        }).then((res) => {
                            files[i].progress = 100; // 设置上传进度为100%
                            files[i].uploadStatus = "success"; // 设置上传状态为成功
                        });
                    } catch (error) {
                        console.error("Error extracting video info:", error); // 打印视频信息提取错误
                        files[i].uploadStatus = "error"; // 设置上传状态为错误
                        errorFiles.push(files[i]); // 将错误文件加入错误文件列表
                    }
                } else {
                    // 如果不是视频文件
                    await uploadFileUrl(values).then((res) => {
                        files[i].progress = 100; // 设置上传进度为100%
                        files[i].uploadStatus = "success"; // 设置上传状态为成功
                    });
                }
            } catch (error) {
                files[i].uploadStatus = "error"; // 设置上传状态为错误
                files[i].uploadMessage = [
                    typeof error === "string" ? error : error.message, // 设置错误信息
                ];
                errorFiles.push(files[i]); // 将错误文件加入错误文件列表
            }
        } else {
            // 如果不需要加速上传
            // 获取文件大小（字节）
            const fileSize = files[i].file.size;
            // 将文件大小转换为兆字节（MB）
            const fileSizeMB = fileSize / (1024 * 1024);
            if (fileSizeMB <= 5) {
                await uploadDirect(values, files, i, errorFiles, onCancel);
            } else {
                await uploadMultipart(values, files, i, errorFiles, setFiles, onCancel)
            }
        }
    }

    // 设置按钮为加载状态
    setUploadLoding(false);
    // 设置按钮为禁用状态
    setUploadBtnDisable(false);

    if (errorFiles.length === 0) {
        // 如果没有错误文件
        toast.success(t("common.common_upload_all_message_success")); // 显示上传成功提示
        handleClose(); // 关闭上传窗口
        return; // 返回
    }

    setFiles([...errorFiles]); // 更新文件列表为错误文件列表
};

//直接上传文件
const uploadDirect = async (values, files, i, errorFiles, onCancel) => {
    // 如果文件小于等于5MB
    values.file = files[i].file; // 设置文件
    await uploadFile({
        ...values,

    })
        .then((res) => {
            files[i].progress = 100; // 设置上传进度为100%
            files[i].uploadStatus = "success"; // 设置上传状态为成功
            onCancel()
        })
        .catch((error) => {
            files[i].uploadStatus = "error"; // 设置上传状态为错误
            files[i].uploadMessage = [
                typeof error === "string" ? error : error.message, // 设置错误信息
            ];
            errorFiles.push(files[i]); // 将错误文件加入错误文件列表
        });
};

const CHUNK_SIZE = 1024 * 1024 * 5; // 5MB

const httpInstance = axios.create({
    baseURL: import.meta.env.VITE_APP_BASE_API,
    timeout: 30000,
    headers: {
        Authorization: "Bearer " + getToken(),
    },
});

//处理第一段分片
const multipartUpload = (multipartBo, groupId, departmentId) => {
    return httpInstance.post(`/sd/v1/material/s3/multipart/${groupId}/${departmentId}`, multipartBo);
};


// 大文件分片上传
const uploadMultipart = async (values, files, i, errorFiles, setFiles, onCancel) => {
    files[i].uploadStatus = "preparing";
    const file = files[i].file;

    try {
        // await chuckUploadCheckFileName(file.name)
        const chunks = [];
        for (let i = 0; i < file.size; i += CHUNK_SIZE) {
            const chunk = file.slice(i, i + CHUNK_SIZE);
            chunks.push(chunk);
        }
        // chunk0转uint8array
        const chunk0 = await chunks[0].arrayBuffer();
        const uint8array0 = new Uint8Array(chunk0);
        // 第一片的md5
        const firstChunkMd5 = await md5(uint8array0);
        const resp = await multipartUpload({
            status: "initiate",
            fileSize: file.size,
            originalName: file.name,
            md5Digest: firstChunkMd5,
        }, values.groupId, values.departmentId); // 提交表单
        const uploadId = resp.data.data.uploadId;
        const partUploadList = [];
        files[i]['uploadStatus'] = "uploading";
        files[i]['progress'] = 0;
        let tempProgress = 0;
        for (let j = 0; j < chunks.length; j++) {
            const chunk = chunks[j];
            const uploadResp = await multipartUpload({
                status: "upload",
                uploadId,
                partNumber: j + 1,
                fileSize: file.size
            }, values.groupId, values.departmentId);


            // 拿到上传地址 privateUrl
            const privateUrl = uploadResp.data.data.privateUrl;
            const minioResp = await axios.put(privateUrl, chunk, {
                headers: {
                    "Content-Type": "application/octet-stream",
                },
            });

            // 从headers拿到etag
            const eTag = minioResp.headers["etag"];
            partUploadList.push({
                partNumber: j + 1,
                entryTag: eTag,
            });
            // 上传进度
            const newProgress = Math.round(((j + 1) / chunks.length) * 100);
            tempProgress = newProgress;
            setFiles(prevFiles => {
                const newFiles = [...prevFiles];
                newFiles[i] = {
                    ...newFiles[i],
                    progress: newProgress,

                };
                return newFiles;
            });
        }

        const data = await extractVideoInfo(file); // 提取视频信息
        // 合并
        await multipartUpload({
            status: "complete",
            uploadId,
            duration: data.duration, // 视频时长
            resolution: data.resolution, // 视频分辨率
            thumbnail: data.thumbnail, // 视频缩略图
            fileSize: file.size,
            originalName: file.name,
            partUploadList,
        }, values.groupId, values.departmentId);
        if (tempProgress == 100) {
            setFiles(prevFiles => {
                return {
                    ...prevFiles,
                    progress: tempProgress,
                    uploadStatus: "success",
                };
            });

            onCancel()
        }
    } catch (error) {

        files[i].uploadStatus = "error"; // 设置上传状态为错误
        files[i].uploadMessage = [
            typeof error === "string" ? error : error.message, // 设置错误信息
        ];
        errorFiles.push(files[i]); // 将错误文件加入错误文件列表
    }


}