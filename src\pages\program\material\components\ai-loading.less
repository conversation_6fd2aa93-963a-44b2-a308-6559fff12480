.rotating-plane {
  min-width: 60px;
  min-height: 60px;
  width: 100px;
  height: 100px;
  background-color: #7ac143;
  -webkit-animation: rotateplane 1.2s infinite ease-in-out;
  -moz-animation: rotateplane 1.2s infinite ease-in-out;
  -o-animation: rotateplane 1.2s infinite ease-in-out;
  animation: rotateplane 1.2s infinite ease-in-out;
}

@keyframes rotateplane {
  0% {
    transform: perspective(120px) rotateX(0deg) rotateY(0deg);
  }
  50% {
    transform: perspective(120px) rotateX(-180.1deg) rotateY(0deg);
  }
  100% {
    transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg);
  }
}

.loading-box {
  width: 25vw;
  height: 50vh;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  gap: 40px;
  background: #f0f0f0;
  border-radius: 13px;
  margin-bottom: 10px;
  // border: 1px solid #d9d9d9;
}

.cube-grid{
  min-width: 60px;
  min-height: 60px;
  width: 100px;
  height: 100px;
}
.cube-grid .sk-cube {
  width: 33%;
  height: 33%;
  min-width: 20px;
  min-height: 20px;
  background-color: #7ac143;
  float: left;
  -webkit-animation: sk-cubeGridScaleDelay 1.3s infinite ease-in-out;
  -moz-animation: sk-cubeGridScaleDelay 1.3s infinite ease-in-out;
  -o-animation: sk-cubeGridScaleDelay 1.3s infinite ease-in-out;
  animation: sk-cubeGridScaleDelay 1.3s infinite ease-in-out;
}
.cube-grid .sk-cube:nth-child(1) {
  -webkit-animation-delay: 0.2s;
  -moz-animation-delay: 0.2s;
  -o-animation-delay: 0.2s;
  animation-delay: 0.2s; }
.cube-grid .sk-cube:nth-child(2) {
  -webkit-animation-delay: 0.3s;
  -moz-animation-delay: 0.3s;
  -o-animation-delay: 0.3s;
  animation-delay: 0.3s; }
.cube-grid .sk-cube:nth-child(3) {
  -webkit-animation-delay: 0.4s;
  -moz-animation-delay: 0.4s;
  -o-animation-delay: 0.4s;
  animation-delay: 0.4s; }
.cube-grid .sk-cube:nth-child(4) {
  -webkit-animation-delay: 0.1s;
  -moz-animation-delay: 0.1s;
  -o-animation-delay: 0.1s;
  animation-delay: 0.1s; }
.cube-grid .sk-cube:nth-child(5) {
  -webkit-animation-delay: 0.2s;
  -moz-animation-delay: 0.2s;
  -o-animation-delay: 0.2s;
  animation-delay: 0.2s; }
.cube-grid .sk-cube:nth-child(6) {
  -webkit-animation-delay: 0.3s;
  -moz-animation-delay: 0.3s;
  -o-animation-delay: 0.3s;
  animation-delay: 0.3s; }
.cube-grid .sk-cube:nth-child(7) {
  -webkit-animation-delay: 0s;
  -moz-animation-delay: 0s;
  -o-animation-delay: 0s;
  animation-delay: 0s; }
.cube-grid .sk-cube:nth-child(8) {
  -webkit-animation-delay: 0.1s;
  -moz-animation-delay: 0.1s;
  -o-animation-delay: 0.1s;
  animation-delay: 0.1s; }
.cube-grid .sk-cube:nth-child(9) {
  -webkit-animation-delay: 0.2s;
  -moz-animation-delay: 0.2s;
  -o-animation-delay: 0.2s;
  animation-delay: 0.2s; }

@-webkit-keyframes sk-cubeGridScaleDelay {
  0%, 70%, 100% {
      -webkit-transform: scale3D(1, 1, 1);
  }
  35% {
      -webkit-transform: scale3D(0, 0, 1);
  }
}
@-moz-keyframes sk-cubeGridScaleDelay {
  0%, 70%, 100% {
      moz-transform: scale3D(1, 1, 1);
  }
  35% {
      -moz-transform: scale3D(0, 0, 1);
   }
}
@-o-keyframes sk-cubeGridScaleDelay {
  0%, 70%, 100% {
      -o-transform: scale3D(1, 1, 1);
  }
  35% {
      -o-transform: scale3D(0, 0, 1);
  }
}
@keyframes sk-cubeGridScaleDelay {
  0%, 70%, 100% {
      transform: scale3D(1, 1, 1);
  }
  35% {
      transform: scale3D(0, 0, 1);
  }
}
