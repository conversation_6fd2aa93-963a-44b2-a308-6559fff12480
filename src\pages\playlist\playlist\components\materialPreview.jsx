/* eslint-disable react/prop-types */
import React, { forwardRef } from 'react';
// 消息提示
import { Dialog, DialogActions, DialogContent, DialogTitle, Container, Stack, Pagination, IconButton } from '@mui/material';
import envConfig from '@/config/env.config';
import ReactPlayer from 'react-player';
import CloseIcon from '@mui/icons-material/Close';
// i18n
import { useTranslation } from 'react-i18next';
const Preview = forwardRef((props, ref) => {
    const { t } = useTranslation();
    const typeObj = {
        image: 'image',
        media: 'media',
        audio: 'audio'
    };
    const type = props.type.trimRight();
    const url = props.url;
    // 素材预览弹窗
    const [open, setOpen] = React.useState(false);
    //视频播放开关
    const [playing, setPlaying] = React.useState(true);

    React.useImperativeHandle(ref, () => ({
        handleClose,
        handleOpen
    }));
    const handleClose = () => {
        setOpen(false);
    };
    const handleOpen = () => {
        setOpen(true);
    };
    return (
        <>
            <Dialog open={open} fullWidth={true} onClose={handleClose} aria-describedby="alert-dialog-slide-description">
                <DialogTitle>
                    {t('common.common_op_preview')}
                    <IconButton
                        aria-label="close"
                        onClick={handleClose}
                        sx={{
                            position: 'absolute',
                            right: 8,
                            top: 8,
                            color: (theme) => theme.palette.grey[500]
                        }}
                    >
                        <CloseIcon />
                    </IconButton>
                </DialogTitle>
                <DialogContent>
                    <Container maxWidth="lg">
                        <Stack direction="row" justifyContent="center" alignItems="center">
                            {type == typeObj.image && (
                                <>
                                    <img loading="lazy" style={{ width: '100%' }} alt="" src={url} />
                                </>
                            )}
                            {type == typeObj.audio && (
                                <>
                                    <audio src={url} autoplay={true} controls={true}></audio>
                                </>
                            )}
                            {/* {type == typeObj.file && (
                                <>
                                    <Stack direction="column" justifyContent="center" alignItems="center" spacing={2}>
                                        <Document file={url} onLoadSuccess={onDocumentLoadSuccess} loading="Please wait!" options={options}>
                                            <Page
                                                scale={scale}
                                                renderAnnotationLayer={false}
                                                renderTextLayer={false}
                                                pageNumber={currentPage}
                                            />
                                        </Document>
                                    </Stack>
                                </>
                            )} */}
                            {type == typeObj.media && (
                                <>
                                    <ReactPlayer url={url} playing={playing} controls={true} />
                                </>
                            )}
                        </Stack>
                    </Container>
                </DialogContent>
            </Dialog>
        </>
    );
});

export default Preview;
