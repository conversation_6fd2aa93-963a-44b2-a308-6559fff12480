import React from 'react'
import {  Grid } from "@mui/material";
import { useState } from "react";
import {
  FormLabel,
} from "./PropertiesComponent";
import CustomInput from "../components/CustomInput";
import { useEffect } from "react";
import ColorPick from "../components/ColorPick";
import { message } from "../common/i18n";

const WeatherComponent = (props) => {
  const currentPageIndex = props.currentPageIndex;
  const currentIndex = props.currentComponentIndex;
  const pages = props.pages;
  const activeTempIndex = props.activeTempIndex;
  const [properties, setProperties] = useState({
    type: "ZKTecoWeather", //组件类型
    left: 12,
    top: 15,
    width: 200,
    height: 50,
    zIndex: 50,
    hide: false, //是否隐藏
    url: "",
    fontColor:"",
    componentId: "",
  });

  useEffect(() => {
    if (currentIndex !== "") {
      const curretnPage = pages[currentPageIndex];
      if (curretnPage.isTemplate) {
        let componentInfo =
          curretnPage.tempLayout[activeTempIndex]?.componentList[currentIndex];
        setProperties({
          ...componentInfo,
        });
      } else {
        let componentInfo = pages[currentPageIndex].componentList[currentIndex];
        setProperties({
          ...componentInfo,
        });
      }
    }
  }, [currentPageIndex, currentIndex, activeTempIndex, pages]);

  const setComponentInfo = (baseInfo) => {
    const newPages = JSON.parse(JSON.stringify(props.pages));
    let currentPage = newPages[currentPageIndex];
    if (currentPage.isTemplate) {
      let oldInfo =
        currentPage.tempLayout[activeTempIndex].componentList[currentIndex];
      let newInfo = {
        ...oldInfo,
        ...baseInfo,
      };
      currentPage.tempLayout[activeTempIndex].componentList[currentIndex] =
        newInfo;
    } else {
      let oldInfo = newPages[currentPageIndex].componentList[currentIndex];
      let newInfo = {
        ...oldInfo,
        ...baseInfo,
      };
      newPages[currentPageIndex].componentList[currentIndex] = newInfo;
    }

    if (props.setPages) {
      props.setPages(newPages);
    }
  };

  const changeProperties = (event) => {
    const name = event.target.name;
    let newInfo = {
      ...properties,
      [name]: event.target.value,
    };
    setComponentInfo(newInfo);
  };


  return (
    <Grid
      sx={{
        width: "100%",
        boxShadow: "0px 0px 6px #00000029",
        borderRadius: "10px",
        backgroundColor: "#ffffff",
        minHeight: "200px",
        overflow: "hidden",
      }}
    >
      <Grid>
        <Grid
          sx={{
            p: 2,
          }}
        >
          <Grid
            sx={{
              display: "flex",
              alignItems: "center",
              mt: 1,
              mb:1
            }}
          >
            <FormLabel sx={{ mr: 2 }}>{message("editor_fontColor")}:</FormLabel>
            <ColorPick
              value={properties.fontColor}
              name="fontColor"
              onChange={changeProperties}
            ></ColorPick>
          </Grid>

          <CustomInput
            label={message("editor_abscissa") + ":"}
            value={properties.left}
            onChange={changeProperties}
            name="left"
          ></CustomInput>
          <CustomInput
            label={message("editor_ordinate") + ":"}
            value={properties.top}
            onChange={changeProperties}
            name="top"
          ></CustomInput>
          <CustomInput
            label={message("editor_width") + ":"}
            value={properties.width}
            onChange={changeProperties}
            name="width"
          ></CustomInput>
          <CustomInput
            label={message("editor_height") + ":"}
            value={properties.height}
            onChange={changeProperties}
            name="height"
          ></CustomInput>
        </Grid>
      </Grid>
    </Grid>
  );
};

export default WeatherComponent;
