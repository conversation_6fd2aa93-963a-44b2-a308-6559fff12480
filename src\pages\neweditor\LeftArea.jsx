import React from 'react'
import { Grid } from "@mui/material";
import { styled } from "@mui/material/styles";

import typeList from "./ComponentConfig";
import { useEffect, useState } from "react";

const WidgetButton = styled(Grid)(() => {
  return {
    "&.MuiGrid-root": {
      width: "80px",
      height: "70px",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      marginBottom: "10px",
      borderTopLeftRadius: "10px",
      borderBottomLeftRadius: "10px",
      cursor: "pointer",
    },
    "&:hover": {
      backgroundColor: "#e3e3e3",
    },
  };
});

const TypeButton = (props) => {
  const clickBtn = (type) => {
    if (props.onClick) {
      props.onClick(type);
    }
  };

  const [isActive, setIsActive] = useState(false);
  useEffect(() => {
    if (props.currentType === props.info.type) {
      setIsActive(true);
    } else {
      if (
        props.info.type === "widget" &&
        (props.currentType === "ZKTecoNews" ||
          props.currentType === "ZKTecoTime" || props.currentType === "ZKTecoLive" ||
          props.currentType === "ZKTecoWeather")
      ) {
        setIsActive(true);
      } else if (
        props.info.type === "scene" &&
        props.currentType === "templateSetting"
      ) {
        setIsActive(true);
      } else {
        setIsActive(false);
      }
    }
  }, [props.currentType]);

  return (
    <WidgetButton
      onClick={() => {
        clickBtn(props.info.type);
      }}
      sx={{
        "&.MuiGrid-root": {
          backgroundColor: isActive ? "#e3e3e3" : "",
        },
        "&:hover": {
          cursor: "pointer",
        },
      }}
    >
      {props.children}
    </WidgetButton>
  );
};

const Header = (props) => {
  const [isTemplate, setIsTemplate] = useState(false);
  const pages = props.pages;
  const currentPageIndex = props.currentPageIndex;

  const {isMobile } = props

  useEffect(() => {
    if (currentPageIndex !== "") {
      let componentInfo = pages[currentPageIndex];
      setIsTemplate(componentInfo.isTemplate);
    }
  }, [currentPageIndex, pages]);

  const clickBtn = (type) => {
    if (props.setCurrentType) {
      if (type === "scene") {
        props.setCurrentType("scene");
      } else {
        props.setCurrentType(type);
      }
    }
    if (props.setCurrentComponentId) {
      props.setCurrentComponentId("");
    }

    if (props.setCurrentComponentIndex) {
      props.setCurrentComponentIndex("");
    }

    if (type === "scene" && props.setActiveTempIndex && !isTemplate) {
      props.setActiveTempIndex("");
    }
  };
  return (
    <Grid
      sx={{
        width: "100%",
        display: "flex",
        flexDirection: "column",
        alignItems: "end",
        mt: 2,
      }}
    >
      {typeList.map((item) => {

        if (isMobile && !item.isMobile) {
           return null
        }

        return (
          <TypeButton
            info={item}
            key={item.type}
            currentType={props.currentType}
            onClick={clickBtn}
          >
            <Grid
              sx={{
                display: "flex",
                flexDirection: "column",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              {item.icon && (
                <Grid
                  sx={{
                    width: item.width || "25px",
                    height: item.height || "25px",
                    position: "relative",
                  }}
                >
                  <img
                    style={{
                      width: "100%",
                      height: "100%",
                    }}
                    src={item.icon}
                  ></img>
                </Grid>
              )}
              <Grid
                sx={{
                  color: "#707070",
                }}
              >
                {item.name}
              </Grid>
            </Grid>
          </TypeButton>
        );
      })}
    </Grid>
  );
};
export default Header;
