import request from "@/utils/request";
const baseProfixURI = "/sd/v1/material";
const baseTemplate = "/sd/v1/layout_template";
const baseLayout = "/sd/v1/material/layout"
export const saveLayout = (params) => {
  return request({
    url: `${baseLayout}`,
    method: "post",
    data: params,
  });
};

export const getResource = (params) => {
  return request({
    url: `${baseProfixURI}/page`,
    method: "get",
    params: params,
  });
};

export const existName = (params) => {
  return request({
    url: `${baseProfixURI}/exist_name`,
    method: "get",
    params: params,
  });
};

export const updateLayout = (params) => {
  return request({
    url: `${baseLayout}`,
    method: "PUT",
    data: params,
  });
};

export const getOptionByType = (type) => {
  return request({
    url: `/dict/data/getOptionByType/${type}`,
    method: "get",
  });
};

export const getIndustry = () => {
  return request({
    url: `${baseTemplate}/industry`,
    method: "get",
  });
};

export const saveTemplate = (params) => {
  return request({
    url: `${baseTemplate}`,
    method: "post",
    data: params,
  });
};

export const getTemplateList = (params) => {
  return request({
    url: `${baseTemplate}/page`,
    method: "get",
    params: params,
  });
};

export const updateTemplate = (params) => {
  return request({
    url: `${baseTemplate}`,
    method: "put",
    data: params,
  });
};

export const deleteTemplate = (id) => {
  return request({
    url: `${baseTemplate}/${id}`,
    method: "delete",
  });
};

export const uploadPsdImg = (params) => {
  return request({
    url: `${baseProfixURI}/psd/upload`,
    method: "post",
    data: params,
  });
};

export const getTemplateById = (id) => {
  return request({
    url: `${baseTemplate}/query/${id}`,
    method: "get",
  });
}
