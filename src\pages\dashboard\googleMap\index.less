.map-container {
  position: relative;
  width: 200px;
  height: 100px;
  //   background-color: #f5f5f5;
}

.marker {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  //   background-color: #4285f4;
  border-radius: 50%;
}

.ripple {
  position: absolute;
  top: 100%;
  //   bottom: 100%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 0;
  height: 0;
  padding: 10px;
  border-radius: 50%;
  background-color: rgba(122, 193, 67, 0.4);
  opacity: 0;
  animation: rippleAnim 2s ease-out infinite;
}

.ripple1 {
  position: absolute;
  top: 100%;
  //   bottom: 100%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 0;
  height: 0;
  padding: 10px;
  border-radius: 50%;
  background-color: rgba(122, 193, 67, 0.8);
  opacity: 0;
  animation: rippleAnim1 1s ease-in-out infinite;
}

@keyframes rippleAnim1 {
  0% {
    opacity: 0.2;
  }
  100% {
    width: 40px;
    height: 40px;
    opacity: 0;
  }
}

// .ripple2 {
//   position: absolute;
//   top: 100%;
//   left: 50%;
//   transform: translate(-50%, -50%);
//   width: 0;
//   height: 0;
//   padding: 10px;
//   border-radius: 50%;
//   background-color: rgba(122, 193, 67, 0.4);
//   opacity: 0;
//   animation: rippleAnim2 2s ease-out infinite;
// }

// @keyframes rippleAnim2 {
//   0% {
//     opacity: 0.4;
//   }
//   100% {
//     width: 20px;
//     height: 20px;
//     opacity: 0;
//   }
// }

@keyframes rippleAnim {
  0% {
    opacity: 0.7;
  }
  100% {
    width: 50px;
    height: 50px;
    opacity: 0;
  }
}
