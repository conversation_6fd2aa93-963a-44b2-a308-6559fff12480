@tailwind base;
@tailwind components;
@tailwind utilities;

/*整个滚动条*/
::-webkit-scrollbar {
  width: 7px;
  height: 7px;
  background-color: #f5f5f5;
}
/*定义滚动条轨道*/
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  background-color: #f5f5f5;
}
/*定义滑块*/
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  background-color: #c8c8c8;
}
.BMap_cpyCtrl {
  display: none;
}
.anchorBL {
  display: none;
}
.textSpace {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// ::-webkit-scrollbar-corner {
//     background-color: black;
// }
// ::-webkit-scrollbar-button {
//     background-color: #eee;
//     display: none;
// }
