import React, {
  useMemo,
  useState,
  useEffect,
  useRef,
  forwardRef,
  useImperativeHandle,
} from "react";
import MainCard from "@/components/MainCard";
import AddIcon from "@mui/icons-material/Add";
import {
  Box,
  Stack,
  Grid,
  Button,
  Typography,
  InputLabel,
  FormHelperText,
  RadioGroup,
  Radio,
  FormControlLabel,
  Select,
  MenuItem,
  OutlinedInput,
  InputAdornment,
  InputBase,
  Paper,
  TextField,
} from "@mui/material";
import * as Yup from "yup";
import { useFormik } from "formik";
import MaterialReactTable from "material-react-table";
import RemoveCircleOutlineIcon from "@mui/icons-material/RemoveCircleOutline";
import IconButton from "@mui/material/IconButton";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import CloseIcon from "@mui/icons-material/Close";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import { styled } from "@mui/material/styles";
import PropTypes from "prop-types";
import { useTranslation } from "react-i18next";
import Autocomplete from "@mui/material/Autocomplete";
import * as echarts from "echarts";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import VisibilityIcon from "@mui/icons-material/Visibility";
import ZKSelect from "@/components/ZKSelect";
const { t } = useTranslation();
const BootstrapDialog = styled(Dialog)(({ theme }) => ({
  "& .MuiDialogContent-root": {
    padding: theme.spacing(2),
  },
  "& .MuiDialogActions-root": {
    padding: theme.spacing(1),
  },
}));
const BootstrapDialogTitle = (props) => {
  const { children, onClose, ...other } = props;

  return (
    <DialogTitle sx={{ m: 0, p: 2 }} {...other}>
      {children}
      {onClose ? (
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            position: "absolute",
            right: 8,
            top: 8,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <CloseIcon />
        </IconButton>
      ) : null}
    </DialogTitle>
  );
};
BootstrapDialogTitle.propTypes = {
  children: PropTypes.node,
  onClose: PropTypes.func.isRequired,
};
// 方向
const locations = [
  {
    label: t("common.common_top"),
    value: 1,
  },
  {
    label: t("common.common_bottom"),
    value: 2,
  },
];
const duretions = [
  {
    label: t("common.common_left_to_right"),
    value: 1,
  },
  {
    label: t("common.common_right_to_left"),
    value: 2,
  },
];
const AddFont = forwardRef((props, ref) => {
  const [open, setOpen] = React.useState(false);
  useImperativeHandle(ref, () => ({
    handleClose,
    handleClickOpen,
  }));
  const handleClickOpen = () => {
    fontFormik.handleReset();
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
  };
  // 添加到资源页面
  const handelAddFontSubmit = (values) => {
    // 封装数据
    const font = {
      // 代表文字类型
      type: "1",
      name: t("editor.editor_word") + Math.floor(Math.random() * 100),
      advertiserName: "",
      duration: values.duration,
      text_position: values.position,
      text_direction: values.direction,
    };
    props.setMaterialList(font);
    handleClose();
  };
  //  表单
  const fontFormik = useFormik({
    initialValues: {
      text: "",
      position: "",
      direction: "",
      duration: "",
    },
    onSubmit: (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        handelAddFontSubmit(values);
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
    validationSchema: Yup.object().shape({
      text: Yup.string()
        .required("请输入滚动内容")
        .min(0, "滚动内容不能为空")
        .max(100, "滚动内容长度不大于100"),
      position: Yup.string().required("请选择文字放置位置"),
      direction: Yup.string().required("请选择滚动方向"),
      duration: Yup.number()
        .required("请输入播放时长")
        .moreThan(1, "时长至少大于1s"),
    }),
  });

  return (
    <div>
      <BootstrapDialog
        fullWidth
        maxWidth="sm"
        onClose={handleClose}
        aria-labelledby="customized-dialog-title"
        open={open}
      >
        <form noValidate onSubmit={fontFormik.handleSubmit}>
          <DialogContent>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="text">
                    {t("editor.editor_word")} <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <OutlinedInput
                    id="text"
                    fullWidth
                    value={fontFormik.values.text}
                    name="text"
                    multiline
                    rows={8}
                    onBlur={fontFormik.handleBlur}
                    onChange={fontFormik.handleChange}
                    placeholder={t("common.common_input_scroll_text")}
                    error={Boolean(
                      fontFormik.touched.text && fontFormik.errors.text
                    )}
                  />
                  {fontFormik.touched.text && fontFormik.errors.text && (
                    <FormHelperText error id="text-error">
                      {fontFormik.errors.text}
                    </FormHelperText>
                  )}
                </Stack>
              </Grid>
              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="duration">
                    {t("common.common_durations")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <OutlinedInput
                    id="duration"
                    fullWidth
                    type="number"
                    value={fontFormik.values.duration}
                    name="duration"
                    onBlur={fontFormik.handleBlur}
                    onChange={fontFormik.handleChange}
                    placeholder={t("common.common_input_playback_duration")}
                    error={Boolean(
                      fontFormik.touched.duration && fontFormik.errors.duration
                    )}
                  />
                  {fontFormik.touched.duration &&
                    fontFormik.errors.duration && (
                      <FormHelperText error id="duration-error">
                        {fontFormik.errors.duration}
                      </FormHelperText>
                    )}
                </Stack>
              </Grid>
              <Grid item xs={6}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="position">
                    {t("ips.ips_store_location")}{" "}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <ZKSelect
                    displayEmpty
                    name="position"
                    placeholder={t("common.common_select_place_place")}
                    onBlur={fontFormik.handleBlur}
                    onChange={fontFormik.handleChange}
                    value={fontFormik.values.position}
                    onClear={() => {
                      fontFormik.setFieldValue("position", "");
                    }}
                    options={locations}
                    inputProps={{ "aria-label": "Without label" }}
                    error={Boolean(
                      fontFormik.touched.position && fontFormik.errors.position
                    )}
                  />
                  {fontFormik.touched.position &&
                    fontFormik.errors.position && (
                      <FormHelperText error id="position-error">
                        {fontFormik.errors.position}
                      </FormHelperText>
                    )}
                </Stack>
              </Grid>
              <Grid item xs={6}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="direction">
                    {t("common.common_direction")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <ZKSelect
                    name="direction"
                    placeholder={t("common.common_select_direction")}
                    onBlur={fontFormik.handleBlur}
                    onChange={fontFormik.handleChange}
                    value={fontFormik.values.direction}
                    onClear={() => {
                      fontFormik.setFieldValue("direction", "");
                    }}
                    options={duretions}
                    error={Boolean(
                      fontFormik.touched.direction &&
                        fontFormik.errors.direction
                    )}
                  />
                  {fontFormik.touched.direction &&
                    fontFormik.errors.direction && (
                      <FormHelperText error id="direction-error">
                        {fontFormik.errors.direction}
                      </FormHelperText>
                    )}
                </Stack>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button color="info" variant="outlined" onClick={handleClose}>
              {t("common.common_edit_cancel")}
            </Button>
            <Button
              disableElevation
              type="submit"
              variant="contained"
              color="primary"
            >
              {t("common.common_edit_save")}
            </Button>
          </DialogActions>
        </form>
      </BootstrapDialog>
    </div>
  );
});
export default AddFont;
