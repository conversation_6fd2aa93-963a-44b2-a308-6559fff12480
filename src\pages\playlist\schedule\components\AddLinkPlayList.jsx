/* eslint-disable react/prop-types */
import { <PERSON><PERSON>, DialogContent, DialogTitle, IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import React, { useState, forwardRef, useImperativeHandle } from "react";
import PlayListTable from "./PlayListTable";
import { But<PERSON>, Stack } from "@mui/material";
import { t } from "i18next";

//播放清单
const AddLinkPlayList = forwardRef((props, ref) => {
  const { playListType } = props;
  // 播放清单选择弹窗
  const [open, setOpen] = useState(false);
  const [playListIndex, setPlayListIndex] = useState(0);
  const [screenIndex, setScreenIndex] = useState(0);
  const [tableObject, setTableObject] = useState([]);

  useImperativeHandle(ref, () => ({
    handleClose,
    handleOpen,
    setPlayListIndex,
    setScreenIndex,
  }));

  const handlePlayList = (playListId, playListName) => {
    props.setPlayListFormValues(
      playListId,
      playListName,
      playListIndex,
      screenIndex
    );
  };

  const handleClose = () => {
    setOpen(false);
  };
  const handleOpen = () => {
    setOpen(true);
  };
  return (
    <>
      <Dialog
        open={open}
        maxWidth="md"
        fullWidth={true}
        onClose={handleClose}
        aria-describedby="alert-dialog-slide-description"
      >
        <DialogTitle>
          {t("ips.ips_playlist_select")}
          <IconButton
            aria-label="close"
            onClick={handleClose}
            sx={{
              position: "absolute",
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500],
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <PlayListTable
            setTableObject={setTableObject}
            playListType={playListType}
          />
        </DialogContent>
        <Stack
          direction="row"
          justifyContent="flex-end"
          sx={{ margin: "15px" }}
        >
          <Button
            variant="contained"
            color="primary"
            disabled={tableObject.length > 1 || tableObject.length === 0}
            onClick={() => {
              handlePlayList(tableObject.id, tableObject.name);
              handleClose();
            }}
          >
            {t("common.common_edit_ok")}
          </Button>
        </Stack>
      </Dialog>
    </>
  );
});

export default AddLinkPlayList;
