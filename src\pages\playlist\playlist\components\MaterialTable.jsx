/* eslint-disable react/prop-types */
/* eslint-disable react-hooks/rules-of-hooks */
import React, {
  useMemo,
  useState,
  useEffect,
  useRef,
  forwardRef,
  useImperativeHandle,
} from "react";
import { Box, Stack, Typography, Tooltip } from "@mui/material";
import { tableI18n } from "@/utils/tableLang";
import MaterialReactTable from "material-react-table";
import RemoveCircleOutlineIcon from "@mui/icons-material/RemoveCircleOutline";
import IconButton from "@mui/material/IconButton";
import { useTranslation } from "react-i18next";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import CloseIcon from "@mui/icons-material/Close";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
// 素材列表页
const MaterialTable = forwardRef((props, ref) => {
  const { t } = useTranslation();
  const [isError, setIsError] = useState(false);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  // 表格数据
  const [data, setData] = useState([]);
  // 分页的参数
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);
  // 过滤参数
  const [globalFilter, setGlobalFilter] = useState("");
  // 排序参数
  const [sorting, setSorting] = useState([]);
  // 列过滤
  const [columnFilters, setColumnFilters] = useState([]);
  //记录当前素材的类型
  const [materialType, setMaterialType] = useState([]);
  // 构建参数
  const buildParams = () => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
    };
    return params;
  };
  const columns = useMemo(
    () => [
      {
        accessorKey: "name", //access nested data with dot notation
        header: t("common.common_name"),
        size: 100,
        Cell: ({ row }) => {
          return (
            <>
              {row?.original?.name === null ? (
                <Typography>-</Typography>
              ) : (
                <Tooltip title={row?.original?.name} placement="top">
                  <Typography
                    sx={{
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap",
                    }}
                  >
                    {row?.original?.name}
                  </Typography>
                </Tooltip>
              )}
            </>
          );
        },
      },
      {
        accessorKey: "type", //access nested data with dot notation
        header: t("ips.ips_type"),
        size: 60,
        Cell: ({ cell, row }) => {
          let title;
          switch (row.original.type) {
            case "media":
              title = t("ips.ips_media");
              break;
            case "image":
              title = t("ips.ips_picture");
              break;
            case "audio":
              title = t("common.common_audio");
              break;
            case "layout":
              title = t("common.common_layout");
              break;
            default:
              title = t("common.common_unknown");
              break;
          }
          return (
            <Tooltip title={title} placement="top">
              <Typography
                sx={{
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                }}
              >
                {title}
              </Typography>
            </Tooltip>
          );
        },
      },
      {
        accessorKey: "advertiserName", //access nested data with dot notation
        header: t("ips.ips_belong_retail"),
        size: 80,
        Cell: ({ cell, row }) => {
          if (
            row.original.advertiserName == undefined ||
            row.original.advertiserName === null ||
            row.original.advertiserName === ""
          ) {
            return <Typography>-</Typography>;
          } else {
            return (
              <Tooltip title={row?.original?.advertiserName} placement="top">
                <Typography
                  sx={{
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                  }}
                >
                  {row.original.advertiserName}
                </Typography>
              </Tooltip>
            );
          }
        },
      },
      {
        accessorKey: "groupName",
        header: t("common.common_material_category_table_column_name"),
        enableColumnActions: false,
        size: 80,
        Cell: ({ row }) => {
          return (
            <>
              {row?.original?.groupName === null ? (
                <Typography>-</Typography>
              ) : (
                <Tooltip title={row?.original?.groupName} placement="top">
                  <Typography
                    sx={{
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap",
                    }}
                  >
                    {row?.original?.groupName}
                  </Typography>
                </Tooltip>
              )}
            </>
          );
        },
      },
      {
        accessorKey: "duration", //access nested data with dot notation
        header: t("common.common_duration"),
        minSize: 100,
        maxSize: 200,
        size: 120,
        Cell: ({ cell, row }) => {
          if (
            row.original.duration == undefined ||
            row.original.duration === null
          ) {
            return <Typography>-</Typography>;
          } else {
            return <Typography>{row.original.duration}s</Typography>;
          }
        },
      },
    ],
    []
  );

  const [open, setOpen] = React.useState(false);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };
  // 追加列表
  const appendMaterialData = (valueData) => {
    console.log(valueData);
    const tempMaterialData = [...data];
    tempMaterialData.push(valueData);
    console.log(tempMaterialData);
    setData(tempMaterialData);
  };
  const appendMaterialDatas = (valueData) => {
    const tempMaterialData = [...data];
    valueData.forEach((item) => {
      tempMaterialData.push(item);
      setMaterialType([...materialType, item.type]);
    });
    setData(tempMaterialData);
    // setData([...data, ...valueData]);
  };
  const removeByIndex = (index) => {
    const removeType = data[index].type;
    let lastTypeArray = [];
    //删除列表中的资源
    const tempMaterialData = [...data];
    tempMaterialData.splice(index, 1);
    setData(tempMaterialData);
    //修改资源类型数组的数据
    tempMaterialData.forEach((data) => {
      lastTypeArray.push(data.type);
    });
    if (lastTypeArray.indexOf(removeType) === -1) {
      materialType.splice(materialType.indexOf(removeType), 1);
      setMaterialType(materialType);
    }
    // 删除图表
    props.removeChartsByIndex(index);
  };

  const handleSetMaterialType = (data) => {
    setMaterialType(data);
  };
  // 校验当前数据是否包含Layout,如果包含则返回true,否则返回false
  const checkMaterialhasLayot = () => {
    return data.some((item) => item.type === "layout");
  };
  const getMaterialDatas = () => {
    return data;
  };

  const removeLayout = () => {
    setData(data.filter((item) => item?.type !== "layout"));
  };

  const removeNotLayout = () => {
    setData(data.filter((item) => item?.type === "layout"));
  };

  useImperativeHandle(ref, () => ({
    appendMaterialData,
    appendMaterialDatas,
    checkMaterialhasLayot,
    removeNotLayout,
    getMaterialDatas,
    removeLayout,
    data,
    materialType,
    handleSetMaterialType,
  }));

  return (
    <>
      <MaterialReactTable
        // table 状态
        state={{
          // 加载状态
          isLoading,
          // 分页参数
          pagination,
          // 重新拉取
          showProgressBars: isRefetching,
          showAlertBanner: isError,
          columnFilters,
          columnPinning: { right: ["mrt-row-actions"] },
          globalFilter,
          sorting,
          density: "compact",
        }}
        // muiTableBodyCellProps={({ cell }) => ({
        //     onDoubleClick: (event) => {
        //         console.log(event, cell);
        //     }
        // })}
        enableRowOrdering
        displayColumnDefOptions={{
          "mrt-row-numbers": {
            header: t("common.common_sort_number"),
            enableOrdering: false,
            enablePinning: false,
            enableColumnActions: false,
          },
          "mrt-row-drag": {
            header: t("common.common_sort"),
          },
          "mrt-row-actions": {
            header: t("common.common_op_del"), //change header text
            size: 60, //make actions column wider
          },
        }}
        enableRowNumbers
        muiTablePaperProps={{
          elevation: 0,
          sx: {
            border: "1px solid",
            borderColor: "#e6ebf1",
          },
        }}
        // 设置table宽度
        // muiTablePaperProps={{
        //     sx: {
        //         maxWidth: '1588px',
        //         m: 'auto'
        //     }
        // }}
        autoResetPageIndex={false}
        muiTableBodyRowDragHandleProps={({ table }) => ({
          onDragEnd: () => {
            const { draggingRow, hoveredRow } = table.getState();
            if (hoveredRow && draggingRow) {
              data.splice(
                hoveredRow.index,
                0,
                data.splice(draggingRow.index, 1)[0]
              );
              setData([...data]);
            }
          },
        })}
        // 是否开启关闭头部底部工具类
        enableTopToolbar={false}
        enableColumnActions={false}
        enableBottomToolbar={false}
        // 关闭过滤搜素
        enableColumnFilters={false}
        // 关闭排序
        enableSorting={false}
        // 布局方式
        layoutMode="grid"
        // 开启列对齐
        muiTableHeadCellProps={{
          sx: {
            "& .Mui-TableHeadCell-Content": {
              justifyContent: "space-between",
            },
          },
        }}
        // 解决列太多宽度太长问题
        enableColumnResizing
        // enablePinning
        // 初始化状态
        initialState={{ columnVisibility: { createTime: true } }}
        muiToolbarAlertBannerProps={
          isError
            ? {
                color: "error",
                children: "Error loading data",
              }
            : undefined
        }
        // 开启多选
        // enableRowSelection
        // 列数
        // rowCount={rowCount}
        // 固定头部
        enableStickyHeader
        sx={{ boxShadow: "none" }}
        // 处理表格高度
        muiTableContainerProps={{ sx: { minHeight: "420px" } }}
        // 设置背景颜色
        muiTableBodyCellProps={({ row }) => ({
          onDoubleClick: (event) => {
            handleClickOpen();
            console.info(event, row);
          },
          sx: {
            backgroundColor: "white",
          },
        })}
        muiTableBodyProps={{
          sx: {
            backgroundColor: "white",
            tableLayout: "fixed",
            boxShadow: "none",
          },
        }}
        muiTableProps={{
          sx: {
            backgroundColor: "white",
            tableLayout: "fixed",
            boxShadow: "none",
          },
        }}
        muiBottomToolbarProps={{
          sx: { backgroundColor: "white", boxShadow: "none" },
        }}
        muiTopToolbarProps={{
          sx: { backgroundColor: "white", boxShadow: "none" },
        }}
        // 分页回调函数
        onPaginationChange={setPagination}
        // 记得开启这些 tips:否则会出现useEffect 反作用函数发起多次请求点击下一页会出问题
        manualFiltering
        manualPagination
        manualSorting
        // 列过滤
        onColumnFiltersChange={setColumnFilters}
        // 全局过滤
        onGlobalFilterChange={setGlobalFilter}
        // 排序
        onSortingChange={setSorting}
        // 开启分页
        enablePagination={false}
        // 列定义
        columns={columns}
        style={{ whiteSpace: "normal", wordWrap: "break-word" }}
        // 数据
        data={data}
        // 初始化国际化语言  --- todo: i18n key doc address: https://github.com/KevinVandy/material-react-table/blob/main/packages/material-react-table/src/_locales/en.ts
        localization={tableI18n}
        // 多选底部提示
        // positionToolbarAlertBanner="none"
        // 开启action操作
        enableRowActions
        // action操作位置
        positionActionsColumn="last"
        renderRowActions={({ row, table }) => (
          <Stack direction="row" spacing={1} alignItems="center">
            <IconButton
              color="primary"
              aria-label="cliear"
              component="label"
              onClick={() => {
                removeByIndex(row.index);
              }}
            >
              <RemoveCircleOutlineIcon />
            </IconButton>
          </Stack>
        )}
      />
    </>
  );
});

export default MaterialTable;
