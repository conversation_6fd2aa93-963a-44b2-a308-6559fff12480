import React from 'react'
import { useEffect, useState, useMemo } from "react";

const VideoPlay = (props) => {
  const [currentUrl, setCurrentUrl] = useState("");
  const [posterUrl, setPosterUrl] = useState("");
  useEffect(() => {
    setCurrentUrl(props.url);
  }, [props.url]);

  useEffect(() => {
    setPosterUrl(props.posterUrl);
  }, [props.posterUrl]);

  const VideoCom = useMemo(() => {
    if (currentUrl) {
      return (
        <video
          poster={posterUrl}
          style={{ width: "100%", maxHeight: "150px" }}
          src={currentUrl + "?_=" + new Date().getTime()}
        ></video>
      );
    } else {
      return "";
    }
  }, [currentUrl]);
  return VideoCom;
};

export default VideoPlay;
