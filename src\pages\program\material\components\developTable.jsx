import React from "react";
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/prop-types */
import { useEffect, useMemo, useState, forwardRef, useRef } from "react";
import MaterialReactTable from "material-react-table";
import { <PERSON><PERSON>, <PERSON>ack, Link, Grid, TextField } from "@mui/material";
import { useFormik } from "formik";
// api
import { listUserPageByMerchant } from "@/service/api/user";
import { useConfirm } from "@/components/zkconfirm";
import { useLocation } from "react-router-dom";

// import ProductDialog from './productDialog';
// i18n
import { useTranslation } from "react-i18next";
import { tableI18n } from "@/utils/tableLang";
import { removeEmpty } from "@/utils/StringUtils";
// import MainCard from "@/components/MainCard";
// import ZKSelect from "@/components/ZKSelect";
// import { getMerchantSelect } from "@/service/api/merchant";
import { getLoginInfor } from "@/service/api/user";
const DevopsTableList = forwardRef((props, ref) => {
  const { setTableObject = () => {} } = props;
  // const [merchantOptions, setMerchantOptions] = useState([]);
  const { t } = useTranslation();
  const confirm = useConfirm();
  const location = useLocation();
  const [isError, setIsError] = useState(false);
  const [rowSelection, setRowSelection] = useState([]);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  // 表格数据
  const [data, setData] = useState([]);
  // 分页的参数
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);
  // 排序参数
  const [sorting, setSorting] = useState([{ id: "createTime", desc: true }]);
  // 查询参数
  const requestParams = useRef(null);
  const treeSelectRef = useRef(null);
  // 构建参数
  const buildParams = () => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      orderProp: sorting[0]?.id,
      orderBy: sorting[0]?.desc == true ? "DESC" : "ASC",
      ...requestParams.current,
    };
    return params;
  };

  // 获取数据
  const getTableData = async () => {
    if (!data.length) {
      setIsLoading(true);
    } else {
      setIsRefetching(true);
    }
    await listUserPageByMerchant(buildParams())
      .then((res) => {
        // 设置数据
        setData(res.data.data);
        // 设置总记录数
        setRowCount(res.data.total);
        setIsLoading(false);
        setIsRefetching(false);
      })
      .catch((err) => {
        setIsError(true);
        setIsLoading(false);
        setIsRefetching(false);
      });
  };
  // 请求商户数据
  // const handleRequestMerchant = () => {
  //   getMerchantSelect("1").then((res) => {
  //     setMerchantOptions(res.data);
  //   });
  // };
  useEffect(() => {
    setRowSelection([]);
    // 发请求
    getTableData();
  }, [pagination.pageIndex, pagination.pageSize, location]);
  useEffect(() => {}, []);
  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "firstName",
        header: t("common.common_last_name"),
        enableColumnActions: false,
        enableSorting: false,
      },
      {
        accessorKey: "lastName",
        header: t("common.common_first_name"),
        enableColumnActions: false,
        enableSorting: false,
      },
      {
        accessorKey: "email",
        header: t("common.common_email"),
        enableColumnActions: false,
        enableSorting: false,
      },
      {
        accessorKey: "phone",
        header: t("ips.ips_phone"),
        enableColumnActions: false,
        enableSorting: false,
      },
    ],
    []
  );
  return (
    <>
      <MaterialReactTable
        // table 状态
        state={{
          // 加载状态
          isLoading,
          // 分页参数
          pagination,
          // 重新拉取
          showProgressBars: isRefetching,
          showAlertBanner: isError,
          rowSelection,
        }}
        // enableTopToolbar={false}
        //将表格右上角的操作选项隐藏
        renderToolbarInternalActions={({ table }) => <></>}
        muiTablePaperProps={{
          elevation: 0,
          sx: {
            borderRadius: "5px",
            border: "1px solid #f0f0f0",
          },
        }}
        muiTableHeadRowProps={{
          sx: { backgroundColor: "#fafafa", boxShadow: "none" },
        }}
        // 解决列太多宽度太长问题
        enableColumnResizing
        // enablePinning
        // 初始化状态
        initialState={{ columnVisibility: { createTime: true } }}
        muiToolbarAlertBannerProps={
          isError
            ? {
                color: "error",
                children: t("table.loading_error"),
              }
            : undefined
        }
        //行选中
        onRowSelectionChange={setRowSelection}
        muiTableBodyRowProps={({ row }) => ({
          onClick: row.getToggleSelectedHandler(),
          sx: { cursor: "pointer" },
        })}
        // 开启多选
        enableRowSelection
        // 列数
        rowCount={rowCount}
        // 固定头部
        enableStickyHeader
        // 处理表格高度
        muiTableContainerProps={{ sx: { maxHeight: "620px" } }}
        // 设置背景颜色
        muiTableBodyCellProps={{ sx: { backgroundColor: "white" } }}
        muiTableProps={{ sx: { backgroundColor: "white" } }}
        muiBottomToolbarProps={{ sx: { backgroundColor: "white" } }}
        muiTopToolbarProps={{ sx: { backgroundColor: "white" } }}
        // 分页回调函数
        onPaginationChange={setPagination}
        // 记得开启这些 tips:否则会出现useEffect 反作用函数发起多次请求点击下一页会出问题
        manualFiltering
        manualPagination
        manualSorting
        // 排序
        onSortingChange={setSorting}
        // 开启分页
        enablePagination
        // 列定义
        columns={columns}
        // 数据
        data={data}
        // 初始化国际化语言  --- todo: i18n key doc address: https://github.com/KevinVandy/material-react-table/blob/main/packages/material-react-table/src/_locales/en.ts
        localization={tableI18n}
        muiLinearProgressProps={({ isTopToolbar }) => ({
          sx: { display: isTopToolbar ? "block" : "none" },
        })}
        // 多选底部提示
        positionToolbarAlertBanner="none"
        // 自定义表头按钮
        renderTopToolbarCustomActions={({ table }) => {
          setTableObject(table.getSelectedRowModel().rows);
        }}
      />
    </>
  );
});

export default DevopsTableList;
