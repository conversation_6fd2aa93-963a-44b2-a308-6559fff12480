/* eslint-disable react-hooks/rules-of-hooks */
import React, { useEffect, useMemo, useState, useRef } from "react";
import MaterialReactTable from "material-react-table";
import {
  Button,
  Stack,
  IconButton,
  Avatar,
  Typography,
  Link,
  Grid,
  TextField,
} from "@mui/material";
import { getScreenListByStoreId } from "@/service/api/screen.js";
import { listByPage, remove } from "@/service/api/screenShot";
import { tableI18n } from "@/utils/tableLang";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
// i18n
import { useConfirm } from "@/components/zkconfirm";
// 消息提示
import { toast } from "react-toastify";
import { useTranslation } from "react-i18next";
import { useFormik } from "formik";
import { removeEmpty } from "@/utils/StringUtils";
import MainCard from "@/components/MainCard";
import ZKSelect from "@/components/ZKSelect";
import AuthButton from "@/components/AuthButton";

import BasicDateRangePicker from "@/components/datetimePicker";
import { dateFormate } from "@/utils/zkUtils";
import ScreenShotDetails from "./components/ScreenShotDetails";
const TotalOutlet = () => {
  const [open, setOpen] = useState(false);
  const dateRangeRef = React.useRef(null);
  const confirm = useConfirm();
  const { t } = useTranslation();
  const [rowSelection, setRowSelection] = useState([]);
  const [isError, setIsError] = useState(false);
  const [merchantOptions, setMerchantOptions] = useState([]);
  const [areaData, setAreaData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [idValue, setIdValue] = useState(undefined);
  const [showOpen, setShowOpen] = useState(false);
  const [record, setRecord] = useState(null);
  const [showSearch, setShowSearch] = useState(false);
  // 显示搜索
  // const [showSearch, setShowSearch] = useState(true);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  // 表格数据
  const [data, setData] = useState([]);
  // 分页的参数
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);

  // 删除总计
  const handelRemove = (ids) => {
    confirm({
      title: t("message.messageBox_title"),
      confirmationText: t("common.common_edit_ok"),
      cancellationText: t("common.common_edit_cancel"),
      description: t("common.common_delete_confirm_description"),
    }).then(() => {
      remove(ids).then((res) => {
        toast.success(res.message);
        getTableData();
        setRowSelection([]);
      });
    });
  };

  // 查询参数
  const requestParams = useRef(null);

  // 构建参数
  const buildParams = () => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      ...requestParams.current,
      // zoneId: Intl.DateTimeFormat().resolvedOptions().timeZone,
    };

    return params;
  };
  // 获取数据
  const getTableData = async () => {
    if (!data.length) {
      setIsLoading(true);
    } else {
      setIsRefetching(true);
    }

    await listByPage(buildParams())
      .then((res) => {
        // 设置数据
        setData(res.data.data);
        // 设置总记录数
        setRowCount(res.data.total);
        setIsLoading(false);
        setIsRefetching(false);
      })
      .catch((err) => {
        setIsError(true);
        setIsLoading(false);
        setIsRefetching(false);
      });
  };
  useEffect(() => {
    handleRequestMerchant();
  }, []);
  useEffect(() => {
    // const params = buildParams();
    // 发请求
    setRowSelection([]);
    getTableData();
  }, [pagination.pageIndex, pagination.pageSize]);
  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "url",
        header: t("common.common_screen_shot_preview_thumbnail"),
        enableColumnActions: false,
        enableSorting: false,
        Cell: ({ cell, row }) => {
          return <Avatar variant="square" src={row.original.url} />;
        },
      },
      {
        accessorKey: "merchantName",
        header: t("ips.ips_store_client_name"),
        enableColumnActions: false,
        enableSorting: false,
      },
      {
        accessorKey: "storeName",
        header: t("ips.ips_store_name"),
        enableColumnActions: false,
        enableSorting: false,
      },
      {
        accessorKey: "screenName",
        header: t("ips.ips_device"),
        enableColumnActions: false,
        enableSorting: false,
      },
      {
        accessorKey: "shotTime",
        header: t("common.common_screen_shot_time"),
        enableColumnActions: false,
        enableSorting: false,
        Cell: ({ cell, row }) => {
          return (
            <>
              {row.original.shotTimeStr
                ? row.original.shotTimeStr
                : row.original.shotTime}
            </>
          );
        },
      },
    ],
    []
  );
  // 查询表单
  const queryFormik = useFormik({
    initialValues: {
      merchantId: "",
      storeName: "",
      startTime: "",
      endTime: "",
      screenName: "",
    },
    onSubmit: async (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        // 拷贝value值
        const tempValue = { ...values };
        await removeEmpty(tempValue);
        // setRequestParams(tempValue);
        requestParams.current = tempValue;
        getTableData();
        // 查询table
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
  });
  // 重置查询
  const resetQuery = () => {
    // 重置表单
    queryFormik.resetForm();
    requestParams.current = null;
    dateRangeRef?.current?.restInputValue();
    setPagination({
      pageIndex: 0,
      pageSize: 10,
    });
    getTableData();
  };
  // 请求商户数据
  const handleRequestMerchant = () => {
    // getScreenListByStoreId("1").then((res) => {
    //   setMerchantOptions(res.data);
    // });
  };

  return (
    <>
      <MainCard style={{ marginBottom: "10px" }}>
        <form noValidate onSubmit={queryFormik.handleSubmit}>
          <Grid
            container
            direction="row"
            justifyContent="flex-start"
            alignItems="center"
            spacing={2}>
            <Grid item xs={6} sm={4} md={3}>
              <ZKSelect
                name="merchantId"
                size="small"
                value={queryFormik.values.merchantId}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                options={merchantOptions}
                onClear={() => {
                  queryFormik.setFieldValue("merchantId", "");
                }}
                placeholder={t("ips.ips_select_merchant")}
                menuWidth={200}
              />
            </Grid>
            <Grid item xs={6} sm={4} md={3}>
              <TextField
                label={t("ips.ips_store_name")}
                value={queryFormik.values.storeName}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                size="small"
                type="text"
                name="storeName"
                fullWidth
                placeholder={t("common.common_please_input_store_name")}
              />
            </Grid>
            <Grid item xs={6} sm={4} md={3}>
              <TextField
                label={t("ips.ips_device")}
                value={queryFormik.values.screenName}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                size="small"
                type="text"
                name="screenName"
                fullWidth
                placeholder={t("common.common_please_input")}
              />
            </Grid>
            {showSearch && (
              <Grid item xs={8} sm={5} md={3}>
                <BasicDateRangePicker
                  ref={dateRangeRef}
                  onChange={(value) => {
                    if (value?.startTime && value?.endTime) {
                      if (
                        value.startTime === "Invalid Date" ||
                        value.endTime === "Invalid Date"
                      ) {
                        queryFormik.setValues({
                          startTime: "",
                          endTime: "",
                        });
                        return;
                      }
                      queryFormik.setValues(value);
                    }
                  }}
                />
              </Grid>
            )}

            <Grid
              item
              xs={5}
              sm={3}
              md={2}
              direction="row"
              justifyContent="flex-end">
              <Stack
                direction="row"
                justifyContent="flex-start"
                alignItems="flex-start"
                spacing={2}>
                <Button
                  disableElevation
                  type="submit"
                  variant="contained"
                  size="small">
                  {t("common.common_table_query")}
                </Button>

                <Button
                  variant="outlined"
                  onClick={resetQuery}
                  color="info"
                  size="small"
                  sx={{
                    minWidth: "90px",
                  }}>
                  {t("common.common_op_reset")}
                </Button>
                <IconButton
                  onClick={() => setShowSearch(!showSearch)}
                  size="middle">
                  {!showSearch ? (
                    <>
                      <ExpandMoreIcon sx={{ mt: -0.5 }} />
                      <Typography sx={{ mt: -0.5 }}>
                        {t("common.common_form_unfold")}
                      </Typography>
                    </>
                  ) : (
                    <>
                      <ExpandLessIcon sx={{ mt: -0.5 }} />
                      <Typography sx={{ mt: -0.5 }}>
                        {t("common.common_form_packup")}
                      </Typography>
                    </>
                  )}
                </IconButton>
                {/* <IconButton
                                    onClick={() => setShowSearch(!showSearch)}
                                    size="middle"
                                >
                                    {showSearch ? (
                                        <>
                                            <ExpandMoreIcon sx={{ mt: -0.5 }} />
                                            <Typography sx={{ mt: -0.5 }}>
                                                {t("common.common_form_unfold")}
                                            </Typography>
                                        </>
                                    ) : (
                                        <>
                                            <ExpandLessIcon sx={{ mt: -0.5 }} />
                                            <Typography sx={{ mt: -0.5 }}>
                                                {t("common.common_form_packup")}
                                            </Typography>
                                        </>
                                    )}
                                </IconButton> */}
              </Stack>
            </Grid>
          </Grid>
        </form>
      </MainCard>
      <MaterialReactTable
        // table 状态
        state={{
          // 加载状态
          isLoading,
          // 分页参数
          pagination,
          // 重新拉取
          showProgressBars: isRefetching,
          showAlertBanner: isError,

          rowSelection,
        }}
        renderToolbarInternalActions={({ table }) => <></>}
        muiTablePaperProps={{
          elevation: 0,
          sx: {
            borderRadius: "5px",
            border: "1px solid #f0f0f0",
          },
        }}
        muiTableHeadRowProps={{
          sx: { backgroundColor: "#fafafa", boxShadow: "none" },
        }}
        // 关闭过滤搜素
        enableColumnFilters={true}
        // 关闭排序
        enableSorting={false}
        // 布局方式
        layoutMode="grid"
        // 开启列对齐
        muiTableHeadCellProps={{
          sx: {
            "& .Mui-TableHeadCell-Content": {
              justifyContent: "space-between",
            },
          },
        }}
        // 解决列太多宽度太长问题
        enableColumnResizing
        // enablePinning
        // 初始化状态
        initialState={{ columnVisibility: { createTime: true } }}
        muiToolbarAlertBannerProps={
          isError
            ? {
                color: "error",
                children: "Error loading data",
              }
            : undefined
        }
        //行选中
        onRowSelectionChange={setRowSelection}
        muiTableBodyRowProps={({ row }) => ({
          onClick: row.getToggleSelectedHandler(),
          sx: { cursor: "pointer" },
        })}
        // 开启多选
        enableRowSelection
        // 列数
        rowCount={rowCount}
        // 固定头部
        enableStickyHeader
        // 处理表格高度
        muiTableContainerProps={{ sx: { maxHeight: "570px" } }}
        // 设置背景颜色
        muiTableBodyCellProps={{ sx: { backgroundColor: "white" } }}
        muiTableProps={{
          sx: { backgroundColor: "white", tableLayout: "fixed" },
        }}
        muiBottomToolbarProps={{ sx: { backgroundColor: "white" } }}
        muiTopToolbarProps={{ sx: { backgroundColor: "white" } }}
        // 分页回调函数
        onPaginationChange={setPagination}
        // 记得开启这些 tips:否则会出现useEffect 反作用函数发起多次请求点击下一页会出问题
        manualFiltering
        manualPagination
        manualSorting
        // 开启分页
        enablePagination
        // 列定义
        columns={columns}
        // 数据
        data={data}
        // 初始化国际化语言  --- todo: i18n key doc address: https://github.com/KevinVandy/material-react-table/blob/main/packages/material-react-table/src/_locales/en.ts
        localization={tableI18n}
        // 自定义表头按钮
        renderTopToolbarCustomActions={({ table }) => {
          return (
            <Stack direction="row" spacing={1} alignItems="center">
              <AuthButton button="sd:report:screen_shot:delete">
                <Button
                  variant="contained"
                  color="secondary"
                  disabled={
                    !table.getIsSomeRowsSelected() &&
                    !table.getIsAllRowsSelected()
                  }
                  onClick={() => {
                    const ids = [];
                    // const names = [];
                    table.getSelectedRowModel().rows.map((row) => {
                      ids.push(row.original.id);
                    });
                    handelRemove(ids);
                  }}>
                  {t("common.common_op_batch_del")}
                </Button>
              </AuthButton>
            </Stack>
          );
        }}
        // 多选底部提示
        positionToolbarAlertBanner="none"
        // 开启action操作
        enableRowActions
        // action操作位置
        positionActionsColumn="last"
        displayColumnDefOptions={{
          "mrt-row-actions": {
            header: t("common.common_relatedOp"), //change header text
            size: 120, //make actions column wider
          },
        }}
        renderRowActions={(row, index, table) => (
          <Stack direction="row" spacing={1} alignItems="center">
            <Link
              component="button"
              underline="none"
              onClick={() => {
                setShowOpen(true);
                setRecord(row?.row?.original);
              }}>
              {t("common.common_op_view")}
            </Link>
            <AuthButton button="sd:report:screen_shot:delete">
              <Link
                component="button"
                underline="none"
                color="error"
                onClick={() => handelRemove(row.row.original.id)}>
                {t("common.common_op_del")}
              </Link>
            </AuthButton>
          </Stack>
        )}
      />
      <ScreenShotDetails
        data={record}
        open={showOpen}
        onClose={() => {
          setShowOpen(false);
          setRecord(undefined);
        }}
      />
    </>
  );
};
export default TotalOutlet;
