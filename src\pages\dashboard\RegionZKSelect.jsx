/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable react/prop-types */
import { useState, forwardRef, useImperativeHandle } from "react";
import { FormControl } from "@mui/material";
import ZKSelect from "@/components/ZKSelect";
import chinaRegions from "./ChinaRegions";
import { t } from "i18next";

// 省、市、区数据
const chinaRegionArray = chinaRegions;
const TableBarSearch = forwardRef((props, ref) => {
  //选中的省、市、区的值
  const [province, setProvince] = useState("");
  const [city, setCity] = useState("");
  const [region, setRegion] = useState("");
  //市、区的下拉数据
  const [cityArray, setCityArray] = useState([]);
  const [regionArray, setRegionArray] = useState([]);

  useImperativeHandle(ref, () => ({
    handleGetRegionInfo,
    handleClear,
  }));

  const handleGetRegionInfo = () => {
    return { province, city, region };
  };

  //选择省份后调用函数
  const handleGetCityArray = (province) => {
    const selectCityArray = chinaRegionArray.filter(
      (item) => item.name === province
    );
    setCityArray(selectCityArray[0].children);
    setCity("");
    setRegionArray([]);
    setRegion("");
  };
  //选择城市后调用函数
  const handleGetRegionArray = (city) => {
    const selectRegionArray = cityArray.filter((item) => item.name === city);
    setRegionArray(selectRegionArray[0].children);
    setRegion("");
  };

  const handleClear = () => {
    setProvince("");
    setCity("");
    setRegion("");
    setRegionArray([]);
    setCityArray([]);
  };
  return (
    <>
      <FormControl sx={{ m: 1, minWidth: 200, maxWidth: 200 }} size="small">
        <ZKSelect
          placeholder={t("ips.ips_store_province")}
          labelOptions={{ label: "name", value: "name" }}
          value={province}
          onChange={(event) => {
            const { value } = event.target;
            setProvince(value);
            handleGetCityArray(value);
          }}
          options={chinaRegionArray}
          onClear={() => {
            setProvince(undefined);
            setCity(undefined);
            setRegion(undefined);
            setRegionArray([]);
            setCityArray([]);
          }}
          menuWidth={200}
        />
      </FormControl>
      <FormControl sx={{ m: 1, minWidth: 200, maxWidth: 200 }} size="small">
        <ZKSelect
          placeholder={t("ips.ips_store_city")}
          labelOptions={{ label: "name", value: "name" }}
          value={city}
          onChange={(event) => {
            const { value } = event.target;
            setCity(value);
            handleGetRegionArray(value);
          }}
          options={cityArray}
          onClear={() => {
            setCity(undefined);
            setRegion(undefined);
            setRegionArray([]);
          }}
          menuWidth={200}
        />
      </FormControl>
      <FormControl sx={{ m: 1, minWidth: 200, maxWidth: 200 }} size="small">
        <ZKSelect
          placeholder={t("ips.ips_region")}
          labelOptions={{ label: "name", value: "name" }}
          value={region}
          onChange={(event) => {
            setRegion(event.target.value);
          }}
          options={regionArray}
          onClear={() => {
            setRegion(undefined);
          }}
          menuWidth={200}
        />
      </FormControl>
    </>
  );
});

export default TableBarSearch;
