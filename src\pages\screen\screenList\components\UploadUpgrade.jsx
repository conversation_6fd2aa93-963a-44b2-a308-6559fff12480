/* eslint-disable react-refresh/only-export-components */
/* eslint-disable no-debugger */
/* eslint-disable no-dupe-else-if */
/* eslint-disable react/jsx-key */
/* eslint-disable react-hooks/rules-of-hooks */
import {
  Dropzone,
  FileMosaic,
  FileCard,
  useFakeProgress,
} from "@files-ui/react";
import React, { useState, useRef } from "react";
import {
  Button,
  Stack,
  Typography,
  Grid,
  Alert,
  InputLabel,
  Box,
  FormHelperText,
  AlertTitle,
  TextField,
} from "@mui/material";
import {
  BootstrapActions,
  BootstrapContent,
  BootstrapDialog,
  BootstrapDialogTitle,
} from "@/components/dialog";
import { useTranslation } from "react-i18next";
import { uploadCompnatLang } from "@/utils/langUtils";

import { saveFirmware } from "@/service/api/upgradeFirmware";
import { getFileSize } from "@/utils/zkUtils";
// 消息提示
import { toast } from "react-toastify";
import { useFormik } from "formik";
import * as Yup from "yup";
import LoadingButton from "@mui/lab/LoadingButton";
import ChunkUploader from "@/utils/uploadUtils";

const UploadUpgrade = (props, ref) => {
  const { t } = useTranslation();
  const { open, onCancel, screenId } = props;
  const [files, setFiles] = useState([]);
  const [type, setType] = useState(undefined);
  const [errorMsg, setErrorMsg] = useState(undefined);
  const [filesError, setFilesError] = useState(
    t("common.common_plese_select_file")
  );

  const [isFilesError, setIsFilesError] = useState(false);
  const dropzoneRef = useRef(null);
  const progress = useFakeProgress();
  const [uploadLoading, setUploadLoding] = useState(false);
  const errorStyle = { border: "1px solid #ff4d4f" };

  // 表单定义
  const uploadFormik = useFormik({
    initialValues: {
      // type: type,
      version: "",
    },
    onSubmit: (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        handelSaveSubmit(values);
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
    validationSchema: Yup.object().shape({
      // type: Yup.string().required(t("common.common_select_package_type")),
      version: Yup.string()
        .matches(/^V/, t("common.common_enter_limit_start_v"))
        .required(t("common.common_enter_package_version")),
    }),
  });

  const updateFiles = (incommingFiles) => {
    setIsFilesError(false);
    let arrayFile = [];
    arrayFile.push(incommingFiles[incommingFiles.length - 1]);
    setFiles(arrayFile);
  };
  const removeFile = (id) => {
    setFiles([]);
  };
  // 样式
  const sxGridContainer = React.useMemo(
    () => ({
      display: "flex",
      flexWrap: "wrap",
      width: "100%",
      gap: 5,
    }),
    []
  );
  // 支持的格式，用于正则校验
  const suffix = `(apk)`;
  //表单提交
  const handelSaveSubmit = async (formValues) => {
    // 校验文件
    if (files.length === 0) {
      setIsFilesError(true);
      return;
    }
    setIsFilesError(false);
    // 设置按钮等于加载
    setUploadLoding(true);

    const values = {
      version: formValues.version,
      screenIdList: screenId,
    };
    const errorFiles = [];
    const uploader = new ChunkUploader({
      uploadUrl: "/file/chunk/upload",
      mergeUrl: "/file/chunk/merge",
    });

    values.file = files[0].file;
    const tempFile = { ...files[0] };
    tempFile.uploadStatus = "uploading";
    tempFile.progress = 10;
    let templFiles = [tempFile];
    setFiles(templFiles);
    uploader
      .upload(values.file, (progress) => {
        tempFile.progress = progress;
        let templFiles = [tempFile];
        console.log(progress);
        setFiles(templFiles);
      })
      .then((data) => {
        (tempFile.uploadMessage = "files-ui <3"),
          (tempFile.uploadStatus = "success");
        // console.log(data, values);
        if (!data?.flag) {
          toast.error("文件上传失败");
          setUploadLoding(false);
          return;
        }
        const requestData = {
          tokenId: data?.token,
          version: values?.version,
          screenId: screenId,
        };
        //处理提交
        saveFirmware(requestData)
          .then((res) => {
            setUploadLoding(false);
            // toast.success(res?.msg);
            toast.success(t("common.common_upload_all_message_success"));
            handleClose();
          })
          .catch((err) => {
            setUploadLoding(false);
          });
      })
      .catch(errorFiles.push(files[0]));
  };

  //关闭清除表单值
  const handleClose = () => {
    setFiles([]);
    uploadFormik.handleReset();
    onCancel();
  };

  return (
    <>
      <BootstrapDialog
        open={open}
        maxWidth={"xs"}
        fullWidth
        onClose={handleClose}>
        <form noValidate onSubmit={uploadFormik.handleSubmit}>
          <BootstrapDialogTitle onClose={handleClose}>
            <Typography variant="h4" component="p">
              {t("common.common_upload_upgrade_package")}
            </Typography>
          </BootstrapDialogTitle>
          <BootstrapContent>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Stack
                  justifyContent="flex-start"
                  alignItems="flex-start"
                  spacing={1}
                  sx={{ marginBottom: 2 }}>
                  <InputLabel htmlFor="version">
                    {t("ips.ips_version_code")}
                    <span style={{ color: "red" }}>*</span>
                  </InputLabel>
                  <TextField
                    id="version"
                    fullWidth
                    type="text"
                    placeholder={t("common.common_enter_package_version")}
                    variant="outlined"
                    name="version"
                    onBlur={uploadFormik.handleBlur}
                    onChange={uploadFormik.handleChange}
                    value={uploadFormik.values.version}
                    error={Boolean(
                      uploadFormik.touched.version &&
                        uploadFormik.errors.version
                    )}
                  />
                  {uploadFormik.touched.version &&
                    uploadFormik.errors.version && (
                      <FormHelperText error id="version-error">
                        {uploadFormik.errors.version}
                      </FormHelperText>
                    )}
                </Stack>
              </Grid>
              <Grid item xs={12}>
                <Stack
                  justifyContent="flex-start"
                  alignItems="flex-start"
                  spacing={1}
                  sx={{ marginBottom: 2 }}>
                  <InputLabel htmlFor="username-login">
                    {t("system.system_upload_file")}
                    <span style={{ color: "red" }}>*</span>
                  </InputLabel>
                  {files.length < 1 && (
                    <Dropzone
                      localization={() => uploadCompnatLang()}
                      ref={dropzoneRef}
                      style={{ ...(isFilesError ? errorStyle : {}) }}
                      autoClean
                      // fakeUpload={false}
                      // uploading
                      onUploadStart={(uploadAbleFiles) => {
                        console.log(uploadAbleFiles);
                      }}
                      uploadConfig={{ url: "#", autoUpload: false }}
                      disabled={files.length >= 1 ? true : false}
                      footer={false}
                      accept={"application/vnd.android.package-archive"}
                      validator={(file) => {
                        let fileName = file.name.substring(
                          0,
                          file.name.lastIndexOf(".")
                        );
                        // 校验文件类型名称无法同时上传相同的
                        for (let i = 0; i < files.length; i++) {
                          let filestemName = files[i].name.substring(
                            0,
                            file.name.lastIndexOf(".")
                          );
                          if (fileName === filestemName) {
                            setErrorMsg(
                              t("common.common_upload_name_exist", {
                                name: file.name,
                              })
                              // `文件名："${file.name}" , 相同，无法上传`
                            );
                            return {
                              valid: false,
                              errors: [
                                t("common.common_upload_name_exist", {
                                  name: file.name,
                                }),
                              ],
                            };
                          }
                        }
                        const fileSize = getFileSize(file.size);
                        // eslint-disable-next-line no-useless-escape
                        let regular = new RegExp(`.*\.${suffix}`);
                        if (!regular.test(file.name.toLocaleLowerCase())) {
                          setErrorMsg(
                            t("common.common_upload_type_not_support", {
                              type: file.type,
                            })
                          );
                          return {
                            valid: false,
                            errors: [
                              t("common.common_upload_type_not_support", {
                                type: file.type,
                              }),
                            ],
                          };
                        } else if (fileSize > 300) {
                          setErrorMsg(
                            t("common.common_upload_file_size_max", {
                              fileSize: fileSize,
                            })
                          );
                          return {
                            valid: false,
                            errors: [
                              t("common.common_upload_file_size_max", {
                                fileSize: fileSize,
                              }),
                            ],
                          };
                        } else {
                          return { valid: true };
                        }
                        // return regular.test(file.name) ? true : null;
                      }}
                      header={false}
                      uploadOnDrop={() => {
                        console.log("aaa");
                      }}
                      onChange={updateFiles}
                      value={files}
                      maxFileSize={1 * 1024 * 1024 * 1024}
                      maxFiles={1}
                      // accept={"image/*"}
                    >
                      <Stack
                        direction="column"
                        justifyContent="center"
                        alignItems="center"
                        spacing={1}>
                        <svg
                          t="1688974869714"
                          class="icon"
                          viewBox="0 0 1024 1024"
                          version="1.1"
                          xmlns="http://www.w3.org/2000/svg"
                          p-id="2826"
                          width="80"
                          height="80">
                          <path
                            d="M1024 640.192C1024 782.912 919.872 896 787.648 896h-512C123.904 896 0 761.6 0 597.504 0 451.968 94.656 331.52 226.432 302.976 284.16 195.456 391.808 128 512 128c152.32 0 282.112 108.416 323.392 261.12C941.888 413.44 1024 519.04 1024 640.192z m-341.312-139.84L512 314.24 341.312 500.48h341.376z m-213.376 0v256h85.376v-256H469.312z"
                            fill="#bfbfbf"
                            p-id="2827"></path>
                        </svg>
                        <Typography>
                          {t("common.common_drage_branch_file")}
                        </Typography>
                      </Stack>
                    </Dropzone>
                  )}

                  {isFilesError && (
                    <FormHelperText error id="files-error">
                      {filesError}
                    </FormHelperText>
                  )}
                </Stack>
                {errorMsg && (
                  <Alert
                    severity="error"
                    onClose={() => {
                      setFiles([]);
                      setErrorMsg(undefined);
                    }}
                    sx={{ marginBottom: 1 }}>
                    {errorMsg}
                  </Alert>
                )}

                <Alert severity="warning" sx={{ marginBottom: 2 }}>
                  <AlertTitle>{t("message.messageBox_title")}</AlertTitle>
                  <div
                    dangerouslySetInnerHTML={{
                      __html: t("common.common_only_file_apk"),
                    }}
                  />
                </Alert>
                {files.length > 0 && (
                  <Grid item xs={6}>
                    <Stack
                      justifyContent="flex-start"
                      alignItems="flex-start"
                      spacing={1}>
                      <Typography>{t("common.common_op_preview")}</Typography>
                      <Box sx={sxGridContainer} key="preview">
                        {files.map((file, index) => {
                          return (
                            <Box>
                              <FileMosaic
                                elevation={1}
                                info
                                smartImgFit={"center"}
                                localization={() => uploadCompnatLang()}
                                {...file}
                                progress={
                                  file?.progress === undefined
                                    ? 0
                                    : file?.progress
                                }
                                key={file?.id}
                                onDelete={removeFile}
                                preview
                              />
                            </Box>
                          );
                        })}
                      </Box>
                    </Stack>
                  </Grid>
                )}
              </Grid>
            </Grid>
          </BootstrapContent>
          <BootstrapActions>
            <Stack spacing={1} direction="row">
              <Button color="info" variant="outlined" onClick={handleClose}>
                {t("common.common_edit_cancel")}
              </Button>
              <LoadingButton
                loading={uploadLoading}
                variant="contained"
                color="primary"
                disableElevation
                // disabled={uploadBtnDisable}
                type="submit">
                {t("common.common_edit_ok")}
              </LoadingButton>
            </Stack>
          </BootstrapActions>
        </form>
      </BootstrapDialog>
    </>
  );
};
export default React.forwardRef(UploadUpgrade);
