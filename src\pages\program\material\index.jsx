import React from "react";
/* eslint-disable react/prop-types */
import { useState, useMemo, forwardRef } from "react";
import { Link as RouterLink } from "react-router-dom";
// project import
import Dot from "@/components/@extended/Dot";
// materiral
import { Tab, Box, IconButton } from "@mui/material";
import TabContext from "@mui/lab/TabContext";
import TabList from "@mui/lab/TabList";
import TabPanel from "@mui/lab/TabPanel";
import AppsIcon from "@mui/icons-material/Apps";
import FormatAlignJustifyIcon from "@mui/icons-material/FormatAlignJustify";
// project import
import MaterialTableList from "./components/materialTableList";
// i18n
import { useTranslation } from "react-i18next";
// project import
import MainCard from "@/components/MainCard";
import "./index.less";
import { reourceType } from "@/dict/commonDict";
import { useStateUserInfo } from "@/hooks/user";
const Material = () => {
  const userInfor = useStateUserInfo();
  const showMobileTab = userInfor.showMobileTab;
  const { t } = useTranslation();
  const [tabValue, setTableValue] = React.useState("all");
  const [type, setType] = React.useState("list");
  const handleTabChange = (event, newValue) => {
    setTableValue(newValue);
  };
  const switchType = () => {
    if (type === "list") {
      setType("card");
    } else {
      setType("list");
    }
  };

  return (
    <div>
      <TabContext value={tabValue}>
        <Box
          sx={{
            borderBottom: 1,
            borderColor: "divider",
            display: "flex",
            alignItems: "center",
            background: "white",
            justifyContent: "space-between",
          }}>
          <TabList
            onChange={handleTabChange}
            aria-label="lab API tabs example"
            style={{ background: "white" }}>
            {reourceType.map((item, index) => {
              if (item.value === "mobile" && !showMobileTab) {
                return null;
              } else {
                return (
                  <Tab label={item.label} value={item.value} key={index} />
                );
              }
            })}
          </TabList>
          <IconButton onClick={() => switchType()}>
            {type == "list" ? <AppsIcon /> : <FormatAlignJustifyIcon />}
          </IconButton>
        </Box>
        {/* 搜索栏 */}
        <TabPanel value="all" style={{ padding: "10px 0 0 0px" }}>
          <MaterialTableList type={type} tabValues={tabValue} />
        </TabPanel>
        <TabPanel value="image" style={{ padding: "10px 0 0 0px" }}>
          <MaterialTableList type={type} tabValues={tabValue} />
        </TabPanel>

        <TabPanel value="media" style={{ padding: "10px 0 0 0px" }}>
          <MaterialTableList type={type} tabValues={tabValue} />
        </TabPanel>

        <TabPanel value="layout" style={{ padding: "10px 0 0 0px" }}>
          <MaterialTableList type={type} tabValues={tabValue} />
        </TabPanel>

        {showMobileTab && (
          <TabPanel value="mobile" style={{ padding: "10px 0 0 0px" }}>
            <MaterialTableList type={type} tabValues={tabValue} />
          </TabPanel>
        )}
      </TabContext>
    </div>
  );
};
export default Material;
