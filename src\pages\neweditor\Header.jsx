import React from 'react'
import AppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
const Header = (props) => {
    return <AppBar sx={{
        height: '50px',
        backgroundColor: "#ffffff",
        color: '#000000',
        boxShadow:'inset 0px -1px 2px -1px rgba(0,0,0,0.2), 0px 4px 5px 0px rgba(0,0,0,0.14), 0px 1px 10px 0px rgba(0,0,0,0.12)'
    }} position="static">
        <Toolbar variant="dense" sx={{ padding: "0px", height: '50px', alignItems: 'stretch' }}>
            {props.children}
        </Toolbar>
    </AppBar>
}
export default Header