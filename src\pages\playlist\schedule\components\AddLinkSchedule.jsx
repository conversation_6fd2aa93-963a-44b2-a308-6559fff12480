/* eslint-disable no-debugger */
/* eslint-disable react/prop-types */
import React, { useState, useRef, forwardRef } from "react";
import {
  Grid,
  Paper,
  Button,
  InputLabel,
  OutlinedInput,
  Stack,
  InputAdornment,
  InputBase,
  FormHelperText,
  Typography,
  Container,
} from "@mui/material";
import LoadingButton from "@mui/lab/LoadingButton";
import "./link.less";
import IconButton from "@mui/material/IconButton";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import { toast } from "react-toastify";
import dayjs from "dayjs";
import Popover from "@mui/material/Popover";
import TimePickert from "@/components/zktime";
import * as Yup from "yup";
import { useFormik } from "formik";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm, Controller, useFieldArray } from "react-hook-form";
// api
import { saveLinkSchedule } from "@/service/api/schedule";
import { getGroupScreenById } from "@/service/api/linkScreen";
import "@amir04lm26/react-modern-calendar-date-picker/lib/DatePicker.css";
import { Calendar, utils } from "@amir04lm26/react-modern-calendar-date-picker";
import { getCalendarLocales } from "@/utils/calendarLocales";
// i18n
import { useTranslation } from "react-i18next";
import AdvancedLinkDialog from "./AdvanceLinkModal";
import MainCard from "@/components/MainCard";
import { useNavigate } from "react-router-dom";
import moment from "moment";
import LinkScreenSelect from "./LinkScreenSelect";
import AddLinkPlayList from "./AddLinkPlayList";
import TimeButtonSelect from "./TimeButtonSelect";
import { makeStyles } from "@material-ui/core";
import { useTheme } from "@mui/material/styles";
import AddCircleIcon from "@mui/icons-material/AddCircle";
import DateSvg from "./DateSvg";
const useStyles = makeStyles({
  hidden: { display: "none" },
  show: {},
  big: { fontSize: "3rem" },
});
import { isEmpty, isNotEmpty, isAnyEmpty } from "@/utils/zkUtils";
export default function AddCommonSchedule(props) {
  const [advancedOpen, setAdvancedOpen] = useState(false);
  const advancedRef = useRef();
  const classes = useStyles();
  const theme = useTheme();
  const addLinkPlayListRef = useRef(null);
  const timeButtonSelectRef = useRef(null);
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [publishLoading, setPublishLoading] = useState(false);
  const [publishDisable, setPublishDisable] = useState(false);
  const [saveDisable, setSaveDisable] = useState(false);
  //设备的所在元素的宽度
  const [xsValue, setXsValue] = useState(3);
  //设备的所在父级元素的高度
  const [heightValue, setHeightValue] = useState(160);
  //步骤
  const [activeStep, setActiveStep] = useState(0);
  const [tips, setTips] = useState(t("ips.ips_first_select_link"));
  const [anchorEl, setAnchorEl] = useState(null);
  //联屏组下的屏幕数量(选择设备后需要设置该值)
  const [screenNumber, setScreenNumber] = useState(1);
  //联屏组行设备数量
  const [screenLine, setScreenLine] = useState();
  // 下发设备多选集合
  const [advancedData, setAdvancedData] = useState([]);
  const [tableObject, setTableObject] = useState([
    {
      original: {
        columns: 2,
        line: 2,
      },
    },
  ]);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };
  const open = Boolean(anchorEl);

  //清单对象
  const inventoryObject = { playListId: "", playListName: "" };
  //区域设备对象,添加清单对象时，需要给每个区域设备对象都添加清单
  const areaScreenObject = [];
  //屏幕组清单对象
  const screenGroupPlayListObject = {
    startTime: "",
    stopTime: "",
    areaScreenPlayList: [],
  };

  //设备组下有多台设备区域
  //每个设备区域对应多个播放清单对象
  //-----------------设备组下的区域（如果选择多个联屏组，则代表多个位于该区域的）设备对象----------
  //-----------------区域设备对象对应多个清单--------------------------

  //表单
  const scheduleForm = useFormik({
    initialValues: {
      name: "",
      playDirection: "0",
      startDate: "",
      stopDate: "",
      screenGroupIds: "",
      //集合内部的集合对象对应为区域设备对象
      screenGroupPlayList: [
        {
          startTime: "",
          stopTime: "",
          areaScreenPlayList: [
            { playListId: "", playListName: "" },
            { playListId: "", playListName: "" },
            { playListId: "", playListName: "" },
            { playListId: "", playListName: "" },
          ],
        },
      ],
    },
    onSubmit: async (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        handelTableSubmit(values);
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
    validationSchema: Yup.object().shape({
      name: Yup.string().required(t("common.common_input_scheduling_name")),
      startDate: Yup.date().required(t("common.common_select_dateInterval")),
      stopDate: Yup.date().required(t("common.common_select_dateInterval")),
    }),
  });

  const handelTableSubmit = async () => {
    if (timeButtonSelectRef.current.monthsSelectList.length === 0) {
      toast.error(t("ips.ips_select_month"));
      return;
    }
    if (timeButtonSelectRef.current.daysSelectList.length === 0) {
      toast.error(t("ips.ips_select_date"));
      return;
    }
    //清除冗余播放清单对象
    let newGroupPlayList = scheduleForm.values.screenGroupPlayList.filter(
      (playListItem) => {
        let count = 0;
        playListItem.areaScreenPlayList.forEach((item, index) => {
          if (item.playListId === "" && item.playListName === "") {
            //用户未选的清单对象数量
            count++;
          }
        });
        return !(
          playListItem.startTime === "" &&
          playListItem.stopTime === "" &&
          count != 0
        );
      }
    );
    if (newGroupPlayList.length === 0) {
      toast.error(t("ips.ips_please_select_playlist"));
      return;
    }
    let errorPlayLists = newGroupPlayList.filter((newItem) => {
      let count = 0;
      newItem.areaScreenPlayList.forEach((item, index) => {
        if (item.playListId === "" && item.playListName === "") {
          //未进行配置的清单对象个数
          count++;
        }
      });
      return newItem.startTime === "" || newItem.stopTime === "" || count != 0;
    });
    if (errorPlayLists.length > 0) {
      toast.error(t("ips.ips_playlist_exist"));
      return;
    }
    //表单重新赋值，清除清单名称和时间都未选择的清单对象
    scheduleForm.values.screenGroupPlayList = newGroupPlayList;
    if (loading) {
      toast.error(t("ips.ips_schdule_publishing"));
    }
    const ids = await getRowId(tableObject);

    let params = scheduleForm.values;
    // 表单赋值
    params.screenGroupIds = ids;
    params.playWeeks = timeButtonSelectRef.current.weeksSelectList;
    params.playDays = timeButtonSelectRef.current.daysSelectList;
    params.playMonths = timeButtonSelectRef.current.monthsSelectList;
    //分辨率
    params.resolution = "1920x1080";

    if (params.operateType === "1") {
      setPublishDisable(true);
      setLoading(true);
    }
    if (params.operateType === "0") {
      setSaveDisable(true);
      setPublishLoading(true);
    }
    console.log(params);
    //提交表单
    // await saveLinkSchedule(params)
    //   .then((res) => {
    //     toast.success(res.message);
    //     //跳转到投放排期界面
    //     navigate(-1);
    //   })
    //   .catch((error) => {
    //     handleResetLoading();
    // });
  };
  const handleResetLoading = () => {
    setSaveDisable(false);
    setPublishDisable(false);
    setLoading(false);
    setPublishLoading(false);
  };
  // 获取rowID
  const getRowId = async (selects) => {
    return await new Promise((reslove) => {
      const ids = [];
      selects.forEach((select) => {
        ids.push(select.original.id);
      });
      reslove(ids.toString());
    });
  };

  // 规则校验
  const schema = Yup.object().shape({
    name: Yup.string().required(t("common.common_input_scheduling_name")),
    startDate: Yup.string().required(t("common.common_select_dateInterval")),
    stopDate: Yup.string().required(t("common.common_select_dateInterval")),
  });

  // 新表单组件
  const {
    control,
    handleSubmit,
    setValue,
    register,
    getValues,
    watch,
    reset,
    formState: { errors },
  } = useForm({
    mode: "all",
    resolver: yupResolver(schema),
  });

  // 清单列表，用于动态创建渲染
  const { fields, append, remove } = useFieldArray({
    control,
    name: "linkPlayLists",
  });

  //上一步
  const previousStep = () => {
    if (activeStep === 0) {
      return;
    }
    setActiveStep(activeStep - 1);
    setTips(t("ips.ips_first_select_link"));
  };

  //下一步
  const nextStep = () => {
    if (tableObject.length === 0) {
      toast.error(t("ips.ips_at_least_one_group"));
      return;
    }

    //判断相同规格设备
    let oneItem = tableObject[0];
    let flag = false;
    for (let item of tableObject) {
      if (
        item.original.line != oneItem.original.line ||
        item.original.columns != oneItem.original.columns
      ) {
        flag = true;
        break;
      }
    }
    if (flag) {
      toast.error(t("ips.ips_select_same_specifications"));
      return;
    }

    let screenStatus = true;
    let allPromises = [];
    // todo 需要优化，可以直接把IDS传入，只调用一次接口
    for (let item of tableObject) {
      let promise = getGroupScreenById(item.original.id)
        .then((res) => {
          if (!res.data) {
            screenStatus = false;
          }
        })
        .catch((error) => {
          screenStatus = false;
        });
      if (!screenStatus) {
        break;
      }
      allPromises.push(promise);
    }

    Promise.all(allPromises).then(() => {
      if (!screenStatus) {
        toast.error(t("ips.ips_select_contain_offline"));
      } else {
        // 设置清空默认动态表单的值
        reset({ linkPlayLists: [] });
        // 日历选择清空
        setSelectedDayRange(defaultRange);
        let screenNumber = oneItem.original.line * oneItem.original.columns;
        setScreenNumber(screenNumber);
        setScreenLine(oneItem.original.columns);
        let newnewscreenGroupPlayLists = [];
        let newAreaScreenObject = [];

        for (let i = 0; i < screenNumber; i++) {
          let newInventoryObject = { ...inventoryObject };
          newAreaScreenObject.push(newInventoryObject);
        }
        let newscreenGroupPlayListObject = {};
        newscreenGroupPlayListObject = {
          startTime: "",
          stopTime: "",
          areaScreenPlayList: newAreaScreenObject,
        };

        newnewscreenGroupPlayLists.push(newscreenGroupPlayListObject);

        newnewscreenGroupPlayLists.forEach((data) => {
          append(data);
        });

        setActiveStep(activeStep + 1);
        setTips(t("ips.ips_second_step"));
      }
    });
    handleCalculateXS(
      parseInt(oneItem.original.columns),
      parseInt(oneItem.original.line)
    );
  };
  //计算出每个设备占的宽度
  const handleCalculateXS = (cloumnNum, lineNum) => {
    // const newCloumnNum = cloumnNum > 4 ? 4 : cloumnNum;
    const xs = 12 / cloumnNum;
    setXsValue(xs);
    //计算总高度
    // const expandRow = cloumnNum > 4 ? 1 : 0;
    // const newRowNum = lineNum + expandRow;
    // setHeightValue(newRowNum * 160);
  };
  const defaultRange = {
    from: null,
    to: null,
  };

  const [selectedDayRange, setSelectedDayRange] = useState(defaultRange);

  // 表单提交
  const onSubmit = async (data, event) => {
    event.preventDefault();
    if (timeButtonSelectRef.current.monthsSelectList.length === 0) {
      toast.error(t("ips.ips_select_month"));
      return;
    }
    if (timeButtonSelectRef.current.daysSelectList.length === 0) {
      toast.error(t("ips.ips_select_date"));
      return;
    }
    //清除冗余播放清单对象
    removePlayListEmpty();
    // 校验非空
    const linkPlayLists = getValues().linkPlayLists;
    let count = 0;
    linkPlayLists.forEach((item, index) => {
      let flag = false;
      item.areaScreenPlayList.forEach((item2) => {
        if (
          isEmpty(item.startTime) ||
          isEmpty(item.stopTime) ||
          isEmpty(item2.playListId) ||
          isEmpty(item2.playListName)
        ) {
          flag = true;
        }
      });
      if (flag) {
        count++;
      }
    });
    if (count > 0) {
      toast.error(t("ips.ips_playlist_exist"));
      return;
    }
    if (loading) {
      toast.error(t("ips.ips_schdule_publishing"));
    }
    const ids = await getRowId(tableObject);

    const params = {
      ...data,
      screenGroupIds: ids,
      playWeeks: timeButtonSelectRef.current.weeksSelectList,
      playDays: timeButtonSelectRef.current.daysSelectList,
      playMonths: timeButtonSelectRef.current.monthsSelectList,
      playDirection: "0",
    };
    params.screenGroupPlayList = params.linkPlayLists;
    delete params.linkPlayLists;
    //分辨率
    params.resolution = "1920x1080";
    if (params.operateType === "1") {
      setPublishDisable(true);
      setLoading(true);
    }
    if (params.operateType === "0") {
      setSaveDisable(true);
      setPublishLoading(true);
    }
    // 提交表单
    await saveLinkSchedule(params)
      .then((res) => {
        toast.success(res.message);
        //跳转到投放排期界面
        navigate(-1);
      })
      .catch((error) => {
        handleResetLoading();
      });
  };
  // 清除冗余动态表单
  const removePlayListEmpty = () => {
    const emptyIndexes = [];
    const linkPlayLists = getValues().linkPlayLists;
    linkPlayLists.forEach((item, index) => {
      let flag = false;
      item.areaScreenPlayList.forEach((item2) => {
        if (
          isEmpty(item.startTime) &&
          isEmpty(item.stopTime) &&
          isEmpty(item2.playListId) &&
          isEmpty(item2.playListName)
        ) {
          flag = true;
        }
      });
      if (flag) {
        emptyIndexes.push(index);
        flag = false;
      }
    });
    if (linkPlayLists.length > 1) {
      emptyIndexes.reverse().forEach((index) => remove(index));
    }
  };
  // 动态创建联屏form表单
  const CreateLinkScreenFormField = (props) => {
    const selectPlayListObject = (screenIndex, playListIndex) => {
      addLinkPlayListRef.current.handleOpen();
      addLinkPlayListRef.current.setPlayListIndex(playListIndex);
      addLinkPlayListRef.current.setScreenIndex(screenIndex);
    };
    // 设置更新动态表单值
    const setDynameFormFiledValue = (
      playListId,
      playListName,
      playListIndex,
      screenIndex
    ) => {
      const updatedValue = [
        ...watch(`linkPlayLists[${screenIndex}].areaScreenPlayList`),
      ];
      updatedValue[playListIndex] = {
        playListId: playListId,
        playListName: playListName,
      };
      console.log(updatedValue, "AA");
      setValue(
        `linkPlayLists[${screenIndex}].areaScreenPlayList`,
        updatedValue
      );
    };
    // 时间选择组件相关
    const [anchorTime, setAnchorTime] = useState(null);
    const openTimeRange = Boolean(anchorTime);
    // 所选输入框的下标
    const [currentIndex, setCurrentIndex] = useState(null);
    const timeRangeId = openTimeRange ? "time-popover" : undefined;
    const handleTimeClose = () => {
      setAnchorTime(null);
    };
    const handleTimeClick = (event, index) => {
      setAnchorTime(event.currentTarget);
      setCurrentIndex(index);
    };
    return (
      <>
        <AddLinkPlayList
          ref={addLinkPlayListRef}
          setPlayListFormValues={setDynameFormFiledValue}
          playListType={"1"}
        />
        <Popover
          id={timeRangeId}
          open={openTimeRange}
          anchorEl={anchorTime}
          onClose={handleTimeClose}
          anchorOrigin={{
            vertical: "bottom",
            horizontal: "left",
          }}>
          <TimePickert
            endTimeText={t("common.common_endTime")}
            startTimeText={t("common.common_startTime")}
            confirmText={t("common.common_edit_ok")}
            onChange={(time) => {
              //时间段是否存在重叠标识
              let flag = false;
              //已选播放清单
              let timePeriodList = [...getValues().linkPlayLists];
              //开始时间
              let startTime = new Date("2022-06-01T" + time.startTime);
              //结束时间
              let endTime = new Date("2022-06-01T" + time.endTime);

              if (startTime.getTime() >= endTime.getTime()) {
                toast.error(t("ips.ips_starttime_greater_endtime"));
                return;
              }

              timePeriodList.forEach((timePeriod, index) => {
                //排除自身与其它时间段比较
                if (currentIndex != index) {
                  //排除未选择的播放时间段
                  if (timePeriod.startTime != "" && timePeriod.stopTime != "") {
                    //该时间段开始时间
                    let thisStartTime = new Date(
                      "2022-06-01T" + timePeriod.startTime
                    );
                    //该时间段结束时间
                    let thisStopTime = new Date(
                      "2022-06-01T" + timePeriod.stopTime
                    );
                    //交集判断结果
                    let result =
                      (startTime.getTime() < thisStartTime.getTime() &&
                        thisStartTime.getTime() < endTime.getTime()) ||
                      (startTime.getTime() < thisStopTime.getTime() &&
                        thisStopTime.getTime() < endTime.getTime()) ||
                      (startTime.getTime() >= thisStartTime.getTime() &&
                        endTime.getTime() <= thisStopTime.getTime()) ||
                      (startTime.getTime() <= thisStartTime.getTime() &&
                        endTime.getTime() >= thisStopTime.getTime());

                    if (result) {
                      //时间段存在交集
                      flag = true;
                      return;
                    }
                  }
                }
              });

              if (flag) {
                toast.error(t("ips.ips_overlap_play_time"));
                return;
              } else {
                // 表单赋值
                setValue(
                  `linkPlayLists[${currentIndex}].startTime`,
                  time.startTime
                );
                setValue(
                  `linkPlayLists[${currentIndex}].stopTime`,
                  time.endTime
                );
                setCurrentIndex(null);
                handleTimeClose();
              }
            }}
          />
        </Popover>
        {fields.map((field, index) => {
          return (
            <>
              <Grid item xs={6} key={field.id}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="listrangeTime">
                    {t("ips.ips_playlist_play_time")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <Controller
                    name={`linkPlayLists[${index}].stopTime`}
                    control={control}
                    defaultValue={field.stopTime}
                    render={({ field }) => (
                      <OutlinedInput
                        id="stopTime"
                        {...field}
                        type="text"
                        onClick={(event) => {
                          handleTimeClick(event, index);
                        }}
                        placeholder={t("common.common_endTime")}
                        startAdornment={
                          <InputAdornment
                            position="start"
                            sx={{ width: "120%" }}>
                            <Controller
                              name={`linkPlayLists[${index}].startTime`}
                              control={control}
                              defaultValue={field.startTime}
                              render={({ field }) => (
                                <InputBase
                                  {...field}
                                  endAdornment={<DateSvg />}
                                  onClick={(event) => {
                                    handleTimeClick(event, index);
                                  }}
                                  type="text"
                                  placeholder={t("common.common_startTime")}
                                  sx={{
                                    width: "100%",
                                  }}
                                />
                              )}
                            />
                          </InputAdornment>
                        }
                      />
                    )}
                  />
                </Stack>
              </Grid>
              <Grid
                container
                spacing={1}
                justifyContent="flex-start"
                sx={{
                  marginTop: "10px",
                  width: "900px",
                  maxHeight: "270px",
                  padding: "20px 0px 30px 0px",
                  backgroundColor: "#f9f9f9",
                  overflow: "auto",
                  marginBottom: "10px",
                }}>
                {field.areaScreenPlayList.map((item, playListIndex) => {
                  return (
                    <>
                      <Grid
                        item
                        container={
                          tableObject[0].original.columns > 4 ? true : false
                        }
                        xs={xsValue}
                        key={playListIndex}
                        sx={{ paddingRight: "8px" }}
                        align="center">
                        <Paper
                          className={"box"}
                          elevation={0}
                          style={{
                            border: "1px solid rgba(120, 189, 66,0.7)",
                            height: "100px",
                            width: "200px",
                            boxShadow: "0px 5px 10px 2px rgba(0, 0, 0, 0.1)",
                          }}>
                          {watch(
                            `linkPlayLists[${index}].areaScreenPlayList[${playListIndex}].playListName`
                          ) !== "" && (
                            <>
                              <div className="ovrly"></div>
                              <div className="btn">
                                <Button
                                  className="bt1"
                                  onClick={() => {
                                    setDynameFormFiledValue(
                                      "",
                                      "",
                                      playListIndex,
                                      index
                                    );
                                  }}
                                  style={{
                                    fontSize: "14px",
                                  }}>
                                  {t("ips.ips_reset")}
                                </Button>
                              </div>
                            </>
                          )}
                          <Stack
                            spacing={1}
                            sx={{
                              height: "100%",
                              width: "100%",
                            }}
                            justifyContent="center"
                            alignItems="center">
                            {watch(
                              `linkPlayLists[${index}].areaScreenPlayList[${playListIndex}].playListName`
                            ) == "" ? (
                              <>
                                <IconButton
                                  color="primary"
                                  aria-label="add playList"
                                  className={`
                                    ${
                                      watch(
                                        `linkPlayLists[${index}].areaScreenPlayList[${playListIndex}].playListName`
                                      ) == ""
                                        ? classes.show
                                        : classes.hidden
                                    }
                                  `}
                                  variant="contained"
                                  onClick={() => {
                                    selectPlayListObject(index, playListIndex);
                                  }}>
                                  <AddCircleIcon className={classes.big} />
                                </IconButton>
                                <Typography
                                  sx={{
                                    color: "rgba(0, 0, 0,.3)",
                                    lineHeight: "22px",
                                    fontSize: "14px",
                                  }}>
                                  {t("ips.ips_add_playlist")}
                                  <span
                                    style={{
                                      color: "red",
                                    }}>
                                    *
                                  </span>
                                </Typography>
                              </>
                            ) : (
                              <Typography
                                sx={{
                                  color: "rgba(0, 0, 0, 0.3)",
                                  wordBreak: "break-all",
                                  padding: "5px 10px 5px 10px",
                                }}>
                                {watch(
                                  `linkPlayLists[${index}].areaScreenPlayList[${playListIndex}].playListName`
                                )}
                              </Typography>
                            )}
                          </Stack>
                        </Paper>
                      </Grid>
                    </>
                  );
                })}
              </Grid>
            </>
          );
        })}
      </>
    );
  };
  const handleOpenAdvanced = () => {
    const playLists = getValues().linkPlayLists;
    setAdvancedData(playLists);
    advancedRef.current?.handeOpenChange();
    setAdvancedOpen(true);
  };
  const handleRestPlayListFormField = (values) => {
    return new Promise((reslove, reject) => {
      reset({
        ...getValues(),
        linkPlayLists: values,
      });
      reslove();
    });
  };
  return (
    <MainCard
      title={
        <MainCardTitle title={t("ips.ips_new_link_schedule")} tips={tips} />
      }
      border={false}
      contentSX={{ p: 0 }}>
      <form noValidate onSubmit={handleSubmit(onSubmit)}>
        {/* 排期form 修改为先选择设备 */}
        {activeStep === 0 && (
          <div style={{ padding: "8px 8px 8px 8px" }}>
            {/* 联屏设备选择*/}
            <LinkScreenSelect setTableObject={setTableObject} />
            <Stack
              sx={{ marginTop: "20px" }}
              direction="row"
              justifyContent="flex-end"
              alignItems="center"
              spacing={2}>
              <Button
                variant="outlined"
                // fullWidth
                disableElevation
                size="large"
                color="info"
                onClick={() => navigate(-1)}>
                {t("common.common_close")}
              </Button>
              <Button
                variant="contained"
                // fullWidth
                disableElevation
                size="large"
                color="primary"
                onClick={() => {
                  //下一步选择清单和播放时间
                  nextStep();
                }}>
                {t("common.common_next")}
              </Button>
            </Stack>
          </div>
        )}
        {activeStep === 1 && (
          <Container maxWidth="md" sx={{ padding: 3 }}>
            <Grid container spacing={1} direction="row">
              <Grid item xs={5.5}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="schedule-name" sx={{ width: 120 }}>
                    {t("ips.ips_scheduling")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <Controller
                    name="name"
                    control={control}
                    defaultValue=""
                    render={({ field }) => (
                      <OutlinedInput
                        onBlur={field.onBlur}
                        {...field}
                        fullWidth
                        type="text"
                        error={Boolean(errors.name)}
                        placeholder={t("common.common_input_scheduling_name")}
                      />
                    )}
                  />
                  {errors.name?.message && (
                    <FormHelperText
                      error
                      id="standard-weight-helper-text-name-schedule">
                      {errors.name?.message}
                    </FormHelperText>
                  )}
                </Stack>
              </Grid>
              <Grid item xs={5.5}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="schedule-ranges" sx={{ width: 250 }}>
                    {t("ips.ips_startDate_endDate")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <Controller
                    name="startDate"
                    control={control}
                    defaultValue=""
                    render={({ field }) => (
                      <OutlinedInput
                        {...field}
                        onBlur={field.onBlur}
                        onClick={handleClick}
                        startAdornment={
                          <InputAdornment position="start">
                            <CalendarMonthIcon />
                          </InputAdornment>
                        }
                        endAdornment={
                          <InputAdornment position="end" sx={{ width: "120%" }}>
                            <Controller
                              name="stopDate"
                              control={control}
                              defaultValue=""
                              render={({ field }) => (
                                <InputBase
                                  {...field}
                                  readOnly
                                  autoComplete="off"
                                  startAdornment={<DateSvg></DateSvg>}
                                  autoFocus={false}
                                  type="text"
                                  placeholder={t("common.common_end_date")}
                                />
                              )}
                            />
                          </InputAdornment>
                        }
                        fullWidth
                        type="text"
                        error={Boolean(errors.startDate)}
                        placeholder={t("common.common_start_date")}
                      />
                    )}
                  />
                  {errors.startDate?.message || errors.stopDate?.message ? (
                    <FormHelperText
                      error
                      id="standard-weight-helper-text-name-schedule">
                      {errors.startDate?.message || errors.stopDate?.message}
                    </FormHelperText>
                  ) : null}
                  <Popover
                    elevation={3}
                    id="date"
                    open={open}
                    anchorEl={anchorEl}
                    onClose={handleClose}
                    anchorOrigin={{
                      vertical: "bottom",
                      horizontal: "center",
                    }}
                    transformOrigin={{
                      vertical: "top",
                      horizontal: "center",
                    }}>
                    <Calendar
                      colorPrimary="#7ac143" // added this
                      colorPrimaryLight="rgba(122, 193, 67, 0.1)"
                      minimumDate={utils().getToday()}
                      value={selectedDayRange}
                      locale={getCalendarLocales()}
                      onChange={(date) => {
                        setSelectedDayRange(date);
                        const startDate = date.from
                          ? dayjs(
                              date.from.year +
                                `${
                                  date.from.month <= 9
                                    ? "0" + date.from.month
                                    : date.from.month
                                }` +
                                date.from.day
                            ).format("YYYY-MM-DD")
                          : "";
                        const endDate = date.to
                          ? dayjs(
                              date.to.year +
                                `${
                                  date.to.month <= 9
                                    ? "0" + date.to.month
                                    : date.to.month
                                }` +
                                date.to.day
                            ).format("YYYY-MM-DD")
                          : "";
                        //计算开始时间和结束时间跨度不能超过一整年
                        let timeSpan = moment(new Date(startDate)).diff(
                          moment(new Date(endDate)),
                          "years"
                        );

                        if (timeSpan >= 1) {
                          toast.error(t("ips.ips_exceeds_one_year"));
                          return;
                        } else {
                          setValue("startDate", startDate);
                          setValue("stopDate", endDate);
                          //将排期播放时间传给时间按钮选择组件进行计算
                          timeButtonSelectRef.current.getDaysWeeksAndMonthsDate(
                            startDate,
                            endDate
                          );
                        }
                      }}
                    />
                  </Popover>
                </Stack>
              </Grid>
              <Grid
                item
                xs={11}
                style={{
                  display: "flex",
                  justifyContent: "flex-end",
                }}>
                <Button
                  variant="contained"
                  onClick={() => {
                    handleOpenAdvanced();
                  }}>
                  {t("common.common_advanced_menu_title")}
                </Button>
              </Grid>
              <Grid item sx={{ marginBottom: "10px" }} xs={11}>
                <Stack spacing={2}>
                  <CreateLinkScreenFormField />
                </Stack>
              </Grid>
              <Grid
                item
                xs={12}
                container
                direction="row"
                justifyContent="flex-start">
                <InputLabel sx={{ color: "grey", fontSize: "0.8rem" }}>
                  {t("common.common_time_not_repeat_annotation")}
                </InputLabel>
              </Grid>
              <Grid
                xs={11}
                container
                direction="row"
                justifyContent="flex-end"
                alignItems="center">
                <Stack direction="row" spacing={1}>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={() => {
                      const newList = [];
                      //对象中的areaScreenPlayList集合中对象的个数对应屏幕组中的设备数量
                      let newAreaScreenObject = [];
                      for (let i = 0; i < screenNumber; i++) {
                        let newInventoryObject = {
                          ...inventoryObject,
                        };
                        newAreaScreenObject.push(newInventoryObject);
                      }
                      let newscreenGroupPlayListObject = {
                        ...screenGroupPlayListObject,
                      };
                      newscreenGroupPlayListObject.areaScreenPlayList =
                        newAreaScreenObject;
                      newList.push(newscreenGroupPlayListObject);
                      append(newList);
                    }}>
                    {t("ips.ips_add_new_playlist")}
                  </Button>
                  {fields.length > 1 && (
                    <Button
                      variant="contained"
                      color="error"
                      onClick={() => {
                        remove(fields.length - 1);
                      }}>
                      {t("common.common_op_del")}
                    </Button>
                  )}
                </Stack>
              </Grid>
              <Grid item xs={12}>
                <TimeButtonSelect ref={timeButtonSelectRef} />
              </Grid>
              <Grid item xs={12}>
                <Stack
                  direction="row"
                  justifyContent="center"
                  alignItems="center"
                  spacing={2}>
                  <Button
                    size="large"
                    color="info"
                    variant="outlined"
                    onClick={() => navigate(-1)}>
                    {t("common.common_edit_cancel")}
                  </Button>
                  <Button
                    size="large"
                    variant="contained"
                    color="secondary"
                    onClick={() => {
                      //上一步
                      previousStep();
                    }}>
                    {t("common.common_pre")}
                  </Button>
                  <LoadingButton
                    loading={loading}
                    disabled={saveDisable}
                    variant="contained"
                    size="large"
                    type="submit"
                    color="warning"
                    onClick={() => setValue("operateType", "1")}>
                    {t("common.common_edit_save")}
                  </LoadingButton>
                  <LoadingButton
                    loading={publishLoading}
                    disabled={publishDisable}
                    variant="contained"
                    size="large"
                    type="submit"
                    color="primary"
                    onClick={() => setValue("operateType", "0")}>
                    {t("common.common_op_publish")}
                  </LoadingButton>
                </Stack>
              </Grid>
            </Grid>
          </Container>
        )}
      </form>
      <AdvancedLinkDialog
        ref={advancedRef}
        open={advancedOpen}
        advancedData={advancedData}
        onClose={() => {
          setAdvancedOpen(false);
          advancedRef.current?.handelReset();
        }}
        onImport={handleRestPlayListFormField}
        tableObject={tableObject}
        inventoryObject={inventoryObject}
        data={[]}
      />
    </MainCard>
  );
}
function MainCardTitle({ title, tips }) {
  return (
    <>
      <Grid item container xs={12}>
        <Grid item xs={5}>
          <Typography>{title}</Typography>
        </Grid>
        <Grid item xs={3}>
          <Typography color="#7AC143" sx={{ fontWeight: "bold" }}>
            {tips}
          </Typography>
        </Grid>
      </Grid>
    </>
  );
}
