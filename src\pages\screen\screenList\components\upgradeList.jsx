import React from "react";
import { useEffect, useMemo, useState, useRef } from "react";
import { list, featchUpgrade } from "@/service/api/upgradeFirmware";
import {
  BootstrapActions,
  BootstrapContent,
  BootstrapDialog,
  BootstrapDialogTitle,
} from "@/components/dialog";
import MaterialReactTable from "material-react-table";
import {
  Button,
  Stack,
  Tooltip,
  IconButton,
  Typography,
  Grid,
  Alert,
  InputLabel,
  Box,
  FormHelperText,
  AlertTitle,
  TextField,
} from "@mui/material";
import LoadingButton from "@mui/lab/LoadingButton";
import { useConfirm } from "@/components/zkconfirm";
import { CloseOutlined } from "@material-ui/icons";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import CancelIcon from "@mui/icons-material/Cancel";
import InfoIcon from "@mui/icons-material/Info";
import AuthButton from "@/components/AuthButton";

import { useTranslation } from "react-i18next";
import { tableI18n } from "@/utils/tableLang";
import MainCard from "@/components/MainCard";
import { useForm, Controller, useFieldArray } from "react-hook-form";
import { toast } from "react-toastify";
import ToastContent from "@/components/@extended/ToastContent";
import UploadUpgrade from "./UploadUpgrade";
import { useBoolean } from "ahooks";

const UpgradeList = (props) => {
  const { screens = [], open, onClose } = props;
  const upload = useRef(null);
  const [uploadOpen, { setTrue, setFalse }] = useBoolean(false);
  const confirm = useConfirm();

  const { t } = useTranslation();
  // 表格数据
  const [data, setData] = useState([]);
  const [isRefetching, setIsRefetching] = useState(false);
  const [isError, setIsError] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [rowSelection, setRowSelection] = useState([]);
  const [rowCount, setRowCount] = useState(0);
  // 新表单组件
  const { control, handleSubmit, reset, getValues } = useForm({
    defaultValues: {
      version: "",
    },
  });
  // 分页的参数
  //设备升级列表
  const getUpgradeFirmware = () => {
    if (!data.length) {
      setIsLoading(true);
    } else {
      setIsRefetching(true);
    }
    //查询可用版本号
    list({
      ...buildParams(),
      screenId: screens[0]?.id,
    }).then(({ data }) => {
      setRowCount(data.length);
      setIsLoading(false);
      setIsRefetching(false);
      setData(data);
    });
  };

  useEffect(() => {
    if (screens && open) {
      console.log("screens", screens[0]);
      //查询设备可用版本号
      getUpgradeFirmware();
      setRowSelection([]);
    }
  }, [screens]);

  //关闭清除表单值
  const handleClose = () => {
    // setFiles([]);
    // uploadFormik.handleReset();
    // onCancel();
    onClose();
    setData([]);
    setRowCount(0);
  };
  // 构建参数
  const buildParams = () => {
    const params = {
      ...getValues(),
      // ...requestParams.current,
    };
    return params;
  };
  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "firmwareName", //access nested data with dot notation
        header: t("common.upgrade_list_column_name"),
        //列头点击相关事件关闭
        enableColumnActions: false,
        enableSorting: false,
        // size: '200',
        Cell: ({ cell, row }) => {
          return (
            <Tooltip title={row.original.firmwareName} placement="top">
              <Typography className="textSpace">
                {row.original.firmwareName}
              </Typography>
            </Tooltip>
          );
        },
      },
      {
        accessorKey: "version", //access nested data with dot notation
        header: t("common.upgrade_list_column_version"),
        enableColumnActions: false,
        enableSorting: false,
        size: 200,
      },
      // {
      //   accessorKey: "releaseTime",
      //   header: t("common.upgrade_list_column_time"),
      //   size: 100,
      //   enableColumnActions: false,
      //   enableSorting: false,
      //   Cell: ({ cell, row }) => {
      //     return (
      //       <Tooltip title={row.original.releaseTime} placement="top">
      //         <Typography className="textSpace">
      //           {row.original.releaseTime}
      //         </Typography>
      //       </Tooltip>
      //     );
      //   },
      // },
    ],
    []
  );
  const onSubmit = (data) => {
    getUpgradeFirmware();
    // console.log(data);
  };
  const restForm = () => {
    reset({
      version: "",
    });
    getUpgradeFirmware();
  };

  const handleDoUpgrade = (version) => {
    const ids = screens.map((item) => item.id);
    let loadingToast = toast.loading((toast) => (
      <ToastContent text={t("common.loading_message")} t={toast} />
    ));
    featchUpgrade(ids, version)
      .then((res) => {
        toast.dismiss(loadingToast);
        const successCount = res?.data?.success;
        const errorCount = res?.data?.error;
        toast(
          (t1) => (
            <CustomToastComponent
              successCount={successCount}
              errorCount={errorCount}
              t1={t1}
            />
          ),
          {
            icon: (
              <CustomerToastIcon
                successCount={successCount}
                errorCount={errorCount}
              />
            ),
          }
        );
        handleClose();
      })
      .catch(() => {
        toast.dismiss(loadingToast);
      });
  };

  const CustomToastComponent = ({ successCount, errorCount, t1 }) => {
    return (
      <div
        style={{
          maxWidth: 500, // Corrigido para maxWidth ao invés de maxWeight
          minWidth: 300,
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}>
        <div>
          {successCount > 0 && errorCount == 0
            ? t("screen.executed_success_all")
            : errorCount > 0 && successCount == 0
            ? t("screen.executed_error_all")
            : t("screen.execution_tips_section", {
                success: successCount,
                error: errorCount,
              })}
        </div>
        <IconButton onClick={() => toast.dismiss(t1.id)}>
          <CloseOutlined />
        </IconButton>
      </div>
    );
  };
  const CustomerToastIcon = ({ successCount, errorCount }) => {
    return (
      <>
        {successCount > 0 && errorCount === 0 ? (
          <CheckCircleIcon sx={{ color: "#61d345" }} />
        ) : errorCount > 0 && successCount === 0 ? (
          <CancelIcon sx={{ color: "#ff4b4b" }} />
        ) : (
          <InfoIcon sx={{ color: "#1890ff" }} />
        )}
      </>
    );
  };
  const handleSubmitUpgrade = (row) => {
    // console.log();
    if (validateVersion(row.version, screens)) {
      toast.error(t("common.no_select_lower_version"));
      return;
    }
    //升级
    confirm({
      title: t("message.messageBox_title"),
      confirmationText: t("common.common_edit_ok"),
      cancellationText: t("common.common_edit_cancel"),
      description: t("common.confirm_upgrade_version", {
        version: row.version,
      }),
    }).then(() => {
      //触发升级
      handleDoUpgrade(row.version);
    });
  };
  // 版本号比较函数
  function compareVersions(version1, version2) {
    const v1 = version1.replace("V", "").split(".");
    const v2 = version2.replace("V", "").split(".");

    for (let i = 0; i < Math.max(v1.length, v2.length); i++) {
      const num1 = parseInt(v1[i] || 0, 10);
      const num2 = parseInt(v2[i] || 0, 10);

      if (num1 > num2) {
        return 1; // 版本1大于版本2
      } else if (num1 < num2) {
        return -1; // 版本1小于版本2
      }
    }

    return 0; // 版本号相等
  }
  // 获取最高版本
  function getHighestVersion(screens) {
    let highestVersion = "";

    screens.forEach((screen) => {
      const currentVersion = screen.fwVersion;
      if (compareVersions(currentVersion, highestVersion) > 0) {
        highestVersion = currentVersion;
      }
    });

    return highestVersion;
  }
  // 校验给定版本是否小于最高版本
  function validateVersion(firmwareVersion, screens) {
    const highestVersion = getHighestVersion(screens);

    // 比较给定版本与最高版本
    const comparisonResult = compareVersions(firmwareVersion, highestVersion);

    return comparisonResult <= 0; // 返回是否小于或等于最高版本
  }

  const handleUpload = () => {
    setTrue();
  };
  return (
    <>
      <BootstrapDialog
        open={open}
        maxWidth={"md"}
        fullWidth
        onClose={handleClose}>
        <BootstrapDialogTitle onClose={handleClose}>
          <Typography variant="h4" component="p">
            {t("common.device_upgrade_list")}
          </Typography>
        </BootstrapDialogTitle>
        <BootstrapContent>
          {/* <MainCard style={{ marginBottom: "10px" }}> */}
          <Alert severity="warning">
            <AlertTitle>{t("common.warm_tips")}</AlertTitle>
            {t("common.if_same_version")}
            <br />
            {/* 当前所选设备版本为：<b>1.0.0</b> , 设备型号为：<b>ZKA40M</b> */}
            {t("common.please_choose_larger_version")}
            <b>
              ({t("common.all_current_device_version")}
              {screens?.map((item, index) => {
                return (
                  <React.Fragment key={index}>
                    {item?.fwVersion}{" "}
                    {index === screens?.length - 1 ? "" : " , "}
                  </React.Fragment>
                );
              })}
              )
            </b>
          </Alert>
          {/* </MainCard> */}
          <MaterialReactTable
            state={{
              // 分页参数
              isLoading,
              pagination: false,
              // 重新拉取
              showProgressBars: isRefetching,
              showAlertBanner: isError,
              enableSorting: true,
              columnPinning: { right: ["version"] },
              // sorting,
              rowSelection,
            }}
            muiTablePaperProps={{
              elevation: 0,
              sx: {
                borderRadius: "5px",
              },
            }}
            renderToolbarInternalActions={({ table }) => (
              <>
                <Stack direction={"row"} spacing={1}>
                  <AuthButton button="screen:firmware:save">
                    <Button
                      variant="contained"
                      onClick={() => {
                        handleUpload();
                      }}>
                      {t("common.common_upload")}
                    </Button>
                  </AuthButton>
                </Stack>
              </>
            )}
            muiTableHeadRowProps={{
              sx: { backgroundColor: "#fafafa", boxShadow: "none" },
            }}
            // 关闭过滤搜素
            enableColumnFilters={true}
            // 关闭排序
            enableSorting={false}
            // 布局方式
            layoutMode="grid"
            enableColumnActions={false}
            enableMultiRowSelection={false}
            // 开启列对齐
            muiTableHeadCellProps={{
              sx: {
                "& .Mui-TableHeadCell-Content": {
                  justifyContent: "space-between",
                },
              },
            }}
            // 解决列太多宽度太长问题
            enableColumnResizing
            // enablePinning
            // 初始化状态
            initialState={{ columnVisibility: { createTime: true } }}
            muiToolbarAlertBannerProps={
              isError
                ? {
                    color: "error",
                    children: "Error loading data",
                  }
                : undefined
            }
            //行选中
            onRowSelectionChange={setRowSelection}
            muiTableBodyRowProps={({ row }) => ({
              onClick: row.getToggleSelectedHandler(),
              sx: { cursor: "pointer" },
            })}
            // 开启多选
            enableRowSelection
            getRowId={(row) => row.id}
            // 列数
            rowCount={rowCount}
            // 固定头部
            enableStickyHeader
            // 处理表格高度
            muiTableContainerProps={{ sx: { maxHeight: "340px" } }}
            // 设置背景颜色
            muiTableBodyCellProps={{ sx: { backgroundColor: "white" } }}
            muiTableProps={{
              sx: { backgroundColor: "white", tableLayout: "fixed" },
            }}
            muiBottomToolbarProps={{ sx: { backgroundColor: "white" } }}
            muiTopToolbarProps={{ sx: { backgroundColor: "white" } }}
            // 分页回调函数
            // 记得开启这些 tips:否则会出现useEffect 反作用函数发起多次请求点击下一页会出问题
            manualFiltering
            manualPagination
            manualSorting
            // 排序
            // onSortingChange={setSorting}
            // 开启分页
            enablePagination={false}
            // 列定义
            columns={columns}
            // 数据
            data={data}
            // 初始化国际化语言  --- todo: i18n key doc address: https://github.com/KevinVandy/material-react-table/blob/main/packages/material-react-table/src/_locales/en.ts
            localization={tableI18n}
            // 自定义表头按钮
            renderTopToolbarCustomActions={({ table }) => {
              // setTableObject(table.getSelectedRowModel().rows);
              return (
                <Grid
                  container
                  direction="row"
                  justifyContent="flex-start"
                  alignItems="center"
                  spacing={2}>
                  <Grid item xs={6} md={3} sm={3} lg={3}>
                    <Controller
                      name="version"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label={t("ips.ips_version_code")}
                          size="small"
                          type="text"
                          fullWidth
                          placeholder={t("common.common_please_input")}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={6} md={3} sm={3} lg={3}>
                    <Stack
                      direction="row"
                      justifyContent="flex-start"
                      alignItems="flex-start"
                      spacing={2}>
                      <Button
                        disableElevation
                        onClick={handleSubmit(onSubmit)}
                        variant="contained"
                        size="small">
                        {t("common.common_table_query")}
                      </Button>
                      <Button
                        variant="outlined"
                        color="info"
                        sx={{
                          minWidth: "100px",
                        }}
                        onClick={restForm}
                        size="small">
                        {t("common.common_op_reset")}
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              );
            }}
            renderBottomToolbar={({ table }) => {
              console.log(table);

              return (
                <Stack
                  sx={{ mt: 1 }}
                  spacing={2}
                  direction="row"
                  justifyContent={"flex-end"}>
                  <Button color="info" variant="outlined" onClick={handleClose}>
                    {t("common.common_edit_cancel")}
                  </Button>
                  <LoadingButton
                    variant="contained"
                    color="primary"
                    disableElevation
                    onClick={() => {
                      if (table.getSelectedRowModel().rows.length > 0) {
                        handleSubmitUpgrade(
                          table.getSelectedRowModel().rows[0].original
                        );
                      }
                    }}
                    // disabled={!table?.getIsSomeRowsSelected()}
                    type="submit">
                    {t("common.common_edit_ok")}
                  </LoadingButton>
                </Stack>
              );
            }}
            positionToolbarAlertBanner="none"
          />
        </BootstrapContent>
      </BootstrapDialog>
      <UploadUpgrade
        ref={upload}
        open={uploadOpen}
        onCancel={() => {
          setFalse();
          getUpgradeFirmware();
        }}
        screenId={screens[0]?.id}
      />
    </>
  );
};

export default UpgradeList;
