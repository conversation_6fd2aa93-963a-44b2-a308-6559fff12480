import React from "react";

import { useRef } from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import IconButton from "@mui/material/IconButton";
import Typography from "@mui/material/Typography";
import CloseIcon from "@mui/icons-material/Close";
import Slide from "@mui/material/Slide";
import DialogContent from "@mui/material/DialogContent";
import ZKSelect from "@/components/ZKSelect";
import { getPrincipaList } from "@/service/api/L3Sevice.js";
import * as Yup from "yup";
import Alert from "@mui/material/Alert";
import SimpleBar from "simplebar-react";
import {
  Card,
  Grid,
  InputLabel,
  Paper,
  Stack,
  TextField,
  Tooltip,
} from "@mui/material";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import Box from "@mui/material/Box";
import { makeStyles } from "@material-ui/core/styles";
import Radio from "@mui/material/Radio";
import RadioGroup from "@mui/material/RadioGroup";
import FormControlLabel from "@mui/material/FormControlLabel";
import FormControl from "@mui/material/FormControl";
import FormLabel from "@mui/material/FormLabel";
import imgGenPath from "@/assets/images/icons/imgGen.svg";
import Skeleton from "@mui/material/Skeleton";
import { useForm, Controller } from "react-hook-form";
import { toast } from "react-toastify";
// import { genPicture } from "@/service/api/ai-helper.js";
import { useState } from "react";
import LoadingButton from "@mui/lab/LoadingButton";
import VisibilityIcon from "@material-ui/icons/Visibility";
import { Lightbox } from "react-modal-image";
import "./ai-loading.less";
import { useTranslation } from "react-i18next";
import Treeselect from "@/components/zktreeselect";
import { improtImage } from "@/service/api/material";
import {
  BootstrapActions,
  BootstrapContent,
  BootstrapDialog,
  BootstrapDialogTitle,
} from "@/components/dialog";
import { getTreeSelect } from "@/service/api/materialGroup";
import { FormHelperText } from "@mui/material";
import { useBoolean } from "ahooks";
import { useEffect } from "react";
import { styled, alpha } from "@mui/material/styles";
const SimpleBarStyle = styled(SimpleBar)(({ theme }) => ({
  maxHeight: "calc(100vh - 200px)",
  paddingRight: "15px",
  "& .simplebar-scrollbar": {
    "&:before": {
      backgroundColor: alpha(theme.palette.grey[500], 0.48),
    },
    "&.simplebar-visible:before": {
      opacity: 1,
    },
  },
  "& .simplebar-track.simplebar-vertical": {
    width: 10,
  },
  "& .simplebar-track.simplebar-horizontal .simplebar-scrollbar": {
    height: 6,
  },
  "& .simplebar-mask": {
    zIndex: "inherit",
  },
}));

const useMakeStyles = makeStyles((theme) => ({
  root: {
    // borderRadius: "12px",
    "& .MuiInputBase-root": {
      borderRadius: "12px",
    },
  },
  genButton: {
    "&.MuiButtonBase-root": {
      borderRadius: "12px",
    },
  },
  imgContainer: {
    height: "50%",
    position: "relative",
    display: "inline-block",
  },
  previewBtn: {
    position: "absolute",
    top: "50%",
    left: "50%",
    color: "white",
    transform: "translate(-50%, -50%)",
    opacity: 0,
    transition: "opacity 0.3s ease-in-out",
  },
  imgOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    width: "100%",
    height: "100%",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    opacity: 0,
    transition: "opacity 0.3s ease-in-out",
    "&:hover": {
      opacity: 1,
    },
  },
  imgHovered: {
    "& $previewBtn": {
      opacity: 1,
    },
    "& $imgOverlay": {
      opacity: 1,
    },
  },
}));
const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});
function CustomTabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}>
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

function a11yProps(index) {
  return {
    id: `simple-tab-${index}`,
    "aria-controls": `simple-tabpanel-${index}`,
  };
}
export default function AiHelper({ open = false, onClose }) {
  const classes = useMakeStyles();
  const { t } = useTranslation();
  const [importOpen, { setTrue: openImport, setFalse: closeImport }] =
    useBoolean(false);
  const handleClose = () => {
    onClose();
    setLoading(false);
    setImgSrc([]);
    reset();
  };

  const [value, setValue] = React.useState(0);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  const { control, handleSubmit, reset, getValues } = useForm({
    defaultValues: {
      message: "",
      count: "1",
      size: "1024*1024",
      style: "<auto>",
      negativePrompt: "",
    },
  });
  const [imgSrc, setImgSrc] = useState([]);
  const [loading, setLoading] = useState(false);
  const [previewOpen, setPreviewOpen] = useState(false);

  const handleGenImage = () => {
    if (!getValues().message) {
      toast.error(t("common.please_picture_content"));
      return;
    }
    if (Number(getValues().count) <= 0 || Number(getValues().count) >= 4) {
      toast.error(t("common.gen_picture_num_error"));
      return;
    }
    if (getValues().message.length > 500) {
      toast.error(t("common.prompt_max_length_error"));
      return;
    }

    let model = "qianw";
    if (value === 0) {
      model = "qianw";
    } else if (value == 1) {
      model = "workers";
    }
    handleRequestGen(model, getValues());
  };
  const [isDownLoad, setIsDownLoad] = useState(true);
  const handleRequestGen = (model, params) => {
    if (params.negativePrompt) {
      if (getValues().negativePrompt.length > 500) {
        toast.error(t("common.negative_prompt_max_length_error"));
        return;
      }
      params.hasNegativePrompt = true;
    }
    setCurrentId(1);
    setCurrentImg(imgSrc[0]);
    setLoading(true);
    // genPicture(model, params)
    //   .then(({ data }) => {
    //     setIsDownLoad(data.downLoad);
    //     setLoading(false);
    //     setImgSrc(data.urls);
    //     setCurrentId(1);
    //     setCurrentImg(data.urls[0]);
    //   })
    //   .catch(() => {
    //     setLoading(false);
    //   });
  };

  const downloadImg = () => {
    const randomNum = Math.floor(Math.random() * 1000000);
    const fileName = `image_${randomNum}.jpg`; // 随机命名文件
    const link = document.createElement("a");
    link.href = imgSrc[currentId - 1];
    link.download = fileName; // 设置下载后的文件名
    document.body.appendChild(link);
    // 触发点击事件以下载图片
    link.click();
    // 完成后移除链接
    document.body.removeChild(link);
  };

  const [isHovered, setIsHovered] = useState(false);
  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
  };
  const [currentId, setCurrentId] = useState(1);
  const [currentImg, setCurrentImg] = useState(imgSrc[0]);
  const handleImgChange = (v) => {
    setCurrentId(v);
    setCurrentImg(imgSrc[v - 1]);
  };
  const [merchants, setMerchants] = useState([]);
  const getMerchant = () => {
    getPrincipaList(1).then((res) => {
      setMerchants(res.data);
    });
  };
  useEffect(() => {
    if (open) {
      getMerchant();
    }
  }, [open]);

  const handleCloseImport = () => {
    closeImport();
    setIsDownLoad(true);
    // setMerchants([])
  };

  return (
    <React.Fragment>
      <Dialog
        fullScreen
        disableEscapeKeyDown
        open={open}
        onClose={handleClose}
        TransitionComponent={Transition}>
        <DialogTitle sx={{ m: 0 }} id="customized-dialog-title">
          <Typography variant="h5">{t("common.ai_text_to_picture")}</Typography>
          <div>{t("common.disclaimer_message")}</div>
        </DialogTitle>
        <IconButton
          aria-label="close"
          onClick={handleClose}
          sx={(theme) => ({
            position: "absolute",
            right: 8,
            top: 8,
            color: theme.palette.grey[500],
          })}>
          <CloseIcon />
        </IconButton>
        <DialogContent
          dividers
          sx={{
            backgroundImage:
              "radial-gradient(circle at center,rgba(0,0,0,.13) .8px,transparent 0)",
            backgroundSize: "16px 16px",
            backgroundRepeat: "round",
            padding: "0 10px 10px 40px",
            height: "calc(100vh - 270px)",
            overflow: "hidden",
          }}>
          <Grid
            container
            spacing={2}
            sx={{
              width: "100%",
              border: "1px solid #e5e7eb",
              borderRadius: "6px",
              marginTop: 1,
              height: "calc(100% - 12px)",
            }}>
            <Grid
              xs={3.5}
              item
              sx={{
                height: "100%",
                borderRight: "1px solid #e5e7eb",
                background: "white",
                padding: "0 0 0 5px !important",
              }}>
              <div
                style={{ width: "100%", height: "100%", background: "white" }}>
                <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
                  <Tabs
                    value={value}
                    onChange={handleChange}
                    aria-label="basic tabs example">
                    <Tab label={t("common.gen_model_1")} {...a11yProps(0)} />
                    <Tab label={t("common.gen_model_2")} {...a11yProps(1)} />
                    {/* <Tab label="智谱AI" {...a11yProps(2)} /> */}
                  </Tabs>
                </Box>
                <CustomTabPanel
                  value={value}
                  index={0}
                  style={{ height: "calc(100vh - 150px)" }}>
                  <SimpleBarStyle timeout={500} clickOnTrack={false}>
                    <Grid
                      container
                      spacing={2}
                      sx={{
                        height: "100%",
                      }}>
                      <Grid item xs={12}>
                        <Stack spacing={1}>
                          <InputLabel>
                            {t("common.picture_content_desc")}{" "}
                            <i style={{ color: "red" }}>*</i>
                          </InputLabel>
                          <Controller
                            name="message"
                            control={control}
                            render={({ field }) => (
                              <TextField
                                {...field}
                                fullWidth
                                className={classes.root}
                                placeholder={t(
                                  "common.please_content_placeholder"
                                )}
                                multiline
                                rows={4}
                              />
                            )}
                          />
                        </Stack>
                      </Grid>
                      <Grid item xs={12}>
                        <Stack spacing={1}>
                          <InputLabel>
                            {t("common.negative_prompt_label")}
                          </InputLabel>
                          <Controller
                            name="negativePrompt"
                            control={control}
                            render={({ field }) => (
                              <TextField
                                {...field}
                                fullWidth
                                multiline
                                rows={1}
                                className={classes.root}
                                placeholder={t(
                                  "common.please_content_placeholder"
                                )}
                              />
                            )}
                          />
                          <FormHelperText>
                            {t("common.negative_prompt_label_tips")}
                          </FormHelperText>
                        </Stack>
                      </Grid>
                      <Grid item xs={12}>
                        <Stack spacing={1}>
                          <InputLabel>
                            {t("common.gen_picture_num")}{" "}
                            <i style={{ color: "red" }}>*</i>
                          </InputLabel>
                          <Controller
                            name="count"
                            control={control}
                            render={({ field }) => (
                              <TextField
                                value="1"
                                {...field}
                                type="number"
                                fullWidth
                                className={classes.root}
                                placeholder={t(
                                  "common.please_content_placeholder"
                                )}
                              />
                            )}
                          />
                        </Stack>
                      </Grid>
                      <Grid item xs={12}>
                        <LoadingButton
                          disableElevation
                          loading={loading}
                          className={classes.genButton}
                          variant="contained"
                          fullWidth
                          onClick={handleSubmit(handleGenImage)}
                          round>
                          {t("common.gen_image_btn_text")}
                        </LoadingButton>
                      </Grid>
                      <Grid item xs={12}>
                        <Stack spacing={1}>
                          <InputLabel>
                            {t("common.gen_image_size")}{" "}
                            <i style={{ color: "red" }}>*</i>
                          </InputLabel>
                          <Controller
                            name="size"
                            control={control}
                            render={({ field }) => (
                              <RadioGroup
                                row
                                {...field}
                                aria-labelledby="demo-row-radio-buttons-group-label"
                                name="row-radio-buttons-group">
                                <FormControlLabel
                                  value="1024*1024"
                                  control={<Radio />}
                                  label="1024*1024"
                                />
                                <FormControlLabel
                                  value="720*1280"
                                  control={<Radio />}
                                  label="720*1280"
                                />
                                <FormControlLabel
                                  value="1280*720"
                                  control={<Radio />}
                                  label="1280*720"
                                />
                                <FormControlLabel
                                  value="768*1152"
                                  control={<Radio />}
                                  label="768*1152"
                                />
                              </RadioGroup>
                            )}
                          />
                        </Stack>
                      </Grid>
                      <Grid item xs={12}>
                        <Stack spacing={1}>
                          <InputLabel>
                            {t("common.gen_image_style")}{" "}
                            <i style={{ color: "red" }}>*</i>
                          </InputLabel>
                          <Controller
                            name="style"
                            control={control}
                            render={({ field }) => (
                              <RadioGroup
                                {...field}
                                row
                                aria-labelledby="demo-row-radio-buttons-group-label"
                                name="row-radio-buttons-group">
                                <FormControlLabel
                                  value="<auto>"
                                  control={<Radio />}
                                  label={t("common.gen_image_style_random")}
                                />
                                <FormControlLabel
                                  value="<photography>"
                                  control={<Radio />}
                                  label={t(
                                    "common.gen_image_style_photography"
                                  )}
                                />
                                <FormControlLabel
                                  value="<portrait>"
                                  control={<Radio />}
                                  label={t("common.gen_image_style_portrait")}
                                />

                                <FormControlLabel
                                  value="<anime>"
                                  control={<Radio />}
                                  label={t("common.gen_image_style_anime")}
                                />
                                <FormControlLabel
                                  value="<oil painting>"
                                  control={<Radio />}
                                  label={t(
                                    "common.gen_image_style_oil_painting"
                                  )}
                                />
                                <FormControlLabel
                                  value="<watercolor>"
                                  control={<Radio />}
                                  label={t("common.gen_image_style_watercolor")}
                                />
                                <FormControlLabel
                                  value="<sketch>"
                                  control={<Radio />}
                                  label={t("common.gen_image_style_sketch")}
                                />

                                <FormControlLabel
                                  value="<flat illustration>"
                                  control={<Radio />}
                                  label={t(
                                    "common.gen_image_style_flat_illustration"
                                  )}
                                />
                                <FormControlLabel
                                  value="<chinese painting>"
                                  control={<Radio />}
                                  label={t("common.gen_image_style_painting")}
                                />
                                <FormControlLabel
                                  value="<3d cartoon>"
                                  control={<Radio />}
                                  label={t("common.gen_image_style_cartoon")}
                                />
                              </RadioGroup>
                            )}
                          />
                        </Stack>
                      </Grid>
                    </Grid>
                  </SimpleBarStyle>
                </CustomTabPanel>
                <CustomTabPanel
                  value={value}
                  index={1}
                  style={{ minHeight: "calc(100vh - 150px)" }}>
                  <SimpleBarStyle timeout={500} clickOnTrack={false}>
                    <Grid
                      container
                      spacing={2}
                      sx={{
                        height: "100%",
                      }}>
                      <Grid item xs={12}>
                        <Stack spacing={1}>
                          <InputLabel>
                            {t("common.picture_content_desc")}{" "}
                            <i style={{ color: "red" }}>*</i>
                          </InputLabel>
                          <Controller
                            name="message"
                            control={control}
                            render={({ field }) => (
                              <TextField
                                {...field}
                                fullWidth
                                className={classes.root}
                                placeholder={t(
                                  "common.please_content_placeholder"
                                )}
                                multiline
                                rows={5}
                              />
                            )}
                          />
                        </Stack>
                      </Grid>
                      <Grid item xs={12}>
                        <Alert severity="warning">
                          {t("common.gen_model_2_tips")}
                        </Alert>
                      </Grid>
                      {/* <Grid item xs={12}>
                      <Stack spacing={1}>
                        <InputLabel>
                          {t('common.gen_picture_num')} <i style={{ color: "red" }}>*</i>
                        </InputLabel>
                        <Controller
                          name="count"
                          control={control}
                          render={({ field }) => (
                            <TextField
                              value="1"
                              {...field}
                              type="number"
                              fullWidth
                              className={classes.root}
                              placeholder={t('common.please_content_placeholder')}
                            />
                          )}
                        />
                      </Stack>
                    </Grid> */}
                      <Grid item xs={12}>
                        <LoadingButton
                          disableElevation
                          loading={loading}
                          className={classes.genButton}
                          variant="contained"
                          fullWidth
                          onClick={handleSubmit(handleGenImage)}
                          round>
                          {t("common.gen_image_btn_text")}
                        </LoadingButton>
                      </Grid>
                    </Grid>
                  </SimpleBarStyle>
                </CustomTabPanel>
                <CustomTabPanel value={value} index={2}>
                  <Stack
                    spacing={2}
                    alignItems={"center"}
                    sx={{ width: "100%", height: "100%" }}>
                    <svg
                      t="1728464875237"
                      class="icon"
                      viewBox="0 0 1024 1024"
                      version="1.1"
                      xmlns="http://www.w3.org/2000/svg"
                      p-id="33896"
                      width="128"
                      height="128">
                      <path
                        d="M96 844.8c0 38.4 166.4 70.4 364.8 70.4 204.8 0 364.8-32 364.8-70.4 0-38.4-166.4-70.4-364.8-70.4s-364.8 32-364.8 70.4z"
                        fill="#F5F5F7"
                        fill-opacity=".8"
                        p-id="33897"></path>
                      <path
                        d="M755.2 646.4l-128-160c-6.4-6.4-12.8-12.8-25.6-12.8H326.4c-12.8 0-19.2 6.4-25.6 12.8l-128 160v83.2h582.4v-83.2z"
                        fill="#AEB8C2"
                        p-id="33898"></path>
                      <path
                        d="M281.6 268.8h364.8c12.8 0 19.2 12.8 19.2 19.2v505.6c0 12.8-12.8 19.2-19.2 19.2H281.6c-12.8 0-19.2-12.8-19.2-19.2V294.4c-6.4-12.8 6.4-25.6 19.2-25.6z"
                        fill="#F5F5F7"
                        p-id="33899"></path>
                      <path
                        d="M326.4 326.4h268.8c6.4 0 12.8 6.4 12.8 12.8v134.4c0 6.4-6.4 12.8-12.8 12.8H326.4C320 480 320 473.6 320 467.2V332.8c0-6.4 0-6.4 6.4-6.4z m0 211.2h268.8c6.4 0 12.8 6.4 12.8 12.8s-6.4 12.8-12.8 12.8H326.4c-6.4 0-6.4-6.4-6.4-12.8s0-12.8 6.4-12.8z m0 64h268.8c6.4 0 12.8 6.4 12.8 12.8s-6.4 12.8-12.8 12.8H326.4c-6.4 0-6.4-6.4-6.4-12.8s0-12.8 6.4-12.8z m428.8 236.8c-6.4 19.2-19.2 32-38.4 32h-512c-19.2 0-32-12.8-38.4-32v-192h140.8c12.8 0 25.6 12.8 25.6 32 0 12.8 12.8 32 25.6 32h185.6c12.8 0 25.6-12.8 25.6-32s12.8-32 25.6-32h140.8v179.2c19.2 6.4 19.2 6.4 19.2 12.8z m19.2-556.8l-38.4 12.8h-6.4v-6.4l12.8-32c-12.8-12.8-19.2-38.4-19.2-57.6 0-51.2 57.6-96 121.6-96s115.2 38.4 115.2 96-57.6 96-121.6 96c-25.6 0-44.8-6.4-64-12.8z"
                        fill="#DCE0E6"
                        p-id="33900"></path>
                      <path
                        d="M870.4 198.4c0 6.4 6.4 12.8 12.8 12.8s12.8-6.4 12.8-12.8 0-12.8-6.4-12.8c-12.8 0-19.2 6.4-19.2 12.8zM825.6 198.4c0 6.4 6.4 12.8 12.8 12.8s12.8-6.4 12.8-12.8-6.4-12.8-12.8-12.8-12.8 6.4-12.8 12.8zM774.4 198.4c0 6.4 6.4 12.8 12.8 12.8s12.8-6.4 12.8-12.8-6.4-12.8-12.8-12.8-12.8 6.4-12.8 12.8zM230.4 198.4c0 6.4 6.4 12.8 12.8 12.8s12.8-6.4 12.8-12.8 0-12.8-6.4-12.8c-12.8 0-19.2 6.4-19.2 12.8zM166.4 198.4c0 6.4 6.4 12.8 12.8 12.8s12.8-6.4 12.8-12.8 0-12.8-6.4-12.8c-12.8 0-19.2 6.4-19.2 12.8z"
                        fill="#FFFFFF"
                        p-id="33901"></path>
                    </svg>
                    <Typography variant="caption">敬请期待！</Typography>
                  </Stack>
                </CustomTabPanel>
              </div>
            </Grid>
            <Grid xs={8.5} item sx={{ height: "calc(100% - 16px - 36px)" }}>
              <Stack
                direction={"row"}
                alignItems={"center"}
                sx={{ pl: 1, pr: 2 }}
                justifyContent={"space-between"}>
                <div>
                  <Typography variant="h5" gutterBottom>
                    {t("common.gen_image_preview")}
                  </Typography>
                </div>
                <Tooltip title={t("common.select_image_import")}>
                  <Button
                    // disabled="loading"
                    variant="outlined"
                    onClick={openImport}>
                    {t("common.import_resource")}
                  </Button>
                </Tooltip>
              </Stack>
              <Box
                sx={{
                  width: "100%",
                  paddingTop: "100px",
                  height: "100%",
                  // height: "calc(100% - 16px - 36px)",
                  display: "flex",
                  paddingBottom: "100px",
                  alignItems: "center",
                  /* 设置高斯模糊效果，参数值越大模糊程度越高 */

                  justifyContent: "center",
                  // "&:before":{
                  //   backgroundColor: '#f0f0f0',
                  //   filter: 'blur(10px)',
                  // }
                }}>
                {loading ? (
                  <Stack alignItems={"center"} spacing={2}>
                    <div class="loading-box">
                      <div class="mop-css-x cube-grid">
                        <div class="sk-cube"></div>
                        <div class="sk-cube"></div>
                        <div class="sk-cube"></div>
                        <div class="sk-cube"></div>
                        <div class="sk-cube"></div>
                        <div class="sk-cube"></div>
                        <div class="sk-cube"></div>
                        <div class="sk-cube"></div>
                        <div class="sk-cube"></div>
                      </div>
                      <Typography variant="h5" color="#5e5e5e">
                        {t("common.ai_model_loading_gen")}
                      </Typography>
                    </div>
                  </Stack>
                ) : (
                  <>
                    {imgSrc.length > 0 ? (
                      <Box
                        sx={{
                          width: "100%",
                          height: "100%",
                          display: "flex",
                          alignItems: "center",
                          flexDirection: "column",
                          justifyContent: "space-between",
                        }}>
                        <Box
                          className={`${classes.imgContainer} ${
                            isHovered ? classes.imgHovered : ""
                          }`}
                          onMouseEnter={handleMouseEnter}
                          onMouseLeave={handleMouseLeave}>
                          <img height={"100%"} src={currentImg} />
                          <Box className={classes.imgOverlay} />
                          <IconButton
                            className={classes.previewBtn}
                            onClick={() => {
                              // setSrc(data?.url);
                              setPreviewOpen(true);
                              // const title =
                              //   data?.merchantName +
                              //   "-" +
                              //   data.storeName +
                              //   "-" +
                              //   data?.screenName +
                              //   "-" +
                              //   data?.shotTime;
                              // setTitle(title);
                            }}>
                            <VisibilityIcon
                              xs={{ backgroundColor: "white", fontSize: 60 }}
                            />
                          </IconButton>
                        </Box>
                        <div
                          style={{
                            border: "1px solid #e5e7eb",
                            padding: "12px",
                            borderRadius: "12px",
                            minHeight: "100px",
                            minWidth: "90%",
                            boxSizing: "border-box",
                          }}>
                          <Tabs
                            value={value}
                            onChange={(e, v) => {
                              handleImgChange(v);
                            }}
                            variant="scrollable"
                            sx={{
                              ".MuiTabs-indicator": {
                                display: "none",
                              },
                              boxSizing: "border-box",
                            }}
                            scrollButtons={"auto"}
                            aria-label="scrollable prevent tabs example">
                            {imgSrc.map((img, index) => {
                              return (
                                <Tab
                                  key={index}
                                  value={index + 1}
                                  icon={
                                    <img width={150} height={90} src={img} />
                                  }
                                  sx={{
                                    padding: "1px",
                                    fontSize: "12px",
                                    boxSizing: "border-box",
                                    cursor: "pointer",
                                    border:
                                      index + 1 === currentId
                                        ? "3px solid #7ac143"
                                        : "none",
                                    margin: "0px 5px",
                                    borderRadius: "2px",
                                  }}
                                />
                              );
                            })}
                          </Tabs>
                        </div>
                      </Box>
                    ) : (
                      <div
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          alignItems: "center",
                        }}>
                        <div>
                          <img width={80} src={imgGenPath} />
                        </div>
                        <Typography variant="h4" gutterBottom>
                          {t("common.text_to_image")}
                        </Typography>
                        <Typography
                          color={"#9ca3af"}
                          variant="overline"
                          gutterBottom>
                          {t("common.text_to_image_desc")}
                        </Typography>
                      </div>
                    )}
                  </>
                )}
              </Box>
            </Grid>
          </Grid>
        </DialogContent>
      </Dialog>
      {previewOpen && (
        <Lightbox
          hideZoom={false}
          medium={currentImg}
          large={currentImg}
          small={currentImg}
          smallSrcSet={currentImg}
          alt={getValues().message}
          showRotate={true}
          onClose={() => {
            // setSrc("");
            setPreviewOpen(false);
            // setTitle("");
          }}
        />
      )}
      <ImportMaterialOpen
        open={importOpen}
        merchants={merchants}
        url={imgSrc[currentId - 1]}
        onClose={handleCloseImport}
      />
    </React.Fragment>
  );
}
import { yupResolver } from "@hookform/resolvers/yup";
const ImportMaterialOpen = ({ open, merchants, onClose, url, isDownLoad }) => {
  const { t } = useTranslation();
  const treeSelectRef = useRef(null);
  const [groups, setGroups] = useState([]);
  // 定义验证模式
  const schema = Yup.object().shape({
    merchantId: Yup.string().required(t("ips.ips_select_merchant")),
    groupId: Yup.string().required(t("common.common_material_category_please")),
  });
  const {
    control,
    handleSubmit,
    watch,
    reset,
    setValue,
    getValues,
    trigger,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      merchantId: "",
      groupId: "",
    },
  });
  const getOption = (merchantId) => {
    getTreeSelect({
      merchantId: merchantId,
    }).then((res) => {
      setGroups(res.data);
    });
  };
  const watchedField = watch("merchantId");

  useEffect(() => {
    if (!watchedField) {
      setGroups([]);
    } else {
      getOption(watchedField);
    }
    // console.log(watchedField,"watchedField");
  }, [watchedField]);

  //关闭清除表单值
  const handleClose = () => {
    onClose();
    console.log(merchants);
    setLoading(false);
  };
  const [loading, setLoading] = useState(false);
  const handleImportImage = () => {
    if (!url) {
      toast.error(t("common.image_no_gen_error"));
    }
    setLoading(true);
    improtImage({
      ...getValues(),
      url,
      download: isDownLoad,
    })
      .then(({ message }) => {
        toast.success(message);
        handleClose();
      })
      .catch(() => {
        setLoading(false);
      });
  };

  return (
    <>
      <BootstrapDialog
        open={open}
        maxWidth={"xs"}
        fullWidth
        onClose={handleClose}>
        <BootstrapDialogTitle onClose={handleClose}>
          <Typography variant="h4" component="p">
            {t("common.import_resource")}
          </Typography>
        </BootstrapDialogTitle>
        <BootstrapContent>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Stack spacing={1} sx={{ marginBottom: 2 }}>
                <InputLabel htmlFor="brandCooperate">
                  {t("ips.ips_store_brand")}
                  <i style={{ color: "red" }}>*</i>
                </InputLabel>
                <Controller
                  name="merchantId"
                  control={control}
                  render={({ field }) => (
                    <ZKSelect
                      {...field}
                      fullWidth
                      options={merchants}
                      onChange={(e) => {
                        field.onChange(e); // 更新值
                        trigger("merchantId"); // 触发验证
                      }}
                      onClear={() => {
                        setValue("merchantId", "");
                        trigger("merchantId"); // 触发验证
                      }}
                      placeholder={t("common.common_please_select_retail")}
                      error={Boolean(errors.merchantId)}
                    />
                  )}
                />
                {errors.merchantId && (
                  <FormHelperText error id="advertiserId-error">
                    {errors.merchantId.message}
                  </FormHelperText>
                )}
              </Stack>
              <Stack spacing={1} sx={{ marginBottom: 2 }}>
                <InputLabel htmlFor="store-id">
                  {t("common.common_material_category")}
                  <i style={{ color: "red" }}>*</i>
                </InputLabel>
                <Controller
                  name="merchantId"
                  control={control}
                  render={({ field }) => (
                    <Treeselect
                      {...field}
                      ref={treeSelectRef}
                      data={groups}
                      isClear={true}
                      optionValue="id"
                      optionLabel="name"
                      placeholder={t("common.common_material_category_please")}
                      onChange={(valuas) => {
                        setValue("groupId", valuas.id);
                        trigger("groupId"); // 触发验证
                      }}
                      onClear={() => {
                        setValue("groupId", "");
                        trigger("groupId"); // 触发验证
                      }}
                      disableParent={true}
                      error={Boolean(errors.groupId)}
                    />
                  )}
                />
                {errors.groupId && (
                  <FormHelperText error id="advertiserId-error">
                    {errors.groupId.message}
                  </FormHelperText>
                )}
              </Stack>
            </Grid>
          </Grid>
        </BootstrapContent>
        <BootstrapActions>
          <Stack spacing={1} direction="row">
            <Button color="info" variant="outlined" onClick={handleClose}>
              {t("common.common_edit_cancel")}
            </Button>
            <LoadingButton
              variant="contained"
              loading={loading}
              disabled={loading}
              color="primary"
              disableElevation
              onClick={handleSubmit(handleImportImage)}
              // onClick={handleSubmit}
              type="submit">
              {t("common.import_image_btn")}
            </LoadingButton>
          </Stack>
        </BootstrapActions>
      </BootstrapDialog>
    </>
  );
};
