import React from "react";
import {
  <PERSON>,
  Grid,
  TablePagination,
  Divider,
  Stack,
  Button,
  TextField,
  InputLabel,
} from "@mui/material";
import Pagination from "@mui/material/Pagination";
import AppBar from "@mui/material/AppBar";
import Toolbar from "@mui/material/Toolbar";
import { styled } from "@mui/material/styles";
import AddIcon from "@mui/icons-material/Add";
import { getComponentId } from "../common/utils";
import { useState } from "react";
import { getToken, setToken } from "@/utils/auth";
import Autocomplete, { createFilterOptions } from "@mui/material/Autocomplete";
const filter = createFilterOptions();
import {
  AntTab,
  AntTabs,
  FormLabel,
  PrettoSlider,
} from "./PropertiesComponent";
import CustomInput from "../components/CustomInput";
import CustomSelect from "../components/CustomSelect";
import CustomGroupSelect from "../components/CustomGroupSelect";
import { useEffect, useRef } from "react";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import Typography from "@mui/material/Typography";
import UploadMaterial from "@/pages/program/material/components/UploadMaterial";
import {
  saveLayout,
  getResource,
  updateLayout,
  getOptionByType,
} from "@/service/api/layout";
import { fetchEventSource } from "@microsoft/fetch-event-source";

import {
  fontSizeList,
  fontList,
  animationList,
  photoLabelList,
  isPositiveInteger,
} from "../common/utils";
import { message } from "../common/i18n";
import { toast } from "react-toastify";
const BootstrapDialog = styled(Dialog)(({ theme }) => ({
  "& .MuiDialogContent-root": {
    padding: theme.spacing(2),
  },
  "& .MuiDialogActions-root": {
    padding: theme.spacing(1),
  },
}));

let initproperties = {
  title: "editor_slide_img", //名称
  name: "editor_slide_img",
  type: "ZKTecoSlideShowImg", //轮播图组件
  editType: "ZKTecoSlideShowImgEdit",
  icon: "icon-img",
  tag: "",
  left: 20,
  top: 20,
  width: 250,
  height: 136,
  zIndex: 50,
  borderRadius: 0, //边框
  hide: false,
  imgList: [],
  rotate: 0,
  duration: 60, //时长
  transparency: 1, //透明度
  componentId: "",
  anim: "", //动画
};

const PhotosComponent = (props) => {
  const isMobile = props.isMobile;
  const currentType = props.currentType;
  const setCurrentType = props.setCurrentType;
  const currentPageIndex = props.currentPageIndex;
  const setCurrentPageIndex = props.setCurrentPageIndex;
  const currentComponentId = props.currentComponentId;
  const setCurrentComponentId = props.setCurrentComponentId;
  const pages = props.pages;
  const setPages = props.setPages;
  const scale = props.scale;
  const setScale = props.setScale;
  const setCurrentComponentIndex = props.setCurrentComponentIndex;
  const activeTempIndex = props.activeTempIndex;
  const setActiveTempIndex = props.setActiveTempIndex;
  const currentIndex = props.currentComponentIndex;

  const [properties, setProperties] = useState({
    ...initproperties,
  });

  const [showUpload, setShowUpload] = useState(false);
  const [imgOptions, setImgOptions] = useState({
    current: 0,
    size: 10,
    total: 0,
    sizes: [6, 10, 20, 40, 100],
  });

  const addUploadMaterial = useRef(null);
  const [imageList, setImageList] = useState([]);
  const [pageNumber, setPageNumber] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const ctrlAbout = useRef(null);
  const [photoLabelList, setPhotoLabelList] = useState([]);

  const [selectResult, setSelectResult] = useState([]);

  useEffect(() => {
    if (isMobile) {
      let tagList = pages[currentPageIndex].componentList
        .filter((item) => {
          if (item.type === "ZKTecoSlideShowImg" && item.tag) {
            return true;
          } else {
            return false;
          }
        })
        .map((item) => {
          return item.tag;
        });
      setSelectResult(tagList);
    }
  }, [
    currentPageIndex,
    currentIndex,
    activeTempIndex,
    properties,
    pages,
    photoLabelList,
  ]);

  useEffect(() => {
    if (isMobile) {
      getOptionByType("mobile_layout_photo_option").then((res) => {
        if (res.code === 0) {
          setPhotoLabelList(res.data);
        } else {
          setPhotoLabelList([]);
        }
      });
    }
  }, [isMobile]);

  const changeTagProperties = (value) => {
    let newInfo = {
      ...properties,
      tag: value,
    };
    setComponentInfo(newInfo);
  };

  useEffect(() => {
    return () => {
      closeEES();
    };
  }, []);

  const initEES = () => {
    if (ctrlAbout && ctrlAbout.current) {
      return false;
    }
    ctrlAbout.current = new AbortController();
    fetchEventSource(import.meta.env.VITE_APP_BASE_API + "/sse/connect", {
      method: "POST",
      headers: {
        Accept: "text/event-stream",
        Authorization: import.meta.env.VITE_TOKEN_HEADER + getToken(),
      },
      signal: ctrlAbout.current.signal,
      body: JSON.stringify({}),
      onmessage(msg) {
        let data = msg.data;
        data = JSON.parse(data);
        if (data.code === "M00000") {
          getImageList(0);
        }
      },
      onerror() {
        // 服务异常
        console.log("服务异常");
      },
      onclose() {
        // 服务关闭
        console.log("服务关闭");
      },
    });
  };
  const closeEES = () => {
    if (ctrlAbout && ctrlAbout.current) {
      ctrlAbout.current.abort();
      ctrlAbout.current = null;
    }
  };

  const getImageList = (page) => {
    let currentPage = page === undefined ? pageNumber + 1 : page + 1;
    getResource({
      page: currentPage,
      pageSize: pageSize,
      showAudited: true,
      type: "image",
      status: 2,
    }).then((res) => {
      let resData = res.data;
      let list = resData.data?.map((it) => {
        it.url = it.downloadUrl;
        return it;
      });
      setImageList(list);
      setImgOptions({
        ...imgOptions,
        current: currentPage - 1,
        total: resData.total,
      });
    });
  };

  useEffect(() => {
    getImageList();
  }, [pageSize, pageNumber]);

  useEffect(() => {
    setPageNumber(imgOptions.current);
    setPageSize(imgOptions.size);
  }, [imgOptions]);

  useEffect(() => {
    if (currentIndex !== "") {
      const curretnPage = pages[currentPageIndex];
      if (curretnPage.isTemplate) {
        let componentInfo =
          curretnPage.tempLayout[activeTempIndex]?.componentList[currentIndex];
        setProperties({
          ...componentInfo,
        });
      } else {
        let componentInfo = pages[currentPageIndex].componentList[currentIndex];
        setProperties({
          ...componentInfo,
        });
      }
    }
  }, [currentPageIndex, currentIndex, activeTempIndex, pages]);

  const setComponentInfo = (baseInfo) => {
    const newPages = JSON.parse(JSON.stringify(props.pages));
    let currentPage = newPages[currentPageIndex];
    if (currentPage.isTemplate) {
      let oldInfo =
        currentPage.tempLayout[activeTempIndex].componentList[currentIndex];
      let newInfo = {
        ...oldInfo,
        ...baseInfo,
      };
      currentPage.tempLayout[activeTempIndex].componentList[currentIndex] =
        newInfo;

      try {
        let imageDuration = pages[currentPageIndex].tempLayout
          .map((cmpList) => {
            return cmpList.componentList
              .filter((item) => {
                if (item.type === "ZKTecoSlideShowImg") {
                  return true;
                } else {
                  return false;
                }
              })
              .map((item) => {
                return item.imgList
                  ?.map((imgItem) => {
                    try {
                      return parseInt(imgItem.duration);
                    } catch (e) {
                      return 0;
                    }
                  })
                  .reduce(
                    (accumulator, currentValue) => accumulator + currentValue
                  );
              });
          })
          .reduce(function (acc, curr) {
            return acc.concat(curr);
          }, []);

        let videoDuration = pages[currentPageIndex].tempLayout
          .map((cmpList) => {
            return cmpList.componentList
              .filter((item) => {
                if (item.type === "ZKTecoVideo") {
                  return true;
                } else {
                  return false;
                }
              })
              .map((item) => {
                return item.videoList
                  ?.map((imgItem) => {
                    try {
                      return parseInt(imgItem.duration);
                    } catch (e) {
                      return 0;
                    }
                  })
                  .reduce(
                    (accumulator, currentValue) => accumulator + currentValue
                  );
              });
          })
          .reduce(function (acc, curr) {
            return acc.concat(curr);
          }, []);
        let durationArry = [...imageDuration, ...videoDuration];
        if (durationArry.length > 0) {
          const maxNum = Math.max(...durationArry);
          currentPage.tempLayout[activeTempIndex].duration = maxNum;
          currentPage.duration = maxNum;
        }
      } catch (e) {
        console.log(e);
      }
    } else {
      let oldInfo = newPages[currentPageIndex].componentList[currentIndex];
      let newInfo = {
        ...oldInfo,
        ...baseInfo,
      };
      newPages[currentPageIndex].componentList[currentIndex] = newInfo;
      try {
        let imageDuration = pages[currentPageIndex].componentList
          .filter((item) => {
            if (item.type === "ZKTecoSlideShowImg") {
              return true;
            } else {
              return false;
            }
          })
          .map((item) => {
            return item.imgList
              ?.map((imgItem) => {
                try {
                  return parseInt(imgItem.duration);
                } catch (e) {
                  return 0;
                }
              })
              .reduce(
                (accumulator, currentValue) => accumulator + currentValue
              );
          });

        let videoSize = pages[currentPageIndex].componentList.filter((item) => {
          if (item.type === "ZKTecoVideo") {
            return true;
          } else {
            return false;
          }
        });

        if (videoSize.length === 1) {
          const sum = videoSize[0].videoList
            ?.map((item) => {
              try {
                return parseInt(item.duration);
              } catch (e) {
                return 0;
              }
            })
            .reduce((accumulator, currentValue) => accumulator + currentValue);

          imageDuration.push(sum);
        }
        if (imageDuration.length > 0) {
          const maxNum = Math.max(...imageDuration);
          newPages[currentPageIndex].duration = maxNum;
        }
      } catch (e) {
        console.log(e);
      }
    }

    if (props.setPages) {
      props.setPages(newPages);
    }
  };

  const addImageBox = (item) => {
    const newPages = JSON.parse(JSON.stringify(props.pages));
    let currentPage = newPages[currentPageIndex];
    if (currentPage.isTemplate) {
      if (activeTempIndex === "") {
        toast.success(message("editor_select_template_tip"));
      } else {
        let ComponentId = getComponentId(pages);
        let initProps = {
          ...initproperties,
          componentId: ComponentId,
          imgList: [
            {
              duration: 10,
              imgSrc: item.downloadUrl,
              imgId: item.id,
              checksum: item.checksum,
            },
          ],
        };
        setProperties({ ...initProps });
        currentPage.tempLayout[activeTempIndex].componentList.push({
          ...initProps,
        });
        setPages([...newPages]);
        let index =
          currentPage.tempLayout[activeTempIndex].componentList.length - 1;
        setCurrentComponentIndex(index);
        setCurrentComponentId(ComponentId);
      }
    } else {
      let ComponentId = getComponentId(newPages);
      let initProps = {
        ...initproperties,
        componentId: ComponentId,
        imgList: [
          {
            duration: 10,
            imgSrc: item.downloadUrl,
            imgId: item.id,
            checksum: item.checksum,
          },
        ],
      };
      setProperties({ ...initProps });
      newPages[currentPageIndex].componentList.push({ ...initProps });
      setPages([...newPages]);
      let index = newPages[currentPageIndex].componentList.length - 1;
      setCurrentComponentIndex(index);
      setCurrentComponentId(ComponentId);
    }
  };
  const [value, setValue] = useState("attributes");

  const [open, setOpen] = useState(false);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  const changeProperties = (event) => {
    const name = event.target.name;
    let newInfo = {
      ...properties,
      [name]: event.target.value,
    };
    setComponentInfo(newInfo);
  };

  const changeConvertProperties = (event) => {
    const name = event.target.name;
    let newInfo = {
      ...properties,
      [name]: Math.floor(event.target.value),
    };
    setComponentInfo(newInfo);
  };
  const changeDuration = (event, index) => {
    let value = event.target.value;
    if (value === "" || isPositiveInteger(value)) {
      if (value > 999999) {
        value = 999999;
      }
      if (value < 1) {
        value = 1;
      }
      properties.imgList[index].duration = value;
      setProperties({
        ...properties,
      });
      let newInfo = {
        ...properties,
      };
      setComponentInfo(newInfo);
    } else {
      properties.imgList[index].duration = 10;
      setProperties({
        ...properties,
      });
      let newInfo = {
        ...properties,
      };
      setComponentInfo(newInfo);
      toast.error(message("editor_number_error_message"));
    }
  };

  const onBlue = (event, index) => {
    let value = event.target.value;
    if (value === "") {
      properties.imgList[index].duration = 10;
      setProperties({
        ...properties,
      });
      let newInfo = {
        ...properties,
      };
      setComponentInfo(newInfo);
      toast.error(message("editor_number_error_message"));
    }
  };

  const handleClose = () => {
    setOpen(false);
  };

  const addImage = (item) => {
    properties.imgList.push({
      duration: 5,
      imgSrc: item.downloadUrl,
      imgId: item.id,
      checksum: item.checksum,
    });
    let newInfo = {
      ...properties,
    };
    setOpen(false);
    setComponentInfo(newInfo);
  };

  const deleteImage = (index) => {
    properties.imgList.splice(index, 1);
    let newInfo = {
      ...properties,
    };
    setComponentInfo(newInfo);
  };

  const handleSliderChange = (event, newValue) => {
    let newInfo = {
      ...properties,
      transparency: newValue,
    };
    setComponentInfo(newInfo);
  };

  const handleRotationChange = (event, newValue) => {
    let newInfo = {
      ...properties,
      rotate: newValue,
    };
    setComponentInfo(newInfo);
  };

  const handleChangePage = (e, v) => {
    setImgOptions({
      ...imgOptions,
      current: v,
    });
  };

  const handleChangeRowsPerPage = (e, v) => {
    let value = e.target.value;
    setImgOptions({
      ...imgOptions,
      current: 0,
      size: value,
    });
  };

  const uploadCallback = () => {
    setImgOptions({
      ...imgOptions,
      current: 0,
    });
    setShowUpload(false);
  };

  const clickUpload = () => {
    initEES();
    setShowUpload(true);
  };

  return (
    <Grid
      sx={{
        display: "flex",
        justifyContent: "center",
        height: "100%",
      }}>
      {currentComponentId ? (
        <Grid
          sx={{
            width: "100%",
            boxShadow: "0px 0px 6px #00000029",
            borderRadius: "10px",
            backgroundColor: "#ffffff",
            overflow: "auto",
          }}>
          <AntTabs
            value={value}
            onChange={handleChange}
            aria-label="ant example">
            <AntTab value="attributes" label={message("editor_attribute")} />
            <AntTab value="style" label={message("editor_style")} />
          </AntTabs>
          <Grid>
            {value === "attributes" && (
              <Grid
                sx={{
                  p: 2,
                }}>
                {!isMobile && (
                  <Grid
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      justifyContent: "center",
                      alignItems: "center",
                      width: "100%",
                    }}>
                    <Grid
                      sx={{
                        mb: 1,
                      }}>
                      {message("editor_swicthEffect")}
                    </Grid>
                    <CustomGroupSelect
                      sx={{
                        width: "100%",
                        mt: 2,
                      }}
                      label=""
                      name="anim"
                      onChange={changeProperties}
                      value={properties.anim}
                      items={animationList}></CustomGroupSelect>
                  </Grid>
                )}

                {isMobile && (
                  <Grid
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      justifyContent: "center",
                      alignItems: "center",
                      width: "100%",
                      mb: 2,
                    }}>
                    <Stack
                      sx={{
                        marginBottom: "5px",
                        display: "flex",
                        flexDirection: "row",
                        alignItems: "center",
                        width: "100%",
                      }}
                      spacing={0}>
                      <InputLabel
                        sx={{
                          color: "#707070",
                          fontSize: "14px",
                          mr: 2,
                          flexShrink: 0,
                          whiteSpace: "nowrap",
                        }}>
                        {message("photoLabel")}
                      </InputLabel>

                      <Autocomplete
                        value={properties.tag}
                        disableClearable={!properties.tag}
                        style={{
                          width: "100%",
                          position: "relative",
                        }}
                        sx={{
                          ".MuiAutocomplete-endAdornment": {
                            top: "0px",
                            transform: "none",
                          },
                        }}
                        size="small"
                        options={photoLabelList}
                        onChange={(event, newValue) => {
                          if (newValue === null) {
                            changeTagProperties("");
                          } else {
                            changeTagProperties(newValue.value);
                          }
                        }}
                        getOptionDisabled={(option) => {
                          if (selectResult.includes(option.value)) {
                            return true;
                          }
                          return false;
                        }}
                        filterOptions={(options, params) => {
                          const filtered = filter(options, params);
                          const { inputValue } = params;
                          const isExisting = options.some(
                            (option) => inputValue === option.title
                          );
                          if (inputValue !== "" && !isExisting) {
                            filtered.push({
                              value: inputValue,
                              label: inputValue,
                            });
                          }
                          return filtered;
                        }}
                        renderInput={(params) => {
                          const { inputProps } = params;
                          const value = inputProps.value;
                          let label = value;
                          const list = photoLabelList.filter(
                            (option) => value === option.value
                          );
                          if (list && list.length === 1) {
                            label = list[0].label;
                          }
                          params.inputProps.value = label;
                          return (
                            <TextField
                              {...params}
                              label=""
                              InputProps={{
                                ...params.InputProps,
                              }}
                            />
                          );
                        }}
                        renderOption={(props, option) => {
                          const { key, ...optionProps } = props;
                          return (
                            <li key={key} {...optionProps}>
                              {option.label}
                            </li>
                          );
                        }}
                      />
                    </Stack>

                    {/* <Grid
                      sx={{
                        mb: 1,
                      }}
                    >
                      {message("photoLabel")}
                    </Grid>
                    <CustomSelect
                      isClear={true}
                      name="tag"
                      disableArray={selectResult}
                      onChange={changeProperties}
                      value={properties.tag}
                      items={photoLabelList}
                    ></CustomSelect> */}
                  </Grid>
                )}

                <Grid>
                  {properties.imgList?.map((item, index) => {
                    return (
                      <Grid key={item.imgSrc}>
                        <Grid
                          sx={{
                            position: "relative",
                          }}>
                          <img
                            style={{ width: "100%", maxHeight: "200px" }}
                            src={item.imgSrc}></img>

                          {properties.imgList.length > 1 && (
                            <Grid
                              onClick={() => {
                                deleteImage(index);
                              }}
                              sx={{
                                position: "absolute",
                                top: "50%",
                                right: "-10px",
                                zIndex: "1000",
                                backgroundColor: "#ffffff",
                                border: "1px solid gray",
                                padding: "3px",
                                height: "20px",
                                width: "20px",
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center",
                                borderRadius: "5px",
                                "&:hover": {
                                  cursor: "pointer",
                                },
                              }}>
                              X
                            </Grid>
                          )}
                        </Grid>

                        {!isMobile && (
                          <Grid
                            sx={{
                              mb: 1,
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                            }}>
                            <Grid>{message("editor_duration")}</Grid>
                            <Grid>
                              <CustomInput
                                sx={{
                                  width: "60px",
                                  marginTop: "0px",
                                  input: {
                                    padding: "5px",
                                  },
                                }}
                                type="number"
                                value={item.duration}
                                onChange={(e) => {
                                  changeDuration(e, index);
                                }}
                                onBlur={(e) => {
                                  onBlue(e, index);
                                }}
                                name="duration"></CustomInput>
                            </Grid>
                            <Grid
                              sx={{
                                ml: 1,
                              }}>
                              <Grid>{message("editor_second")}</Grid>
                            </Grid>
                          </Grid>
                        )}
                      </Grid>
                    );
                  })}

                  {!isMobile && (
                    <Grid
                      sx={{
                        display: "flex",
                        justifyContent: "center",
                      }}>
                      <Button
                        onClick={() => {
                          setOpen(true);
                        }}>
                        {message("editor_addPictures")}
                      </Button>
                    </Grid>
                  )}
                </Grid>
              </Grid>
            )}

            {value === "style" && (
              <Grid sx={{ p: 2, pt: 0 }}>
                <CustomInput
                  label={message("editor_layerName") + ":"}
                  value={properties.name}
                  onChange={changeProperties}
                  name="name"></CustomInput>

                <Grid
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    mt: 1,
                  }}>
                  <FormLabel sx={{ mr: 2 }}>
                    {message("editor_diaphaneity")}:
                  </FormLabel>
                  <PrettoSlider
                    onChange={handleSliderChange}
                    size="small"
                    min={0}
                    max={1}
                    step={0.1}
                    color="secondary"
                    value={properties.transparency}
                    aria-label="Small"
                    // valueLabelDisplay="off"
                  ></PrettoSlider>
                </Grid>

                <Grid
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    mt: 1,
                  }}>
                  <FormLabel sx={{ mr: 2 }}>
                    {message("editor_rotate")}:
                  </FormLabel>
                  <PrettoSlider
                    onChange={handleRotationChange}
                    size="small"
                    min={0}
                    max={360}
                    step={1}
                    color="secondary"
                    value={properties.rotate}
                    aria-label="Small"
                    // valueLabelDisplay="off"
                  ></PrettoSlider>
                </Grid>

                <CustomInput
                  label={message("editor_abscissa") + ":"}
                  value={properties.left}
                  onChange={changeConvertProperties}
                  name="left"></CustomInput>

                <CustomInput
                  label={message("editor_ordinate") + ":"}
                  value={properties.top}
                  onChange={changeConvertProperties}
                  name="top"></CustomInput>

                <CustomInput
                  label={message("editor_width") + ":"}
                  value={properties.width}
                  onChange={changeConvertProperties}
                  name="width"></CustomInput>

                <CustomInput
                  label={message("editor_height") + ":"}
                  value={properties.height}
                  onChange={changeConvertProperties}
                  name="height"></CustomInput>
              </Grid>
            )}
          </Grid>
        </Grid>
      ) : (
        <Grid>
          <FormLabel>{message("editor_uploadPhoto")}</FormLabel>
          <Grid
            onClick={() => {
              clickUpload();
            }}
            sx={{
              width: "100%",
              height: "80px",
              background: "#ffffff",
              borderRadius: "10px",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              cursor: "pointer",
              mt: 2,
            }}>
            {message("editor_uploadPhoto")}
            <AddIcon></AddIcon>
          </Grid>

          <Grid
            sx={{
              mt: 2,
              pb: 2,
            }}>
            <Grid
              sx={{
                pb: 2,
              }}
              container
              rowSpacing={1}
              columnSpacing={1}>
              {imageList.map((item, index) => (
                <Grid
                  onClick={() => {
                    addImageBox(item);
                  }}
                  item
                  xs={6}
                  sx={{
                    height: "100px",
                  }}
                  key={item.id + index}>
                  <img
                    srcSet={`${item.downloadUrl}`}
                    src={`${item.downloadUrl}`}
                    style={{
                      height: "100%",
                      width: "100%",
                    }}
                    loading="lazy"
                  />
                </Grid>
              ))}
            </Grid>
            <TablePagination
              component="div"
              labelRowsPerPage={<p>{message("editor_rowsPerPage")}:</p>}
              count={imgOptions.total}
              page={imgOptions.current}
              onPageChange={handleChangePage}
              rowsPerPage={imgOptions.size}
              onRowsPerPageChange={handleChangeRowsPerPage}
              rowsPerPageOptions={imgOptions.sizes}
            />
          </Grid>
        </Grid>
      )}

      <BootstrapDialog
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description">
        <DialogTitle
          sx={{ m: 0, p: 3 }}
          id="customized-dialog-title"></DialogTitle>
        <IconButton
          aria-label="close"
          onClick={handleClose}
          sx={{
            position: "absolute",
            right: 8,
            top: 8,
            color: (theme) => theme.palette.grey[500],
          }}>
          <CloseIcon />
        </IconButton>
        <DialogContent dividers>
          <Grid
            sx={{
              minWidth: "700px",
            }}>
            <Grid
              sx={{
                pb: 2,
              }}
              container
              rowSpacing={1}
              columnSpacing={1}>
              {imageList.map((item) => (
                <Grid
                  onClick={() => {
                    addImage(item);
                  }}
                  item
                  xs={4}
                  sx={{
                    height: "100px",
                  }}
                  key={item.id}>
                  <img
                    srcSet={`${item.downloadUrl}`}
                    src={`${item.downloadUrl}`}
                    style={{
                      height: "100%",
                      width: "100%",
                    }}
                    loading="lazy"
                  />
                </Grid>
              ))}
            </Grid>
            <TablePagination
              component="div"
              count={imgOptions.total}
              page={imgOptions.current}
              onPageChange={handleChangePage}
              rowsPerPage={imgOptions.size}
              onRowsPerPageChange={handleChangeRowsPerPage}
              rowsPerPageOptions={imgOptions.sizes}
              labelRowsPerPage={<p>{message("editor_rowsPerPage")}:</p>}
            />
          </Grid>
        </DialogContent>
      </BootstrapDialog>
      {showUpload && (
        <UploadMaterial
          ref={addUploadMaterial}
          open={showUpload}
          onCancel={() => {
            uploadCallback();
            setShowUpload(false);
          }}
        />
      )}
    </Grid>
  );
};

export default PhotosComponent;
