import React, { forwardRef, useState, useEffect, useMemo, useRef } from "react";
import * as Yup from "yup";
import { useFormik } from "formik";
import LoadingButton from "@mui/lab/LoadingButton";
import {
  Stack,
  CircularProgress,
  IconButton,
  Tab,
  Grid,
  Button,
  InputLabel,
  Slider,
  Box,
  FormHelperText,
  Typography,
  OutlinedInput,
  Switch,
} from "@mui/material";
import { toast } from "react-toastify";
import TabContext from "@mui/lab/TabContext";
import TabList from "@mui/lab/TabList";
import TabPanel from "@mui/lab/TabPanel";
import { useNavigate, useSearchParams } from "react-router-dom";
import MainCard from "@/components/MainCard";
import ZKSelect from "@/components/ZKSelect";
import { getOutletList } from "@/service/api/L3Sevice.js";
import { updateScreen, getScreenInfo } from "@/service/api/screen";
import { useTranslation } from "react-i18next";
import { screendirections, deviceTypies } from "@/dict/commonDict";
import Descriptions from "@/components/descriptions";
import { getPrincipaList } from "@/service/api/L3Sevice.js";
import BorderColorIcon from "@mui/icons-material/BorderColor";
import CheckIcon from "@mui/icons-material/Check";
import ClearIcon from "@mui/icons-material/Clear";
import { useTheme } from "@mui/material/styles";
import { screenControl } from "@/service/api/screenParamsContrl";
import useLoading from "@/hooks/useLoading";
import ZKAutocomplete from "@/components/ZKAutocomplete";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
const addScreenForm = forwardRef((props, ref) => {
  const theme = useTheme();
  const [loading, toggleLoading, openLoading] = useLoading();
  const [merchantOptions, setMerchantOptions] = useState([]);

  const navigate = useNavigate();
  const [tabValue, setTableValue] = React.useState("base");
  const { t } = useTranslation();
  const [storeOptions, setStoreOptions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [search, setSearch] = useSearchParams();
  const [store, setStore] = useState(null);
  const [showParamTab, setShowParamTab] = useState(false);
  const [deviceType, setDeviceType] = useState("0");
  const [adbSwitchStatus, setAdbSwitchStatus] = useState(false);
  const [columnData, setColumnData] = useState({
    brightness: 10,
    volume: 100,
    color: 2200,
  });
  const getOption = (merchantId) => {
    getOutletList(merchantId).then((res) => {
      setStoreOptions(res.data);
    });
  };
  // 请求商户数据
  const handleRequestMerchant = () => {
    getPrincipaList().then((res) => {
      setMerchantOptions(res.data);
    });
  };

  const handleGetScreenInfo = () => {
    openLoading();
    getScreenInfo(search.get("id"))
      .then((res) => {
        const { data } = res;
        setShowParamTab(true);
        // setShowParamTab(data?.deviceType === '1')
        if (tabValue === "params") {
          setColumnData({
            screenLuminance: data?.screenLuminance,
            screenColourTemperature: data?.screenColourTemperature,
            volume: data?.volume,
          });
        }
        setAdbSwitchStatus(data?.adbSwitch === "1" ? true : false);
        setFormData(data);
        toggleLoading();
      })
      .catch((err) => {
        toggleLoading();
        console.error(err);
      });
  };
  useEffect(() => {
    handleGetScreenInfo();
  }, [tabValue]);
  useEffect(() => {
    handleRequestMerchant();
  }, []);

  const handelSaveSubmit = (values) => {
    setIsLoading(true);
    updateScreen(values)
      .then((res) => {
        toast.success(res.message);
        setIsLoading(false);
        navigate(-1);
        // navigate("/screen");
      })
      .catch((error) => {
        setIsLoading(false);
      });
  };

  //表单填充
  const setFormData = (data) => {
    const volume = null === data.volume ? 50 : data.volume;
    const brightness = null === data.brightness ? 50 : data.brightness;
    getOption(data?.merchantId);
    console.log(data);
    screenFormik.setValues(
      {
        name: data.name,
        storeId: data.storeId,
        // deviceAlias: data.deviceAlias == null ? "" : data.deviceAlias,
        id: data.id,
        direction: data.direction,
        address: data.storeAddress,
        siteId: data.siteId,
        zoneId: data.zoneId,
        volume: volume,
        brightness: brightness,
        wide: data.wide,
        high: data.high,
        deviceType: data?.deviceType,
        // deviceType: data?.deviceType ? "" : data.deviceType,
        merchantId: data?.merchantId,
      },
      true
    );
  };

  //  表单
  const screenFormik = useFormik({
    initialValues: {},
    onSubmit: (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        handelSaveSubmit(values);
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
    validationSchema: Yup.object().shape({
      name: Yup.string()
        .required(t("common.common_input_device_name"))
        .min(0, t("common.common_rule_device_name"))
        .max(20, t("common.common_rule_device_name_length_max")),
      storeId: Yup.string().required(
        t("common.common_plese_scrren_onwer_outlet")
      ),
      // deviceAlias: Yup.string().required(t('common.common_please_device_Alias')),
      merchantId: Yup.string().required(t("ips.ips_select_merchant")),
      deviceType: Yup.string().required(t("common.common_please_type")),
      // siteId: Yup.string().required("请输入数字标牌siteId"),
      // zoneId: Yup.string().required("请输入数字标牌zoneId"),
      direction: Yup.string().required(
        t("common.common_plese_screen_direction")
      ),
      wide: Yup.string()
        .matches(/^[0-9]*$/, {
          message: t("ips.ips_resolution_input_number"),
          excludeEmptyString: true,
        })
        .required(t("common.common_input_width")),
      high: Yup.string()
        .matches(/^[0-9]*$/, {
          message: t("ips.ips_resolution_input_number"),
          excludeEmptyString: true,
        })
        .required(t("common.common_input_height")),
      // volume: Yup.string().required("请输入数字标牌音量"),
      // brightness: Yup.string().required("请输入数字标牌亮度"),
    }),
  });
  const handleTabChange = (event, newValue) => {
    setTableValue(newValue);
  };
  const [editorIndex, setEditorIndex] = useState(null);
  const [columns, setColumns] = useState([
    {
      key: "screenLuminance",
      valueLabelDisplay: "off",
      label: t("ips.ips_screen_brightnes"),
      step: 1,
      min: 1,
      max: 100,
      unit: "%",
    },
    {
      key: "volume",
      valueLabelDisplay: "off",
      label: t("ips.ips_device_volume"),
      step: 1,
      min: 0,
      max: 100,
      unit: "%",
    },
    {
      key: "screenColourTemperature",
      valueLabelDisplay: "off",
      label: t("common.common_screen_colour_temperature"),
      step: 1,
      min: 2000,
      max: 10000,
      unit: "K",
    },
  ]);

  const [tempEditValue, setTempEditValue] = useState(null);

  const handleSliderChange = (newValue, key) => {
    setTempEditValue((prevState) => ({
      ...prevState,
      [key]: newValue,
    }));
  };

  const handleCancelEdit = () => {
    // if (tempEditValue) {
    //   const { key, value } = tempEditValue;
    //   handleSliderChange(value, key);
    //   setTempEditValue(null); // 清空临时编辑值
    // }
    if (tempEditValue) {
      const originalValue = columnData[tempEditValue];
      handleSliderChange(originalValue, tempEditValue);
      setTempEditValue(null); // 清空临时编辑值
    }
  };

  const handleParamContrl = async (key, value) => {
    const loadingToast = toast.loading("Saving..."); // 显示加载状态
    try {
      const data = await screenControl(search.get("id"), {
        key: key,
        value: value,
      })
        .then((res) => {
          return res;
        })
        .catch((err) => {
          return null;
        });
      if (data) {
        toast.success(data.message); // 保存成功的消息
        // 清空临时编辑值
        // setTempEditValue({
        //   key: value
        // });
        setColumnData((prevState) => ({
          ...prevState,
          [key]: value,
        }));
        // 关闭编辑状态
        setEditorIndex(null);
      }
    } finally {
      toast.dismiss(loadingToast); // 关闭加载状态
    }
  };
  const handleAdbSwitchControl = async (key, value) => {
    const loadingToast = toast.loading("Saving..."); // 显示加载状态
    try {
      const data = await screenControl(search.get("id"), {
        key: "adbSwitch",
        value: value ? "1" : "0",
      })
        .then((res) => {
          return res;
        })
        .catch((err) => {
          return null;
        });
      if (data) {
        toast.success(data.message); // 保存成功的消息
        setAdbSwitchStatus(value);
        setEditorIndex(null);
      }
    } finally {
      toast.dismiss(loadingToast); // 关闭加载状态
    }
  };
  // 加载图标的样式
  const loadingStyle = {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    height: "calc(400px)",
  };
  return (
    <>
      <MainCard
        contentSX={{ minHeight: "calc(100vh - 175px)", padding: "0" }}
        divider={false}
        title={
          <Stack direction="row" alignItems="center" spacing={1}>
            <IconButton
              onClick={() => {
                navigate(-1);
              }}>
              <ArrowBackIcon />
            </IconButton>
            <Typography variant="h4" component="p">
              {t("common.common_edit_screen")}
            </Typography>
          </Stack>
        }
        border={false}>
        <TabContext value={tabValue}>
          <Box
            sx={{
              borderBottom: 1,
              borderColor: "divider",
              display: "flex",
              alignItems: "center",
              background: "white",
              justifyContent: "space-between",
            }}>
            <TabList style={{ background: "white" }} onChange={handleTabChange}>
              <Tab
                label={t("common.common_screen_base_tab")}
                value={"base"}
                key="base"
              />
              {/* {showParamTab &&
                <Tab label={t('common.common_screen_controller_tab')} value={'params'} key="params" />
              } */}
              <Tab
                label={t("common.common_screen_controller_tab")}
                value={"params"}
                key="params"
              />
            </TabList>
          </Box>

          <TabPanel value="base" sx={{ padding: "5px 0" }}>
            <div style={loading ? loadingStyle : null}>
              {loading ? (
                <CircularProgress />
              ) : (
                <form noValidate onSubmit={screenFormik.handleSubmit}>
                  <Grid container spacing={3} sx={{ padding: "20px" }}>
                    <Grid item xs={12} md={4} lg={4}>
                      <Stack spacing={1}>
                        <InputLabel htmlFor="screen-name">
                          {t("ips.ips_device")}{" "}
                          <i style={{ color: "red" }}>*</i>
                        </InputLabel>
                        <OutlinedInput
                          id="dictLabel"
                          value={screenFormik.values.name}
                          type="text"
                          fullWidth
                          name="name"
                          error={Boolean(
                            screenFormik.touched.name &&
                              screenFormik.errors.name
                          )}
                          onBlur={screenFormik.handleBlur}
                          onChange={screenFormik.handleChange}
                          placeholder={t("common.common_please_input")}
                        />
                        {screenFormik.touched.name &&
                          screenFormik.errors.name && (
                            <FormHelperText error id="name-error">
                              {screenFormik.errors.name}
                            </FormHelperText>
                          )}
                      </Stack>
                    </Grid>
                    <Grid item xs={12} md={4} lg={4}>
                      <Stack spacing={1}>
                        <InputLabel htmlFor="deviceType">
                          {t("common.common_deviceType")}
                          <i style={{ color: "red" }}>*</i>
                        </InputLabel>
                        <ZKSelect
                          displayEmpty
                          name="deviceType"
                          value={screenFormik.values.deviceType}
                          placeholder={t("common.common_plese_select")}
                          options={deviceTypies}
                          onChange={screenFormik.handleChange}
                          onBlur={screenFormik.handleBlur}
                          onClear={() => {
                            screenFormik.setFieldValue("deviceType", "");
                          }}
                          error={Boolean(
                            screenFormik.touched.deviceType &&
                              screenFormik.errors.deviceType
                          )}
                        />
                        {screenFormik.touched.deviceType &&
                          screenFormik.errors.deviceType && (
                            <FormHelperText error id="direction-error">
                              {screenFormik.errors.deviceType}
                            </FormHelperText>
                          )}
                      </Stack>
                    </Grid>
                    <Grid item xs={12} md={4} lg={4}>
                      <Stack spacing={1}>
                        <InputLabel htmlFor="direction">
                          {t("ips.ips_screen_direction")}{" "}
                          <i style={{ color: "red" }}>*</i>
                        </InputLabel>
                        <ZKSelect
                          displayEmpty
                          name="direction"
                          value={screenFormik.values.direction}
                          placeholder={t(
                            "common.common_plese_screen_direction"
                          )}
                          options={screendirections}
                          onChange={screenFormik.handleChange}
                          onBlur={screenFormik.handleBlur}
                          error={Boolean(
                            screenFormik.touched.direction &&
                              screenFormik.errors.direction
                          )}
                        />
                        {screenFormik.touched.direction &&
                          screenFormik.errors.direction && (
                            <FormHelperText error id="direction-error">
                              {screenFormik.errors.direction}
                            </FormHelperText>
                          )}
                      </Stack>
                    </Grid>
                    <Grid item xs={12} md={4} lg={4}>
                      <Stack spacing={1}>
                        <InputLabel htmlFor="store-name">
                          {t("common.common_los_merchant_name")}{" "}
                          <i style={{ color: "red" }}>*</i>
                        </InputLabel>
                        <ZKSelect
                          name="merchantId"
                          size="small"
                          value={screenFormik.values.merchantId}
                          onChange={(e) => {
                            //调用获取门店
                            getOption(e.target.value);
                            screenFormik.handleChange(e);
                          }}
                          onBlur={screenFormik.handleBlur}
                          options={merchantOptions}
                          onClear={() => {
                            setStoreOptions([]);
                            screenFormik.setFieldValue("storeId", "");
                            screenFormik.setFieldValue("merchantId", "");
                          }}
                          error={Boolean(
                            screenFormik.touched.merchantId &&
                              screenFormik.errors.merchantId
                          )}
                          placeholder={t("ips.ips_select_merchant")}
                        />
                        {screenFormik.touched.merchantId &&
                          screenFormik.errors.merchantId && (
                            <FormHelperText error id="merchantId-error">
                              {screenFormik.errors.merchantId}
                            </FormHelperText>
                          )}
                        {/* <ZKAutocomplete
                  name="storeId"
                  onClear={() => {
                    screenFormik.setFieldValue("storeId", undefined);
                    console.log(screenFormik.touched.storeId && screenFormik.errors.storeId);
                    setStore(null);
                  }}
                  value={screenFormik.values.storeId}
                  id="store-id"
                  onChange={(e, newValue) => {
                    screenFormik.handleChange(e);
                    screenFormik.setFieldValue("storeId", newValue.value)
                    setStore(newValue)
                  }}
                  onBlur={screenFormik.handleBlur}
                  data={storeOptions}
                  placeholder={t('ips.ips_select_a_outlet')}
                  error={Boolean(
                    screenFormik.touched.storeId && screenFormik.errors.storeId
                  )}
                />
                {screenFormik.touched.storeId && screenFormik.errors.storeId && (
                  <FormHelperText error id="storeId-error">
                    {screenFormik.errors.storeId}
                  </FormHelperText>
                )} */}
                      </Stack>
                    </Grid>
                    <Grid item xs={12} md={4} lg={4}>
                      <Stack spacing={1}>
                        <InputLabel htmlFor="store-name">
                          {t("common.common_outlet_owner")}
                          <i style={{ color: "red" }}>*</i>
                        </InputLabel>
                        {/* <ZKSelect
                          id="storeId"
                          name="storeId"
                          value={screenFormik.values.storeId}
                          options={storeOptions}
                          onClose={() => {
                            screenFormik.setFieldValue('storeId', '');
                          }}
                          onBlur={screenFormik.handleBlur}
                          onChange={screenFormik.handleChange}
                          type="text"
                          placeholder={t('common.common_plese_scrren_onwer_outlet')}
                          error={Boolean(screenFormik.touched.storeId && screenFormik.errors.storeId)}
                          getAddress={getAddress}
                        /> */}
                        <ZKAutocomplete
                          name="storeId"
                          onClear={() => {
                            screenFormik.setFieldValue("storeId", "");
                            // console.log(screenFormik.touched.storeId && screenFormik.errors.storeId);
                            setStore(null);
                          }}
                          value={screenFormik.values.storeId}
                          id="store-id"
                          onChange={(e, newValue) => {
                            // screenFormik.handleChange(e);
                            screenFormik.setFieldValue(
                              "storeId",
                              newValue.value
                            );
                            setStore(newValue);
                          }}
                          onBlur={screenFormik.handleBlur}
                          data={storeOptions}
                          placeholder={t(
                            "common.common_plese_scrren_onwer_outlet"
                          )}
                          error={Boolean(
                            screenFormik.touched.storeId &&
                              screenFormik.errors.storeId
                          )}
                        />
                        {screenFormik.touched.storeId &&
                          screenFormik.errors.storeId && (
                            <FormHelperText error id="storeId-error">
                              {screenFormik.errors.storeId}
                            </FormHelperText>
                          )}
                      </Stack>
                    </Grid>

                    <Grid item xs={12} md={4} lg={4}>
                      <Stack spacing={1}>
                        <InputLabel htmlFor="address">
                          {t("common.common_location")}
                          <i style={{ color: "red" }}>*</i>
                        </InputLabel>
                        <OutlinedInput
                          disabled
                          id="address"
                          placeholder={t("common.common_loading_select_outlet")}
                          value={
                            null !== store
                              ? store.remark
                              : screenFormik.values.address
                          }
                          error={Boolean(
                            screenFormik.touched.address &&
                              screenFormik.errors.address
                          )}
                          type="text"
                          fullWidth
                          name="address"
                        />
                      </Stack>
                    </Grid>

                    {/* <Grid item xs={12} md={4} lg={4}>
                      <Stack spacing={1}>
                        <InputLabel htmlFor="deviceAlias">
                          {t('ips.ips_screen_alias')}
                          <i style={{ color: 'red' }}>*</i>
                        </InputLabel>
                        <OutlinedInput
                          id="deviceAlias"
                          onBlur={screenFormik.handleBlur}
                          onChange={screenFormik.handleChange}
                          value={screenFormik.values.deviceAlias}
                          error={Boolean(screenFormik.touched.deviceAlias && screenFormik.errors.deviceAlias)}
                          type="text"
                          fullWidth
                          name="deviceAlias"
                          placeholder={t('common.common_please_input')}
                        />
                        {screenFormik.touched.deviceAlias && screenFormik.errors.deviceAlias && (
                          <FormHelperText error id="deviceAlias-error">
                            {screenFormik.errors.deviceAlias}
                          </FormHelperText>
                        )}
                      </Stack>
                    </Grid> */}
                    <Grid item xs={12} md={4} lg={4}>
                      <Stack spacing={1}>
                        <InputLabel htmlFor="resolution_wide">
                          {t("ips.ips_resolution_wide")}{" "}
                          <i style={{ color: "red" }}>*</i>
                        </InputLabel>
                        <OutlinedInput
                          id="wide"
                          value={screenFormik.values.wide}
                          type="text"
                          fullWidth
                          name="wide"
                          error={Boolean(
                            screenFormik.touched.wide &&
                              screenFormik.errors.wide
                          )}
                          onBlur={screenFormik.handleBlur}
                          onChange={screenFormik.handleChange}
                          placeholder={t("common.common_please_input")}
                        />
                        {screenFormik.touched.wide &&
                          screenFormik.errors.wide && (
                            <FormHelperText error id="wide-error">
                              {screenFormik.errors.wide}
                            </FormHelperText>
                          )}
                      </Stack>
                    </Grid>
                    <Grid item xs={12} md={4} lg={4}>
                      <Stack spacing={1}>
                        <InputLabel htmlFor="resolution_high">
                          {t("ips.ips_resolution_high")}{" "}
                          <i style={{ color: "red" }}>*</i>
                        </InputLabel>
                        <OutlinedInput
                          id="high"
                          value={screenFormik.values.high}
                          type="text"
                          fullWidth
                          name="high"
                          error={Boolean(
                            screenFormik.touched.high &&
                              screenFormik.errors.high
                          )}
                          onBlur={screenFormik.handleBlur}
                          onChange={screenFormik.handleChange}
                          placeholder={t("common.common_please_input")}
                        />
                        {screenFormik.touched.high &&
                          screenFormik.errors.high && (
                            <FormHelperText error id="high-error">
                              {screenFormik.errors.high}
                            </FormHelperText>
                          )}
                      </Stack>
                    </Grid>
                    <Grid item xs={12}>
                      <Stack
                        direction="row"
                        justifyContent="flex-end"
                        alignItems="center"
                        spacing={2}>
                        <Button
                          color="info"
                          variant="outlined"
                          onClick={() => {
                            navigate(-1);
                          }}>
                          {t("common.common_edit_cancel")}
                        </Button>
                        <LoadingButton
                          loading={isLoading}
                          disableElevation
                          type="submit"
                          variant="contained"
                          color="primary">
                          {t("common.common_submit")}
                        </LoadingButton>
                      </Stack>
                    </Grid>
                  </Grid>
                </form>
              )}
            </div>
          </TabPanel>
          {showParamTab && (
            <TabPanel value="params" sx={{ padding: "5px 0" }}>
              <div style={loading ? loadingStyle : null}>
                {loading ? (
                  <CircularProgress />
                ) : (
                  <Descriptions column={2} title={null} bordered size="large">
                    {columns.map((column, index) => {
                      const data = columnData[column?.key];
                      return (
                        <Descriptions.Item label={column.label} key={index}>
                          {editorIndex === index ? (
                            <Stack
                              direction="row"
                              spacing={2}
                              alignItems={"center"}
                              sx={{
                                width: 220,
                              }}>
                              <Slider
                                min={column?.min}
                                max={column?.max}
                                value={
                                  tempEditValue
                                    ? tempEditValue[column.key]
                                    : data
                                }
                                valueLabelDisplay={column.valueLabelDisplay}
                                step={column.step}
                                onChange={(e, newValue) => {
                                  handleSliderChange(newValue, column.key);
                                }}
                                disabled={data == -1}
                              />
                              <Typography>
                                {tempEditValue
                                  ? tempEditValue[column.key]
                                  : data}
                              </Typography>
                              <Stack
                                direction="row"
                                justifyContent="center"
                                alignItems={"center"}>
                                <CheckIcon
                                  sx={{
                                    color: theme.palette.primary.main,
                                    cursor: "pointer",
                                  }}
                                  onClick={() => {
                                    handleParamContrl(
                                      column.key,
                                      tempEditValue
                                        ? tempEditValue[column.key]
                                        : data
                                    );
                                    setTempEditValue(null);
                                    setEditorIndex(null);
                                  }}
                                />
                                <ClearIcon
                                  sx={{
                                    color: theme.palette.error.main,
                                    cursor: "pointer",
                                  }}
                                  onClick={() => {
                                    handleCancelEdit();
                                    setEditorIndex(null);
                                  }}
                                />
                              </Stack>
                            </Stack>
                          ) : (
                            <Stack
                              direction="row"
                              alignItems="center"
                              spacing={1}
                              sx={{
                                cursor: data == -1 ? "not-allowed" : "pointer",
                              }}>
                              <Typography sx={{ fontSize: "16px" }}>
                                {data}
                                {column.unit}
                              </Typography>
                              {data !== -1 && (
                                <BorderColorIcon
                                  sx={{ fontSize: "16px", cursor: "pointer" }}
                                  onClick={() => {
                                    setEditorIndex(index);
                                  }}
                                />
                              )}
                            </Stack>
                          )}
                        </Descriptions.Item>
                      );
                    })}
                    <Descriptions.Item label={t("common.adb_mode")}>
                      <Switch
                        checked={adbSwitchStatus}
                        onChange={handleAdbSwitchControl}
                      />
                    </Descriptions.Item>
                  </Descriptions>
                )}
              </div>
            </TabPanel>
          )}
        </TabContext>
      </MainCard>
    </>
  );
});

export default addScreenForm;
