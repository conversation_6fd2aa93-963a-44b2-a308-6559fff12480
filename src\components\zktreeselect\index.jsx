import React from "react";
/* eslint-disable react/prop-types */
import { <PERSON>Item, TreeView } from "@mui/lab";
import { useState, forwardRef, useEffect } from "react";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import DeleteIcon from "@mui/icons-material/Close";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import { Box, IconButton, Popover, TextField, Typography } from "@mui/material";
import styled from "@emotion/styled";
import Measure from "react-measure";

const StyledPopover = styled(Popover)((props) => ({
  "& .MuiPaper-root": {
    width: "100%",
    //@ts-ignore
    maxWidth: props?.__width + "px",
    maxHeight: "18rem",
    boxShadow:
      " 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)",
    "&::-webkit-scrollbar": {
      width: "0.4em",
    },
    "&::-webkit-scrollbar-track": {
      backgroundColor: "#f5f5f5",
      borderRadius: "5px",
      // boxShadow: 'inset 0 0 6px rgba(0,0,0,0.00)',
      webkitBoxShadow: "inset 0 0 3px rgba(0,0,0,0.1)",
    },
    "&::-webkit-scrollbar-thumb": {
      backgroundColor: "rgba(0, 0, 0, 0.2)",
      borderRadius: "5px",
      // outline: '1px solid slategrey'
    },
    "&::-webkit-scrollbar-button": {
      backgroundColor: "#eee",
      display: "none",
    },
    "&::-webkit-scrollbar-corner": {
      backgroundColor: "black",
    },
    // overflowY: 'scroll',
    // overflow: 'hidden'
  },
}));

const StyledTreeView = styled(TreeView)(() => ({
  "& .MuiTreeItem-iconContainer > svg": {
    fontSize: 22,
  },
}));

const ZKTreeSelect = forwardRef(
  (
    {
      error,
      disableParent = false,
      parentNode = "0",
      data = [],
      label,
      placeholder = "请选择",
      optionValue = "id",
      optionLabel = "label",
      isClear = true,
      onChange = () => {},
      onClear = () => {},
      dir = "ltr",
      emptyLabel = "No data found",
      defaultValue = undefined,
      size = "Normal",
    },
    ref
  ) => {
    useEffect(() => {
      data = data === null ? [] : data;
    }, [data]);
    const [anchorEl, setAnchorEl] = React.useState(null);
    const open = Boolean(anchorEl);
    const id = open ? "simple-popover" : undefined;

    const [equipmentItem, setEquipmentItem] = useState(
      defaultValue ? defaultValue[optionLabel] : ""
    );
    const [equipmentId, setEquipmentId] = useState(
      defaultValue ? defaultValue[optionValue]?.toString() : ""
    );
    const [expanded, setExpanded] = useState([]);

    const handleClick = (event) => {
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const setItem = (item) => {
      setEquipmentId(item[optionValue]);
      setEquipmentItem(item[optionLabel]);
    };

    const clear = () => {
      setEquipmentId(undefined);
      setEquipmentItem("");
    };
    React.useImperativeHandle(ref, () => ({
      setItem,
      clear,
      handleClose,
    }));

    const RenderItems = ({ items }) => {
      if (items.length <= 0) return <></>;
      return (
        <>
          {items?.map((item) => {
            if (item.children && item.children?.length > 0) {
              return (
                <TreeItem
                  key={item.id}
                  onClick={() => {
                    if (!disableParent) {
                      // 展开
                      setExpanded((oldExpanded) => {
                        const index = oldExpanded.indexOf(
                          String(item[optionValue])
                        );
                        if (index > -1) {
                          return oldExpanded.slice(0, index);
                        }
                        return [...oldExpanded, String(item.id)];
                      });
                      return;
                    }
                    // 展开
                    let first = false;
                    setExpanded((oldExpanded) => {
                      const index = oldExpanded.indexOf(
                        String(item[optionValue])
                      );
                      if (index > -1) {
                        return oldExpanded.slice(0, index);
                      }
                      first = true;
                      return [...oldExpanded, String(item.id)];
                    });

                    //首次点击顶级结点，直接退出不进行选中
                    if (first) {
                      return;
                    }
                    if (!item || !item[optionLabel] || !item[optionValue])
                      return;
                    setEquipmentId(item[optionValue]);
                    setEquipmentItem(item[optionLabel]);
                    setAnchorEl(null);
                    onChange(item);
                  }}
                  nodeId={String(item[optionValue])}
                  label={
                    <Typography sx={{ p: 1.2 }} variant="body2">
                      {item[optionLabel]}
                    </Typography>
                  }>
                  <RenderItems items={item.children} />
                </TreeItem>
              );
            } else {
              return (
                <TreeItem
                  key={item.id}
                  nodeId={String(item[optionValue])}
                  onClick={() => {
                    // 展开
                    setExpanded((oldExpanded) => {
                      const index = oldExpanded.indexOf(
                        String(item[optionValue])
                      );
                      if (index > -1) {
                        return oldExpanded.slice(0, index);
                      }
                      return [...oldExpanded, String(item.id)];
                    });

                    if (!item || !item[optionLabel] || !item[optionValue])
                      return;
                    setEquipmentId(item[optionValue]);
                    setEquipmentItem(item[optionLabel]);
                    setAnchorEl(null);
                    onChange(item);
                  }}
                  label={
                    <Typography sx={{ p: 1.2 }} variant="body2">
                      {item[optionLabel]}
                    </Typography>
                  }
                />
              );
            }
          })}
        </>
      );
    };

    return (
      <>
        <Measure bounds>
          {({
            measureRef,
            contentRect: {
              bounds: { width },
            },
          }) => (
            <Box
              ref={measureRef}
              sx={{
                width: "100%",
              }}>
              <TextField
                error={error}
                placeholder={placeholder}
                variant="outlined"
                required={false}
                label={label}
                name="equipmentItem"
                id="equipmentItem"
                // defaultValue={equipmentItem}
                value={equipmentItem}
                className="w-100"
                size={size}
                inputProps={{ readOnly: true }}
                onClick={handleClick}
                fullWidth
                InputProps={{
                  endAdornment:
                    equipmentItem && isClear ? (
                      <IconButton
                        onClick={(e) => {
                          e.stopPropagation();
                          setEquipmentItem("");
                          setEquipmentId("");
                          setExpanded([]);
                          onClear();
                        }}>
                        <DeleteIcon />
                      </IconButton>
                    ) : (
                      ""
                    ),
                }}
              />
              <StyledPopover
                id={id}
                open={open}
                anchorEl={anchorEl}
                onClose={handleClose}
                anchorOrigin={{
                  vertical: "bottom",
                  horizontal: "left",
                }}
                __width={width}>
                {data.length > 0 ? (
                  <StyledTreeView
                    defaultSelected={equipmentId}
                    selected={equipmentId}
                    aria-label="tree-view"
                    defaultCollapseIcon={
                      <ExpandMoreIcon sx={{ fontSize: 40 }} />
                    }
                    defaultExpandIcon={
                      dir === "ltr" ? (
                        <ChevronRightIcon sx={{ fontSize: 40 }} />
                      ) : (
                        <ChevronLeftIcon sx={{ fontSize: 40 }} />
                      )
                    }
                    onNodeSelect={(event, nodeId) => {
                      // console.log('event', event, 'nodeId', nodeId);
                      // // 展开
                      // setExpanded((oldExpanded) => {
                      //     // find if in the array and remove everything after it
                      //     const index = oldExpanded.indexOf(String(nodeId));
                      //     if (index > -1) {
                      //         return oldExpanded.slice(0, index);
                      //     }
                      //     return [...oldExpanded, String(nodeId)];
                      // });
                      // if (!event || !event?.target?.innerText || !nodeId) return;
                      // setEquipmentId(nodeId);
                      // setEquipmentItem(event.target.innerText);
                      // setAnchorEl(null);
                      // onChange(nodeId);
                    }}
                    expanded={expanded}>
                    <RenderItems items={data == null ? [] : data} />
                  </StyledTreeView>
                ) : (
                  <Typography p={1} color="text.secondary">
                    {emptyLabel}
                  </Typography>
                )}
              </StyledPopover>
            </Box>
          )}
        </Measure>
      </>
    );
  }
);
export default ZKTreeSelect;

// ZKTreeSelect.propTypes = {
//     data: PropTypes.object,
//     label: PropTypes.string,
//     optionValue: PropTypes.string,
//     optionLabel: string,
//     onChange: PropTypes.func,
//     onClear: PropTypes.func,
//     dir: 'ltr' | 'rtl',
//     emptyLabel: string,
//     defaultValue: PropTypes.object
//     // defaultValue?: {
//     //     [key: PropTypes.string]: PropTypes.string | PropTypes.number
//     // }
// };
// export interface TreeSelectProps {
//     data: Data;
//     label: string;
//     optionValue?: string;
//     optionLabel?: string;
//     onChange?: (value: any) => void;
//     onClear?: () => void;
//     dir?: 'ltr' | 'rtl';
//     emptyLabel?: string;
//     defaultValue?: {
//         [key: string]: string | number;
//     };
// }
