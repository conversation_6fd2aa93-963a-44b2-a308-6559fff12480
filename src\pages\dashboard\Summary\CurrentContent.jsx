import {
  Grid,
  Card,
  InputAdornment,
  IconButton,
  Button,
  TextField,
  Typography,
  Stack,
  CircularProgress,
  Box,
} from "@mui/material";
import SimpleBar from "@/components/third-party/SimpleBar";

import List from "@mui/material/List";
import React, { forwardRef, useRef, useEffect, useState } from "react";
import GradientBox from "@/components/GradientBox";
import { useTranslation } from "react-i18next";
import ModalImage from "react-modal-image";
const CurrentContent = (props) => {
  let list = props.contentData || [];
  let [boxHeight, setBoxHeight] = useState(500);
  const { t } = useTranslation();
  const getHeight = () => {
    try {
      let div = document.getElementById("currentContent_id");
      let h = div.offsetHeight; // 返回元素的总高
      setBoxHeight(h);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getHeight();
  }, []);
  return (
    <GradientBox
      id="currentContent_id"
      style={{
        width: "100%",
        height: "100%",
        flexGrow: 1,
        padding: "2px",
        display: "flex",
        margin: "0px 0px 15px 0px",
      }}
    >
      <List
        sx={{
          width: "100%",
          position: "relative",
          // overflow: "auto",
          maxHeight: boxHeight,
          flexGrow: 1,
          "& ul": { padding: 0 },
          '& .simplebar-track.simplebar-vertical': {
            width: 15
          }
          // '&::-webkit-scrollbar': {
          //     width: '13px',
          // },
          // '&::-webkit-scrollbar-thumb': {
          //     background: '#c1cdcd',
          // },
          // '&::-webkit-scrollbar-thumb:hover': {
          //     background: '#9aa4a4',
          // },
        }}
      >
        <SimpleBar
          xs={{
            "& .simplebar-content": {
              display: "flex",
              flexDirection: "column",
              background: "white",
              border: `1px solid #f4f4f4`,
            },

          }}
        >
          {list?.map((sectionId) => {
            return (
              <li
                style={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  margin: "5px",
                }}
                key={`section-${sectionId}`}
              >
                <ModalImage
                  style={{ width: "95%" }}
                  // alt={sectionId}
                  small={sectionId}
                  large={sectionId}
                  hideDownload={true} // 隐藏下载按钮
                  hideZoom={true} // 禁用放大操作
                />
              </li>
            );
          })}

          {list.length === 0 && (
            <div
              style={{
                width: "100%",
                height: "100%",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              {t("summary.noDataTip")}
            </div>
          )}
        </SimpleBar>
      </List>
    </GradientBox>
  );
};

export default CurrentContent;
