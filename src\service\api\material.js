import request from "@/utils/request";

const baseProfixURI = "/sd/v1/material";

/**
 *  分页查询 素材列表
 * <AUTHOR>
 * @date 2022-12-14 15:29
 */
export const listByPage = (params) => {
  return request({
    url: `${baseProfixURI}/page`,
    method: "get",
    params: params,
  });
};

/**
 *  素材上传
 * <AUTHOR>
 * @date 2022-12-16 11:29
 */
export const uploadFile = (params) => {
  let formData = new FormData();
  // formData.append("format", params.file);
  formData.append("multipartFile", params.file);
  formData.append("departmentId", params.departmentId);
  formData.append("groupId", params.groupId);
  return request({
    url: `${baseProfixURI}/upload/direct`,
    method: "post",
    headers: {
      isRetry: false,
      "Content-Type": "multipart/form-data",
    },
    data: formData,
  });


};

/**
 *  素材上传
 * <AUTHOR>
 */
export const uploadFileUrl = (params) => {
  return request({
    url: `${baseProfixURI}/uploadUrl/${params.advertiserId}`,
    method: "post",
    data: params,
  });
};

export const uploadVideoUrl = (params) => {
  return request({
    url: `${baseProfixURI}/uploadUrl/video`,
    method: "post",
    data: params,
  });
};

/**
 * 素材删除
 *
 * @param
 * @returns
 */
export const del = (params) => {
  return request({
    url: `${baseProfixURI}/` + params,
    method: "delete",
  });
};

/**
 * 获取预览地址
 *
 * @returns
 * @param params
 */
export const getPreviewUrl = (params) => {
  return request({
    url: `${baseProfixURI}/getPreviewUrl`,
    method: "get",
    params: params,
  });
};

/**
 * 更新素材封面图
 *
 * <AUTHOR>
 * @param params
 */
export const updateMaterialCoverImage = (params) => {
  let formData = new FormData();
  formData.append("file", params.file);
  return request({
    url: `${baseProfixURI}/updateMaterialCoverImage/` + params.id,
    method: "post",
    headers: {
      "Content-Type": "multipart/form-data",
    },
    data: formData,
  });
};

/**
 *  获取用户素材统计的数据
 * <AUTHOR>
 * @date 2023-07-07 15:29
 */
export const countMaterialStatistics = () => {
  return request({
    url: `${baseProfixURI}/countMaterialStatistics`,
    method: "get",
  });
};

/**
 *  取用户每日流量曲线图数据
 * <AUTHOR>
 * @date 2023-07-07 15:29
 */
export const getMaterialDownloadStatistics = (type, startData, endData) => {
  return request({
    url:
      `${baseProfixURI}/getMaterialDownloadStatistics?type=` +
      `${type}` +
      "&startDay=" +
      `${startData}` +
      "&endDay=" +
      `${endData}`,
    method: "get",
  });
};

export const audit = (params) => {
  return request({
    url: `${baseProfixURI}/audit/` + params,
    method: "get",
  });
};

export const getS3Token = () => {
  return request({
    url: `${baseProfixURI}/s3/token`,
    method: "get",
  });
};

export const checkUploadFileName = (params) => {
  return request({
    url: `/sd/v1/material/exist_name`,
    method: "get",
    params: params,
  });
};

export const saveFileToUploadAws = (groupId, merchantId, params) => {
  return request({
    url: `${baseProfixURI}/upload/group/${groupId}/${merchantId}`,
    method: "post",
    params: params,
  });
};

export const improtImage = (data) => {
  return request({
    url: `${baseProfixURI}/import`,
    method: "post",
    data,
  });
};
