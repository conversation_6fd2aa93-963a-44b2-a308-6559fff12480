import React from "react";
/* eslint-disable react/prop-types */

import { CloseOutlined } from "@material-ui/icons";
import { IconButton } from "@mui/material";
import { toast } from "react-toastify";
const ToastContent = (props) => {
  const { text, sx, t } = props;
  return (
    <div
      style={{
        maxWeight: 500,
        minWidth: 300,
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        ...sx,
      }}>
      <div>{text}</div>
      <IconButton onClick={() => toast.dismiss(t.id)}>
        <CloseOutlined />
      </IconButton>
    </div>
  );
};

export default ToastContent;
