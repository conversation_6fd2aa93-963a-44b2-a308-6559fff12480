
import GradientBox from '@/components/GradientBox'
import PercentCircle from '@/components/PercentCircle'
import React, { forwardRef, useRef, useEffect, useState } from "react";
import {
    Grid,
    Card,
    InputAdornment,
    IconButton,
    Button,
    TextField,
    Typography,
    Stack,
    CircularProgress,
    Box,
} from "@mui/material";
import { getSummaryOutlet_screen } from '@/service/api/summary'
import { useTranslation } from "react-i18next";
const CustomizedList = (props) => {
    const { t } = useTranslation();

    const [dataList, setDataList] = useState([]);
    useEffect(() => {
        if (props.retailClientId && props.areaId) {
            getSummaryOutlet_screen({
                retailClientId: props.retailClientId,
                areaId: props.areaId
            }).then((res) => {
                if (res.code === 0) {
                    let resData = res.data
                    setDataList([...resData])
                } else {
                    setDataList([])
                }
            }).catch((e) => {
                setDataList([])
            })
        } else {
            setDataList([])
        }
    }, [props.retailClientId, props.areaId])


    const cellStyle = {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center'
    }
    return (
        <Grid>
            <GradientBox style={{ padding: '5px', minHeight: '40px', lineHeight: '40px' }}>
                <Grid columns={{ xs: 7, sm: 7, md: 7 }} container>
                    <Grid style={cellStyle} item xs={1}>{t('summary.province')} </Grid>
                    <Grid style={cellStyle} item xs={1}>
                        {t('summary.p_outlets_installed')}
                    </Grid>
                    <Grid style={cellStyle} item xs={1}>
                        {t('summary.outlets_installed2')}
                    </Grid>
                    <Grid style={cellStyle} item xs={1}>
                        {t('summary.total_outlets')}
                    </Grid>
                    <Grid style={cellStyle} item xs={1}>
                        {t('summary.p_ds_online')}
                    </Grid>
                    <Grid style={cellStyle} item xs={1}>
                        {t('summary.ds_online')}
                    </Grid>
                    <Grid style={cellStyle} item xs={1}>
                        {t('summary.ds_with_note')}
                    </Grid>
                </Grid>
            </GradientBox>
            <Grid style={{
                maxHeight: '500px',
                overflow: 'auto'
            }}>
                {dataList.map((item, index) => {
                    return <GradientBox key={item.areaName} style={{ padding: '5px' }}>
                        <Grid columns={{ xs: 7, sm: 7, md: 7 }} container>
                            <Grid style={cellStyle} item xs={1}>{item.areaName}</Grid>
                            <Grid style={cellStyle} item xs={1}>
                                <PercentCircle textStyle={{ fontSize: '10px' }} strokeWidth={4} width={50} Percent={item.outletsRate} />
                            </Grid>
                            <Grid style={cellStyle} item xs={1}>{item.outletsInstalled}</Grid>
                            <Grid style={cellStyle} item xs={1}>{item.totalOutlets}</Grid>
                            <Grid style={cellStyle} item xs={1}>
                                <PercentCircle textStyle={{ fontSize: '10px' }} strokeWidth={4} width={50} Percent={item.onlineDSRate}></PercentCircle>
                            </Grid>
                            <Grid style={cellStyle} item xs={1}>{item.onlineDevices}</Grid>
                            <Grid style={cellStyle} item xs={1}>{item.offlineDevices}</Grid>
                        </Grid>
                    </GradientBox>
                })}
            </Grid>
        </Grid>
    );

}

export default CustomizedList;