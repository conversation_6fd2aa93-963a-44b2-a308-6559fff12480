/* eslint-disable react/prop-types */
import React, { useState, useEffect, useMemo, useRef } from "react";
import MaterialReactTable from "material-react-table";
import { useTranslation } from "react-i18next";
import { tableI18n } from "@/utils/tableLang";
import { toast } from "react-toastify";
import { Typography, Box, IconButton } from "@mui/material";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import { listScreenPageByAdvertiser } from "@/service/api/dashboard";
import TableBarSearch from "../retailer/tableBarSearch";
import { getCurrentPlaying } from "@/service/api/schedule";
import CurrentPlayList from "../retailer/currentPlayList";
import DictTag from "@/components/DictTag";
import { screenStatus } from "@/dict/commonDict";

const TableList = () => {
  const [data, setData] = useState([]);
  const { t } = useTranslation();
  // 用于hash路由保存到sessionStore拼接
  const [isError, setIsError] = useState(false);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  // 分页的参数
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });

  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);

  const buildParams = () => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      ...requestParams.current,
    };
    return params;
  };
  const currentPlayRef = useRef(null);
  //存放搜索条件的数据
  const requestParams = useRef(null);

  // 获取数据
  const getTableData = async () => {
    await listScreenPageByAdvertiser(buildParams())
      .then((res) => {
        // 设置数据
        setData(res.data.data);
        // 设置总记录数
        setRowCount(res.data.total);
        setIsLoading(false);
        setIsRefetching(false);
      })
      .catch((err) => {
        setIsError(true);
        setIsLoading(false);
        setIsRefetching(false);
      });
  };
  useEffect(() => {
    getTableData();
  }, [pagination.pageIndex, pagination.pageSize]);

  //获取当前播放的广告画面
  const handleGetPlaying = (id) => {
    getCurrentPlaying(id).then((res) => {
      const { data } = res;
      if (data && data.length > 0) {
        currentPlayRef.current.handleOpen(data);
        return;
      }
      toast.error(t("ips.ips_current_time_no_play"));
    });
  };

  const handleGetTableData = (param) => {
    requestParams.current = param;
    getTableData();
  };
  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "address",
        header: t("common.common_location"),
        enableColumnActions: false,
        enableSorting: false,
        minSize: 120,
        maxSize: 120,
      },
      {
        accessorKey: "areaName",
        header: t("common.common_form_area_name"),
        enableColumnActions: false,
        minSize: 120,
        maxSize: 120,
        enableSorting: false,
      },
      {
        accessorKey: "storeName",
        enableColumnActions: false,
        header: t("ips.ips_store_name"),
        minSize: 80,
        maxSize: 120,
        enableSorting: false,
      },
      {
        accessorKey: "deviceName",
        header: t("ips.ips_device"),
        enableColumnActions: false,
        minSize: 90,
        maxSize: 120,
        enableSorting: false,
      },

      {
        accessorKey: "status",
        header: t("ips.ips_device_online"),
        enableColumnActions: false,
        minSize: 50,
        maxSize: 60,
        enableSorting: false,
        Cell: ({ cell, row }) => {
          return (
            <DictTag
              dicts={screenStatus}
              fieldName={{ title: "label" }}
              value={row.original.status}
            />
          );
        },
      },
      {
        accessorKey: "schedule",
        header: t("common.common_scheduling"),
        enableColumnActions: false,
        minSize: 90,
        maxSize: 120,
        enableSorting: false,
        Cell: ({ cell, row }) => {
          let scheduleList = row.original.scheduleList;
          return (
            <Typography key={row.original.id}>
              {scheduleList.map((item) => (
                <>
                  {item}
                  <br />
                </>
              ))}
            </Typography>
          );
        },
      },
      {
        accessorKey: "currentPlaying",
        header: t("ips.ips_playing"),
        enableColumnActions: false,
        minSize: 90,
        maxSize: 120,
        enableSorting: false,
        Cell: ({ cell, row }) => {
          return (
            <IconButton
              size="large"
              sx={{ backgroundColor: "#eaf0f5", color: "#7ac143" }}
              onClick={() => handleGetPlaying(row.original.id)}>
              <PlayArrowIcon fontSize="large" />
            </IconButton>
          );
        },
      },
    ],
    []
  );
  return (
    <>
      <MaterialReactTable
        // table 状态
        state={{
          // 加载状态
          isLoading,
          // 分页参数
          pagination,
          // 重新拉取
          showProgressBars: isRefetching,
          showAlertBanner: isError,

          density: "compact",
        }}
        renderTopToolbar={({ table }) => (
          <Box
            sx={{
              height: "50px",
              display: "flex",
              alignItems: "center",
              justifyContent: "flex-end",
            }}>
            <TableBarSearch
              handleGetTableData={handleGetTableData}
              loadingArea={false}
            />
          </Box>
        )}
        // 解决列太多宽度太长问题
        enableColumnResizing
        // enablePinning
        // 初始化状态
        initialState={{ columnVisibility: { createTime: false } }}
        muiToolbarAlertBannerProps={
          isError
            ? {
                color: "error",
                children: t("table.loading_error"),
              }
            : undefined
        }
        // 列数
        rowCount={rowCount}
        // 固定头部
        enableStickyHeader
        // 处理表格高度
        muiTablePaperProps={{
          elevation: 0,
          sx: {
            borderRadius: "5px",
            border: "1px solid #eaf0f5",
          },
        }}
        muiTableBodyProps={{
          sx: (theme) => ({
            "& tr:nth-of-type(odd)": {
              backgroundColor: "#eaf0f5",
            },
            // 'tr:hover': {
            //     backgroundColor: '#f9fff0'
            // }
          }),
        }}
        muiTableBodyCellProps={{ sx: { color: "#61686b", fontSize: "14px" } }}
        muiTableHeadRowProps={{
          sx: { backgroundColor: "#eaf0f5" },
        }}
        muiTableBodyRowProps={{
          sx: { "tr td:hover": { backgroundColor: "red" } },
        }}
        muiTableHeadCellProps={{
          sx: { color: "#8f9a9e", fontWeight: 500, fontSize: "14px" },
        }}
        muiTableContainerProps={{ sx: { maxHeight: "600px" } }}
        // 设置背景颜色
        // muiTableBodyCellProps={{ sx: { backgroundColor: 'white' } }}
        muiTableProps={{ sx: { backgroundColor: "white" } }}
        muiBottomToolbarProps={{ sx: { backgroundColor: "white" } }}
        muiTopToolbarProps={{ sx: { backgroundColor: "white" } }}
        // 分页回调函数
        onPaginationChange={setPagination}
        // 记得开启这些 tips:否则会出现useEffect 反作用函数发起多次请求点击下一页会出问题
        manualFiltering
        manualPagination
        manualSorting
        // 开启分页
        enablePagination
        // 列定义
        columns={columns}
        // 数据
        data={data}
        // 初始化国际化语言  --- todo: i18n key doc address: https://github.com/KevinVandy/material-react-table/blob/main/packages/material-react-table/src/_locales/en.ts
        localization={tableI18n}
        muiLinearProgressProps={({ isTopToolbar }) => ({
          sx: { display: isTopToolbar ? "block" : "none" },
        })}
        // 多选底部提示
        positionToolbarAlertBanner="none"
      />
      <CurrentPlayList ref={currentPlayRef} />
    </>
  );
};

export default TableList;
