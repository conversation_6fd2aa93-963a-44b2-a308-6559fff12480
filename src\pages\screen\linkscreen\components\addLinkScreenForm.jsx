import React, { forwardRef, useState, useRef, useEffect } from "react";
import * as Yup from "yup";
import { useFormik } from "formik";
import { addLinkScreen } from "@/service/api/linkScreen";
import LoadingButton from "@mui/lab/LoadingButton";
import {
  Stack,
  Grid,
  Button,
  InputLabel,
  FormHelperText,
  OutlinedInput,
  Box,
} from "@mui/material";
import { toast } from "react-toastify";
import MainCard from "@/components/MainCard";
import ZKSelect from "@/components/ZKSelect";

import { useNavigate } from "react-router-dom";
import styles from "./index.module.less";
import ZKAutocomplete from "@/components/ZKAutocomplete";
// i18n
import { useTranslation } from "react-i18next";
import LinkScreenSelect from "./linkScreenSelect";
import { getPrincipaList } from "@/service/api/L3Sevice.js";
import { filterOutlets } from "@/service/api/L3Sevice.js";

const addLinkScreenForm = forwardRef((props, ref) => {
  const [merchantOptions, setMerchantOptions] = useState([]);
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [storeOptions, setStoreOptions] = useState([]);
  const [screenOptionRow, setScreenOptionRow] = useState([]);
  const [screenOptionColumn, setScreenOptionColumn] = useState([]);
  const [cardColumn, setCardColumn] = useState(3);
  const [cardLine, setCardLine] = useState(1);
  const [screenRow, setScreenRow] = useState(1);
  const [screenColumn, setScreenColumn] = useState(3);
  const [storeId, setStoreId] = useState(0);
  const [loading, setLoading] = useState(false);
  //这个是存储当前选中的数字标牌块index
  const [screenBlockIndex, setScreenBlockIndex] = React.useState(0);
  //添加拼接屏界面
  const linkScreenSelectRef = useRef(null);
  //模拟卡片的ref
  const cardsRef = useRef(null);

  var arrRow = [];
  var arrColumn = [];
  var allCardArray = [];
  var allScreenResultValue = [];
  {
    [...new Array(8)].map((value, index) => {
      arrRow.push({ label: index + 1, value: index + 1 });
    });
    [...new Array(8)].map((value, index) => {
      arrColumn.push({ label: index + 1, value: index + 1 });
    });
    [...new Array(cardLine * cardColumn)].map((value, index) => {
      allCardArray.push(index);
    });
  }
  const getOption = (merchantId) => {
    filterOutlets({ departmentId: merchantId }).then((res) => {
      setStoreOptions(res.data);
    });
  };
  // 请求商户数据
  const handleRequestMerchant = () => {
    getPrincipaList().then((res) => {
      setMerchantOptions(res.data);
    });
  };
  useEffect(() => {
    handleRequestMerchant();
  }, []);
  const getScreenOption = () => {
    setScreenOptionRow(arrRow);
    setScreenOptionColumn(arrColumn);
  };
  const handleChangeRow = (event) => {
    if (screenColumn == 0) {
      //赋给表格value
      setScreenRow(event.target.value);
      linkScreenFormik.values.line = event.target.value;
    } else {
      if (screenColumn == 1 && event.target.value == 1) {
        toast.error(t("ips.ips_not_select_single"));
      } else {
        //表格会变化
        resetCardContent();
        handleClick(event.target.value, screenColumn);
        setScreenRow(event.target.value);
        linkScreenFormik.values.line = event.target.value;
      }
    }
  };
  const handleChangeColumn = (event) => {
    if (screenRow == 0) {
      setScreenColumn(event.target.value);
      linkScreenFormik.values.columns = event.target.value;
    } else {
      if (event.target.value == 1 && screenRow == 1) {
        toast.error(t("ips.ips_not_select_single"));
      } else {
        resetCardContent();
        handleClick(screenRow, event.target.value);
        setScreenColumn(event.target.value);
        linkScreenFormik.values.columns = event.target.value;
      }
    }
  };
  const handleChangeStore = (event, newValue) => {
    resetCardContent();
    setStoreId(newValue.value);
    linkScreenFormik.setFieldValue("departmentId", newValue.value);
  };

  const handleClick = (line, count) => {
    setCardLine(line);
    setCardColumn(count);
  };
  const setFormValues = (screen) => {
    const { id, name, direction, sn } = screen;
    const directionName =
      direction === "0"
        ? t("ips.ips_material_landscape")
        : t("ips.ips_material_portrait_screen");
    changeCardValue(screenBlockIndex, id, name, sn, directionName);
  };

  //循环生成多个dom
  const getCardMap = () => {
    if (!cardsRef.current) {
      cardsRef.current = new Map();
    }
    return cardsRef.current;
  };

  //改变指定dom的值
  const changeCardValue = (itemId, id, name, sn, directionName) => {
    //验证是否重复选择数字标牌
    if (linkScreenFormik.values.linkScreenArray.includes(id)) {
      toast.error(t("ips.ips_not_reSelect_signage"));
      return;
    }
    const map = getCardMap();
    const node = map.get(itemId);
    // let text = name + "\n" + sn + "\n" + directionName;
    node.innerText = name;
    node.title = name;
    node.id = id;
    screenResultValue();
  };

  //所有dom的节点的值给到后端
  const screenResultValue = () => {
    //push前必须先清空
    allScreenResultValue = [];
    cardsRef.current.forEach((item) => {
      allScreenResultValue.push(item.id);
    });
    linkScreenFormik.values.linkScreenArray = allScreenResultValue.join(",");
  };

  //清空卡片里所有的内容（重置清空）
  const resetCardContent = () => {
    cardsRef.current.forEach((item) => {
      item.id = "";
      item.innerText = "";
    });
    linkScreenFormik.values.linkScreenArray = "";
  };

  useEffect(() => {
    getScreenOption();
  }, []);

  const handelSaveSubmit = (values) => {
    setLoading(true);
    // console.log(values);
    addLinkScreen(values)
      .then((res) => {
        setLoading(false);
        //用于跳转列表
        navigate(-1);
        toast.success(res.message);
      })
      .catch((error) => {});
    setLoading(false);
  };
  //自定义表单验证
  const validate = (values) => {
    const errors = {};
    //判断数字标牌块是否全部填满
    if (values.linkScreenArray == "") {
      errors.linkScreenArray = t("ips.ips_full_all_signage");
    }
    values.linkScreenArray.split(",").forEach((value) => {
      if (value == "") {
        errors.linkScreenArray = t("ips.ips_full_all_signage");
      }
    });
    return errors;
  };
  //  表单
  const linkScreenFormik = useFormik({
    initialValues: {
      name: "",
      principalDepartmentId: "", // 零售商id
      departmentId: "", //  门店id
      line: "1",
      columns: "3",
      linkScreenArray: "",
    },
    validate,
    onSubmit: (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        handelSaveSubmit(values);
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
    validationSchema: Yup.object().shape({
      name: Yup.string()
        .required(t("ips.ips_enter_link_name"))
        .min(0, t("ips.ips_link_name_null"))
        .max(20, t("ips.ips_link_name_lenth20")),
      principalDepartmentId: Yup.string().required(
        t("ips.ips_select_merchant")
      ),
      departmentId: Yup.string().required(t("ips.ips_select_a_outlet")),
      line: Yup.string().required(t("ips.ips_select_rows")),
      columns: Yup.string().required(t("ips.ips_select_colmuns")),
    }),
  });

  return (
    <>
      <form noValidate onSubmit={linkScreenFormik.handleSubmit}>
        <MainCard title={t("server.server_add_link_screen")} border={false}>
          <Grid container spacing={3} justifyContent="center">
            <Grid item xs={9}>
              <Grid container>
                <Grid item xs={5}>
                  <Stack spacing={1}>
                    <InputLabel htmlFor="link-screen-name">
                      {t("common.common_link_screen_name")}{" "}
                      <i style={{ color: "red" }}>*</i>
                    </InputLabel>
                    <OutlinedInput
                      id="link-screen-name"
                      value={linkScreenFormik.values.name}
                      type="text"
                      fullWidth
                      name="name"
                      error={Boolean(
                        linkScreenFormik.touched.name &&
                          linkScreenFormik.errors.name
                      )}
                      onBlur={linkScreenFormik.handleBlur}
                      onChange={linkScreenFormik.handleChange}
                      placeholder={t("ips.ips_enter_link_name")}
                    />
                    {linkScreenFormik.touched.name &&
                      linkScreenFormik.errors.name && (
                        <FormHelperText error id="name-error">
                          {linkScreenFormik.errors.name}
                        </FormHelperText>
                      )}
                  </Stack>
                </Grid>

                <Grid item xs={5} ml={5}>
                  <Stack spacing={1}>
                    <InputLabel htmlFor="store-name">
                      {t("common.common_los_merchant_name")}{" "}
                      <i style={{ color: "red" }}>*</i>
                    </InputLabel>
                    <ZKSelect
                      name="principalDepartmentId"
                      size="small"
                      value={linkScreenFormik.values.principalDepartmentId}
                      onChange={(e) => {
                        //调用获取门店
                        getOption(e.target.value);
                        linkScreenFormik.handleChange(e);
                      }}
                      onBlur={linkScreenFormik.handleBlur}
                      options={merchantOptions}
                      onClear={() => {
                        setStoreOptions([]);
                        linkScreenFormik.setFieldValue("departmentId", "");
                        linkScreenFormik.setFieldValue(
                          "principalDepartmentId",
                          ""
                        );
                      }}
                      error={Boolean(
                        linkScreenFormik.touched.principalDepartmentId &&
                          linkScreenFormik.errors.principalDepartmentId
                      )}
                      placeholder={t("ips.ips_select_merchant")}
                    />
                    {linkScreenFormik.touched.principalDepartmentId &&
                      linkScreenFormik.errors.principalDepartmentId && (
                        <FormHelperText error id="principalDepartmentId-error">
                          {linkScreenFormik.errors.principalDepartmentId}
                        </FormHelperText>
                      )}
                  </Stack>
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={9}>
              <Grid container>
                <Grid item xs={5}>
                  <Stack spacing={1}>
                    <InputLabel htmlFor="departmentId-id">
                      {t("ips.ips_store_outlet_name")}{" "}
                      <i style={{ color: "red" }}>*</i>
                    </InputLabel>
                    <ZKAutocomplete
                      name="departmentId"
                      onClear={() => {
                        linkScreenFormik.setFieldValue("departmentId", "");
                      }}
                      value={linkScreenFormik.values.departmentId}
                      id="departmentId-id"
                      onChange={handleChangeStore}
                      onBlur={linkScreenFormik.handleBlur}
                      data={storeOptions}
                      placeholder={t("ips.ips_select_a_outlet")}
                      error={Boolean(
                        linkScreenFormik.touched.departmentId &&
                          linkScreenFormik.errors.departmentId
                      )}
                    />
                    {linkScreenFormik.touched.departmentId &&
                      linkScreenFormik.errors.departmentId && (
                        <FormHelperText error id="departmentId-error">
                          {linkScreenFormik.errors.departmentId}
                        </FormHelperText>
                      )}
                  </Stack>
                </Grid>
                <Grid item xs={2}></Grid>
                <Grid item xs={5}></Grid>
              </Grid>
            </Grid>
            <Grid item xs={9}>
              <Grid container>
                <Grid item xs={5}>
                  <Stack spacing={1}>
                    <InputLabel htmlFor="link-screen-line">
                      {t("ips.ips_link_specifications")}{" "}
                      <i style={{ color: "red" }}>*</i>
                    </InputLabel>
                    <ZKSelect
                      id="link-screen-line"
                      name="line"
                      // value={screenRow}
                      value={linkScreenFormik.values.line}
                      options={screenOptionRow}
                      onClear={() => {
                        linkScreenFormik.setFieldValue("line", "");
                      }}
                      onBlur={linkScreenFormik.handleBlur}
                      onChange={handleChangeRow}
                      // onChange={linkScreenFormik.handleChange}
                      type="text"
                      placeholder={t("ips.ips_select_rows")}
                      error={Boolean(
                        linkScreenFormik.touched.line &&
                          linkScreenFormik.errors.line
                      )}
                    />
                    {linkScreenFormik.touched.line &&
                      linkScreenFormik.errors.line && (
                        <FormHelperText error id="line-error">
                          {linkScreenFormik.errors.line}
                        </FormHelperText>
                      )}
                  </Stack>
                </Grid>
                <Grid item xs={2}>
                  <Stack spacing={3} alignItems="center">
                    <InputLabel>&nbsp;</InputLabel>
                    <InputLabel>*</InputLabel>
                  </Stack>
                </Grid>
                <Grid item xs={5}>
                  <Stack spacing={1}>
                    <InputLabel>&nbsp;</InputLabel>
                    <ZKSelect
                      id="link-screen-column"
                      name="columns"
                      // value={screenColumn}
                      value={linkScreenFormik.values.columns}
                      options={screenOptionColumn}
                      onClear={() => {
                        linkScreenFormik.setFieldValue("columns", "");
                      }}
                      onBlur={linkScreenFormik.handleBlur}
                      onChange={handleChangeColumn}
                      // onChange={linkScreenFormik.handleChange}
                      type="text"
                      placeholder={t("ips.ips_select_colmuns")}
                      error={Boolean(
                        linkScreenFormik.touched.columns &&
                          linkScreenFormik.errors.columns
                      )}
                    />
                    {linkScreenFormik.touched.columns &&
                      linkScreenFormik.errors.columns && (
                        <FormHelperText error id="columns-error">
                          {linkScreenFormik.errors.columns}
                        </FormHelperText>
                      )}
                  </Stack>
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={9}>
              <Box sx={{ borderRadius: 3 }} px={1}>
                <Stack
                  direction="row"
                  justifyContent="flex-start"
                  alignItems="flex-start"
                  spacing={1}>
                  <InputLabel>{t("ips.ips_click_signage_bind")}</InputLabel>
                  {linkScreenFormik.touched.linkScreenArray &&
                    linkScreenFormik.errors.linkScreenArray && (
                      <FormHelperText error id="linkScreenArray-error">
                        {linkScreenFormik.errors.linkScreenArray}
                      </FormHelperText>
                    )}
                </Stack>
                <div className={styles.main}>
                  <div className={styles.main_child}>
                    <div className={styles.right}>
                      <div className={styles.right_top}>
                        {allCardArray.map((index) => {
                          return (
                            <div
                              raised="true"
                              className={styles.card}
                              style={{
                                width: `calc(100% / ${cardColumn} - 10px)`,
                                height: `calc(100% / ${cardLine} - 10px)`,
                                margin: "5px",
                              }}
                              key={index}
                              ref={(node) => {
                                const map = getCardMap();
                                node ? map.set(index, node) : map.delete(index);
                              }}
                              onClick={() => {
                                if (
                                  linkScreenFormik.values.departmentId == ""
                                ) {
                                  toast.error(t("ips.ips_not_select_outlet"));
                                  return;
                                }
                                if (screenRow == 0 || screenColumn == 0) {
                                  toast.error(
                                    t("ips.ips_not_select_colmun_row")
                                  );
                                  return;
                                }
                                linkScreenSelectRef.current.handleOpen();
                                setScreenBlockIndex(index);
                              }}></div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                </div>
              </Box>
            </Grid>
            <Grid item xs={9}>
              <Stack direction="row" justifyContent="center" spacing={2}>
                <Button
                  color="info"
                  variant="outlined"
                  onClick={() => {
                    navigate(-1);
                  }}>
                  {t("common.common_edit_cancel")}
                </Button>
                <LoadingButton
                  disableElevation
                  type="submit"
                  variant="contained"
                  color="primary"
                  disabled={loading} // 在加载状态下禁用按钮
                >
                  {t("common.common_edit_ok")}
                </LoadingButton>
              </Stack>
            </Grid>
          </Grid>
          <LinkScreenSelect
            ref={linkScreenSelectRef}
            setFormValues={setFormValues}
            getData={linkScreenFormik.values.departmentId}
          />
        </MainCard>
      </form>
    </>
  );
});

export default addLinkScreenForm;
