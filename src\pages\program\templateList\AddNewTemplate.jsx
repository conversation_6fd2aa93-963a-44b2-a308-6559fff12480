import React from "react";
import { useState, useRef, forwardRef, useEffect } from "react";
import {
  Button,
  Stack,
  Grid,
  Typography,
  InputLabel,
  TextField,
  FormHelperText,
} from "@mui/material";
import LoadingButton from "@mui/lab/LoadingButton";
import {
  BootstrapActions,
  BootstrapContent,
  BootstrapDialog,
  BootstrapDialogTitle,
} from "@/components/dialog";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import * as Yup from "yup";
import { useFormik } from "formik";
import ZKSelect from "@/components/ZKSelect";
import { getIndustry } from "@/service/api/layout";
import { getPrincipaList } from "@/service/api/L3Sevice.js";
import { getTreeSelect } from "@/service/api/materialGroup";
import Treeselect from "@/components/zktreeselect";

// eslint-disable-next-line react-refresh/only-export-components
const AddNewLayout = (props, ref) => {
  const { t } = useTranslation();
  const { open, onCancel } = props;
  const [saveLoading, setSaveLoding] = useState(false);
  const [resolution, setResolution] = useState("");
  const navigate = useNavigate();
  const treeSelectRef = React.useRef(null);

  const resolutionOptions = [
    { label: "1920*1080", value: "1920*1080" },
    { label: "1080*1920", value: "1080*1920" },
    { label: "1366*768", value: "1366*768" },
    { label: "1280*720", value: "1280*720" },
    { label: "720*1280", value: "720*1280" },
    { label: t("layout.custom"), value: "custom" },
  ];

  const publishTypeOptions = [
    { label: t("ips.ips_template_publish_public"), value: 0 },
    { label: t("ips.ips_template_publish_private"), value: 1 },
  ];

  const [industryList, setIndustryList] = useState([]);
  const [merchants, setMerchants] = useState([]);
  const [groups, setGroups] = useState([]);

  const getMerchant = () => {
    getPrincipaList(1).then((res) => {
      setMerchants(res.data);
    });
  };

  useEffect(() => {
    getIndustry().then((res) => {
      if (res.code == "00000000") {
        setIndustryList(res.data);
      } else {
        setIndustryList([]);
      }
    });

    getMerchant();
  }, []);

  const getOption = (merchantId) => {
    getTreeSelect({ merchantId: merchantId }).then((res) => {
      setGroups(res.data);
    });
  };

  const handleClearGroup = () => {
    newLayoutFormik.setFieldValue("groupId", "");
    treeSelectRef?.current?.clear();
  };

  let validationSchema = {
    name: Yup.string()
      .required(t("template.input_template_name"))
      .max(50, t("template.name_max")),
    resolution: Yup.string().required(t("layout.input_select_resolution")),
    industryType: Yup.string().required(t("template.selectIndustryType")),
    license: Yup.string().required(t("ips.ips_plz_select_template_type")),
    customWidth: Yup.number(t("layout.input_number"))
      .required(t("layout.input_width"))
      .positive(t("layout.input_positive_number"))
      .integer(t("layout.input_integer")),
    customHeight: Yup.number(t("layout.input_number"))
      .required(t("layout.input_height"))
      .positive(t("layout.input_positive_number"))
      .integer(t("layout.input_positive_number")),
    advertiserId: Yup.string().required(
      t("common.common_please_select_retail")
    ),
    groupId: Yup.string().when("advertiserId", {
      is: () => {
        return !!newLayoutFormik.values.advertiserId;
      },
      then: () => {
        return Yup.string().required(
          t("common.common_material_category_please")
        );
      },
      otherwise: () => {
        return Yup.mixed();
      },
    }),

    // advertiserId: Yup.string().required(t("layout.select_retail")),
    // groupId: Yup.string().required(
    //   t("common.common_material_category_please")
    // ),
  };

  const newLayoutFormik = useFormik({
    initialValues: {
      name: "",
      resolution: "1920*1080",
      customWidth: 200,
      customHeight: 100,
      industryType: "",
      license: "",
      advertiserId: "", //商户
      groupId: "", //素材组
      // groupId: undefined, //素材组
    },

    onSubmit: (values, { setErrors, setStatus, setSubmitting }) => {
      // values.audit = userInfor.roleCode === "admin" ? "1" : "0";
      // sessionStorage.setItem("PREVIOUS_ROUTE", "program");
      // sessionStorage.setItem("PROGRAM_INFO", JSON.stringify(values));
      // sessionStorage.setItem("isAdd", true);

      sessionStorage.setItem("templateInfo", JSON.stringify(values));
      sessionStorage.setItem("templateLayout", true);
      sessionStorage.setItem("isAddTemplateLayout", true);
      navigate("/neweditor");
    },
    validationSchema: Yup.object().shape(validationSchema),
  });

  const handleClose = () => {
    onCancel();
    newLayoutFormik.handleReset();
  };

  return (
    <>
      <BootstrapDialog
        open={open}
        maxWidth={"xs"}
        fullWidth
        onClose={handleClose}>
        <form noValidate onSubmit={newLayoutFormik.handleSubmit}>
          <BootstrapDialogTitle onClose={handleClose}>
            <Typography variant="h4" component="p">
              {t("template.addTemplate")}
            </Typography>
          </BootstrapDialogTitle>
          <BootstrapContent>
            <Stack
              justifyContent="flex-start"
              alignItems="flex-start"
              spacing={1}
              sx={{ marginBottom: 2 }}>
              <InputLabel htmlFor="name">
                {t("layout.program")}
                <span style={{ color: "red" }}>*</span>
              </InputLabel>
              <TextField
                id="name"
                fullWidth
                type="text"
                placeholder={t("template.input_template_name")}
                variant="outlined"
                name="name"
                onBlur={newLayoutFormik.handleBlur}
                onChange={newLayoutFormik.handleChange}
                value={newLayoutFormik.values.name}
                error={Boolean(
                  newLayoutFormik.touched.name && newLayoutFormik.errors.name
                )}
              />
              {newLayoutFormik.touched.name && newLayoutFormik.errors.name && (
                <FormHelperText error id="name-error">
                  {newLayoutFormik.errors.name}
                </FormHelperText>
              )}
            </Stack>

            <Stack
              justifyContent="flex-start"
              alignItems="flex-start"
              spacing={1}
              sx={{ marginBottom: 2 }}>
              <InputLabel htmlFor="resolution">
                {t("layout.resolution")}
                <span style={{ color: "red" }}>*</span>
              </InputLabel>
              <ZKSelect
                id="resolution"
                fullWidth
                name="resolution"
                value={newLayoutFormik.values.resolution}
                onBlur={newLayoutFormik.handleBlur}
                onClear={() => {
                  setResolution("");
                  newLayoutFormik.setFieldValue("resolution", undefined);
                }}
                onChange={(e) => {
                  setResolution(e.target.value);
                  newLayoutFormik.handleChange(e);
                }}
                placeholder={t("layout.input_select_resolution")}
                options={resolutionOptions}
                error={Boolean(
                  newLayoutFormik.touched.resolution &&
                    newLayoutFormik.errors.resolution
                )}
              />
              {newLayoutFormik.touched.resolution &&
                newLayoutFormik.errors.resolution && (
                  <FormHelperText error id="type-resolution">
                    {newLayoutFormik.errors.resolution}
                  </FormHelperText>
                )}
            </Stack>

            {resolution === "custom" && (
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  height: "80px",
                }}>
                <Stack
                  justifyContent="flex-start"
                  alignItems="flex-start"
                  spacing={1}
                  sx={{ marginBottom: 2 }}>
                  <InputLabel htmlFor="customWidth">
                    {t("layout.width")}
                    <span style={{ color: "red" }}>*</span>
                  </InputLabel>
                  <TextField
                    id="customWidth"
                    fullWidth
                    type="number"
                    placeholder={t("layout.input_width")}
                    variant="outlined"
                    name="customWidth"
                    onBlur={newLayoutFormik.handleBlur}
                    onChange={newLayoutFormik.handleChange}
                    value={newLayoutFormik.values.customWidth}
                    error={Boolean(
                      newLayoutFormik.touched.customWidth &&
                        newLayoutFormik.errors.customWidth
                    )}
                  />
                  {newLayoutFormik.touched.customWidth &&
                    newLayoutFormik.errors.customWidth && (
                      <FormHelperText error id="name-customWidth">
                        {newLayoutFormik.errors.customWidth}
                      </FormHelperText>
                    )}
                </Stack>

                <Stack
                  justifyContent="flex-start"
                  alignItems="flex-start"
                  spacing={1}
                  sx={{ marginBottom: 2 }}>
                  <InputLabel htmlFor="customHeight">
                    {t("layout.height")}
                    <span style={{ color: "red" }}>*</span>
                  </InputLabel>
                  <TextField
                    id="customHeight"
                    fullWidth
                    type="number"
                    placeholder={t("layout.input_height")}
                    variant="outlined"
                    name="customHeight"
                    onBlur={newLayoutFormik.handleBlur}
                    onChange={newLayoutFormik.handleChange}
                    value={newLayoutFormik.values.customHeight}
                    error={Boolean(
                      newLayoutFormik.touched.customHeight &&
                        newLayoutFormik.errors.customHeight
                    )}
                  />
                  {newLayoutFormik.touched.customHeight &&
                    newLayoutFormik.errors.customHeight && (
                      <FormHelperText error id="customHeight-error">
                        {newLayoutFormik.errors.customHeight}
                      </FormHelperText>
                    )}
                </Stack>
              </div>
            )}

            <Stack
              justifyContent="flex-start"
              alignItems="flex-start"
              spacing={1}
              sx={{ marginBottom: 2 }}>
              <InputLabel htmlFor="industryType">
                {t("template.industryType")}
                <span style={{ color: "red" }}>*</span>
              </InputLabel>
              <ZKSelect
                id="industryType"
                fullWidth
                name="industryType"
                value={newLayoutFormik.values.industryType}
                onBlur={newLayoutFormik.handleBlur}
                onClear={() => {
                  newLayoutFormik.setFieldValue("industryType", "");
                }}
                onChange={(e) => {
                  newLayoutFormik.handleChange(e);
                }}
                placeholder={t("template.selectIndustryType")}
                options={industryList}
                error={Boolean(
                  newLayoutFormik.touched.industryType &&
                    newLayoutFormik.errors.industryType
                )}
              />
              {newLayoutFormik.touched.industryType &&
                newLayoutFormik.errors.industryType && (
                  <FormHelperText error id="type-resolution">
                    {newLayoutFormik.errors.industryType}
                  </FormHelperText>
                )}
            </Stack>
            <Stack
              justifyContent="flex-start"
              alignItems="flex-start"
              spacing={1}
              sx={{ marginBottom: 2 }}>
              <InputLabel htmlFor="license">
                {t("ips.ips_template_publish_type")}
                <span style={{ color: "red" }}>*</span>
              </InputLabel>
              <ZKSelect
                id="license"
                fullWidth
                name="license"
                value={newLayoutFormik.values.license}
                onBlur={newLayoutFormik.handleBlur}
                onClear={() => {
                  newLayoutFormik.setFieldValue("license", "");
                }}
                onChange={(e) => {
                  newLayoutFormik.handleChange(e);
                }}
                placeholder={t("ips.ips_plz_select_template_type")}
                options={publishTypeOptions}
                error={Boolean(
                  newLayoutFormik.touched.license &&
                    newLayoutFormik.errors.license
                )}
              />
              {newLayoutFormik.touched.license &&
                newLayoutFormik.errors.license && (
                  <FormHelperText error id="type-resolution">
                    {newLayoutFormik.errors.license}
                  </FormHelperText>
                )}
            </Stack>
            <Grid item xs={6}>
              <Stack spacing={1} sx={{ marginBottom: 2 }}>
                <InputLabel htmlFor="brandCooperate">
                  {t("ips.ips_store_brand")}
                  <i style={{ color: "red" }}>*</i>
                </InputLabel>
                <ZKSelect
                  name="advertiserId"
                  value={newLayoutFormik.values.advertiserId}
                  onBlur={newLayoutFormik.handleBlur}
                  options={merchants}
                  onClear={() => {
                    newLayoutFormik.setFieldValue("advertiserId", "");
                    setGroups([]);
                  }}
                  onChange={(e) => {
                    getOption(e.target.value);
                    newLayoutFormik.handleChange(e);
                  }}
                  placeholder={t("common.common_please_select_retail")}
                  error={Boolean(
                    newLayoutFormik.touched.advertiserId &&
                      newLayoutFormik.errors.advertiserId
                  )}
                />
                {!newLayoutFormik.values.advertiserId &&
                  newLayoutFormik.touched.advertiserId &&
                  newLayoutFormik.errors.advertiserId && (
                    <FormHelperText error id="advertiserId-error">
                      {newLayoutFormik.errors.advertiserId}
                    </FormHelperText>
                  )}
              </Stack>
            </Grid>
            {/* {newLayoutFormik.values.advertiserId && ( */}
            <Grid item xs={6}>
              <Stack spacing={1}>
                <InputLabel htmlFor="store-id">
                  {t("common.common_material_category")}
                  <i style={{ color: "red" }}>*</i>
                </InputLabel>
                <Treeselect
                  ref={treeSelectRef}
                  data={groups}
                  isClear={true}
                  optionValue="id"
                  optionLabel="name"
                  placeholder={t("common.common_material_category_please")}
                  onChange={(valuas) => {
                    newLayoutFormik.setFieldValue("groupId", valuas.id);
                  }}
                  onClear={handleClearGroup}
                  disableParent={true}
                  error={Boolean(
                    newLayoutFormik.touched.groupId &&
                      newLayoutFormik.errors.groupId
                  )}
                />
                {!newLayoutFormik.values.groupId &&
                  newLayoutFormik.touched.groupId &&
                  newLayoutFormik.errors.groupId && (
                    <FormHelperText error id="groupId-error">
                      {newLayoutFormik.errors.groupId}
                    </FormHelperText>
                  )}
              </Stack>
            </Grid>
            {/* )} */}
          </BootstrapContent>
          <BootstrapActions>
            <Stack spacing={1} direction="row">
              <Button color="info" variant="outlined" onClick={handleClose}>
                {t("common.common_edit_cancel")}
              </Button>
              <LoadingButton
                loading={saveLoading}
                variant="contained"
                color="primary"
                disableElevation
                disabled={saveLoading}
                type="submit">
                {t("common.common_edit_ok")}
              </LoadingButton>
            </Stack>
          </BootstrapActions>
        </form>
      </BootstrapDialog>
    </>
  );
};

// eslint-disable-next-line react-refresh/only-export-components
export default React.forwardRef(AddNewLayout);
