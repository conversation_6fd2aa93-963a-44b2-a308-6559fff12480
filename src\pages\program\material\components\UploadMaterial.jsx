import React from "react";
/* eslint-disable react-refresh/only-export-components */
/* eslint-disable no- */
/* eslint-disable no-dupe-else-if */
/* eslint-disable react/jsx-key */
/* eslint-disable react-hooks/rules-of-hooks */
import { Dropzone, FileMosaic, useFakeProgress } from "@files-ui/react";
import { useState, useRef, useEffect } from "react";
import {
  Button,
  Stack,
  Typography,
  Grid,
  Alert,
  InputLabel,
  Box,
  FormHelperText,
  AlertTitle,
  FormControlLabel,
  Checkbox,
  Slider,
  Select,
  MenuItem,
} from "@mui/material";
import {
  BootstrapActions,
  BootstrapContent,
  BootstrapDialog,
  BootstrapDialogTitle,
} from "@/components/dialog";
import { useTranslation } from "react-i18next";
import { getPrincipaList } from "@/service/api/L3Sevice.js";
import {
  validatorFile,
  updateFiles,
  getOption,
  handelSaveSubmit,
  controlAddLabelIsShow,
} from "./utils";
// 消息提示
import ZKSelect from "@/components/ZKSelect";
import { toast } from "react-toastify";
import { useFormik } from "formik";
import * as Yup from "yup";
import LoadingButton from "@mui/lab/LoadingButton";
// useHooks
import { useStateUserInfo } from "@/hooks/user";
import { uploadCompnatLang } from "@/utils/langUtils";
import Treeselect from "@/components/zktreeselect";

const UploadMaterial = (props, ref) => {
  const userInfor = useStateUserInfo();
  const { t } = useTranslation();
  const { open, onCancel, merchantList } = props;

  const [files, setFiles] = useState([]);

  const [errorMsg, setErrorMsg] = useState(undefined);

  const [filesError, setFilesError] = useState(
    t("common.common_plese_select_file")
  );
  const treeSelectRef = useRef(null);
  const [isFilesError, setIsFilesError] = useState(false);
  const dropzoneRef = useRef(null);
  const [uploadLoading, setUploadLoding] = useState(false);
  const [uploadBtnDisable, setUploadBtnDisable] = useState(false);
  const [groups, setGroups] = useState([]);
  const uploaderRef = useRef(null);
  const errorStyle = { border: "1px solid #ff4d4f" };
  const isAccelerate = userInfor.areaAccelerate;
  const [labelChecked, setLabelChecked] = useState(false);
  const [controlLabelShow, setControlLabelShow] = useState(true);
  //年龄范围 性别
  const [ageRange, setAgeRange] = useState([20, 40]);
  const [gender, setGender] = useState("1");

  // 样式
  const sxGridContainer = React.useMemo(
    () => ({
      display: "flex",
      flexWrap: "wrap",
      width: "100%",
      gap: 5,
    }),
    []
  );

  // 表单定义
  const uploadFormik = useFormik({
    initialValues: {
      advertiserId: undefined,
      advertiserName: undefined,
      groupId: undefined,
      gender: gender,
      ageRange: ageRange,
    },
    onSubmit: (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        if (!labelChecked) {
          values.ageRange = "";
          values.gender = "";
        }
        handelSaveSubmit(
          values,
          files,
          setIsFilesError,
          setUploadLoding,
          setUploadBtnDisable,
          isAccelerate,
          setFiles,
          onCancel,
          t
        );
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
    validationSchema: Yup.object().shape({
      advertiserId: Yup.string().required(t("ips.ips_select_merchant")),
      groupId: Yup.string().required(
        t("common.common_material_category_please")
      ),
    }),
  });

  const removeFile = (id) => {
    setFiles(files.filter((x) => x.id !== id));
  };

  const supportedFormats = ["jpg", "jpeg", "png", "mp4"];

  useEffect(() => {
    //控制附加标签是否显示
    controlAddLabelIsShow(userInfor, setControlLabelShow);
  }, []);

  //关闭清除表单值
  const handleClose = () => {
    if (uploaderRef?.current) {
      uploaderRef?.current.stopUpload();
    }
    setFiles([]);
    uploadFormik.handleReset();
    setGroups([]);
    onCancel();
    setLabelChecked(false);
    setAgeRange([20, 40]);
    setGender("1");
  };

  const [departmentId, setDepartMentId] = useState([]);

  useEffect(() => {
    getPrincipaList().then((res) => {
      setDepartMentId(res?.data || []);
    });
  }, []);
  return (
    <>
      <BootstrapDialog
        open={open}
        maxWidth={files.length > 0 ? "md" : "xs"}
        fullWidth
        onClose={handleClose}>
        <form noValidate onSubmit={uploadFormik.handleSubmit}>
          <BootstrapDialogTitle onClose={handleClose}>
            <Typography variant="h4" component="p">
              {t("system.system_material_upload")}
            </Typography>
          </BootstrapDialogTitle>
          <BootstrapContent>
            <Grid container spacing={2}>
              <Grid item xs={files.length > 0 ? 6 : 12}>
                <Stack spacing={1} sx={{ marginBottom: 2 }}>
                  <InputLabel htmlFor="brandCooperate">
                    {t("ips.ips_store_brand")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <ZKSelect
                    name="advertiserId"
                    value={uploadFormik.values.advertiserId}
                    onChange={(e) => {
                      getOption(e.target.value, setGroups);
                      uploadFormik.handleChange(e);
                    }}
                    onBlur={uploadFormik.handleBlur}
                    options={merchantList || departmentId}
                    onClear={() => {
                      uploadFormik.setFieldValue("advertiserId", "");
                      setGroups([]);
                    }}
                    placeholder={t("common.common_please_select_retail")}
                    error={Boolean(
                      uploadFormik.touched.advertiserId &&
                        uploadFormik.errors.advertiserId
                    )}
                  />
                  {uploadFormik.touched.advertiserId &&
                    uploadFormik.errors.advertiserId && (
                      <FormHelperText error id="advertiserId-error">
                        {uploadFormik.errors.advertiserId}
                      </FormHelperText>
                    )}
                </Stack>
                <Stack spacing={1} sx={{ marginBottom: 2 }}>
                  <InputLabel htmlFor="store-id">
                    {t("common.common_material_category")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <Treeselect
                    ref={treeSelectRef}
                    data={groups}
                    isClear={true}
                    optionValue="id"
                    optionLabel="name"
                    placeholder={t("common.common_material_category_please")}
                    onChange={(valuas) => {
                      uploadFormik.values.groupId = valuas.id;
                    }}
                    onClear={() => {
                      uploadFormik.values.groupId = undefined;
                    }}
                    error={Boolean(
                      uploadFormik.touched.groupId &&
                        uploadFormik.errors.groupId
                    )}
                    disableParent={true}
                  />

                  {uploadFormik.touched.groupId &&
                    uploadFormik.errors.groupId && (
                      <FormHelperText error id="groupId-error">
                        {uploadFormik.errors.groupId}
                      </FormHelperText>
                    )}
                </Stack>
                {controlLabelShow && (
                  <Stack sx={{ marginBottom: 1 }}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          sx={{ color: "DimGray" }}
                          checked={labelChecked}
                          onChange={(event) => {
                            setLabelChecked(event.target.checked);
                          }}
                          // name="labelCheck"
                          color="primary"
                          size="small"
                        />
                      }
                      label={
                        <Typography variant="h7" color="DimGray">
                          {t("subscription.material_add_label")}
                        </Typography>
                      }
                    />
                  </Stack>
                )}
                {labelChecked && (
                  <Stack>
                    <Box sx={{ width: "99%" }}>
                      <Box
                        display="flex"
                        alignItems="center"
                        sx={{ marginBottom: 1 }}>
                        <Typography sx={{ width: "18%" }}>
                          {t("subscription.material_label_age")}
                        </Typography>
                        <Slider
                          name="ageRange"
                          value={ageRange}
                          onChange={(value) => {
                            uploadFormik.handleChange(value);
                            uploadFormik.values.ageRange == value.target.value;
                            setAgeRange(value.target.value);
                          }}
                          valueLabelDisplay="left"
                          min={1}
                          max={100}
                          marks={[
                            { value: 0, label: "0" },
                            { value: 20, label: "20" },
                            { value: 40, label: "40" },
                            { value: 60, label: "60" },
                            { value: 80, label: "80" },
                            { value: 100, label: "100" },
                          ]}
                          sx={{
                            color: "primary",
                            height: 6,
                            "& .MuiSlider-thumb": {
                              height: 15,
                              width: 15,
                              backgroundColor: "white",
                              border: "1px solid primary",
                              "&:focus, &:hover, &.Mui-active": {
                                boxShadow: "inherit",
                              },
                            },
                            "& .MuiSlider-track": {
                              height: 6,
                              borderRadius: 3,
                            },
                            "& .MuiSlider-rail": {
                              height: 6,
                              borderRadius: 4,
                              opacity: 0.5,
                              backgroundColor: "#b3e5fc",
                            },
                            width: "80%",
                          }}
                        />
                      </Box>
                      <Box
                        display="flex"
                        alignItems="center"
                        sx={{ marginBottom: 1 }}>
                        <Typography sx={{ width: "18%" }}>
                          {t("common.common_gender")}
                        </Typography>
                        <Select
                          name="gender"
                          value={gender}
                          onChange={(value) => {
                            uploadFormik.handleChange(value);
                            uploadFormik.values.gender == value.target.value;
                            setGender(value.target.value);
                          }}
                          displayEmpty
                          fullWidth
                          sx={{ width: "80%" }}>
                          <MenuItem value="1">
                            {t("common.common_male")}
                          </MenuItem>
                          <MenuItem value="2">
                            {t("common.common_female")}
                          </MenuItem>
                        </Select>
                      </Box>
                    </Box>
                  </Stack>
                )}
                <Stack
                  justifyContent="flex-start"
                  alignItems="flex-start"
                  spacing={1}
                  sx={{ marginBottom: 2 }}>
                  <InputLabel htmlFor="username-login">
                    {t("system.system_upload_file")}
                    <span style={{ color: "red" }}>*</span>
                  </InputLabel>
                  <Dropzone
                    localization={() => uploadCompnatLang()}
                    ref={dropzoneRef}
                    style={{ ...(isFilesError ? errorStyle : {}) }}
                    autoClean
                    onUploadStart={(uploadAbleFiles) => {
                      // console.log(uploadAbleFiles);
                    }}
                    uploadConfig={{ url: "#", autoUpload: false }}
                    disabled={files.length >= 6 ? true : false}
                    footer={false}
                    accept={"video/*, image/png, image/jpeg, image/jpg"}
                    validator={(file) => validatorFile(file, files)}
                    header={false}
                    uploadOnDrop={() => {
                      // console.log("aaa");
                    }}
                    onChange={(e) => {
                      updateFiles(e, setIsFilesError, setFiles, files);
                    }}
                    value={files}
                    maxFileSize={1 * 1024 * 1024 * 1024}
                    maxFiles={6}>
                    <Stack
                      direction="column"
                      justifyContent="center"
                      alignItems="center"
                      spacing={1}>
                      <svg
                        t="1688974869714"
                        className="icon"
                        viewBox="0 0 1024 1024"
                        version="1.1"
                        xmlns="http://www.w3.org/2000/svg"
                        p-id="2826"
                        width="80"
                        height="80">
                        <path
                          d="M1024 640.192C1024 782.912 919.872 896 787.648 896h-512C123.904 896 0 761.6 0 597.504 0 451.968 94.656 331.52 226.432 302.976 284.16 195.456 391.808 128 512 128c152.32 0 282.112 108.416 323.392 261.12C941.888 413.44 1024 519.04 1024 640.192z m-341.312-139.84L512 314.24 341.312 500.48h341.376z m-213.376 0v256h85.376v-256H469.312z"
                          fill="#bfbfbf"
                          p-id="2827"></path>
                      </svg>
                      <Typography>
                        {t("common.common_drage_branch_file")}
                      </Typography>
                    </Stack>
                    {/* {files.map((file) => (
                      <FileMosaic key={file.id} {...file} onDelete={removeFile} info />
                  ))} */}
                  </Dropzone>
                  {isFilesError && (
                    <FormHelperText error id="files-error">
                      {filesError}
                    </FormHelperText>
                  )}
                </Stack>
                {errorMsg && (
                  <Alert
                    severity="error"
                    onClose={() => {
                      setErrorMsg(undefined);
                    }}
                    sx={{ marginBottom: 1 }}>
                    {errorMsg}
                  </Alert>
                )}

                <Alert severity="warning">
                  <AlertTitle>{t("message.messageBox_title")}</AlertTitle>
                  <div
                    dangerouslySetInnerHTML={{
                      __html: t("common.common_upload_file_tips"),
                    }}
                  />
                  {supportedFormats.join(",")}
                </Alert>
              </Grid>
              {files.length > 0 && (
                <Grid item xs={6}>
                  <Stack
                    justifyContent="flex-start"
                    alignItems="flex-start"
                    spacing={1}>
                    <Typography>{t("common.common_op_preview")}</Typography>
                    <Box sx={sxGridContainer}>
                      {files.map((file, index) => (
                        <Box key={index}>
                          <FileMosaic
                            localization={uploadCompnatLang()}
                            {...file}
                            // progress={file.progress}
                            key={file.id}
                            onDelete={removeFile}
                            preview
                            info
                          />
                        </Box>
                      ))}
                    </Box>
                  </Stack>
                </Grid>
              )}
            </Grid>
          </BootstrapContent>
          <BootstrapActions>
            <Stack spacing={1} direction="row">
              <Button color="info" variant="outlined" onClick={handleClose}>
                {t("common.common_edit_cancel")}
              </Button>
              <LoadingButton
                loading={uploadLoading}
                variant="contained"
                color="primary"
                disableElevation
                disabled={uploadBtnDisable}
                // onClick={handleSubmit}
                type="submit">
                {t("common.common_upload")}
              </LoadingButton>
            </Stack>
          </BootstrapActions>
        </form>
      </BootstrapDialog>
      {/* <AdvertiserSelect
        ref={advertiserSelect}
        type={type}
        setFormValues={setFormValues}
      ></AdvertiserSelect> */}
    </>
  );
};
export default React.forwardRef(UploadMaterial);
