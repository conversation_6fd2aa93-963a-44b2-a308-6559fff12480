// 门店总数

import request from "@/utils/request";

/**
 *  获取summary 饼图数据
 */
export const getSummaryPieChartData = (params) => {
  return request({
    url: `/sd/v1/summary/area/screen`,
    method: "get",
    params: params,
  });
};

export const getSummaryContent = (params) => {
  return request({
    url: `/sd/v1/summary/retail/content`,
    method: "get",
    params: params,
  });
};

export const getSummaryRetailFmap = (params) => {
  return request({
    url: `/sd/v1/summary/retail/map`,
    method: "get",
    params: params,
  });
};

export const getSummaryOutletAndScreen = (params) => {
  return request({
    url: `/sd/v1/summary/retail/outlet_screen`,
    method: "get",
    params: params,
  });
};

export const getSummaryOutlet_screen = (params) => {
  return request({
    url: `/sd/v1/summary/outlet_screen/rate`,
    method: "get",
    params: params,
  });
};
