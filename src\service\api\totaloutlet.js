// 门店总数

import request from "@/utils/request";

const baseProfixURI = `/sd/v1/total_outlet`;
/**
 *  门店总数分页
 */
export const listByPage = (params) => {
  return request({
    url: `${baseProfixURI}/page`,
    method: "get",
    params: params,
    headers: {
      isRetry: false,
    },
  });
};

/**
 *  根据ID查询总数
 */
export const getInfo = (id) => {
  return request({
    url: `${baseProfixURI}/${id}`,
    method: "get",
    headers: {
      isRetry: false,
    },
  });
};
/**
 *  保存
 */
export const save = (params) => {
  return request({
    url: `${baseProfixURI}`,
    method: "POST",
    data: params,
    headers: {
      isRetry: false,
    },
  });
};

export const update = (params) => {
  return request({
    url: `${baseProfixURI}`,
    method: "PUT",
    data: params,
    headers: {
      isRetry: false,
    },
  });
};

export const batchRemove = (ids) => {
  return request({
    url: `${baseProfixURI}/${ids}`,
    method: "DELETE",
    headers: {
      isRetry: false,
    },
  });
};
