import { createSlice } from "@reduxjs/toolkit";
// import { getLoginInfor } from '@/service/api/user';
// 初始化状态
const initialState = {
  userInfor: "",
  permission: [], //例如["sys:aa:edit","sys:aa:add","sys:aa:del"]
};

// 创建分片
const user = createSlice({
  name: "user",
  initialState,
  reducers: {
    setUserInfor(state, action) {
      state.userInfor = action.payload;
    },
    clearUser(state) {
      state.userInfor = {};
    },
    // 获取用户信息
    requestUserInfor(state, action) {
      //   getLoginInfor().then((res) => {
      //     const data = res.data;
      //     state.userInfor = data;
      //   });
    },
    // 权限字符串
    setPermission(state, action) {
      state.permission = action.payload;
    },
  },
});

// 导出
export default user.reducer;

export const { setUserInfor, clearUser, requestUserInfor, setPermission } =
  user.actions;
