import React from 'react'
import { <PERSON>, Grid, Divider, <PERSON><PERSON>, But<PERSON> } from "@mui/material";
import AppBar from "@mui/material/AppBar";
import Toolbar from "@mui/material/Toolbar";
import { styled } from "@mui/material/styles";
import AddIcon from "@mui/icons-material/Add";
import { getComponentId } from "../common/utils";
import { useState } from "react";
import {
  AntTab,
  AntTabs,
  FormLabel,
  PrettoSlider,
} from "./PropertiesComponent";
import CustomInput from "../components/CustomInput";
import CustomSelect from "../components/CustomSelect";
import CustomGroupSelect from "../components/CustomGroupSelect";
import { useEffect } from "react";
import {
  fontSizeList,
  fontList,
  formatList,
  animationList,
} from "../common/utils";
import ColorPick from "../components/ColorPick";
import { message } from "../common/i18n";
const TimeComponent = (props) => {
  const currentPageIndex = props.currentPageIndex;
  const currentIndex = props.currentComponentIndex;
  const pages = props.pages;
  const activeTempIndex = props.activeTempIndex;

  const [properties, setProperties] = useState({
    title: "editor_time", //标题
    name: "editor_time", //名称
    type: "ZKTecoTime", //组件类型
    left: 12,
    top: 15,
    width: 200,
    height: 50,
    zIndex: 50,
    hide: false, //是否隐藏
    anim: "pulse", //动画样式名称
    fontColor: "#262626", //颜色
    font: "MyriadPro-Light", //字体
    fontSize: 16, //字体大小
    isBold: false, //是否加粗
    isItaly: true, //是否斜体
    isUnderline: true, //下划线
    textAlign: "center", //字体位置（居中还是左对齐右对齐等）
    lineHeight: 2.5, //行高
    rotate: 0, //宣传
    isScroll: true, //是否滚动
    duration: 60, //默认时长
    scrollDirection: "left", //滚动方向
    speed: 60, //滚动速度
    format: "1",
    transparency: 1,
    componentId: "",
  });

  useEffect(() => {
    if (currentIndex !== "") {
      const curretnPage = pages[currentPageIndex];
      if (curretnPage.isTemplate) {
        let componentInfo =
          curretnPage.tempLayout[activeTempIndex]?.componentList[currentIndex];
        setProperties({
          ...componentInfo,
        });
      } else {
        let componentInfo = pages[currentPageIndex].componentList[currentIndex];
        setProperties({
          ...componentInfo,
        });
      }
    }
  }, [currentPageIndex, currentIndex, activeTempIndex, pages]);

  const setComponentInfo = (baseInfo) => {
    const newPages = JSON.parse(JSON.stringify(props.pages));
    let currentPage = newPages[currentPageIndex];
    if (currentPage.isTemplate) {
      let oldInfo =
        currentPage.tempLayout[activeTempIndex].componentList[currentIndex];
      let newInfo = {
        ...oldInfo,
        ...baseInfo,
      };
      currentPage.tempLayout[activeTempIndex].componentList[currentIndex] =
        newInfo;
    } else {
      let oldInfo = newPages[currentPageIndex].componentList[currentIndex];
      let newInfo = {
        ...oldInfo,
        ...baseInfo,
      };
      newPages[currentPageIndex].componentList[currentIndex] = newInfo;
    }
    if (props.setPages) {
      props.setPages(newPages);
    }
  };

  const changeProperties = (event) => {
    const name = event.target.name;
    let newInfo = {
      ...properties,
      [name]: event.target.value,
    };
    setComponentInfo(newInfo);
  };

  const handleRotationChange = (event, newValue) => {
    let newInfo = {
      ...properties,
      rotate: newValue,
    };
    setComponentInfo(newInfo);
  };

  const handleSliderChange = (event, newValue) => {
    let newInfo = {
      ...properties,
      transparency: newValue,
    };
    setComponentInfo(newInfo);
  };

  return (
    <Grid
      sx={{
        width: "100%",
        boxShadow: "0px 0px 6px #00000029",
        borderRadius: "10px",
        backgroundColor: "#ffffff",
        overflow: "hidden",
        minHeight: "200px",
        padding: "20px",
      }}
    >
      <Grid>
        <Grid
          sx={{
            display: "flex",
            alignItems: "center",
            mt: 1,
          }}
        >
          <CustomInput
            label={message("editor_layerName") + ":"}
            value={properties.name}
            onChange={changeProperties}
            name="name"
          ></CustomInput>
        </Grid>

        <Grid
          sx={{
            display: "flex",
            alignItems: "center",
            mt: 1,
          }}
        >
          <CustomSelect
            label={message("editor_fontSize") + ":"}
            name="fontSize"
            onChange={changeProperties}
            value={properties.fontSize}
            items={fontSizeList}
          ></CustomSelect>
        </Grid>

        <Grid
          sx={{
            display: "flex",
            alignItems: "center",
            mt: 1,
          }}
        >
          <FormLabel sx={{ mr: 2 }}> {message("editor_fontColor")}:</FormLabel>
          <ColorPick
            value={properties.fontColor}
            name="fontColor"
            onChange={changeProperties}
          ></ColorPick>
        </Grid>

        <Grid
          sx={{
            display: "flex",
            alignItems: "center",
            mt: 1,
          }}
        >
          <CustomSelect
            label={message("editor_format") + ":"}
            name="format"
            onChange={changeProperties}
            value={properties.format}
            items={formatList}
          ></CustomSelect>
        </Grid>

        <Grid
          sx={{
            display: "flex",
            alignItems: "center",
            mt: 1,
          }}
        >
          <FormLabel sx={{ mr: 2 }}>{message("editor_rotate")}:</FormLabel>
          <PrettoSlider
            onChange={handleRotationChange}
            size="small"
            min={0}
            max={360}
            step={1}
            color="secondary"
            value={properties.rotate}
            aria-label="Small"
            // valueLabelDisplay="off"
          ></PrettoSlider>
        </Grid>

        <Grid
          sx={{
            display: "flex",
            alignItems: "center",
            mt: 1,
          }}
        >
          <FormLabel sx={{ mr: 2 }}>{message("editor_diaphaneity")}:</FormLabel>
          <PrettoSlider
            onChange={handleSliderChange}
            size="small"
            min={0}
            max={1}
            step={0.1}
            color="secondary"
            value={properties.transparency}
            aria-label="Small"
            // valueLabelDisplay="off"
          ></PrettoSlider>
        </Grid>

        <CustomInput
          label={message("editor_abscissa") + ":"}
          value={properties.left}
          onChange={changeProperties}
          name="left"
        ></CustomInput>

        <CustomInput
          label={message("editor_ordinate") + ":"}
          value={properties.top}
          onChange={changeProperties}
          name="top"
        ></CustomInput>

        <CustomInput
          label={message("editor_width") + ":"}
          value={properties.width}
          onChange={changeProperties}
          name="width"
        ></CustomInput>

        <CustomInput
          label={message("editor_height") + ":"}
          value={properties.height}
          onChange={changeProperties}
          name="height"
        ></CustomInput>
      </Grid>
    </Grid>
  );
};

export default TimeComponent;
