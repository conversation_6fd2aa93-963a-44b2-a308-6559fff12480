/**
 * 参数处理
 * @param {*} params  参数
 */
export function tansParams(params) {
  let result = "";
  for (const propName of Object.keys(params)) {
    const value = params[propName];
    var part = encodeURIComponent(propName) + "=";
    if (value !== null && value !== "" && typeof value !== "undefined") {
      if (typeof value === "object") {
        for (const key of Object.keys(value)) {
          if (
            value[key] !== null &&
            value[key] !== "" &&
            typeof value[key] !== "undefined"
          ) {
            let params = propName + "[" + key + "]";
            var subPart = encodeURIComponent(params) + "=";
            result += subPart + encodeURIComponent(value[key]) + "&";
          }
        }
      } else {
        result += part + encodeURIComponent(value) + "&";
      }
    }
  }
  return result;
}
// 验证是否为blob格式
export async function blobValidate(data) {
  try {
    const text = await data.text();
    JSON.parse(text);
    return false;
  } catch (error) {
    return true;
  }
}

export function uuid() {
  var s = [];
  var hexDigits = "0123456789abcdef";
  for (var i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
  }
  s[14] = "4"; // bits 12-15 of the time_hi_and_version field to 0010
  s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
  s[8] = s[13] = s[18] = s[23] = "-";

  var uuid = s.join("");
  return uuid;
}

export function ping(ip) {
  return new Promise((resolve, reject) => {
    var img = new Image();
    var start = new Date().getTime();
    var flag = false;
    var isCloseWifi = true;
    var hasFinish = false;
    img.onload = function () {
      if (!hasFinish) {
        flag = true;
        hasFinish = true;
        // console.log('Ping ' + ip + ' success. ');
        // alert('成功' + ip);
        resolve(true);
      }
    };
    img.onerror = function () {
      if (!hasFinish) {
        if (!isCloseWifi) {
          flag = true;
          // console.log('Ping ' + ip + ' success. ');
          // alert('成功' + ip);
          resolve(true);
        } else {
          reject(false);
          // console.log('network is not working!');
        }
        hasFinish = true;
      }
    };
    setTimeout(function () {
      isCloseWifi = false;
      // console.log('network is working, start ping...');
      // alert('开始测试' + ip);
    }, 2);
    img.src = "http://" + ip + "/" + start;
    var timer = setTimeout(function () {
      if (!flag) {
        hasFinish = true;
        flag = false;
        reject(false);
      }
    }, 3000);
  });
}

export const base64ImgMain = (src, cb) => {
  let image = new Image();
  image.src = src + "&v=" + Math.random(); // 处理缓存
  image.crossOrigin = "*"; // 支持跨域图片
  image.onload = function () {
    let base64 = getBase64Image(image);
    console.log(base64);
    cb && cb(base64);
  };
};

export const getBase64Image = (img) => {
  let canvas = document.createElement("canvas");
  canvas.width = img.width;
  canvas.height = img.height;
  let ctx = canvas.getContext("2d");
  ctx.drawImage(img, 0, 0, img.width, img.height);
  let dataURL = canvas.toDataURL("image/png"); // 可选其他值 image/jpeg
  return dataURL;
};


export const isBase64 = (str) => {
  // 正则表达式匹配B4-64编码格式
  // eslint-disable-next-line no-useless-escape
  const regex = /^data:([a-z]+\/[a-z]+(;[a-z\-]+=[a-z\-]+)?)?(;base64)?,([a-z0-9\+\/]+=*)$/i;
  return regex.test(str);
};



/**
 * 对象深拷贝
 * @param {*} obj
 * @returns
 */
export function deepClone(obj) {
  return new Promise((resolve) => {
    const { port1, port2 } = new MessageChannel();
    port1.postMessage(obj);
    port2.onmessage = (msg) => {
      resolve(msg.data);
    };
  });
}

/**
 * 计算文件大小，返回MB
 * @param {*} size  file 文件中的size
 */
export function getFileSize(size) {
  var fileSizeInMB = size / (1024 * 1024);
  return fileSizeInMB.toFixed(2); // 返回两位小数的结果
}

export function isEmpty(val) {
  return val == null || val == undefined || val === "" || val == "undefined";
}
export function isNotEmpty(val) {
  return !isEmpty(val);
}

export function isAnyEmpty(...variables) {
  for (let i = 0; i < variables.length; i++) {
    const variable = variables[i];
    if (variable === null || variable === undefined || variable === "") {
      return true;
    }
  }
  return (
    variables.length === 0 ||
    variables.every((variable) => variable === undefined)
  );
}


export const dateFormate = (value)=> {
  // 定义UTC时间
  var utcTime = new Date(value);

  // 将UTC时间转换为本地时间
  var localTime = new Date(utcTime.getTime() - (utcTime.getTimezoneOffset() * 60000));

  // 格式化本地时间为 yyyy-MM-dd HH:mm:ss 格式
  var year = localTime.getFullYear();
  var month = String(localTime.getMonth() + 1).padStart(2, '0');
  var day = String(localTime.getDate()).padStart(2, '0');
  var hours = String(localTime.getHours()).padStart(2, '0');
  var minutes = String(localTime.getMinutes()).padStart(2, '0');
  var seconds = String(localTime.getSeconds()).padStart(2, '0');

  var formattedLocalTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  return formattedLocalTime;
}


export const deepObjectMerge = (target, ...objects) => {
    target = target || {}
    let len = objects.length
    for (let i = 0; i < len; i++) {
      let SecondOBJ = objects[i]
      for (var key in SecondOBJ) {
        target[key] =
          target[key] && target[key].toString() === '[object Object]'
            ? deepObjectMerge(target[key], SecondOBJ[key])
            : (target[key] = SecondOBJ[key])
      }
    }
    return target
}


export const dateFormatStr = (date, fmt) => {
  if (fmt === undefined || fmt === null) {
    fmt = "yyyy-MM-dd";
  }
  if (date === "" || date === undefined|| date === null) {
    return "";
  }
  if (typeof date === "number") {
    date = new Date(date);
  }

  if (typeof date === "string") {
    date = new Date(date);
  }

  var o = {
    "M+": date.getMonth() + 1,
    "d+": date.getDate(),
    "h+": date.getHours() % 12 === 0 ? 12 : date.getHours() % 12,
    "H+": date.getHours(),
    "m+": date.getMinutes(),
    "s+": date.getSeconds(),
    "q+": Math.floor((date.getMonth() + 3) / 3),
    S: date.getMilliseconds(),
  };
  var week = {
    0: "/u65e5",
    1: "/u4e00",
    2: "/u4e8c",
    3: "/u4e09",
    4: "/u56db",
    5: "/u4e94",
    6: "/u516d",
  };
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (date.getFullYear() + "").substr(4 - RegExp.$1.length)
    );
  }
  if (/(E+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (RegExp.$1.length > 1
        ? RegExp.$1.length > 2
          ? "/u661f/u671f"
          : "/u5468"
        : "") + week[this.getDay() + ""]
    );
  }
  for (var k in o) {
    let rg = "(" + k + ")";
    if (new RegExp(rg).test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length)
      );
    }
  }
  return fmt;
};


export const showBase64 = (base64Str) => {
  console.log(
    "%c+",
    `font-size: 1px;
padding: 100px 100px;
background-image: url(` +
      base64Str +
      `);
background-size: contain;
background-repeat: no-repeat;
color: transparent;`
  );

}
