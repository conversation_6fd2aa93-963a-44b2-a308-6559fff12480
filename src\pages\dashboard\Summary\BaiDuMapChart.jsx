

import React, { forwardRef, useRef, useEffect, useState } from "react";
import * as echarts from "echarts";
import GradientBox from '@/components/GradientBox'
import { getSummaryRetailFmap } from '@/service/api/summary'
import { useTranslation } from "react-i18next";
import 'echarts/extension/bmap/bmap';

const BaiDuMapChart = (props) => {
    const { center } = props
    const chartRef = useRef(null);
    const [myEcharts, setMyEcharts] = useState(null);
    const [chartData, setChartData] = useState([]);
    const [position, setPosition] = useState({ lat: 36.92200, lng: 108.55200 });
    const [zoom, setZoom] = useState(1)
    const [map, setMap] = useState(null);
    const handleResize = () => {
        if (myEcharts) {
            myEcharts.resize();
        }
    };

    const { t } = useTranslation();
    const initMapChart = () => {
        let language = 'en'
        let zkBioCloudMediaLang = sessionStorage.getItem('zkBioCloudMediaLang')
        if (zkBioCloudMediaLang && zkBioCloudMediaLang == 'zh') {
            language = 'zh'
        }
        let chart = echarts.init(chartRef.current);
        chart.on('finished', () => {
            var bmap = chart.getModel().getComponent('bmap').getBMap();
            // 设置最小缩放值
            bmap.setMinZoom(1);
            // 设置最大缩放值
            bmap.setMaxZoom(15);
            bmap.addEventListener('zoomend', function () {
                setZoom(bmap.getZoom())
            })
            setMap(bmap)
        })

        setMyEcharts(chart)
        // 设置初始大小
        chart.resize();
        // 监听窗口大小变化，自动调整图表大小
        window.addEventListener("resize", handleResize);
        const options = getOptions(chartData);
        chart.setOption(options);
        chart.on('click', 'series', function (e) {
            let position = e.data.position
            if (position) {
                let center = { lat: position[1], lng: position[0] };
                setPosition(center)
                setZoom(12)
            }
        });

    }

    const getOptions = (tempData) => {
        let option = {
            backgroundColor: 'transparent',
            animation: true,
            tooltip: {
                show: true,
                trigger: "item",
            },
            bmap: {
                center: [position.lng, position.lat],
                zoom: zoom,
                roam: true,
            },
            series: [
                {
                    type: 'lines',
                    z: 3,
                    coordinateSystem: 'bmap',
                    symbolSize: [10, 0],//只保留地图端标记
                    opacity: 1,
                    label: {
                        show: true,
                        position: 'end',
                        formatter: function (params) {//文本提示框
                            return '{value|' + params.data.totalOutlet + '} & {value2|' + params.data.totalScreen + '}'
                        },
                        backgroundColor: '#ffffff',
                        borderColor: '#7ac143',
                        borderWidth: 1,
                        borderRadius: 4,
                        height: 20,
                        lineHeight: 20,
                        padding: 2,
                        align: 'center',
                        rich: { //标题样式
                            value: { //内容样式
                                height: 20,
                                fontSize: '12px',
                                color: 'blue',
                                backgroundColor: '#fff'
                            },
                            value2: { //内容样式
                                height: 20,
                                fontSize: '12px',
                                color: '#7ac143',
                                backgroundColor: '#fff'
                            }
                        }
                    },
                    lineStyle: { //视觉引导线属性
                        type: 'solid',
                        opacity: 1,
                        color: '#595959', //引导线颜色
                        curveness: 0
                    },
                    tooltip: {
                        show: true,
                        trigger: "item",
                        formatter: function (params, ticket, callback) {
                            let { name, totalOutlet, totalScreen } = params.data
                            return `<div>
                               <div>${name}</div>
                               <div><label> ${t('outlet.total_outlets')} :<label>${totalOutlet}</div>
                               <div><label> ${t('summary.digital_signage')} :<label>${totalScreen}</div>
                            </div>`;
                        }
                    },
                    data: tempData.map((item, index) => {
                        let l = 9
                        let t = 9
                        let curveness = 0.2
                        if (zoom === 1) {
                            l = 6
                            t = 5
                        } else if (zoom === 2) {
                            l = 5
                            t = 4
                        } else if (zoom === 3) {
                            l = 6
                            t = 4
                        } else if (zoom === 4) {
                            l = 3
                            t = 3
                        } else if (zoom === 5) {
                            l = 2
                            t = 3
                        } else if (zoom === 6) {
                            l = 0.7
                            t = 0.8
                        } else if (zoom === 7) {
                            l = 0.3
                            t = 0.35
                        } else if (zoom === 8) {
                            l = 0.2
                            t = 0.24
                        } else if (zoom === 9) {
                            l = 0.1
                            t = 0.15
                        } else if (zoom === 10) {
                            l = 0.05
                            t = 0.07
                        } else if (zoom === 11) {
                            l = 0.02
                            t = 0.02
                        } else if (zoom === 12) {
                            l = 0.01
                            t = 0.01
                        } else if (zoom === 13) {
                            l = 0.005
                            t = 0.005
                        } else if (zoom === 14) {
                            l = 0.003
                            t = 0.004
                        } else if (zoom === 15) {
                            l = 0.00
                            t = 0.002
                        }
                        
                        let label = {
                            lineStyle: {
                                curveness: curveness
                            }
                        }
                        let indexTemp = index % 2 === 0 ? 1 : -1
                        return Object.assign({
                            coords: [item.position, [item.position[0] + (indexTemp * l), item.position[1] + t]],
                            ...label
                        }, item);
                    })
                }
            ]
        };
        return option
    }


    useEffect(() => {
        if (map && position) {
            // eslint-disable-next-line no-undef
            let point = new BMap.Point(position.lng, position.lat)
            map.setCenter(point);
        }
    }, [position])


    useEffect(() => {
        if (props.retailClientId && props.areaId) {
            getSummaryRetailFmap({
                retailClientId: props.retailClientId,
                areaId: props.areaId
            }).then((res) => {
                if (res.code === 0) {
                    let resData = res.data || []
                    let dataTemp = resData.map((item) => {
                        let location = item.location.split(',')
                        return {
                            name: item.areaName,
                            totalOutlet: item.totalOutlet,
                            totalScreen: item.totalScreen,
                            position: [Number(location[0]), Number(location[1])]
                        }
                    })
                    setChartData(dataTemp)
                } else {
                    setChartData([])
                }
            }).catch((e) => {
                setChartData([])
            })
        } else {
            setChartData([])
        }
    }, [props.retailClientId, props.areaId])



    useEffect(() => {
        if (myEcharts === null) {
            initMapChart()
        } else {
            const options = getOptions(chartData);
            myEcharts.setOption(options);
        }
    }, [chartData, zoom])


    useEffect(() => {
        if (center) {
            let location = center.split(',')
            if (location.length === 2) {
                let position = { lat: Number(location[1]), lng: Number(location[0]) };
                setPosition(position)
            }
        }
    }, [center])

    return <GradientBox style={{ width: '100%', height: '100%', flexGrow: 1, padding: '2px', margin: 0 }}>
        <div style={{
            width: '100%',
            height: '100%'
        }} ref={chartRef} ></div>

    </GradientBox>
}

export default BaiDuMapChart