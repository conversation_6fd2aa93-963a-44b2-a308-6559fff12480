.select_element {
    box-sizing: border-box;
    cursor: move;
}

.component_cla:hover {
    cursor: pointer;
    box-sizing: border-box;
}

/* .moveable-control-box {
    z-index: 0 !important;
} */

.moveable-control {
    width: 20px !important;
    height: 20px !important;
    margin-top: -10px !important;
    margin-left: -10px !important;
}

.select_Moveable {
    display: none !important;
}

.music-icon {
    width: 100%;
    height: 100%;
}

/***
 从右边到左边
 */

.left-to-right-wrap {
    position: relative;
    animation-name: from-letf-to-right;
    animation-duration: 3s;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
}

@keyframes from-letf-to-right {
    from {
        transform: translateX(-100%);
        left: 0;
    }
    to {
        left: 100%;
    }
}

/***
 从右边到左边
 */
.right-to-left-wrap {
    position: relative;
    animation-name: from-right-to-left;
    animation-duration: 3s;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
}

@keyframes from-right-to-left {
    from {
        left: 100%;
    }
    to {
        transform: translateX(-100%);
        left: 0;
    }
}

/***
 从上边到下边
 */
.top-to-bottom-wrap {
    position: relative;
    animation-name: from-top-to-bottom;
    animation-duration: 3s;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
}

@keyframes from-top-to-bottom {
    from {
        transform: translateY(-100%);
    }
    to {
        transform: translateY(100%);
    }
}

@keyframes from-bottom-to-top {
    from {
        transform: translateY(100%);
    }

    to {
        transform: translateY(-100%);
    }
}

/*
从下往上
*/
.bottom-to-top-wrap {
    position: relative;
    animation-name: from-bottom-to-top;
    animation-duration: 3s;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
}

@keyframes new-from-right-to-left {
    from {
        left: 0%;
    }
    to {
        transform: translateX(-100%);
        left: 0;
    }
}

.news-right-to-left-wrap {
    display: flex;
    width: fit-content;
    white-space: nowrap;
    position: relative;
    animation-name: new-from-right-to-left;
    animation-duration: 43s;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
}

.news-left-to-right-wrap {
    display: flex;
    width: fit-content;
    white-space: nowrap;
    position: relative;
    animation-name: from-letf-to-right;
    animation-duration: 43s;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
}

@keyframes new-from-top-to-bottom {
    from {
        transform: translateY(-100%);
    }
    to {
        transform: translateY(0%);
    }
}

.news-top-to-bottom-wrap {
    display: flex;
    flex-direction: column;
    height: fit-content;
    position: relative;
    transform: translateY(-100%);
    animation-name: new-from-top-to-bottom;
    animation-duration: 43s;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
}

@keyframes new-from-bottom-to-top {
    from {
        transform: translateY(0%);
    }

    to {
        transform: translateY(-100%);
    }
}

.news-bottom-to-top-wrap {
    display: flex;
    flex-direction: column;
    position: relative;
    animation-name: new-from-bottom-to-top;
    animation-duration: 30s;
    height: fit-content;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
}

.center_Moveable {
    --moveable-color: transparent !important;
}

.flow_Moveable {
  --moveable-color: transparent !important;
}

.moveable_flow_target{
  border-radius: 50%;
  cursor: pointer;
  height:45px;
  position: absolute;
  bottom: 100px;
  left: 20px;
  background-color: white;
  z-index: 1000;
  /* background-color: #7ac143; */
  /* color: white; */
  box-shadow: 0 2px 8px 0px rgba(0, 0, 0, .16);
}
.moveable_flow_target:hover{
  box-shadow: 0 2px 12px 0px rgba(0, 0, 0, .24);
}
.moveable_flow_target:active{
  box-shadow: 0 2px 12px 0px rgba(0, 0, 0, .24);
}

.moveable_flow_target .flow-button{
  width: 45px;
  height: 45px;
  text-align: center;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}
