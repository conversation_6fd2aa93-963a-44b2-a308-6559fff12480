.bt1 {
  margin-right: 8px;
  display: flex;
  align-items: center;
  background-color: white;
  // background-color: rgba(0, 0, 0, 0.6);
  // background-color: rgb(256, 256, 256);
  -webkit-box-shadow: 0 0 1px rgba(0, 0, 0, 0.3);
  -moz-box-shadow: 0 0 1px rgba(0, 0, 0, 0.3);
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.3);
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  display: inline-block;
  line-height: 40px;
  font-size: 16px;
  text-align: center;
  text-decoration: none;
  width: 60px;
  height: 60px;
  opacity: 0;
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  -o-transform: scale(0);
  transform: scale(0);
  position: relative;
  -webkit-transition: -webkit-transform 0.3s, opacity 0.3s;
  -moz-transition: -moz-transform 0.3s, opacity 0.3s;
  -o-transition: -o-transform 0.3s, opacity 0.3s;
  transition: transform 0.3s, opacity 0.3s;
  color: transparent;
}
.btn {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
.ovrly {
  background: rgba(196, 196, 196, 0.8);
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
  opacity: 0;
  -webkit-transition: all 0.3s;
  -moz-transition: all 0.3s;
  -o-transition: all 0.3s;
  transition: all 0.3s;
}

.box {
  overflow: hidden;
  position: relative;
  -webkit-box-shadow: 1px 1px 3px 0 rgba(0, 0, 0, 0.3);
  -moz-box-shadow: 1px 1px 3px 0 rgba(0, 0, 0, 0.3);
  box-shadow: 1px 1px 3px 0 rgba(0, 0, 0, 0.3);
}
.box:hover .ovrly {
  opacity: 1;
}
.box:hover .btn .bt1 {
  opacity: 1;
  transform: scale(1);
  color: rgba(50, 50, 50, 0.9);
}
.bt1:hover {
  background: white;
}
