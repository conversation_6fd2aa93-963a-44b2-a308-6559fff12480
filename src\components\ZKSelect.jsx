import React from "react";
/* eslint-disable no-unreachable */
import { forwardRef, useRef } from "react";
import PropTypes from "prop-types";
import { Select, MenuItem, OutlinedInput, IconButton } from "@mui/material";
import ClearIcon from "@mui/icons-material/Clear";
import { FixedSizeList } from "react-window";
const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const ZKSelect = forwardRef((props, ref) => {
  const {
    // 具体传递的对象 格式：[{label:"xx",value:"11"}]
    options = [],
    labelOptions = { label: "label", value: "value" },
    // 回调的value
    value = "",
    isClear = true,
    // 表单name
    name = "",
    size = "Normal",
    // 表单校验错误
    error = false,
    placeholder = "Placeholder",
    // change事件
    onChange = () => {},
    // 虚拟化列表
    virtualized = false,
    // 清除按钮需要双击生效
    onClear = () => {},
    // blur事件
    onBlur = () => {},
    // eslint-disable-next-line react/prop-types
    getAddress,
    // eslint-disable-next-line react/prop-types
    menuWidth = 250,
    disabled = false,
    sx,
  } = props;
  const MenuProps = {
    PaperProps: {
      style: {
        maxHeight: !virtualized
          ? ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP
          : undefined,
        width: menuWidth,
      },
    },
  };
  const selectRef = useRef(null);

  const Row = ({ index, style }) => {
    const item = options[index];
    return (
      <>
        <MenuItem
          style={style}
          key={item[labelOptions.value]}
          value={item[labelOptions.value]?.toString()}
          onClick={(e) => handleItemClick(e, index)}>
          {item[labelOptions.label]}
        </MenuItem>
      </>
    );
  };

  const handleItemClick = (e, index) => {
    const selectedOption = options[index];
    onChange(selectedOption);
    if (getAddress) {
      getAddress(selectedOption);
    }
  };

  // const defaultRow = () => {
  //   if (virtualized) {
  //     return undefined;
  //   } else {
  //     return <>
  //       {
  //         options.map((item) => (
  //           <MenuItem
  //             key={item[labelOptions.value]}
  //             value={item[labelOptions.value].toString()}
  //           >
  //             {item[labelOptions.label]}
  //           </MenuItem>
  //         ))
  //       }
  //     </>;

  //   }
  // };

  return (
    <Select
      disabled={disabled}
      ref={selectRef}
      onChange={(event, child) => {
        onChange(event, child);
        if (getAddress) {
          getAddress(event.target.value);
        }
      }}
      onBlur={onBlur}
      fullWidth
      error={error}
      displayEmpty
      name={name}
      size={size}
      value={value}
      input={
        <OutlinedInput
          endAdornment={
            isClear
              ? value && (
                  <IconButton
                    sx={{
                      marginRight: "20px",
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      onClear();
                    }}>
                    <ClearIcon
                      fontSize="small"
                      sx={{
                        color: "#757575",
                        cursor: "pointer",
                      }}
                    />
                  </IconButton>
                )
              : null
          }
        />
      }
      sx={{ color: value === "" ? "#757575" : "black", ...sx }}
      MenuProps={MenuProps}
      inputProps={{ "aria-label": "Without label" }}>
      <MenuItem disabled value="">
        <span>{placeholder}</span>
      </MenuItem>
      {virtualized && (
        <FixedSizeList height={200} itemCount={options.length} itemSize={40}>
          {Row}
        </FixedSizeList>
      )}

      {!virtualized &&
        options?.map((item) => (
          <MenuItem
            key={item[labelOptions.value]}
            value={item[labelOptions.value]?.toString()}>
            {item[labelOptions.label]}
          </MenuItem>
        ))}

      {/* {virtualized ? (
        <FixedSizeList height={200} itemCount={options.length} itemSize={40}>
          {Row}
        </FixedSizeList>
      ) : (
        <>
          {options.map((item) => (
            <MenuItem
              key={item[labelOptions.value]}
              value={item[labelOptions.value].toString()}
            >
              {item[labelOptions.label]}
            </MenuItem>    
          ))}
        </>
      )} */}
    </Select>
  );
});
ZKSelect.propTypes = {
  options: PropTypes.array,
  value: PropTypes.any,
  error: PropTypes.bool,
  name: PropTypes.string,
  placeholder: PropTypes.string,
  onChange: PropTypes.func,
  onBlur: PropTypes.func,
  onClear: PropTypes.func,
  labelOptions: PropTypes.object,
  disabled: PropTypes.bool,
  sx: PropTypes.object,
};
export default ZKSelect;
