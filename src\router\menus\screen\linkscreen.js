// 屏幕管理
const linkScreenRoute = [
  {
    path: "/screen/linkscreen",
    component: () => import("@/pages/screen/linkscreen/index"),
    meta: {
      title: "联屏列表",
      id: "516489808491577353",
      i18n: "media_link_screen_list",
    },
  },
  {
    path: "/screen/linkscreen/add",
    component: () =>
      import("@/pages/screen/linkscreen/components/addLinkScreenForm"),
    meta: {
      id: "00000000",
      title: "新增联屏",
      i18n: "media_link_screen_add",
    },
  },
  {
    path: "screen/linkscreen/edit",
    component: () =>
      import("@/pages/screen/linkscreen/components/editLinkScreenForm"),
    meta: {
      id: "00000000",
      title: "编辑联屏联屏",
      i18n: "media_link_screen_edit",
    },
  },
];

export default linkScreenRoute;
