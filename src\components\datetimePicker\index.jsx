import React, {
  useEffect,
  useState,
  forwardRef,
  useImperativeHandle,
} from "react";
import dayjs from "dayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DateTimePicker } from "@mui/x-date-pickers/DateTimePicker";
import { Stack, TextField, InputAdornment, IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import updateLocale from 'dayjs/plugin/updateLocale';
import { useTranslation } from "react-i18next";
import "dayjs/locale/zh-cn";
import "dayjs/locale/es";
import "dayjs/locale/en";
import i18n from 'i18next';

const BasicDateTimePicker = forwardRef(
  (
    {
      onChange,
      view = ["year", "day", "hours", "minutes"],
      format = "YYYY-MM-DD HH:mm",
      startKey='startTime',
      startPlaseholder=i18n.t("common.common_startTime"),
      endPlaceholder=i18n.t("common.common_endTime"),
      endKey='endTime'
    },
    ref
  ) => {
    const [startDateTime, setStartDateTime] = useState(undefined);
    const [endDateTime, setEndDateTime] = useState(undefined);
    const { i18n, t } = useTranslation();

    useEffect(() => {
      if (endDateTime != undefined && endDateTime != undefined) {
        let obj = {
          [startKey]: dayjs(startDateTime)?.format(format),
          [endKey]: dayjs(endDateTime)?.format(format),
        }
        onChange(obj);
      }
    }, [startDateTime, endDateTime]);

    const restInputValue = () => {
      setStartDateTime(undefined);
      setEndDateTime(undefined);
    };

    useEffect(() => {
      const currentLanguage = i18n.language;
      // console.log(currentLanguage)
      if (currentLanguage === "zh") {
        dayjs.extend(updateLocale);
        dayjs.updateLocale('zh-cn', {
          weekStart: 0,
        });
        dayjs.locale("zh-cn")
      } else if (currentLanguage === "en") {
        dayjs.locale("en");
      } else {
        dayjs.locale(currentLanguage);
      }
    }, []);

    useImperativeHandle(ref, () => ({
      restInputValue,
    }));
    const handleStartDateTimeChange = (value) => {
      value = dayjs(value);
      setStartDateTime(value);
      if (endDateTime && value?.isAfter(endDateTime)) {
        setEndDateTime(value);
      }
    };

    const handleEndDateTimeChange = (value) => {
      value = dayjs(value);
      if (startDateTime && value?.isBefore(startDateTime)) {
        return; // 结束时间小于开始时间，不更新状态
      }
      setEndDateTime(value);
    };

    const formatDate = (date) => {
      return date ? dayjs(date)?.format(format) : null;
    };

    return (
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <Stack direction="row" alignItems="center" spacing={1}>
          <DateTimePicker
            timeSteps={{
              minutes: 1,
            }}
            label={startPlaseholder}
            ampm={false}
            inputFormat={format}
            value={formatDate(startDateTime)}
            views={view}
            // minutesStep={0}
            onChange={handleStartDateTimeChange}
            maxDateTime={endDateTime}
          />
          <span>-</span>
          <DateTimePicker
            timeSteps={{
              minutes: 1,
            }}
            renderInput={(props) => <TextField {...props} />}
            label={endPlaceholder}
            ampm={false}
            value={formatDate(endDateTime)}
            inputFormat={format}
            views={view}
            onChange={handleEndDateTimeChange}
            minDateTime={startDateTime} // 设置最小可选日期为开始时间
          />
        </Stack>
      </LocalizationProvider>
    );
  }
);
export default BasicDateTimePicker;
