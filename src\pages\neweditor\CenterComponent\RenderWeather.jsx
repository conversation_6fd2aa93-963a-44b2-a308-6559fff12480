import React from 'react'
import {  Grid } from "@mui/material";
import RenderCommon from "./RenderCommon";
import Weather from "../icon/122.png";
import  { useEffect, useState } from "react";
import { getWeather } from "@/service/api/editorApi";

const RenderWeather = (props) => {
  const weaterConfig = props.info;
  const [latitude, setLatitude] = useState("");
  const [longitude, setLongitude] = useState("");

  const [info, setInfo] = useState({
    name: "city name",
    icon: "",
    text: "weather",
    temp_c: "temperature",
  });

  const successCallback = (position) => {
    const lat = position.coords.latitude; // 纬度
    const lon = position.coords.longitude; // 经度
    localStorage.setItem("currentLatitude", lat);
    localStorage.setItem("currentLongitude", lon);
    setLatitude(lat);
    setLongitude(lon);
  };

  const errorCallback = (e) => {
    setLatitude(24.2646);
    setLongitude(118.0404);
  };

  useEffect(() => {
    let lat = localStorage.getItem("currentLatitude");
    let lon = localStorage.getItem("currentLongitude");
    if (lat && lon) {
      setLatitude(lat);
      setLongitude(lon);
    } else {
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          successCallback,
          errorCallback
        );
      } else {
        setLatitude(24.2646);
        setLongitude(118.0404);
      }
    }

    // getIp().then((response) => {
    //
    // });
  }, []);

  useEffect(() => {
    if (latitude && longitude) {
      getWeather({
        lat: latitude,
        lng: longitude,
      })
        .then((res) => {
          if (res.code === 0) {
            let data = res.data;
            let location = data.location;
            let current = data.current;
            let condition = current?.condition;

            setInfo({
              name: location.name,
              icon: condition?.icon,
              text: condition?.text,
              temp_c: current.temp_c,
            });
          }
        })
        .catch(() => {});
    }
  }, [latitude, longitude]);

  return (
    <RenderCommon {...props}>
      <Grid
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100%",
          overflow:'hidden'
        }}
      >
        <Grid>
          <Grid
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              fontFamily:'MyriadPro-Light',
              lineHeight:'normal'
            }}
          >
            <Grid
              style={{
                lineHeight:"30px",
                fontWeight: "600",
                fontSize: "35px",
                fontFamily:'MyriadPro-Light',
                color: weaterConfig.fontColor,
              }}
            >
              {info.name}
            </Grid>
            <Grid sx={{
                display:'flex'
            }}>
              {info.icon ? (
                <img
                  style={{ width: "100%"}}
                  src={info.icon}
                />
              ) : (
                <img
                  crossOrigin="anonymous"
                  style={{ width: "100%"}}
                  src={Weather}
                />
              )}
            </Grid>
          </Grid>
          <Grid
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              fontSize: "35px",
              lineHeight:"35px",
              fontWeight: "600",
              fontFamily:'MyriadPro-Light',
              color: weaterConfig.fontColor,
            }}
          >
            <Grid>{info.text}</Grid>
            <Grid
              style={{
                marginLeft: "15px",
                position:'relative'
              }}
            >
              {info.temp_c}
              {info.temp_c !== "" && (
                <Grid
                  style={{
                    position: 'absolute',
                    right: '-33px',
                    top:'-18px'
                  }}
                >
                  。
                </Grid>
              )}
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </RenderCommon>
  );
};

export default RenderWeather;
