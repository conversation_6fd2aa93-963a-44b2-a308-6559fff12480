import { useEffect } from "react";
import React from 'react'
const Map = () => {
  useEffect(() => {
    const script = document.createElement("script");
    script.type = "text/javascript";
    script.src = `https://maps.googleapis.com/maps/api/js?key=AIzaSyA9MaTVJlWIWpINjcgyJl5eS6JDhe60238&libraries=places,drawing`;
    script.async = true;
    script.defer = true;
    script.addEventListener("load", () => {
      const map = new window.google.maps.Map(
        document.getElementById("google-map"),
        {
          center: { lat: 39.908712, lng: 116.397727 },
          zoom: 13,
        }
      );
    });
    document.getElementsByTagName("head")[0].appendChild(script);

    return () => {
      // 组件卸载时清除脚本
      document.getElementsByTagName("head")[0].removeChild(script);
    };
  }, []);

  return <div id="google-map"></div>;
};

export default Map;
