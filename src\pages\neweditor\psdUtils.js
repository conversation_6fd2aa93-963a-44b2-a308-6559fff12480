export const getLayerType = (item) => {
  let layerType = "image" // 默认类型为图片
  if (item.get("typeTool")) layerType = "text" // 文字
  else if (item.get("vectorOrigination")) {
    if (item.get("vectorMask")) layerType = "path" // 优先判断为路径
    const vectorOrigination = item.get("vectorOrigination")
    const { keyOriginType, keyOriginShapeBBox } = vectorOrigination.data.keyDescriptorList[0]
    if (keyOriginType == 4) layerType = "line" // 线条
    else if (keyOriginType == 5) {
      const { Btom, Left, Rght } = keyOriginShapeBBox
      const Top = keyOriginShapeBBox["Top "]
      if (Btom.value - Top.value === Rght.value - Left.value) {
        layerType = "circle" // 圆形
      } else {
        layerType = "ellipse" // 椭圆
      }
    }
    if ([1, 2].includes(keyOriginType)) {
      layerType = "rect" // 矩形
    }
  }
  return layerType
}
