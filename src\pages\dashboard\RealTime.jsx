import Visitors from "./RealTime/Visitors";
import * as Yup from "yup";
import { useFormik } from "formik";
import BasicDateRangePicker from "@/components/datetimePicker";
import { useTranslation } from "react-i18next";
import React, { forwardRef, useState, useRef, useEffect } from "react";
import LoadingButton from "@mui/lab/LoadingButton";
import {
  Stack,
  Grid,
  Button,
  InputLabel,
  FormHelperText,
  OutlinedInput,
  Box,
  Paper,
  Typography,
} from "@mui/material";
import { getPrincipaList } from "@/service/api/L3Sevice.js";

import { getOutletList } from "@/service/api/L3Sevice.js";
import {
  getTotals,
  getMaterials,
  getAgeGroup,
  getGenderGroup,
  getGenderGroupBar,
  getAgeGroupLine,
  getStayTime
} from "@/service/api/realApi";
import ZKAutocomplete from "@/components/ZKAutocomplete";
import HorizontalCardList from "./RealTime/HorizontalCardList";
import <PERSON>hnut<PERSON><PERSON> from "./RealTime/DoughnutChart ";
const ChartBox = (props) => {
  const { xs = 12, sm = 12, md = 6 } = props;

  return (
    <Grid item xs={xs} sm={sm} md={md}>
      <Paper
        sx={{
          p: 2,
          height: "300px", // 设置固定高度，可以根据需要调整
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          alignItems: "center",
          boxShadow: "none",
          position: "relative",
          backgroundColor: "#ffffff", // 设置背景色，可以根据需要调整
        }}
      >
        {props.children}
      </Paper>
    </Grid>
  );
};

const RealTime = (props) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [merchantOptions, setMerchantOptions] = useState([]); // 商户
  const [storeOptions, setStoreOptions] = useState([]); // 门店
  const [VisitorCount, setVisitorCount] = useState(""); //人数
  const [materials, setMaterials] = useState([]); //素材
  const [currentMaterialId, setCurrentMaterialId] = useState(""); //素材

  const [AgeGroup, setAgeGroup] = useState([]);
  const [GenderGroup, setGenderGroup] = useState([]);
  const [GenderGroupBar, setGenderGroupBar] = useState([]);
  const [AgeGroupLine, setAgeGroupLine] = useState([]);
  const [DurationTrend, setDurationTrend] = useState([]);


  const [searchFormParams, setSearchFormParams] = useState({
    merchantId: "",
    outletId: "",
    startTime: "",
    endTime: "",
    deviceName: "",
  });

  const loadTotal = (params) => {
    getTotals(params).then((res) => {
      if (res.code === 0) {
        setVisitorCount(res.data);
      } else {
        setVisitorCount("");
      }
    });
  };


  const loadMaterials = (params) => {
    getMaterials(params).then((res) => {
      if (res.code === 0) {
        setMaterials(res.data);
        if (res.data && res.data.length > 0) {
          setCurrentMaterialId(res.data[0].materialId);
        }
      } else {
        setMaterials([]);
      }
    });
  };

  useEffect(() => {
    if (
      searchFormParams.merchantId &&
      searchFormParams.startTime &&
      searchFormParams.endTime
    ) {
      loadTotal(searchFormParams);
      loadMaterials(searchFormParams);
    }
  }, [searchFormParams]);

  useEffect(() => {
    if (
      searchFormParams.merchantId &&
      searchFormParams.startTime &&
      searchFormParams.endTime &&
      currentMaterialId
    ) {
      let params = {
        ...searchFormParams,
        materialId: currentMaterialId,
      };

      getAgeGroup(params).then((res) => {
        if (res.code === 0) {
          setAgeGroup(res.data || {});
        } else {
          setAgeGroup([]);
        }
      });
      getGenderGroup(params).then((res) => {
        if (res.code === 0) {
          setGenderGroup(res.data || []);
        } else {
          setGenderGroup([]);
        }
      });
      getGenderGroupBar(params).then((res) => {
        if (res.code === 0) {
          setGenderGroupBar(res.data || []);
        } else {
          setGenderGroupBar([]);
        }
      });
      getAgeGroupLine(params).then((res) => {
        if (res.code === 0) {
          setAgeGroupLine(res.data || []);
        } else {
          setAgeGroupLine([]);
        }
      });

      getStayTime(params).then((res) => {
        if (res.code === 0) {
          debugger
          setDurationTrend(res.data);
        } else {
          setDurationTrend("");
        }
      });



    }
  }, [searchFormParams, currentMaterialId]);

  // 请求商户数据
  const handleRequestMerchant = () => {
    getPrincipaList().then((res) => {
      setMerchantOptions(res.data);
    });
  };
  useEffect(() => {
    handleRequestMerchant();
  }, []);

  const getOption = (merchantId) => {
    getOutletList(merchantId).then((res) => {
      setStoreOptions(res.data);
    });
  };

  //  表单
  const searchFormik = useFormik({
    initialValues: {
      merchantId: "",
      outletId: "",
      startTime: "",
      endTime: "",
      deviceName: "",
    },
    onSubmit: (values, { setErrors, setStatus, setSubmitting }) => {
      debugger;
      setSearchFormParams({
        ...searchFormParams,
        ...values,
      });
    },
    validationSchema: Yup.object().shape({
      merchantId: Yup.string().required(t("ips.ips_select_merchant")),
      startTime: Yup.string().required(t("RealTime.input_select_time_range")),
      endTime: Yup.string().required(t("RealTime.input_select_time_range")),
    }),
  });

  const handleChangeStore = (event, newValue) => {
    searchFormik.setFieldValue("outletId", newValue.value);
  };

  const selectItem = (materialId) => {
    setCurrentMaterialId(materialId);
  };

  return (
    <Grid
      sx={{
        display: "flex",
        flexDirection: "column",
        padding: "0px 10px",
        minHeight: "80vh",
      }}
    >
      <Grid
        sx={{
          display: "flex",
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "center",
          minHeight: "97px",
        }}
      >
        <Grid
          sx={{
            width: "100%",
            minHeight: "97px",
            marginRight: "50px",
            backgroundColor: "#ffffff",
            padding: "10px",
            boxSizing: "border-box",
          }}
        >
          <form noValidate onSubmit={searchFormik.handleSubmit}>
            <Grid
              sx={{
                alignItems: "flex-start",
              }}
              container
              spacing={2}
            >
              <Grid item xs={12} sm={12} md={4.5}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="store-id">
                    {t("RealTime.Period")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <BasicDateRangePicker
                    rangeDate={6}
                    view={["year", "day"]}
                    format="YYYY-MM-DD"
                    startPlaseholder=""
                    endPlaceholder=""
                    onChange={(value) => {
                      if (value?.startTime && value?.endTime) {
                        if (
                          value.startTime === "Invalid Date" ||
                          value.endTime === "Invalid Date"
                        ) {
                          searchFormik.setValues({
                            startTime: "",
                            endTime: "",
                          });
                          return;
                        }
                        searchFormik.setFieldValue(
                          "startTime",
                          value?.startTime
                        );
                        searchFormik.setFieldValue("endTime", value?.endTime);
                      } else {
                        searchFormik.setValues({
                          startTime: "",
                          endTime: "",
                        });
                      }
                    }}
                  />

                  <Grid
                    sx={{
                      display: "flex",
                    }}
                  >
                    {searchFormik.touched.startTime &&
                    searchFormik.errors.startTime ? (
                      <FormHelperText error id="sn-error">
                        {searchFormik.errors.startTime}
                      </FormHelperText>
                    ) : (
                      searchFormik.touched.endTime &&
                      searchFormik.errors.endTime && (
                        <FormHelperText error id="sn-error">
                          {searchFormik.errors.endTime}
                        </FormHelperText>
                      )
                    )}
                  </Grid>
                </Stack>
              </Grid>
              <Grid item xs={12} sm={6} md={1.8}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="store-name">
                    {t("ips.ips_merchant")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>

                  <ZKAutocomplete
                    name="merchantId"
                    onClear={() => {
                      setStoreOptions([]);
                      searchFormik.setFieldValue("outletId", "");
                      searchFormik.setFieldValue("merchantId", "");
                    }}
                    sx={{
                      height: "42px",
                    }}
                    value={searchFormik.values.merchantId}
                    id="store-id"
                    onChange={(e, newValue) => {
                      getOption(newValue?.value);
                      searchFormik.setFieldValue("merchantId", newValue?.value);
                      searchFormik.setFieldValue("outletId", "");
                      searchFormik.handleChange(e);
                    }}
                    onBlur={searchFormik.handleBlur}
                    data={merchantOptions}
                    error={Boolean(
                      searchFormik.touched.merchantId &&
                        searchFormik.errors.merchantId
                    )}
                    placeholder={t("ips.ips_select_merchant")}
                  />

                  {searchFormik.touched.merchantId &&
                    searchFormik.errors.merchantId && (
                      <FormHelperText error id="merchantId-error">
                        {searchFormik.errors.merchantId}
                      </FormHelperText>
                    )}
                </Stack>
              </Grid>

              <Grid item xs={12} sm={6} md={1.8}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="store-id">
                    {t("outlet.outlet")}
                    {/* {t("ips.ips_store_outlet_name")}
                <i style={{ color: "red" }}>*</i> */}
                  </InputLabel>
                  <ZKAutocomplete
                    name="outletId"
                    onClear={() => {
                      searchFormik.setFieldValue("outletId", "");
                    }}
                    sx={{
                      height: "42px",
                    }}
                    value={searchFormik.values.outletId}
                    id="store-id"
                    onChange={handleChangeStore}
                    onBlur={searchFormik.handleBlur}
                    data={storeOptions}
                    placeholder={t("ips.ips_select_a_outlet")}
                    error={Boolean(
                      searchFormik.touched.outletId &&
                        searchFormik.errors.outletId
                    )}
                  />
                  {searchFormik.touched.outletId &&
                    searchFormik.errors.outletId && (
                      <FormHelperText error id="storeId-error">
                        {searchFormik.errors.outletId}
                      </FormHelperText>
                    )}
                </Stack>
              </Grid>

              <Grid item xs={12} sm={6} md={1.8}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="screen-sn">
                    {t("ips.ips_screen_name")}
                  </InputLabel>
                  <OutlinedInput
                    value={searchFormik.values.deviceName}
                    id="deviceName"
                    type="text"
                    fullWidth
                    name="deviceName"
                    error={Boolean(
                      searchFormik.touched.deviceName &&
                        searchFormik.errors.deviceName
                    )}
                    onBlur={searchFormik.handleBlur}
                    onChange={searchFormik.handleChange}
                    placeholder={t("RealTime.input_screen_name")}
                  />
                  {searchFormik.touched.deviceName &&
                    searchFormik.errors.deviceName && (
                      <FormHelperText error id="sn-error">
                        {searchFormik.errors.deviceName}
                      </FormHelperText>
                    )}
                </Stack>
              </Grid>

              <Grid item xs={12} sm={6} md={1.8}>
                <LoadingButton
                  disableElevation
                  type="submit"
                  sx={{
                    marginTop: "30px",
                    background:
                      "transparent linear-gradient(270deg, #1487CB 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
                    borderRadius: "5px",
                    padding: "0px 20px",
                    height: "40px",
                  }}
                  variant="contained"
                  color="primary"
                  disabled={loading} // 在加载状态下禁用按钮
                >
                  {t("RealTime.applyFillter")}
                </LoadingButton>
              </Grid>
            </Grid>
          </form>
        </Grid>
        <Visitors VisitorCount={VisitorCount}></Visitors>
      </Grid>

      <Grid
        sx={{
          height: "110px",
          backgroundColor: "#ffffff",
          marginTop: "20px",
        }}
      >
        <HorizontalCardList
          selectItem={selectItem}
          currentMaterialId={currentMaterialId}
          materials={materials}
        ></HorizontalCardList>
      </Grid>

      <Grid container spacing={2} sx={{ mt: 5 }}>
        <ChartBox>
          <DoughnutChart type="ageRange" data={AgeGroup}></DoughnutChart>
        </ChartBox>
        <ChartBox>
          <DoughnutChart type="genderRanger" data={GenderGroup}></DoughnutChart>
        </ChartBox>
        <ChartBox>
          <DoughnutChart
            data={AgeGroupLine}
            type="AgeGroupLineChart"
          ></DoughnutChart>
        </ChartBox>
        <ChartBox>
          <DoughnutChart
            data={GenderGroupBar}
            type="GenderGroupBar"
          ></DoughnutChart>
        </ChartBox>

         <ChartBox xs={12} sm={12} md={ 12}>
          <DoughnutChart
            data={DurationTrend}
            type="DurationTrend"
          ></DoughnutChart>
        </ChartBox>
      </Grid>
    </Grid>
  );
};

export default RealTime;
