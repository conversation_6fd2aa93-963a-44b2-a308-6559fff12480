import { useEffect, useRef, useState } from "react";
import * as echarts from "echarts";
import { useTranslation } from "react-i18next";
const AgeRange = (props) => {
  const chartRef = useRef(null);
  const myChartRef = useRef(null);

  const { t } = useTranslation();
  //   const [myEcharts, setMyEcharts] = useState(null);
  const initChart = () => {
    myChartRef.value = echarts.init(chartRef.current, null, {
      renderer: "svg",
    });
    // setMyEcharts(chart);
    // 设置初始大小
    myChartRef.value.resize();
    // 监听窗口大小变化，自动调整图表大小
    window.addEventListener("resize", handleResize);
    const options = getOptions(props.trendValue);
    myChartRef.value.setOption(options);
  };

  const handleResize = () => {
    if (myChartRef.value) {
      myChartRef.value.resize();
    }
  };

  useEffect(() => {
    // 在组件挂载时进行初始化
    initChart();
    return () => {
      window.removeEventListener("resize", handleResize);
      if (myChartRef.value) {
        myChartRef.value.dispose();
        myChartRef.value = null;
      }
    };
  }, []);

  useEffect(() => {
    if (myChartRef.value === null) {
      initChart();
    } else {
      const options = getOptions(props.trendValue);
      myChartRef.value.setOption(options);
    }
  }, [props.trendValue]);

  const getOptions = (data) => {
    let xAxis = [];
    let dataObj = {};
    data?.forEach((element) => {
      xAxis.push(element.filedName);
      element?.trendAges?.forEach((item) => {
        if (!dataObj[item.fieldName]) {
          dataObj[item.fieldName] = [];
        }
        dataObj[item.fieldName].push(item);
      });
    });

    var colors = {
      one: "#5470c6",
      two: "#91cc75",
      three: "#fac858",
      four: "#ee6666",
      plus: "#73c0de",
    };

    let series = Object.keys(dataObj).map((item) => {
      let arry = dataObj[item];
      return {
        name: t("realTime." + arry[0]?.fieldName),
        type: "line",
        symbol: "none",
        data: arry?.map((valueItem) => {
          return valueItem.filedValue;
        }),
        itemStyle: {
          normal: {
            color: colors[arry[0]?.fieldName], // 点的颜色
            lineStyle: {
              color: colors[arry[0]?.fieldName], // 线的颜色
            },
          },
        },
      };
    });

    let legend = series?.map((item) => {
      return item?.name;
    });

    return {
      title: {
        text: t("realTime.entryTrendTitle"),
        textStyle: {
          color: "#86909c",
          fontStyle: "normal",
          fontWeight: "normal",
          fontFamily: "Arial",
          fontSize: 14,
        },
      },
      tooltip: {
        trigger: "axis",
      },
      legend: {
        data: legend,
        top: 25,
        left: "center",
        icon: "rect",
        itemWidth: 24,
        align: "right",
        itemHeight: 3,
        itemGap: 30,
        right: "4%",
        textStyle: {
          fontSize: 12,
          color: "#333333",
        },
      },
      grid: {
        top: "25%",
        left: "3%",
        right: "4%",
        bottom: "5%",
        containLabel: true,
      },
      xAxis: {
        type: "category",
        data: xAxis,
        // 自定义轴线样式
        axisLine: {
          // 延伸轴线到最大最小值之外
          extend: "both",
        },
        // 扩展X轴数据范围
        min: "dataMin",
        max: "dataMax",
        // 设置边界空白
        boundaryGap: false, // 关闭边界空白
      },
      yAxis: {
        type: "value",
      },
      series: series,
    };
  };
  return (
    <div
      style={{
        width: "100%",
        boxSizing: "border-box",
        position: "relative",
        height: "100%",
        padding: "10px",
        border: "1px solid #e5e6eb",
        borderRadius: "20px",
        backgroundColor: "#ffffff",
      }}
    >
      <div
        style={{
          width: "100%",
          height: "100%",
        }}
        ref={chartRef}
      ></div>
    </div>
  );
};

export default AgeRange;
