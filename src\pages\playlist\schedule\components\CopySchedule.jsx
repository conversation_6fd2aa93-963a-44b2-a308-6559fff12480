/* eslint-disable no-unsafe-optional-chaining */
import React, { useEffect, useRef } from "react";
import {
  BootstrapDialog,
  BootstrapContent,
  BootstrapActions,
  BootstrapDialogTitle,
} from "@/components/dialog";
import {
  Grid,
  OutlinedInput,
  Stack,
  Box,
  Chip,
  FormHelperText,
  Button,
  CircularProgress,
  Typography,
  InputLabel,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useForm, Controller } from "react-hook-form";
import LoadingButton from "@mui/lab/LoadingButton";
import ScreenSelect from "./ScreenSelect";
import DictTag from "@/components/DictTag";
import { scheduleType, weeks } from "@/dict/commonDict";
import { getScheduleInfo, copySchedule } from "@/service/api/schedule";
import * as yup from "yup";
import { toast } from "react-toastify";
import { yupResolver } from "@hookform/resolvers/yup";
import LinkScreenSelect from "./LinkScreenSelect";
import { getPlayListInfo } from "@/service/api/playList";
export default function CopySchedule({
  open = false,
  id = undefined,
  onClose = () => {},
  refresh = () => {},
}) {
  const { t } = useTranslation();
  const [loading, setLoading] = React.useState(false);
  const [data, setData] = React.useState({});
  const [btnLoading, setBtnLoading] = React.useState(false);
  const statMaterialSizeRef = useRef(null);
  const schema = yup.object().shape({
    name: yup.string().required(t("common.common_input_scheduling_name")),
    screens: yup.array().min(1, t("common.common_deviceNotSelect")),
  });
  const {
    control,
    reset,
    resetField,
    clearErrors,
    handleSubmit,
    setError,
    formState: { errors },
    setValue,
  } = useForm({
    resolver: yupResolver(schema), // 使用 Yup 进行表单验证
    mode: "onBlur",
  });
  const handleClose = () => {
    // setDetailOpen(false);
    reset();
    onClose();
  };
  const onSubmit = async (formValues) => {
    if (formValues?.screens.length <= 0) {
      return;
    }
    //判断LCD-L101设备
    const isExistLCD = formValues.screens.some(
      (item) => item.original.screenModel === "LCD-L101"
    );
    if (isExistLCD) {
      const selectPlayList = data?.inventoryList?.filter(
        (item) =>
          item.playListId !== "" &&
          item.playListName !== "" &&
          item.startTime !== "" &&
          item.stopTime !== ""
      );
      if (selectPlayList.length == 1) {
        await getPlayListInfo(selectPlayList[0].playListId).then((res) => {
          const totalSize = res.data.playListMaterialList.reduce(
            (total, item) => total + (item.size || 0),
            0
          );
          statMaterialSizeRef.current = totalSize;
        });
        if (
          statMaterialSizeRef.current !== null &&
          statMaterialSizeRef.current > 60
        ) {
          toast.error(t("ips.ips_schedule_LCD_L101_valid"));
          return;
        }
      } else {
        toast.error(t("ips.ips_schedule_LCD_L101_valid"));
        return;
      }
    }
    setBtnLoading(true);
    const buildDataParams = {
      id: data?.id,
      name: formValues?.name,
      devices: formValues?.screens.map((item) => item?.original.id),
      type: data?.type,
    };
    copySchedule(buildDataParams)
      .then((res) => {
        toast.success(res?.message);
        setBtnLoading(false);
        handleClose();
        refresh();
      })
      .catch((err) => {
        setBtnLoading(false);
      });
  };
  // 自定义排序函数，按照日期的顺序进行排序
  const customSort = (a, b) => {
    // 将日期字符串转换为数字进行比较
    var dayA = parseInt(a);
    var dayB = parseInt(b);
    // 如果日期相等，则按照字符串的字典顺序进行排序
    if (dayA === dayB) {
      return a.localeCompare(b);
    }
    return dayA - dayB;
  };
  const getSchedule = (id) => {
    setLoading(true);
    getScheduleInfo(id).then((res) => {
      setLoading(false);
      const name = res?.data?.name + t("common.common_copy_text");
      setValue("name", name);
      setValue("id", data?.id);
      const sortPlayDay = [...res?.data?.playDays];
      sortPlayDay?.sort(customSort);
      setData({
        ...res?.data,
        playDays: sortPlayDay,
      });
    });
  };
  useEffect(() => {
    if (id && open) {
      getSchedule(id);
    }
  }, [id, open]);

  return (
    <BootstrapDialog
      fullWidth
      maxWidth="lg"
      onClose={handleClose}
      aria-labelledby="customized-dialog-title"
      open={open}>
      <BootstrapDialogTitle onClose={handleClose}>
        <Typography variant="h4" component="p">
          {t("common.common_copy_schedule_playList")}
        </Typography>
      </BootstrapDialogTitle>
      <BootstrapContent>
        {!loading && (
          <Box sx={{ pl: 2, pr: 2, minHeight: 300 }}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="name">
                    {t("ips.ips_scheduling")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <Controller
                    id="name"
                    name="name"
                    control={control}
                    render={({ field }) => (
                      <OutlinedInput
                        {...field}
                        error={!!errors.name}
                        type="text"
                        fullWidth
                        onBlur={field.onBlur}
                        placeholder={t("common.common_input_scheduling_name")}
                      />
                    )}
                  />
                  {!!errors.name && (
                    <FormHelperText error id="locationType-error">
                      {errors.name?.message}
                    </FormHelperText>
                  )}
                </Stack>
              </Grid>
              <Grid item xs={2}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="store-name">
                    {t("ips.ips_schedule_type")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <div>
                    <DictTag
                      // defaultValue="数字标牌播放计划"
                      dicts={scheduleType}
                      value={data.type}
                      fieldName={{ value: "value", title: "label" }}
                    />
                  </div>
                </Stack>
              </Grid>
              <Grid item xs={6}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="store-name">
                    {t("common.common_start_date_end_date")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <div>
                    {data?.startDate} - {data?.stopDate}
                  </div>
                </Stack>
              </Grid>
              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="name">
                    {t("ips.ips_schedule_list")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  {data?.type === "0" && (
                    <Box
                      display="flex"
                      flexWrap="wrap"
                      justifyContent="flex-start"
                      direction={"row"}
                      spacing={1}>
                      {data?.inventoryList?.map((item, index) => {
                        const label =
                          item?.startTime +
                          "-" +
                          item?.stopTime +
                          "  " +
                          item?.playListName;
                        return (
                          <Chip
                            key={index}
                            sx={{ mr: 1, mb: 1 }}
                            label={label}
                            variant="outlined"
                          />
                        );
                      })}
                    </Box>
                  )}
                  {data?.type === "1" && (
                    <Box
                      display="flex"
                      flexWrap="wrap"
                      justifyContent="flex-start"
                      direction={"row"}
                      spacing={1}>
                      {data?.screenGroupPlayList?.map((item, index) => {
                        console.log(item);
                        return item?.areaScreenPlayList.map((playlist) => {
                          const label =
                            item?.startTime +
                            "-" +
                            item?.stopTime +
                            "  " +
                            playlist?.playListName;
                          return (
                            <Chip
                              key={index}
                              sx={{ mr: 1, mb: 1 }}
                              label={label}
                              variant="outlined"
                            />
                          );
                        });
                      })}
                    </Box>
                  )}
                </Stack>
              </Grid>
              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="name">
                    {t("ips.ips_month")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <Box
                    display="flex"
                    flexWrap="wrap"
                    justifyContent="flex-start"
                    direction={"row"}
                    spacing={1}>
                    {data?.playMonths?.map((item, index) => {
                      return (
                        <Chip
                          key={index}
                          sx={{ mr: 1, mb: 1 }}
                          label={item}
                          variant="outlined"
                        />
                      );
                    })}
                  </Box>
                </Stack>
              </Grid>
              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="name">
                    {t("ips.ips_scheduling_playWeeks")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <Box
                    display="flex"
                    flexWrap="wrap"
                    justifyContent="flex-start">
                    {data?.playWeeksNum?.map((item, index) => {
                      const foundItem = weeks.find(
                        (week) => week.value === item
                      );
                      const label = foundItem ? foundItem.label : "unknown";
                      return (
                        <Chip
                          key={index}
                          label={label}
                          sx={{ mr: 1, mb: 1 }}
                          variant="outlined"
                        />
                      );
                    })}
                    {!data?.playWeeksNum ||
                      (data?.playWeeksNum.length == 0 && (
                        <Chip label="Empty" variant="outlined"></Chip>
                      ))}
                  </Box>
                </Stack>
              </Grid>
              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="name">
                    {t("ips.ips_scheduling_playDate")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <Box
                    display="flex"
                    flexWrap="wrap"
                    justifyContent="flex-start">
                    {data?.playDays?.map((item, index) => {
                      return (
                        <Chip
                          key={index}
                          sx={{ mr: 1, mb: 1 }}
                          label={item}
                          variant="outlined"
                        />
                      );
                    })}
                  </Box>
                </Stack>
              </Grid>
              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="name">
                    {t("common.common_please_screen_store")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  {!!errors.screens && (
                    <FormHelperText error id="locationType-error">
                      {errors.screens?.message}
                    </FormHelperText>
                  )}
                  <div
                    style={{
                      border: "1px solid #d9d9d9",
                      borderRadius: "5px",
                      padding: "10px 10px",
                    }}>
                    {data?.type === "0" && (
                      <ScreenSelect
                        setTableObject={(values) => {
                          resetField("screens");
                          if (values.length == 0) {
                            setError("screens", {
                              type: "manual",
                              message: t("common.common_deviceNotSelect"),
                            });
                            return;
                          } else {
                            clearErrors("screens");
                            setValue("screens", values);
                            return;
                          }
                        }}
                        pageSize={5}
                      />
                    )}
                    {data?.type === "1" && (
                      <LinkScreenSelect
                        line={data?.line}
                        column={data?.columns}
                        setTableObject={(values) => {
                          resetField("screens");
                          if (values.length == 0) {
                            setError("screens", {
                              type: "manual",
                              message: t("common.common_deviceNotSelect"),
                            });
                          } else {
                            let isSuccess = true; // 初始化为 true
                            values?.forEach((element) => {
                              if (
                                !data?.line ||
                                !data?.columns ||
                                !element.original.line ||
                                !element.original.columns ||
                                data?.line !== element.original.line ||
                                data?.columns !== element.original.columns
                              ) {
                                isSuccess = false; // 如果任何一个条件不满足，则设置为 false
                              }
                            });
                            if (isSuccess) {
                              clearErrors("screens");
                              setValue("screens", values);
                              return;
                            } else {
                              setError("screens", {
                                type: "manual",
                                message: t("common.common_select_rule_screen"),
                              });
                              return;
                            }
                          }
                        }}
                        pageSize={5}
                      />
                    )}
                  </div>
                </Stack>
              </Grid>
            </Grid>
          </Box>
        )}
        {loading && (
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              width: "100%",
              minHeight: 300,
            }}>
            <Stack spacing={2} alignItems={"center"}>
              <CircularProgress />
              <Typography>{t("common.common_loading")}</Typography>
            </Stack>
          </Box>
        )}
      </BootstrapContent>
      <BootstrapActions>
        <Button color="info" variant="outlined" onClick={handleClose}>
          {t("common.common_edit_cancel")}
        </Button>
        <LoadingButton
          loading={btnLoading}
          onClick={handleSubmit(onSubmit)}
          disableElevation
          type="submit"
          variant="contained"
          color="primary">
          {t("common.common_schedule_publish_btn")}
        </LoadingButton>
      </BootstrapActions>
    </BootstrapDialog>
  );
}
