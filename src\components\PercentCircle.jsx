import { useEffect, useState } from "react"
const PercentCircle = (props) => {

    const [crycleValue, setCrycleValue] = useState(0);
    const [showValue, setShowValue] = useState(0);

    const width = props.width || 100
    const center = width / 2
    const strokeWidth = props.strokeWidth || 10
    const r = center - strokeWidth
    const transform = 'matrix(0,-1,1,0,0,' + width + ')'
    const propsStyle = props.textStyle || {}

    useEffect(() => {
        let percent = 0
        if (props.Percent) {
            percent = Math.round(props.Percent * 100) / 100
        } 
        
        setShowValue(percent)
    }, [props.Percent])

    const innerCircle = props.innerR || r / 2
    const showInner = props.showInner

    const textStle = {
        fontSize: '25px',
        fontWeight: '600',
        zIndex: 100,
        color: 'rgb(112,112,112)',
        ...propsStyle
    }


    useEffect(() => {
        let perimeter = Math.PI * 2 * r; //周长
        let value = (showValue / 100) * perimeter + ' ' + perimeter;
        setCrycleValue(value)
    }, [showValue, width])

    return <div style={{
        width: width + 'px',
        height: width + 'px',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'relative'
    }}>
        <svg style={{ position: 'absolute' }} width={width} height={width} viewBox={'0 0 ' + width + ' ' + width}>
            <defs>
                <linearGradient id='svg_1' x1='0%' y1='0%' x2='100%' y2='64.9%'>
                    <stop offset='0%' stopColor='rgb(79,150,173)' />
                    <stop offset='50%' stopColor='rgb(87,155,164)' />
                    <stop offset='100%' stopColor='rgb(131,187,90)' />
                </linearGradient>
            </defs>
            <circle
                cx={center}
                cy={center}
                r={r}
                strokeWidth={strokeWidth}
                stroke='rgb(222,222,222)'
                fill='none'
                id="p1"
            ></circle>

            {
                showInner && <circle
                    cx={center}
                    cy={center}
                    r={innerCircle}
                    strokeWidth={strokeWidth}
                    stroke='rgb(222,222,222)'
                    fill='rgb(222,222,222)'
                ></circle>
            }



            <circle
                cx={center}
                cy={center}
                r={r}

                strokeWidth={strokeWidth}
                stroke='url(#svg_1)'
                fill='none'
                transform={transform}
                strokeDasharray={crycleValue}
            ></circle>

        </svg>
        <p style={textStle}>
            {showValue}%
        </p>
    </div>


}

export default PercentCircle;
