/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable react/prop-types */
import React, {
  useEffect,
  useRef,
  useState,
  useImperativeHandle,
  forwardRef,
} from "react";
import * as echarts from "echarts";
// import BMap from 'bmap';
import "echarts/extension/bmap/bmap";
import { Grid } from "@mui/material";
// import LoadBMap from '../Map';
import { getPrincipaList } from "@/service/api/L3Sevice.js";
import { useTranslation } from "react-i18next";
import { getStoreLang } from "@/utils/langUtils";
import StoreDetail from "@/pages/dashboard/storeDetail";

const MapChart = forwardRef((props, ref) => {
  const { zoom, setZoom, centerPosition, setCenterPosition } = props;
  // console.log("centerPosition", centerPosition);
  //获取当前是哪个语言
  let lang = getStoreLang();
  const chartRef = useRef(null);
  const storeDetailRef = useRef(null);
  // const [centerPosition, setCenterPosition] = useState([103.73, 36.03]);
  const { t } = useTranslation();
  // const [zoom, setZoom] = useState(5);
  const [flash, setFlash] = useState(true);
  const [data, setData] = useState({
    totalStoreNum: 0,
    totalScreenNum: 0,
    onlineNum: 0,
    offlineNum: 0,
  });
  //地图左边四个标题框的padding-left值
  const [paddingLeft, setPaddingLeft] = useState([50, 20, 65, 65]);
  useImperativeHandle(ref, () => ({
    setCenterPositionRef,
  }));
  const setCenterPositionRef = (data) => {
    setCenterPosition(data);
    setZoom(10);
  };
  //由于第一次进入页面，数据定位会不准确，通过该定时器来修改data来重新渲染图层
  if (flash) {
    setTimeout(() => {
      setData(data);
      setFlash(false);
    }, 1000);
  }
  const handlegetPrincipaList = () => {
    getPrincipaList().then((res) => {
      // 设置数据
      setData(res.data);
    });
  };
  // const [myChart, setMyChart] = useState();
  const myChart = React.useRef();
  useEffect(() => {
    if (lang === "en") {
      setPaddingLeft([33, 20, 28, 28]);
    }
    handlegetPrincipaList();
    myChart.current = echarts.init(chartRef.current);
    myChart.current.off("click");
    myChart.current.on("click", function (params) {
      let storeIdList = [];
      storeIdList.push(params.data.id);
      storeDetailRef.current.handleOpen(storeIdList, params.data.retail);
    });
  }, []);

  useEffect(() => {
    // LoadBMap(BMap).then(() => {

    const option = {
      title: [
        {
          text: t("ips.ips_total_outlet_num"),
          subtext: `${data.totalStoreNum}`,
          left: "20",
          top: "40",
          padding: [15, paddingLeft[0], 20, 15],
          textStyle: {
            fontSize: 15,
            color: "#7AC143",
          },
          backgroundColor: "#DEEDDE",
          borderColor: "#7AC143",
          borderWidth: 2,
          borderRadius: 8,
          subtextStyle: {
            fontSize: 20,
            color: "#7AC143",
          },
          itemGap: 15,
        },
        {
          text: t("ips.ips_total_signage_num"),
          subtext: `${data.totalScreenNum}`,
          left: "20",
          top: "155",
          padding: [15, paddingLeft[1], 20, 15],
          textStyle: {
            fontSize: 15,
            color: "#7AC143",
          },
          backgroundColor: "#DEEDDE",
          borderColor: "#7AC143",
          borderWidth: 2,
          borderRadius: 8,
          subtextStyle: {
            fontSize: 20,
            color: "#7AC143",
          },
          itemGap: 15,
        },
        {
          text: t("ips.ips_online_number"),
          subtext: `${data.onlineNum}`,
          left: "20",
          top: "270",
          padding: [15, paddingLeft[2], 20, 15],
          textStyle: {
            fontSize: 15,
            color: "#7AC143",
          },
          backgroundColor: "#DEEDDE",
          borderColor: "#7AC143",
          borderWidth: 2,
          borderRadius: 8,
          subtextStyle: {
            fontSize: 20,
            color: "#7AC143",
          },
          itemGap: 15,
        },
        {
          text: t("ips.ips_offline_number"),
          subtext: `${data.offlineNum}`,
          left: "20",
          top: "385",
          padding: [15, paddingLeft[3], 20, 15],
          textStyle: {
            fontSize: 15,
            color: "#7AC143",
          },
          backgroundColor: "#DEEDDE",
          borderColor: "#7AC143",
          borderWidth: 2,
          borderRadius: 8,
          subtextStyle: {
            fontSize: 20,
            color: "#7AC143",
          },
          itemGap: 15,
        },
      ],

      tooltip: {
        trigger: "item",
        // triggerOn: 'click',
        borderColor: "#7AC143 ",
        borderWidth: 2,
        backgroundColor: "#DEEDDE",

        formatter: function (params, ticket, callback) {
          const { name, address, contacts, email, retail } = params.data;
          let context = `<span style='color:#7AC143'>${name}</span></br>`;
          if (retail) {
            context += `${t(
              "ips.ips_store_client_name"
            )} &nbsp; <span style='color:#7AC143'>${retail}</span></br>`;
          }
          context += `${t(
            "ips.ips_outlet_address"
          )} &nbsp;&nbsp;<span style='color:#7AC143'>${address}</span></br>`;
          context += `${t(
            "ips.ips_operator_name"
          )} &nbsp; <span style='color:#7AC143'>${contacts}</span></br>`;
          context += `${t(
            "ips.ips_operator_email"
          )} &nbsp; <span style='color:#7AC143'>${email}</span></br>`;
          return context;
        },
      },
      bmap: {
        center: centerPosition,
        zoom: zoom,
        roam: true,

        // mapStyle: {
        //   styleJson: [
        //     {
        //       featureType: "water",
        //       elementType: "all",
        //       stylers: {
        //         color: "#d1d1d1",
        //       },
        //     },
        //     {
        //       featureType: "land",
        //       elementType: "all",
        //       stylers: {
        //         color: "#DDE6ED",
        //       },
        //     },
        //     {
        //       featureType: "railway",
        //       elementType: "all",
        //       stylers: {
        //         visibility: "off",
        //       },
        //     },
        //     {
        //       featureType: "highway",
        //       elementType: "all",
        //       stylers: {
        //         color: "#fdfdfd",
        //       },
        //     },
        //     {
        //       featureType: "highway",
        //       elementType: "labels",
        //       stylers: {
        //         visibility: "off",
        //       },
        //     },
        //     {
        //       featureType: "arterial",
        //       elementType: "geometry",
        //       stylers: {
        //         color: "#fefefe",
        //       },
        //     },
        //     {
        //       featureType: "arterial",
        //       elementType: "geometry.fill",
        //       stylers: {
        //         color: "#fefefe",
        //       },
        //     },
        //     // {
        //     //   featureType: "poi",
        //     //   elementType: "all",
        //     //   stylers: {
        //     //     visibility: "off",
        //     //   },
        //     // },
        //     // {
        //     //   featureType: "green",
        //     //   elementType: "all",
        //     //   stylers: {
        //     //     visibility: "off",
        //     //   },
        //     // },
        //     // {
        //     //   featureType: "subway",
        //     //   elementType: "all",
        //     //   stylers: {
        //     //     visibility: "off",
        //     //   },
        //     // },
        //     {
        //       featureType: "manmade",
        //       elementType: "all",
        //       stylers: {
        //         color: "#d1d1d1",
        //       },
        //     },
        //     {
        //       featureType: "local",
        //       elementType: "all",
        //       stylers: {
        //         color: "#d1d1d1",
        //       },
        //     },
        //     // {
        //     //   featureType: "arterial",
        //     //   elementType: "labels",
        //     //   stylers: {
        //     //     visibility: "off",
        //     //   },
        //     // },
        //     {
        //       featureType: "boundary",
        //       elementType: "all",
        //       stylers: {
        //         color: "#7AC143",
        //       },
        //     },
        //     {
        //       featureType: "building",
        //       elementType: "all",
        //       stylers: {
        //         color: "#d1d1d1",
        //       },
        //     },
        //     {
        //       featureType: "label",
        //       elementType: "labels.text.fill",
        //       stylers: {
        //         color: "#999999",
        //       },
        //     },
        //     {
        //       featureType: "transportation",
        //       elementType: "labels.text.stroke",
        //       stylers: {
        //         color: "#ffffff",
        //       },
        //     },
        //     {
        //       featureType: "district",
        //       elementType: "labels.text.fill",
        //       stylers: {
        //         color: "#ffffff",
        //       },
        //     },
        //     {
        //       featureType: "district",
        //       elementType: "labels.text.stroke",
        //       stylers: {
        //         color: "#7ac143ff",
        //       },
        //     },
        //   ],
        // },
      },
      series: {
        type: "custom",
        coordinateSystem: "bmap",
        data: data.storeInfoList,
        smooth: true,
        zlevel: 1,
        encode: {
          value: 2,
        },
        showEffectOn: "render",
        rippleEffect: {
          brushType: "stroke",
        },
        label: {
          formatter: "{b}",
          position: "right",
          show: false,
        },
        itemStyle: {
          shadowBlur: 10,
          shadowColor: "#333",
        },
        emphasis: {
          scale: true,
        },
      },
      renderItem(params, api) {
        const coord = api.coord([
          api.value(0, params.dataIndex),
          api.value(1, params.dataIndex),
        ]);
        const circles = [];
        for (let i = 0; i < 5; i++) {
          circles.push({
            type: "circle",
            shape: {
              cx: 0,
              cy: 0,
              r: 30,
            },
            style: {
              stroke: "#8CC862",
              fill: "none",
              lineWidth: 2,
            },
            // Ripple animation
            keyframeAnimation: {
              duration: 4000,
              loop: true,
              delay: (-i / 4) * 4000,
              keyframes: [
                {
                  percent: 0,
                  scaleX: 0,
                  scaleY: 0,
                  style: {
                    opacity: 1,
                  },
                },
                {
                  percent: 1,
                  scaleX: 1,
                  scaleY: 0.4,
                  style: {
                    opacity: 0,
                  },
                },
              ],
            },
          });
        }

        return {
          type: "group",
          x: coord[0],
          y: coord[1],
          children: [
            ...circles,
            {
              type: "path",
              shape: {
                d: "M16 0c-5.523 0-10 4.477-10 10 0 10 10 22 10 22s10-12 10-22c0-5.523-4.477-10-10-10zM16 16c-3.314 0-6-2.686-6-6s2.686-6 6-6 6 2.686 6 6-2.686 6-6 6z",
                x: -10,
                y: -35,
                width: 20,
                height: 30,
              },
              style: {
                fill: "#61A030",
              },
            },
          ],
        };
      },
    };
    myChart.current.setOption(option);
  }, [data, centerPosition]);

  return (
    <>
      <Grid
        item
        ref={chartRef}
        style={{ width: "100%", height: "600px" }}></Grid>
      <StoreDetail ref={storeDetailRef} />
    </>
  );
});

export default MapChart;
