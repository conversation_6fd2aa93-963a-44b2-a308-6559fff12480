/* eslint-disable react-refresh/only-export-components */
/* eslint-disable react/prop-types */
/* eslint-disable react-hooks/rules-of-hooks */
import React, { useState, useEffect, useRef } from "react";
import MainCard from "@/components/MainCard";
import AddIcon from "@mui/icons-material/Add";
import { useNavigate, useSearchParams } from "react-router-dom";
import {
  Box,
  Stack,
  Grid,
  Button,
  Typography,
  InputLabel,
  OutlinedInput,
  InputAdornment,
  InputBase,
  FormHelperText,
  TextField,
} from "@mui/material";
import * as Yup from "yup";
import { useFormik } from "formik";
import DurationChart from "./components/durationChart";
import MaterialTable from "./components/MaterialTable";
import AddMaterial from "./components/AddMaterial";
import AddMusic from "./components/addMusic";
import ZKSelect from "@/components/ZKSelect";
import { toast } from "react-toastify";
import { getPlayListInfo, updatePlayList } from "@/service/api/playList";
import { playListType, directions } from "@/dict/commonDict";
import AnimateButton from "@/components/@extended/AnimateButton";
import LoadingButton from "@mui/lab/LoadingButton";
import { useTranslation } from "react-i18next";
import { getPrincipaList } from "@/service/api/L3Sevice.js";

/**
 *  编辑播放清单页面
 */
const editPlayList = () => {
  const { t } = useTranslation();
  const materialRef = useRef(null);
  const materialListRef = useRef(null);
  const echartsRef = useRef(null);
  const musicRef = useRef(null);
  const navigate = useNavigate();
  const [search, setsearch] = useSearchParams();
  const [loading, setLoading] = useState(false);
  const [playListTypeValue, setPlayListTypeValue] = useState(null);
  const [disableDuration, setDisableDuration] = useState(false);
  const [addResourceText, setAddResourceText] = useState(
    t("ips.ips_meida_image")
  );
  const [merchants, setMerchants] = useState([]);
  const getMerchant = () => {
    getPrincipaList(1).then((res) => {
      setMerchants(res.data);
    });
  };
  useEffect(() => {
    handleGetPlayListInfo();
  }, []);

  const handleGetPlayListInfo = () => {
    getPlayListInfo(search.get("id")).then((res) => {
      setFormData(res.data);
    });
  };

  // 表单赋值
  const setFormData = (data) => {
    getMerchant();
    setMaterialLists(data.playListMaterialList);
    setPlayListTypeValue(data?.type?.toString());
    data?.type.toString() === "2"
      ? setAddResourceText(t("common.common_add_layout_resource"))
      : setAddResourceText(t("ips.ips_meida_image"));
    userFormik.setValues(
      {
        id: data.id,
        name: data.name,
        direction: data.direction,
        merchantId: data?.merchantId,
        type: data?.type?.toString(),
      },
      true
    );
    if (data?.type.toString() === "2") {
      setDisableDuration(true);
    } else {
      setDisableDuration(false);
    }
  };
  // 追加列表
  const setMaterialList = (data) => {
    materialListRef.current.appendMaterialData(data);
    const tempChartValue = [...echartsRef.current.durationValues];
    const chartValue = [data.name, data.duration, tempChartValue.length];
    tempChartValue.push(chartValue);
    echartsRef.current.setDurationValues(tempChartValue);
  };
  const setMaterialLists = (dataList) => {
    // const hasLayout = dataList.some(item => item.type === 'layout');

    // if (hasLayout && playListTypeValue !== '2') {
    //   toast.error(t('common.common_not_add_layout_resource'));
    //   return;
    // }

    // const materialListData = materialListRef.current.getMaterialDatas();
    // const isSameType = materialListData.length > 0 && materialListData[0].type === dataList[0].type;

    // if (isSameType || materialListData.length === 0) {
    const tempChartValue = [...echartsRef.current.durationValues];
    dataList.forEach((data) => {
      const index = tempChartValue.length;
      const chartValue = [data.name, data.duration, index + 1, data?.type];
      tempChartValue.push(chartValue);
    });

    echartsRef.current.setDurationValues(tempChartValue);
    materialListRef.current.appendMaterialDatas(dataList);
    // } else {
    // toast.error(t('common.common_not_add_layout_resource'));
    // }
  };
  // 图表删除
  const removeChartsByIndex = (index) => {
    const tempChartValue = [...echartsRef.current.durationValues];
    tempChartValue.splice(index, 1);
    echartsRef.current.setDurationValues(tempChartValue);
  };
  const getMaterialType = () => {
    return materialListRef.current.materialType;
  };

  const handleUpdatePlayList = (values) => {
    setLoading(true);
    updatePlayList(values)
      .then((res) => {
        toast.success(res.message);
        navigate(-1);
      })
      .catch((err) => {
        setLoading(false);
      });
  };

  //  表单
  const userFormik = useFormik({
    initialValues: {
      id: "",
      name: "",
      direction: "",
      type: "",
      playListMaterialList: [],
    },
    onSubmit: (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        values.playListMaterialList = materialListRef.current.data;
        handleUpdatePlayList(values);
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
    validationSchema: Yup.object().shape({
      name: Yup.string().required(t("ips.ips_enter_playlist_name")),
      direction: Yup.string().required(t("ips.ips_please_select_direction")),
      type: Yup.string().required(t("common.common_select_type")),
      merchantId: Yup.string().required(t("layout.select_retail")),
    }),
  });

  const handleChangeType = (value) => {
    // console.log(value)
    if (value === "2") {
      //选择Layout 需要触发操作
      //修改按钮的标题
      setAddResourceText(t("common.common_add_layout_resource"));
      setPlayListTypeValue("2");
      materialListRef?.current?.removeNotLayout();
      echartsRef?.current?.removeNotLayout();
    } else {
      setAddResourceText(t("ips.ips_meida_image"));
      setPlayListTypeValue(null);
      materialListRef?.current?.removeLayout();
      echartsRef?.current?.removeLayout();
    }
  };

  return (
    <>
      <MainCard title={t("ips.ips_editor_playlist")} border={false}>
        <form noValidate onSubmit={userFormik.handleSubmit}>
          <Grid container spacing={5}>
            <Grid item xs={3}>
              <Stack spacing={1}>
                <InputLabel htmlFor="name">
                  {t("ips.ips_playlist_name")} <i style={{ color: "red" }}>*</i>
                </InputLabel>
                <OutlinedInput
                  id="name"
                  type="text"
                  fullWidth
                  name="name"
                  value={userFormik.values.name}
                  onBlur={userFormik.handleBlur}
                  onChange={userFormik.handleChange}
                  placeholder={t("ips.ips_enter_playlist_name")}
                  error={Boolean(
                    userFormik.touched.name && userFormik.errors.name
                  )}
                />
                {userFormik.touched.name && userFormik.errors.name && (
                  <FormHelperText error id="name-error">
                    {userFormik.errors.name}
                  </FormHelperText>
                )}
              </Stack>
            </Grid>
            <Grid item xs={3}>
              <Stack spacing={1}>
                <InputLabel htmlFor="name">
                  {t("ips.ips_store_brand")}
                  <i style={{ color: "red" }}>*</i>
                </InputLabel>
                {/* <OutlinedInput
                  id="name"
                  type="text"
                  fullWidth
                  name="name"
                  value={userFormik.values.name}
                  onBlur={userFormik.handleBlur}
                  onChange={userFormik.handleChange}
                  placeholder={t("ips.ips_enter_playlist_name")}
                  error={Boolean(
                    userFormik.touched.name && userFormik.errors.name
                  )}
                />
                {userFormik.touched.name && userFormik.errors.name && (
                  <FormHelperText error id="name-error">
                    {userFormik.errors.name}
                  </FormHelperText>
                )} */}
                <ZKSelect
                  name="merchantId"
                  value={userFormik.values.merchantId}
                  onChange={userFormik.handleChange}
                  onBlur={userFormik.handleBlur}
                  options={merchants}
                  onClear={() => {
                    userFormik.setFieldValue("merchantId", "");
                  }}
                  placeholder={t("common.common_please_select_retail")}
                  error={Boolean(
                    userFormik.touched.merchantId &&
                      userFormik.errors.merchantId
                  )}
                />
                {userFormik.touched.merchantId &&
                  userFormik.errors.merchantId && (
                    <FormHelperText error id="merchantId-error">
                      {userFormik.errors.merchantId}
                    </FormHelperText>
                  )}
              </Stack>
            </Grid>
            <Grid item xs={3}>
              <Stack spacing={1}>
                <InputLabel htmlFor="direction">
                  {t("common.common_direction")}
                  <i style={{ color: "red" }}>*</i>
                </InputLabel>
                <ZKSelect
                  displayEmpty
                  name="direction"
                  value={userFormik.values.direction}
                  placeholder={t("ips.ips_select_playlist_direction")}
                  options={directions}
                  onClear={() => {
                    userFormik.setFieldValue("direction", "");
                  }}
                  onChange={userFormik.handleChange}
                  onBlur={userFormik.handleBlur}
                  error={Boolean(
                    userFormik.touched.direction && userFormik.errors.direction
                  )}
                />
                {userFormik.touched.direction &&
                  userFormik.errors.direction && (
                    <FormHelperText error id="direction-error">
                      {userFormik.errors.direction}
                    </FormHelperText>
                  )}
                {/* <MenuItem disabled value="">
                                    请选择播放清单方向
                                </MenuItem>
                                {directions.map((item) => (
                                    <MenuItem key={item.value} value={item.value}>
                                        {item.label}
                                    </MenuItem>
                                ))}
                            </Select> */}
              </Stack>
            </Grid>

            <Grid item xs={3}>
              <Stack spacing={1}>
                <InputLabel htmlFor="type">
                  {t("ips.ips_playlist_type")} <i style={{ color: "red" }}>*</i>
                </InputLabel>
                <ZKSelect
                  name="type"
                  value={userFormik.values.type}
                  placeholder={t("ips.ips_select_playlist_type")}
                  options={playListType}
                  disabled
                  isClear={false}
                  onClear={() => {
                    handleChangeType(null);
                    userFormik.setFieldValue("type", undefined);
                  }}
                  onChange={(e, value) => {
                    userFormik.handleChange(e);
                    handleChangeType(e?.target?.value);
                  }}
                  onBlur={userFormik.handleBlur}
                  error={Boolean(
                    userFormik.touched.type && userFormik.errors.type
                  )}
                />
                {userFormik.touched.type && userFormik.errors.type && (
                  <FormHelperText error id="type-error">
                    {userFormik.errors.type}
                  </FormHelperText>
                )}
              </Stack>
            </Grid>
          </Grid>
          <Grid sx={{ marginTop: "30px" }} container spacing={1}>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <InputLabel htmlFor="store-name">
                  {t("ips.ips_append_playlist_list")}
                  <i style={{ color: "red" }}>*</i>
                </InputLabel>
              </Stack>
            </Grid>
            <Grid item sx={{ marginBottom: "10px" }} xs={12}>
              <Stack spacing={1} direction="row" alignItems="center">
                <Button
                  startIcon={<AddIcon />}
                  variant="contained"
                  color="primary"
                  onClick={() => {
                    materialRef.current.handleClickOpen();
                  }}>
                  {addResourceText}
                </Button>

                {/* <Button
                                    startIcon={<AddIcon />}
                                    variant="contained"
                                    color="primary"
                                    onClick={() => musicRef.current.handleClickOpen()}
                                >
                                    音乐
                                </Button> */}
              </Stack>
            </Grid>
            <Grid item xs={6}>
              <MaterialTable
                ref={materialListRef}
                removeChartsByIndex={removeChartsByIndex}
              />
            </Grid>
            <Grid item xs={6}>
              <DurationChart
                ref={echartsRef}
                materialData={() => {
                  materialListRef.current.data;
                }}
              />
            </Grid>
          </Grid>
          <Grid item xs={12} sx={{ marginTop: "30px" }}>
            <Grid container spacing={2} sx={{ width: "100%" }}>
              <Grid item xs={12}>
                <Stack
                  direction="row"
                  justifyContent="flex-end"
                  alignItems="center"
                  spacing={2}>
                  <Button
                    color="info"
                    variant="outlined"
                    onClick={() => navigate(-1)}>
                    {t("common.common_edit_cancel")}
                  </Button>
                  <AnimateButton>
                    <LoadingButton
                      loading={loading}
                      disableElevation
                      disabled={userFormik.isSubmitting}
                      fullWidth
                      type="submit"
                      variant="contained"
                      color="primary">
                      {t("common.common_submit")}
                    </LoadingButton>
                  </AnimateButton>
                </Stack>
              </Grid>
            </Grid>
          </Grid>
        </form>
      </MainCard>

      <AddMaterial
        ref={materialRef}
        merchants={merchants}
        playListTypeValue={playListTypeValue}
        setMaterialLists={setMaterialLists}
        materialTypeList={getMaterialType}
        disableDuration={disableDuration}
      />
      {/* <AddMusic
        ref={musicRef}
        setMaterialLists={setMaterialLists}
        materialTypeList={getMaterialType}
      /> */}
    </>
  );
};

export default editPlayList;
