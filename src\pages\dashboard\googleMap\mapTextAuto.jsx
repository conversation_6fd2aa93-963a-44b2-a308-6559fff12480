/* eslint-disable no-undef */
/* eslint-disable react/prop-types */
/* eslint-disable react-hooks/rules-of-hooks */
import React, { useEffect, useState, useMemo, useRef } from "react";
import Box from "@mui/material/Box";
import { TextField, InputAdornment, IconButton } from "@mui/material";
import Autocomplete from "@mui/material/Autocomplete";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import Grid from "@mui/material/Grid";
import SearchIcon from "@mui/icons-material/Search";

import { debounce } from "@mui/material/utils";

import { getAddress, postGoogleData } from "@/service/api/bmap";
import { useTranslation } from "react-i18next";
const mapTextAutoComplete = (props) => {
  const {
    setZoom,
    setCoordinates,
    detailAddress,
    onCoordinatesChange,
    onDetailAddress,
  } = props;
  const { t } = useTranslation();
  const [value, setValue] = useState(null);
  const [inputValue, setInputValue] = useState("");
  const [options, setOptions] = useState([]);
  // const [selectOption, setSelectedOption] = useState([]);
  const autocompleteService = useRef(null);
  const timeoutId = null;
  const fetch = useMemo(
    () =>
      debounce((request, callback) => {
        autocompleteService.current(request, callback);
      }, 800),
    []
  );

  useEffect(() => {
    let active = true;
    if (!autocompleteService.current) {
      autocompleteService.current = (request, callback) => {
        console.log("request", request);
        postGoogleData(request.query, request.region)
          .then((res) => {
            callback(res.data);
          })
          .catch(function (error) {
            console.log(error);
          });
      };
    }

    if (inputValue === "") {
      setOptions(value ? [value] : []);
      return undefined;
    }
    const params = {
      query: inputValue,
      region: detailAddress,
    };
    // let requestUrl = `/baidu/?query=${inputValue}&region=${props.code}&city_limit=false&output=json&ak=APeE0SHYyqFcNp2bRlrSiKniDUZDaTMA`;
    fetch(params, (results) => {
      // // 找到 "市" 的位置索引
      // const cityProvince = detailAddress.indexOf("省");
      // const cityIndex = detailAddress.indexOf("市");
      // // 根据 "市" 的位置截取出市的名称
      // const cityData =
      //   cityIndex !== -1
      //     ? detailAddress.substring(cityProvince + 1, cityIndex + 1)
      //     : "";
      if (active) {
        // const filterData = results.filter((item) => item.city === cityData);
        setOptions(results);
      }
    });
    return () => {
      active = false;
    };
  }, [value, inputValue, fetch, detailAddress]);

  const handlerAutocompleteChange = (event, newValue) => {
    setOptions(newValue ? [newValue, ...options] : options);
    setValue(newValue);
    // setSelectedOption(newValue); // 更新选中的数据
    setInputValue(newValue ? newValue.name : ""); // Update inputValue
    setCoordinates(newValue);
  };

  const handleSearch = (value) => {
    console.log("8rfasgafgaga", value);
    const address = detailAddress + value;
    console.log("address", address);
    const geocoder = new window.google.maps.Geocoder();
    geocoder.geocode({ address: address }, (results, status) => {
      if (status === "OK") {
        const position = results[0].geometry.location;
        const selectedLatLng = {
          lat: position.lat(), // 纬度
          lng: position.lng(), // 经度
        };

        setCoordinates(selectedLatLng);
        // 调用父组件传递的函数，传递经纬度
        onCoordinatesChange(selectedLatLng);
        setZoom(20);
      } else {
        console.error(
          "Geocode was not successful for the following reason:",
          status
        );
      }
    });
    onDetailAddress(address);
  };
  return (
    <Box>
      <Autocomplete
        id="baidu-map-search"
        label="address"
        disablePortal
        disableClearable //清除按钮
        freeSolo
        getOptionLabel={(option) =>
          typeof option === "string" ? option : option.name
        }
        filterOptions={(x) => x}
        options={options}
        autoComplete
        includeInputInList
        filterSelectedOptions
        value={value}
        size="small"
        noOptionsText={t("common.common_input_location_search")}
        onChange={(event, newValue) => {
          handlerAutocompleteChange(event, newValue);
          setInputValue(newValue ? newValue.name : ""); // Update inputValue
        }}
        onInputChange={(event, newInputValue) => {
          // 出发搜索
          setInputValue(newInputValue);
          setValue(newInputValue);
        }}
        renderInput={(params) => (
          <TextField
            placeholder={props.placeholder}
            {...params}
            sx={{ width: 200, marginTop: 1, height: 60 }}
            label={t("ips.ips_enter_location_address")}
            InputProps={{
              ...params.InputProps,
              type: "search",
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton onClick={() => handleSearch(inputValue)}>
                    <SearchIcon />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        )}
        renderOption={(props, option) => {
          return (
            <li {...props}>
              <Grid container alignItems="center">
                <Grid item sx={{ display: "flex", width: 44 }}>
                  <LocationOnIcon sx={{ color: "text.secondary" }} />
                </Grid>
                <Grid
                  item
                  sx={{ width: "calc(100% - 44px)", wordWrap: "break-word" }}
                >
                  <Box component="span" sx={{ fontWeight: "bold" }}>
                    {option.name}
                  </Box>
                </Grid>
              </Grid>
            </li>
          );
        }}
      />
    </Box>
  );
};
export default mapTextAutoComplete;
