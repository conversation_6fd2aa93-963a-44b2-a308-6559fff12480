/* eslint-disable react-hooks/rules-of-hooks */
/**
 * @param {string} pathname 当前路由路径
 * @param {object} meta 当前路由自定义meta字段
 * @return {string} 需要跳转到其他页时，就返回一个该页的path路径，或返回resolve该路径的promise对象
 */
import { getToken } from "@/utils/auth";
import { useDispatchUser } from "@/hooks/user";
import { useDispatch } from "react-redux";
import { setMenuList } from "@/store/reducers/menu";
import { getUserMenus } from "@/service/api/L3Sevice";
import i18n from "i18next";
import { getAuthButton } from "@/service/api/L3Sevice.js";
import { toast } from "react-toastify";

const flattenTree = (nodes) => {
  let result = [];
  nodes.forEach((node) => {
    result.push(node.id);
    if (node.children) {
      let ids = flattenTree(node.children);
      result = [...result, ...ids];
    }
  });
  return result;
};

const onRouteBefore = async ({ pathname, meta }) => {
  const { stateSetPermission } = useDispatchUser();
  const dispatch = useDispatch();
  // 示例：动态修改页面title
  if (meta.i18n !== undefined) {
    document.title = i18n.t("menu." + meta?.i18n);
  }

  // 示例：判断未登录跳转登录页

  // if (!getToken()) {
  //   return "/login";
  // } else {
  //   try {
  //     const [userRes, menuRes] = await Promise.all([
  //       getAuthButton("SD"),
  //       getUserMenus({ applicationCode: "SD" }),
  //     ]);

  //     if (userRes?.data && menuRes?.data) {
  //       dispatch(setMenuList(menuRes.data));
  //       dispatch(stateSetPermission(userRes?.data));
  //     } else {
  //       toast.error("您没有任何权限，请联系管理员");
  //       setTimeout(() => {
  //         window.location.href = "/application/center";
  //       }, 1000);
  //     }
  //   } catch (err) {
  //     // toast.error("您没有任何权限，请联系管理员");
  //     // setTimeout(() => {
  //     //   window.location.href = "/application/center";
  //     // }, 1000);
  //   }

  // }


  if (!getToken()) {
    toast.error("登录已过期");
    setTimeout(() => {
      window.location.href = "/login";
    }, 1000);

  }

};
export default onRouteBefore;
