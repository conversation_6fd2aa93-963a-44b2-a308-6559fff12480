import React, { useRef, useState, useEffect, useMemo, forwardRef } from "react";
import {
  Stack,
  Alert,
  Grid,
  Checkbox,
  InputBase,
  Button,
  Popover,
  IconButton,
  RadioGroup,
  InputAdornment,
  Tooltip,
  OutlinedInput,
  FormControlLabel,
  FormGroup,
  TextField,
  Radio,
  Typography,
  InputLabel,
} from "@mui/material";
import {
  BootstrapDialog,
  BootstrapDialogTitle,
  BootstrapContent,
  BootstrapActions,
} from "@/components/dialog";
import { useTranslation } from "react-i18next";
import Descriptions from "@/components/descriptions";
import { Box, makeStyles } from "@material-ui/core";
import VisibilityIcon from "@material-ui/icons/Visibility";
import ModalImage from "react-modal-image";
import { Lightbox } from "react-modal-image";
import "./index.less";
import { dateFormate } from "@/utils/zkUtils";
const useStyles = makeStyles((theme) => ({
  imgContainer: {
    position: "relative",
    display: "inline-block",
  },
  previewBtn: {
    position: "absolute",
    top: "50%",
    left: "50%",
    color: "white",
    transform: "translate(-50%, -50%)",
    opacity: 0,
    transition: "opacity 0.3s ease-in-out",
  },
  imgOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    width: "100%",
    height: "100%",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    opacity: 0,
    transition: "opacity 0.3s ease-in-out",
    "&:hover": {
      opacity: 1,
    },
  },
  imgHovered: {
    "& $previewBtn": {
      opacity: 1,
    },
    "& $imgOverlay": {
      opacity: 1,
    },
  },
}));

const ScreenShotDetails = forwardRef((props, ref) => {
  const { data, open, onClose } = props;
  const [isHovered, setIsHovered] = useState(false);
  const [src, setSrc] = useState("");
  const classes = useStyles();
  const [previewOpen, setPreviewOpen] = useState(false);
  const [title, setTitle] = useState("");
  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
  };
  const handleClose = () => {
    onClose();
  };
  const { t } = useTranslation();
  return (
    <>
      <BootstrapDialog
        fullWidth
        maxWidth="md"
        aria-labelledby="customized-dialog-title"
        open={open}
        onClose={handleClose}
      >
        <BootstrapDialogTitle onClose={handleClose}>
          <Typography variant="h4" component="p">
            {t("common.common_screen_shot_detail")}
          </Typography>
        </BootstrapDialogTitle>

        <BootstrapContent dividers>
          <Grid
            container
            direction="row"
            justifyContent="flex-start"
            spacing={2}
          >
            <Grid
              item
              xs={7}
              style={{
                display: "flex",
                height: "100%",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <Box
                className={`${classes.imgContainer} ${
                  isHovered ? classes.imgHovered : ""
                }`}
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
              >
                <img
                  height={400}
                  width={"100%"}
                  style={{
                    objectFit: "scale-down",
                  }}
                  src={data?.url}
                />
                <Box className={classes.imgOverlay} />
                <IconButton
                  className={classes.previewBtn}
                  onClick={() => {
                    setSrc(data?.url);
                    setPreviewOpen(true);
                    const title =
                      data?.merchantName +
                      "-" +
                      data.storeName +
                      "-" +
                      data?.screenName +
                      "-" +
                      data?.shotTime;
                    setTitle(title);
                  }}
                >
                  <VisibilityIcon
                    xs={{ backgroundColor: "white", fontSize: 60 }}
                  />
                </IconButton>
              </Box>
              {/* <img
                            height={400}
                            width={"100%"}
                            style={{
                                objectFit: "scale-down",
                            }}
                            src={data?.url}
                        /> */}
            </Grid>
            <Grid item xs={5}>
              <Descriptions
                title={t("common.common_screen_base_info")}
                column={1}
                bordered
                colon={false}
              >
                <Descriptions.Item label={t("system.system_belong_merchant")}>
                  <Tooltip title={data?.merchantName} placement="top">
                    <Typography className="overflow-ellipsis">
                      {data?.merchantName ? data?.merchantName : "-"}
                    </Typography>
                  </Tooltip>
                </Descriptions.Item>
                <Descriptions.Item label={t("common.common_outlet_owner")}>
                  <Tooltip title={data?.storeName} placement="top">
                    <Typography className="overflow-ellipsis">
                      {data?.storeName ? data?.storeName : "-"}
                    </Typography>
                  </Tooltip>
                </Descriptions.Item>
                <Descriptions.Item label={t("ips.ips_device")}>
                  <Tooltip title={data?.screenName} placement="top">
                    <Typography className="overflow-ellipsis">
                      {data?.screenName ? data?.screenName : "-"}
                    </Typography>
                  </Tooltip>
                </Descriptions.Item>
                <Descriptions.Item label={t("common.common_screen_shot_time")}>
                  <Tooltip title={data?.shotTimeStr ? data?.shotTimeStr : data?.shotTime} placement="top">
                    <Typography className="overflow-ellipsis">
                      {data?.shotTimeStr
                        ? data?.shotTimeStr
                        : data?.shotTime
                        ? data?.shotTime
                        : "-"}
                      {/* {data?.shotTime ? data?.shotTime : "-"} */}
                    </Typography>
                  </Tooltip>
                </Descriptions.Item>
              </Descriptions>
            </Grid>
          </Grid>
        </BootstrapContent>
        <BootstrapActions>
          <Button color="info" variant="outlined" onClick={handleClose}>
            {t("common.common_edit_cancel")}
          </Button>
        </BootstrapActions>
      </BootstrapDialog>
      {previewOpen && (
        <Lightbox
          hideZoom={false}
          medium={src}
          large={src}
          small={src}
          smallSrcSet={src}
          alt={title}
          showRotate={true}
          onClose={() => {
            setSrc("");
            setPreviewOpen(false);
            setTitle("");
          }}
        />
      )}
    </>
  );
});

export default ScreenShotDetails;
