import React from 'react'
import {  Grid } from "@mui/material";
import { useState, useEffect ,useRef} from "react";
import {
  FormLabel,
  PrettoSlider,
} from "./PropertiesComponent";
import CustomInput from "../components/CustomInput";
import CustomSelect from "../components/CustomSelect";
import {
  fontSizeList,
  dictScrollDirection,
  dictScrollSpeed,
} from "../common/utils";
import ColorPick from "../components/ColorPick";
import {
  getNewsLocals,
  getNewsCategories,
  getNewsLanguages,
} from "@/service/api/editorApi";
import { message } from "../common/i18n";

const NewsComponent = (props) => {
  const currentPageIndex = props.currentPageIndex;
  const currentIndex = props.currentComponentIndex;
  const pages = props.pages;
  const [properties, setProperties] = useState({
    title: "editor_News", //标题
    name: "editor_News", //名称
    type: "ZKTecoNews", //组件类型
    left: 12,
    top: 15,
    width: 200,
    height: 50,
    zIndex: 50,
    hide: false,
    newsType: "",
    languageType: "",
    newsArea: "",
    fontSize: 16, //字体大小
    fontColor: "#262626", //颜色
    bgColor: "", //背景颜色
    scrollDirection: "left", //滚动方向
    speed: 60, //滚动速度
    transparency: 1,
    componentId: "",
  });

  const [newsType, setNewsType] = useState("");
  const [languageType, setLanguageType] = useState("");
  const [newsArea, setNewsArea] = useState("");

  const activeTempIndex = props.activeTempIndex;

  const newsTypeRef = useRef("");
  const languageTypeRef = useRef("");
  const newsAreaRef = useRef("");

  useEffect(() => {
    if (currentIndex !== "") {
      const curretnPage = pages[currentPageIndex];
      let componentInfo = null;
      if (curretnPage.isTemplate) {
        componentInfo =
          curretnPage.tempLayout[activeTempIndex]?.componentList[currentIndex];
      } else {
        componentInfo = pages[currentPageIndex].componentList[currentIndex];
      }
      let newPP = {
        ...componentInfo,
        newsType: componentInfo.newsType ? componentInfo.newsType : newsType,
        languageType: componentInfo.languageType
          ? componentInfo.languageType
          : languageType,
        newsArea: componentInfo.newsArea
          ? componentInfo.newsArea
          : newsArea,
      };

      newsTypeRef.current = newPP.newsType;
      languageTypeRef.current = newPP.languageType;
      newsAreaRef.current = newPP.newsArea;

      setProperties(newPP);
    }
  }, [currentPageIndex, currentIndex, activeTempIndex, pages]);

  const [areaList, setAreaList] = useState([]);
  const [languageList, setLanguageList] = useState([]);
  const [newsTypeList, setNewsTypeList] = useState([]);

  useEffect(() => {
    const curretnPage = pages[currentPageIndex];
    let componentInfo = null;
    if (curretnPage.isTemplate) {
      componentInfo =
        curretnPage.tempLayout[activeTempIndex]?.componentList[currentIndex];
    } else {
      componentInfo = pages[currentPageIndex].componentList[currentIndex];
    }



    let newPP = {
      ...properties,
      newsType: newsType === "" ? componentInfo.newsType : newsType,
      languageType:
        languageType === "" ? componentInfo.languageType : languageType,
      newsArea: newsArea === "" ? componentInfo.newsArea : newsArea,
    };
    setProperties(newPP);
    let newComPP = {
      newsType: newsType === "" ? componentInfo.newsType : newsType,
      languageType:
        languageType === "" ? componentInfo.languageType : languageType,
      newsArea: newsArea === "" ? componentInfo.newsArea : newsArea,
    };
    setComponentInfo(newComPP);

    newsTypeRef.current = newPP.newsType;
    languageTypeRef.current = newPP.languageType;
    newsAreaRef.current = newPP.newsArea;


  }, [newsType, languageType, newsArea]);

  useEffect(() => {
    getNewsLocals().then((res) => {
      let data = res.data;
      if (data?.length > 0) {
        setAreaList(data);
        if (newsAreaRef.current === "" || newsAreaRef.current === null) {

          setNewsArea(data[0].value);
        }
      } else {
        setAreaList([]);
      }
    });
    getNewsCategories().then((res) => {
      let data = res.data;
      if (data?.length > 0) {
        setNewsTypeList(data);
        if (newsTypeRef.current === "" || newsTypeRef.current === null) {
          setNewsType(data[0].value);
        }
      } else {
        setNewsTypeList([]);
      }
    });
    getNewsLanguages().then((res) => {
      let data = res.data;
      if (data?.length > 0) {
        setLanguageList(data);
        if (
            languageTypeRef.current === "" ||
            languageTypeRef.current === null
        ) {

          setLanguageType(data[0].value);
        }
      } else {
        setLanguageList([]);
      }
    });
  }, []);

  const setComponentInfo = (baseInfo) => {
    const newPages = JSON.parse(JSON.stringify(props.pages));
    let currentPage = newPages[currentPageIndex];
    if (currentPage.isTemplate) {
      let oldInfo =
        currentPage.tempLayout[activeTempIndex].componentList[currentIndex];
      let newInfo = {
        ...oldInfo,
        ...baseInfo,
      };
      currentPage.tempLayout[activeTempIndex].componentList[currentIndex] =
        newInfo;
    } else {
      let oldInfo = newPages[currentPageIndex].componentList[currentIndex];
      let newInfo = {
        ...oldInfo,
        ...baseInfo,
      };
      newPages[currentPageIndex].componentList[currentIndex] = newInfo;
    }

    if (props.setPages) {
      props.setPages(newPages);
    }
  };

  const changeProperties = (event) => {
    const name = event.target.name;
    let newInfo = {
      ...properties,
      [name]: event.target.value,
    };
    setComponentInfo(newInfo);
  };

  const handleRotationChange = (event, newValue) => {
    let newInfo = {
      ...properties,
      rotate: newValue,
    };
    setComponentInfo(newInfo);
  };

  const handleSliderChange = (event, newValue) => {
    let newInfo = {
      ...properties,
      transparency: newValue,
    };
    setComponentInfo(newInfo);
  };

  const changeNewInfo = (event) => {
    const name = event.target.name;
    if (name === "newsArea") {
      setNewsArea(event.target.value);
    } else if (name === "newsType") {
      setNewsType(event.target.value);
    } else if (name === "languageType") {
      setLanguageType(event.target.value);
    }
  };

  return (
    <Grid
      sx={{
        width: "100%",
        boxShadow: "0px 0px 6px #00000029",
        borderRadius: "10px",
        backgroundColor: "#ffffff",
        overflow: "hidden",
        minHeight: "200px",
      }}
    >
      <Grid
        sx={{
          p: 2,
        }}
      >
        <Grid
          sx={{
            display: "flex",
            alignItems: "center",
            mt: 1,
          }}
        >
          <CustomSelect
            label={message("editor_newsArea") + ":"}
            name="newsArea"
            onChange={changeNewInfo}
            value={properties.newsArea}
            items={areaList}
          ></CustomSelect>
        </Grid>

        <Grid
          sx={{
            display: "flex",
            alignItems: "center",
            mt: 1,
          }}
        >
          <CustomSelect
            label={message("editor_newsType") + ":"}
            name="newsType"
            onChange={changeNewInfo}
            value={properties.newsType}
            items={newsTypeList}
          ></CustomSelect>
        </Grid>

        <Grid
          sx={{
            display: "flex",
            alignItems: "center",
            mt: 1,
          }}
        >
          <CustomSelect
            label={message("editor_languageType") + ":"}
            name="languageType"
            onChange={changeNewInfo}
            value={properties.languageType}
            items={languageList}
          ></CustomSelect>
        </Grid>

        <Grid
          sx={{
            display: "flex",
            alignItems: "center",
            mt: 1,
          }}
        >
          <CustomSelect
            label={message("editor_fontSize") + ":"}
            name="fontSize"
            onChange={changeProperties}
            value={properties.fontSize}
            items={fontSizeList}
          ></CustomSelect>
        </Grid>

        <Grid
          sx={{
            display: "flex",
            alignItems: "center",
            mt: 1,
          }}
        >
          <FormLabel sx={{ mr: 2 }}>{message("editor_fontColor")}:</FormLabel>
          <ColorPick
            value={properties.fontColor}
            name="fontColor"
            onChange={changeProperties}
          ></ColorPick>
        </Grid>
        <Grid
          sx={{
            mt: 1,
          }}
        >
          <CustomSelect
            label={message("editor_scrollDirection") + ":"}
            name="scrollDirection"
            onChange={changeProperties}
            value={properties.scrollDirection}
            items={dictScrollDirection}
          ></CustomSelect>
        </Grid>

        <Grid>
          <CustomSelect
            label={message("editor_scrollSpeed") + ":"}
            name="speed"
            onChange={changeProperties}
            value={properties.speed}
            items={dictScrollSpeed}
          ></CustomSelect>
        </Grid>

        {/* <Grid
          sx={{
            display: "flex",
            alignItems: "center",
            mt: 1,
          }}
        >
          <FormLabel sx={{ mr: 2 }}> {message("editor_rotate")}:</FormLabel>
          <PrettoSlider
            onChange={handleRotationChange}
            size="small"
            min={0}
            max={360}
            step={1}
            color="secondary"
            value={properties.rotate}
            aria-label="Small"
          ></PrettoSlider>
        </Grid> */}

        <Grid
          sx={{
            display: "flex",
            alignItems: "center",
            mt: 1,
          }}
        >
          <FormLabel sx={{ mr: 2 }}>
            {" "}
            {message("editor_diaphaneity")}:
          </FormLabel>
          <PrettoSlider
            onChange={handleSliderChange}
            size="small"
            min={0}
            max={1}
            step={0.1}
            color="secondary"
            value={properties.transparency}
            defaultValue={properties.transparency}
            aria-label="Small"
          ></PrettoSlider>
        </Grid>

        <CustomInput
          label={message("editor_abscissa") + ":"}
          value={properties.left}
          onChange={changeProperties}
          name="left"
        ></CustomInput>

        <CustomInput
          label={message("editor_ordinate") + ":"}
          value={properties.top}
          onChange={changeProperties}
          name="top"
        ></CustomInput>

        <CustomInput
          label={message("editor_width") + ":"}
          value={properties.width}
          onChange={changeProperties}
          name="width"
        ></CustomInput>

        <CustomInput
          label={message("editor_height") + ":"}
          value={properties.height}
          onChange={changeProperties}
          name="height"
        ></CustomInput>
      </Grid>
    </Grid>
  );
};

export default NewsComponent;
