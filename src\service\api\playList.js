import request from '@/utils/request';

const baseProfixURI = '/sd/v1/playlist';

/**
 * 新增播放清单
 * @param {} params
 * @returns
 */
export const savePlayList = (params) => {
    return request({
        url: `${baseProfixURI}`,
        method: 'post',
        data: params
    });
};

/**
 *  获取播放清单列表
 * <AUTHOR>
 * @date 2023-03-27 15:29
 */
export const listPlayListByPage = (params) => {
    return request({
        url: `${baseProfixURI}/page`,
        method: 'get',
        params: params
    });
};

/**
 *  删除播放清单
 * @param ids long 类型id用逗号隔开
 * <AUTHOR>
 * @date 2023-03-27 15:29
 */
export const removePlayList = (ids) => {
    return request({
        url: `${baseProfixURI}/${ids}`,
        method: 'delete'
    });
};

/**
 *  获取播放清单详情
 * @param id
 * <AUTHOR>
 * @date 2023-03-27 15:29
 */
export const getPlayListInfo = (id) => {
    return request({
        url: `${baseProfixURI}/${id}`,
        method: 'get'
    });
};

/**
 * 更新播放清单
 * @param {} params
 * @returns
 */
export const updatePlayList = (params) => {
    return request({
        url: `${baseProfixURI}`,
        method: 'PUT',
        data: params
    });
};
