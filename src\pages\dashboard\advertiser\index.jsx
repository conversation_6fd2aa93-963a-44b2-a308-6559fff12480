/* eslint-disable no-undef */
/* eslint-disable react/prop-types */
/* eslint-disable react-hooks/rules-of-hooks */
import { useEffect, useState, useRef } from "react";
// import { useLocation } from "react-router-dom";
import { Grid, Typography, Stack, Card, CircularProgress } from "@mui/material";
import TableList from "./tableList";
// import ChangePasswordTip from "../authentication/auth-forms/ChangePasswordTip";

import MapChart from "../googleMap/Map"; //谷歌地图组件
import CitySelect from "../googleMap/CitySelect"; //谷歌地图 城市选择
import MapInput from "../googleMap/GoogleMaps"; //谷歌地图 搜索框

import BaiduMapChart from "../baiduMap/MapChart"; //百度地图
import BaiduCitySelect from "../baiduMap/CitySelect"; //百度地图城市选择
import BaiduMapTextAutoComplete from "../baiduMap/mapTextAutoComplete"; //百度地图搜索框

import {
  countMaterialStatistics,
  getMaterialDownloadStatistics,
} from "@/service/api/material";

import useLoading from "@/hooks/useLoading";
import { useTranslation } from "react-i18next";
const AdvertiserDashboard = () => {
  const { t } = useTranslation();
  const [loading, toggleLoading] = useLoading();
  //禁止双击左侧菜单栏重新获取地图数据
  // const { state } = useLocation();
  const mapRef = useRef(null);
  const searchRef = useRef(null); //百度地图引用
  //下拉框选中的地址
  const [selectAddress, setSelectAddress] = useState("");
  //输入框选择的地址
  const [inputAddress, setInputAddress] = useState("");
  const [materialData, setMaterialData] = useState({});
  const [materialDownloadData, setMaterialDownloadData] = useState({});
  const [screenStatusList, setScreenStatusList] = useState({});
  const [cash, setCash] = useState(""); //流量使用情况
  const [detailAddress, setDetailAddress] = useState("");
  const [addressData, setAddressData] = useState("");
  const [zoom, setZoom] = useState(4); //谷歌地图画面比例
  // const [zoom, setZoom] = useState(5); //百度地图画面比例
  const [center, setCenter] = useState({});
  const [code, setCode] = useState(undefined);
  const [centerPosition, setCenterPosition] = useState([103.73, 36.03]);
  const [centerData, setCenterData] = useState(null);
  const [coordinates, setCoordinates] = useState({
    lat: 34.932032320983154,
    lng: 103.8510534864727,
  }); //初始化经纬度

  /**
   * @param {*} detailsAddress 省+市+区/县
   * @param {*} zoom  区域的邮政编码
   * @param {*} address 省/市/区
   * 将选中的地址转换为经纬度坐标
   */
  //谷歌地图搜索方法
  const handleSearchGoogle = async (detailsAddress, zoom, address) => {
    setDetailAddress(detailsAddress);
    // 调用百度地图 API 的 geocoder.geocode 方法将地址信息转换为经纬度坐标
    const geocoder = new google.maps.Geocoder();
    geocoder.geocode({ address: address }, function (results, status) {
      if (results) {
        // 获取第一个结果的经纬度坐标
        var location = results[0].geometry.location;
        const selectedLatLng = {
          lat: location.lat(), // 纬度
          lng: location.lng(), // 经度
        };
        setCoordinates(selectedLatLng);
      } else {
        console.error("地理编码失败：" + status);
      }
    });
  };
  // googleMap 处理从搜索框组件获取的经纬度
  const handleCoordinatesChange = (newCoordinates) => {
    setCenterData(newCoordinates);
  };
  //获取项目地址名称
  const handlerDetailAddress = (newDetailAddress) => {
    setAddressData(newDetailAddress);
  };

  //获取当前位置
  const getNowLocation = () => {
    return new Promise((resolve, reject) => {
      const geolocation = new BMapGL.Geolocation();
      geolocation.getCurrentPosition(function (r) {
        if (this.getStatus() === BMAP_STATUS_SUCCESS) {
          setCode(r.address.city);
          resolve(r.point);
        } else {
          toast.error(t("common.common_current_location_error"));
          reject(t("common.common_current_location_error"));
        }
      });
    });
  };
  // 根据坐标得到地址描述
  const Geolocation = async (lng, lat) => {
    return new Promise((resolve, reject) => {
      const myGeo = new BMapGL.Geocoder();
      // 根据坐标得到地址描述
      myGeo.getLocation(new BMapGL.Point(lng, lat), function (result) {
        if (result) {
          resolve(result);
        } else {
          reject(null);
        }
      });
    });
  };

  //百度地图搜索方法
  const handleSearch = (detailsAddress, zoom, address) => {
    // 调用百度地图 API 的 geocoder.geocode 方法将地址信息转换为经纬度坐标
    const geocoder = new BMapGL.Geocoder();
    geocoder.getPoint(
      detailsAddress,
      (point) => {
        if (point) {
          setCenter({
            lng: point.lng,
            lat: point.lat,
          });
          let zoom = 8;
          // console.log(address.indexOf("区"));
          if (address.indexOf("市") > 0) {
            zoom = 10;
          } else if (address.indexOf("区") > 0 || address.indexOf("县") > 0) {
            zoom = 14;
          }
          setCenterPosition([point.lng, point.lat]);
          setZoom(zoom);
          // mapRef.current.setCenterPositionRef([point.lng, point.lat], zoom);
          if (!(address.indexOf("区") > 0 || address.indexOf("县") > 0)) {
            setCode(address);
          }
        }
      },
      detailsAddress
    );
  };

  //选中下拉框选中的地址
  const handleChangeSelectAddress = (address) => {
    // console.log("address6666", address);
    setSelectAddress(address);
  };

  //修改输入框选择的地址
  const handleTextOnChange = () => {
    setInputAddress(searchRef.current.value);
  };

  //百度地图模糊搜索
  const handleChildValueChange = (newValue) => {
    getNowLocation(newValue).then((point) => {
      setCenter(point);
    });

    // 在子组件的回调函数中获取子组件的值
    const geocoder = new BMapGL.Geocoder();
    geocoder.getPoint(newValue, (point) => {
      if (point) {
        // mapRef.current.setCenterPositionRef([point.lng, point.lat], zoom);
        setCenterPosition([point.lng, point.lat]);
        setZoom(16);
      }
    });
  };
  useEffect(() => {
    const fetchDataAndCloseLoading = async () => {
      // 假设数据渲染需要一秒钟
      setTimeout(() => {
        toggleLoading(); // 关闭加载状态
      }, 1000);
    };
    fetchDataAndCloseLoading();
  }, []);

  // 加载图标的样式
  const loadingStyle = {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    height: "calc(100vh - 200px)",
  };
  return (
    <div style={loading ? loadingStyle : null}>
      {loading ? (
        <CircularProgress />
      ) : (
        <>
          <Grid item xs={12}>
            {/* 谷歌地图集成 */}

            {/* 百度地图集成 */}
            {
              window.localStorage.getItem("localVersion")
                === 'CN' ? <Card sx={{ mt: -1 }}>
                <Grid container columnSpacing={2.75}>
                  <Grid item xs={6.3}></Grid>
                  <Grid
                    item
                    xs={12}
                    container
                    direction="row"
                    justifyContent="flex-end"
                    alignItems="flex-end"
                  >
                    <BaiduCitySelect
                      search={handleSearch}
                      changeSelectAddress={handleChangeSelectAddress}
                    />

                    <BaiduMapTextAutoComplete
                      code={code}
                      onChildValueChange={handleChildValueChange}
                      detailAddress={detailAddress}
                      placeholder={t("common.common_input_location_search")}
                      sx={{
                        height: "41px",
                        width: "100%",
                        ".MuiAutocomplete-endAdornment": { top: 0 },
                      }}
                    />
                  </Grid>
                </Grid>
                <BaiduMapChart
                  ref={mapRef}
                  centerPosition={centerPosition}
                  setCenterPosition={setCenterPosition}
                  zoom={zoom}
                  setZoom={setZoom}
                />
              </Card> : <Card sx={{ mt: -1 }}>
                <Grid container>
                  <Grid
                    item
                    xs={12}
                    container
                    direction="row"
                    justifyContent="flex-end"
                    alignItems="flex-end"
                  >
                    {/* <CitySelect
                    setZoom={setZoom}
                    search={handleSearchGoogle}
                    changeSelectAddress={handleChangeSelectAddress}
                  ></CitySelect> */}

                    <MapInput
                      coordinates={coordinates}
                      setCoordinates={setCoordinates}
                      setZoom={setZoom}
                      detailAddress={detailAddress}
                      onCoordinatesChange={handleCoordinatesChange}
                      onDetailAddress={handlerDetailAddress}
                    ></MapInput>
                  </Grid>
                </Grid>
                <MapChart
                  ref={mapRef}
                  centerData={centerData}
                  coordinates={coordinates}
                  setCoordinates={setCoordinates}
                  zoom={zoom}
                  setZoom={setZoom}
                  addressData={addressData}
                />
              </Card>
            }
          </Grid>

          <Grid
            container
            direction="row"
            justifyContent="flex-start"
            alignItems="flex-start"
            columnSpacing={3}
          >
            <Grid item xs={12}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Stack sx={{ marginTop: "30px" }}>
                    <Typography variant="h5" gutterBottom>
                      {t("ips.ips_real_playback")}
                    </Typography>
                    <TableList />
                  </Stack>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </>
      )}
      {/* {null === state ? null : <ChangePasswordTip from={state.from} />} */}
    </div>
  );
};

export default AdvertiserDashboard;
