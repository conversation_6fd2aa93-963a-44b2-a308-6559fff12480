/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable react/prop-types */
import MainCard from "@/components/MainCard";
import React, { useEffect, useMemo, useState, useRef } from "react";
import MaterialReactTable from "material-react-table";
import LoadingButton from "@mui/lab/LoadingButton";
import BasicDateRangePicker from "@/components/datetimePicker";
import * as Yup from "yup";
import {
  Button,
  Stack,
  Typography,
  Box,
  Link,
  Grid,
  TextField,
  IconButton,
  Alert,
  Checkbox,
  RadioGroup,
  FormGroup,
  FormControlLabel,
  Radio,
  InputLabel,
  FormHelperText,
} from "@mui/material";

import {
  BootstrapDialog,
  BootstrapDialogTitle,
  BootstrapContent,
  BootstrapActions,
} from "@/components/dialog";
import AuthButton from "@/components/AuthButton";
// api
import { listByPage, exportData } from "@/service/api/adverStatistics";
import { tableI18n } from "@/utils/tableLang";
import Dot from "@/components/@extended/Dot";
// i18n
import { useTranslation } from "react-i18next";
import DictTag from "@/components/DictTag";
import { operLogStatus } from "@/dict/commonDict";
import { useConfirm } from "@/components/zkconfirm";
import WarningIcon from "@mui/icons-material/Warning";
import { toast } from "react-toastify";
import { useFormik } from "formik";
import ZKSelect from "@/components/ZKSelect";
import { removeEmpty } from "@/utils/StringUtils";
import { getScreenListByStoreId } from "@/service/api/screen.js";
import { blobValidate, download } from "@/utils/downloadFile";

export default function tableList() {
  const { t } = useTranslation();
  const confirm = useConfirm();
  // 查询参数
  const requestParams = useRef(null);
  const [isError, setIsError] = useState(false);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  // 表格数据
  const [data, setData] = useState([]);
  // 分页的参数
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  const [merchants, setMerchants] = useState([]);
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);

  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);

  //获取广告商列表
  const getMerchant = () => {
    // getScreenListByStoreId("1").then((res) => {
    //   setMerchants(res.data);
    // });
  };

  useEffect(() => {
    getMerchant();
  }, []);
  // 构建参数
  const buildParams = () => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      ...requestParams.current,
    };
    return params;
  };
  // 获取数据
  const getTableData = async () => {
    if (!data.length) {
      setIsLoading(true);
    } else {
      setIsRefetching(true);
    }
    // // 开启加载
    // setIsLoading(true);
    // setIsRefetching(true);
    await listByPage(buildParams())
      .then((res) => {
        // 设置数据
        setData(res.data.data);
        // 设置总记录数
        setRowCount(res.data.total);
        setIsLoading(false);
        setIsRefetching(false);
      })
      .catch((err) => {
        setIsError(true);
        setIsLoading(false);
        setIsRefetching(false);
      });
  };
  useEffect(() => {
    // const params = buildParams();
    // 发请求
    getTableData();
  }, [pagination.pageIndex, pagination.pageSize]);
  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "materialName", //access nested data with dot notation
        header: t("adverStatistics.materialName"),
        enableColumnActions: false,
        enableSorting: false,
      },
      {
        accessorKey: "materialGroupName", //access nested data with dot notation
        header: t("adverStatistics.materialGroupName"),
        enableColumnActions: false,
        enableSorting: false,
      },
      {
        accessorKey: "outletName", //access nested data with dot notation
        header: t("adverStatistics.storeName"),
        enableColumnActions: false,
        enableSorting: false,
      },
      {
        accessorKey: "deviceName", //access nested data with dot notation
        header: t("adverStatistics.screenName"),
        enableColumnActions: false,
        enableSorting: false,
      },
      {
        accessorKey: "principalName", //access nested data with dot notation
        header: t("adverStatistics.merchantName"),
        enableColumnActions: false,
        enableSorting: false,
      },
      {
        accessorKey: "playCount", //access nested data with dot notation
        header: t("adverStatistics.playCount"),
        enableColumnActions: false,
        enableSorting: false,
      },

      {
        accessorKey: "countTime", //access nested data with dot notation
        header: t("adverStatistics.countTime"),
        enableColumnActions: false,
        enableSorting: false,
      },
    ],
    []
  );

  // 查询表单
  const queryFormik = useFormik({
    initialValues: {
      deviceName: "",
      outletName: "",
      materialName: "",
      groupName: "",
      principalName: "",
    },
    onSubmit: async (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        const tempValue = { ...values };
        await removeEmpty(tempValue);
        requestParams.current = tempValue;
        getTableData();
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
  });
  // 重置查询
  const resetQuery = () => {
    // 重置表单
    queryFormik.resetForm();
    requestParams.current = null;
    getTableData();
  };

  const handleClose = () => {
    setOpen(false);
  };

  // 查询表单
  const exportFormik = useFormik({
    initialValues: {
      merchantId: "",
      startTime: "",
      endTime: "",
    },
    onSubmit: (values, { setErrors, setStatus, setSubmitting }) => {
      setLoading(true);

      return new Promise((resolve, reject) => {
        download(
          "/material/report/exportReport",
          values,
          `${t("adverStatistics.exportName", {
            startTime: values.startTime,
            endTime: values.endTime,
          })}.xlsx`
        )
          .then(() => {
            resolve();
            setOpen(false);
            setLoading(false);
          })
          .catch(() => {
            reject();
          })
          .finally(() => {
            setLoading(false);
          });
      });
    },
    validationSchema: Yup.object().shape({
      merchantId: Yup.string().required(t("common.common_select_retail")),
      startTime: Yup.string().required(t("common.common_select_startTime")),
      endTime: Yup.string().required(t("common.common_select_endTime")),
    }),
  });

  const handleOpenExport = () => {
    exportFormik.resetForm();
    setOpen(true);
  };

  const handleExport = () => {
    exportFormik.handleSubmit();
  };

  return (
    <>
      <MainCard style={{ marginBottom: "10px" }}>
        <form noValidate onSubmit={queryFormik.handleSubmit}>
          <Grid
            container
            direction="row"
            justifyContent="flex-start"
            alignItems="center"
            spacing={3}>
            <Grid item xs={3} sm={4} md={2}>
              <TextField
                label={t("adverStatistics.screenName")}
                value={queryFormik.values.deviceName}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                size="small"
                type="text"
                name="deviceName"
                fullWidth
                placeholder={t("common.common_please_input")}
              />
            </Grid>

            <Grid item xs={3} sm={4} md={2}>
              <TextField
                label={t("adverStatistics.storeName")}
                value={queryFormik.values.outletName}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                size="small"
                type="text"
                name="outletName"
                fullWidth
                placeholder={t("common.common_please_input")}
              />
            </Grid>

            <Grid item xs={3} sm={4} md={2}>
              <TextField
                label={t("adverStatistics.materialName")}
                value={queryFormik.values.materialName}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                size="small"
                type="text"
                name="materialName"
                fullWidth
                placeholder={t("common.common_please_input")}
              />
            </Grid>

            <Grid item xs={3} sm={4} md={2}>
              <TextField
                label={t("adverStatistics.materialGroupName")}
                value={queryFormik.values.groupName}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                size="small"
                type="text"
                name="groupName"
                fullWidth
                placeholder={t("common.common_please_input")}
              />
            </Grid>

            <Grid item xs={3} sm={4} md={2}>
              <TextField
                label={t("adverStatistics.merchantName")}
                value={queryFormik.values.principalName}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                size="small"
                type="text"
                name="principalName"
                fullWidth
                placeholder={t("common.common_please_input")}
              />
            </Grid>

            <Grid item xs={2}>
              <Stack
                direction="row"
                justifyContent="flex-start"
                alignItems="flex-start"
                spacing={2}>
                <Button
                  disableElevation
                  type="submit"
                  variant="contained"
                  size="small">
                  {t("common.common_table_query")}
                </Button>
                <Button
                  variant="outlined"
                  onClick={resetQuery}
                  color="info"
                  size="small"
                  sx={{
                    minWidth: "90px",
                  }}>
                  {t("common.common_op_reset")}
                </Button>
              </Stack>
            </Grid>
          </Grid>
        </form>
      </MainCard>
      <MaterialReactTable
        // table 状态
        state={{
          // 加载状态
          isLoading,
          // 分页参数
          pagination,
          // 重新拉取
          showProgressBars: isRefetching,
          showAlertBanner: isError,
        }}
        renderToolbarInternalActions={({ table }) => (
          <>
            <AuthButton button="sd:report:statistics:export:material_statistics">
              <Button variant="outlined" onClick={handleOpenExport}>
                {t("common.common_export_btn")}
              </Button>
            </AuthButton>
          </>
        )}
        muiTablePaperProps={{
          elevation: 0,
          sx: {
            borderRadius: "5px",
            border: "1px solid #f0f0f0",
          },
        }}
        muiTableHeadRowProps={{
          sx: { backgroundColor: "#fafafa", boxShadow: "none" },
        }}
        // 布局方式
        layoutMode="grid"
        // 开启列对齐
        muiTableHeadCellProps={{
          sx: {
            "& .Mui-TableHeadCell-Content": {
              justifyContent: "space-between",
            },
          },
        }}
        // 解决列太多宽度太长问题
        enableColumnResizing
        // enablePinning
        // 初始化状态
        initialState={{ columnVisibility: { createTime: true } }}
        muiToolbarAlertBannerProps={
          isError
            ? {
                color: "error",
                children: "Error loading data",
              }
            : undefined
        }
        muiTableBodyRowProps={({ row }) => ({
          onClick: row.getToggleSelectedHandler(),
          sx: { cursor: "pointer" },
        })}
        // 列数
        rowCount={rowCount}
        // 固定头部
        enableStickyHeader
        // 处理表格高度
        muiTableContainerProps={{ sx: { maxHeight: "620px" } }}
        // 设置背景颜色
        muiTableBodyCellProps={{ sx: { backgroundColor: "white" } }}
        muiTableProps={{
          sx: { backgroundColor: "white", tableLayout: "fixed" },
        }}
        muiBottomToolbarProps={{ sx: { backgroundColor: "white" } }}
        muiTopToolbarProps={{ sx: { backgroundColor: "white" } }}
        // 分页回调函数
        onPaginationChange={setPagination}
        // 记得开启这些 tips:否则会出现useEffect 反作用函数发起多次请求点击下一页会出问题
        manualFiltering
        manualPagination
        manualSorting
        // 开启分页
        enablePagination
        // 列定义
        columns={columns}
        // 数据
        data={data}
        localization={tableI18n}
        positionToolbarAlertBanner="none"
      />

      <BootstrapDialog
        fullWidth
        onClose={handleClose}
        aria-labelledby="customized-dialog-title"
        open={open}>
        <BootstrapDialogTitle>
          <Typography variant="h4" component="p">
            {t("adverStatistics.exportStatistic")}
          </Typography>
        </BootstrapDialogTitle>
        <BootstrapContent dividers>
          <Grid>
            <form noValidate onSubmit={exportFormik.handleSubmit}>
              <Grid container spacing={3}>
                <Grid item xs={6}>
                  <Stack spacing={1}>
                    <InputLabel htmlFor="merchantId">
                      {t("common.common_brand")}
                      <i style={{ color: "red" }}>*</i>
                    </InputLabel>
                    <ZKSelect
                      name="merchantId"
                      placeholder={t("common.common_select_retail")}
                      options={merchants}
                      value={exportFormik.values.merchantId}
                      onClear={() =>
                        exportFormik.setFieldValue("merchantId", "")
                      }
                      onChange={exportFormik.handleChange}
                      onBlur={exportFormik.handleBlur}
                      error={Boolean(
                        exportFormik.touched.merchantId &&
                          exportFormik.errors.merchantId
                      )}
                    />
                    {exportFormik.touched.merchantId &&
                      exportFormik.errors.merchantId && (
                        <FormHelperText error id="merchantId-error">
                          {exportFormik.errors.merchantId}
                        </FormHelperText>
                      )}
                  </Stack>
                </Grid>
              </Grid>

              <Grid
                sx={{
                  mt: 4,
                }}>
                <Grid item xs={6}>
                  <Stack spacing={1}>
                    <InputLabel htmlFor="merchantId">
                      {t("adverStatistics.dateRange")}
                      <i style={{ color: "red" }}>*</i>
                    </InputLabel>

                    <BasicDateRangePicker
                      view={["year", "day"]}
                      format="YYYY-MM-DD"
                      onChange={(value) => {
                        if (value?.startTime && value?.endTime) {
                          if (
                            value.startTime === "Invalid Date" ||
                            value.endTime === "Invalid Date"
                          ) {
                            exportFormik.setValues({
                              startTime: "",
                              endTime: "",
                            });
                            return;
                          }
                          exportFormik.setFieldValue(
                            "startTime",
                            value?.startTime
                          );
                          exportFormik.setFieldValue("endTime", value?.endTime);
                        }
                      }}
                    />
                  </Stack>

                  <Grid container spacing={3}>
                    <Grid item xs={4}>
                      {exportFormik.touched.startTime &&
                        exportFormik.errors.startTime && (
                          <FormHelperText error id="merchantId-error">
                            {exportFormik.errors.startTime}
                          </FormHelperText>
                        )}
                    </Grid>

                    <Grid item xs={4}>
                      {exportFormik.touched.endTime &&
                        exportFormik.errors.endTime && (
                          <FormHelperText error id="merchantId-error">
                            {exportFormik.errors.endTime}
                          </FormHelperText>
                        )}
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </form>
          </Grid>
        </BootstrapContent>
        <BootstrapActions>
          <Button color="info" variant="outlined" onClick={handleClose}>
            {t("common.common_edit_cancel")}
          </Button>
          <LoadingButton
            disableElevation
            loading={loading}
            type="submit"
            variant="contained"
            color="primary"
            onClick={handleExport}>
            {t("common.common_export_btn")}
          </LoadingButton>
        </BootstrapActions>
      </BootstrapDialog>
    </>
  );
}
