/* eslint-disable react-hooks/rules-of-hooks */
import React, { useEffect, useState, useMemo, useRef } from "react";
import Box from "@mui/material/Box";
import {
  TextField,
  FormControl,
  OutlinedInput,
  IconButton,
  InputAdornment,
} from "@mui/material";
import Autocomplete from "@mui/material/Autocomplete";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import parse from "autosuggest-highlight/parse";
import { debounce } from "@mui/material/utils";
import SearchIcon from "@mui/icons-material/Search";
import { getAddress } from "@/service/api/bmap";
import { useTranslation } from "react-i18next";
const mapTextAutoComplete = (props) => {
  const { code, detailAddress } = props;
  const { t } = useTranslation();
  const [value, setValue] = useState(null);
  const [inputValue, setInputValue] = useState("");
  const [options, setOptions] = useState([]);

  const loaded = useRef(false);
  const autocompleteService = useRef(null);

  const fetch = React.useMemo(
    () =>
      debounce((request, callback) => {
        autocompleteService.current(request, callback);
      }, 400),
    []
  );
  useEffect(() => {
    let active = true;
    if (!autocompleteService.current) {
      autocompleteService.current = (request, callback) => {
        getAddress(request.query, request.region)
          .then((res) => {
            callback(res.data);
          })
          .catch(function (error) {
            // callback([]);
            console.log(error);
          });
      };
    }

    if (inputValue === "") {
      setOptions(value ? [value] : []);

      return undefined;
    }

    // let requestUrl = `/baidu/?query=${inputValue}&region=${props.code}&city_limit=false&output=json&ak=APeE0SHYyqFcNp2bRlrSiKniDUZDaTMA`;
    fetch({ query: inputValue, region: code }, (results) => {
      setOptions(results);
      // 找到 "市" 的位置索引
      const cityProvince = code.indexOf("省");
      const cityIndex = code.indexOf("市");
      // 根据 "市" 的位置截取出市的名称
      const cityData =
        cityIndex !== -1 ? code.substring(cityProvince + 1, cityIndex + 1) : "";
      if (active) {
        const filterData = results.filter((item) => item.city === cityData);
        setOptions(filterData);
      }
    });
    return () => {
      active = false;
    };
  }, [value, inputValue, fetch]);

  const handlerAutocompleteChange = (event, newValue) => {
    setOptions(newValue ? [newValue, ...options] : options);
    setValue(newValue);
    // setSelectedOption(newValue); // 更新选中的数据
    setInputValue(newValue ? newValue.name : ""); // Update inputValue
  };
  const [childAddress, setChildAddress] = useState("");
  const handleSearch = (value) => {
    const address = code + value;
    setChildAddress(address);
    props.onChildValueChange(address);
  };

  return (
    <Autocomplete
      id="baidu-map-search"
      sx={props.sx}
      label={t("ips.ips_enter_location_address")}
      disablePortal
      disableClearable //清除按钮
      freeSolo
      getOptionLabel={(option) =>
        typeof option === "string" ? option : option.name
      }
      style={{ width: "325px", marginTop: "-20px" }}
      filterOptions={(x) => x}
      options={options}
      autoComplete
      includeInputInList
      filterSelectedOptions
      value={value}
      size="small"
      noOptionsText={t("common.common_input_location_search")}
      onChange={(event, newValue) => {
        handlerAutocompleteChange(event, newValue);
        setInputValue(newValue ? newValue.name : ""); // Update inputValue
      }}
      onInputChange={(event, newInputValue) => {
        // 出发搜索
        setInputValue(newInputValue);
        setValue(newInputValue);
      }}
      renderInput={(params) => (
        <TextField
          placeholder={props.placeholder}
          sx={{ width: 325, marginTop: -1,label:{marginTop:0.25}}}
          {...params}
          label={t("ips.ips_enter_location_address")}
          InputProps={{
            ...params.InputProps,
            type: "search",
            endAdornment: (
              <InputAdornment position="end">
                <IconButton onClick={() => handleSearch(inputValue)}>
                  <SearchIcon />
                </IconButton>
              </InputAdornment>
            ),
          }}
        />
      )}
      renderOption={(props, option) => {
        return (
          <li {...props}>
            <Grid container alignItems="center">
              <Grid item sx={{ display: "flex", width: 44 }}>
                <LocationOnIcon sx={{ color: "text.secondary" }} />
              </Grid>
              <Grid
                item
                sx={{ width: "calc(100% - 44px)", wordWrap: "break-word" }}
              >
                <Box component="span" sx={{ fontWeight: "bold" }}>
                  {option.name}
                </Box>
              </Grid>
            </Grid>
          </li>
        );
      }}
    />
  );
};
export default mapTextAutoComplete;
