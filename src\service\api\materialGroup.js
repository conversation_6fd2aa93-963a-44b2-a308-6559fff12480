import request from "@/utils/request";

const baseProfixURI = "/sd/v1/material/group";


export const listTree = (params) => {
  return request({
    url: `${baseProfixURI}/tree`,
    method: "get",
    params: params,
  });
};
export const getTreeSelect = (params) => {
  return request({
    url: `${baseProfixURI}/select`,
    method: "get",
    params: params,
  });
};
export const saveGroup = (params) => {
  return request({
    url: `${baseProfixURI}`,
    method: "POST",
    data: params,
  });
};
export const deleteGroup = (ids = []) => {
  return request({
    url: `${baseProfixURI}/delete/${ids}`,
    method: "DELETE",
  });
};
export const getGroup = (id) => {
  return request({
    url: `${baseProfixURI}/${id}`,
    method: "GET",
  });
};
export const updateGroup = (params) => {
  return request({
    url: `${baseProfixURI}`,
    method: "PUT",
    data: params,
  });
};

export const getMateriaGroupOptions = () => {
  return request({
    url: `${baseProfixURI}/option`,
    method: "GET",
  });
};

export const getMateriaGroupOptionsByMerchant = (merchantId) => {
  return request({
    url: `${baseProfixURI}/optionByMerchant/${merchantId}`,
    method: "GET",
  });
};
