import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { setUserInfor, clearUser, setPermission } from '@/store/reducers/user';
export const useStateUserInfo = () => useSelector((store) => store.user.userInfor);
export const useStatePermission = () => useSelector((store) => store.user.permission);
export function useDispatchUser() {
    const dispatch = useDispatch();
    const stateSetUser = useCallback((userInfor) => dispatch(setUserInfor(userInfor)), [dispatch]);
    const stateClearUser = useCallback(() => dispatch(clearUser()), [dispatch]);
    const stateSetPermission = useCallback((permission) => dispatch(setPermission(permission)), [dispatch]);
    return { stateSetUser, stateClearUser, stateSetPermission };
}
