import React from "react";
import { Grid, TablePagination } from "@mui/material";
import { styled } from "@mui/material/styles";
import { useState } from "react";
import { FormLabel } from "./PropertiesComponent";
import CustomInput from "../components/CustomInput";
import { useConfirm } from "@/components/zkconfirm";
import { useEffect } from "react";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import { getResource } from "@/service/api/layout";
import ImageIcon from "@mui/icons-material/Image";
import ColorPick from "../components/ColorPick";
import ClearIcon from "@mui/icons-material/Clear";
import { isPositiveInteger } from "../common/utils";
import { toast } from "react-toastify";
import { message } from "../common/i18n";

const BootstrapDialog = styled(Dialog)(({ theme }) => ({
  "& .MuiDialogContent-root": {
    padding: theme.spacing(2),
  },
  "& .MuiDialogActions-root": {
    padding: theme.spacing(1),
  },
}));

const SceneComponent = (props) => {
  const currentPageIndex = props.currentPageIndex;
  const pages = props.pages;
  const [properties, setProperties] = useState({
    title: message("editor_scene"),
    duration: 10,
    bgColor: "#ffffff",
    imgId: null,
    bgImg: "",
    datasetId: "",
    datasetName: "",
    checksum: "",
    datasetFields: "",
    isTemplate: false,
    componentList: [],
  });

  useEffect(() => {
    if (currentPageIndex !== "") {
      let componentInfo = pages[currentPageIndex];
      setProperties({
        ...componentInfo,
      });
    }
  }, [currentPageIndex, pages]);

  const [imageList, setImageList] = useState([]);
  const [open, setOpen] = useState(false);
  const [imgOptions, setImgOptions] = useState({
    current: 0,
    size: 6,
    total: 0,
    sizes: [6, 10, 20, 40, 100],
  });

  const [pageNumber, setPageNumber] = useState(0);
  const [pageSize, setPageSize] = useState(10);

  const getImageList = (page) => {
    let currentPage = page === undefined ? imgOptions.current + 1 : page + 1;
    getResource({
      page: currentPage,
      pageSize: imgOptions.size,
      showAudited: true,
      type: "image",
      status: 2,
    }).then((res) => {
      let resData = res.data;
      let list = resData.data.map((it) => {
        it.url = it.downloadUrl;
        return it;
      });
      setImageList(list);
      setImgOptions({
        ...imgOptions,
        current: currentPage - 1,
        total: resData.total,
      });
    });
  };

  useEffect(() => {
    getImageList();
  }, [pageSize, pageNumber]);

  useEffect(() => {
    setPageNumber(imgOptions.current);
    setPageSize(imgOptions.size);
  }, [imgOptions]);

  const setComponentInfo = (baseInfo) => {
    const newPages = JSON.parse(JSON.stringify(props.pages));
    let oldInfo = newPages[currentPageIndex];
    let newInfo = {
      ...oldInfo,
      ...baseInfo,
    };
    newPages[currentPageIndex] = newInfo;
    if (props.setPages) {
      props.setPages(newPages);
    }
  };

  const changeProperties = (event) => {
    const name = event.target.name;
    let newInfo = {
      ...properties,
      [name]: event.target.value,
    };

    setComponentInfo(newInfo);
  };

  const handleChangePage = (e, v) => {
    setImgOptions({
      ...imgOptions,
      current: v,
    });
  };

  const handleChangeRowsPerPage = (e, v) => {
    let value = e.target.value;
    setImgOptions({
      ...imgOptions,
      current: 0,
      size: value,
    });
  };

  const handleClose = () => {
    setOpen(false);
  };

  const addImage = (item) => {
    setProperties({
      ...properties,
      imgId: item.id,
      bgImg: item.downloadUrl,
      checksum: item.checksum,
    });
    setComponentInfo({
      imgId: item.id,
      bgImg: item.downloadUrl,
      checksum: item.checksum,
    });
    setOpen(false);
  };

  const changeDuration = (event) => {
    let value = event.target.value;
    if (value === "" || isPositiveInteger(value)) {
      if (value > 999999) {
        value = 999999;
      }
      if (value < 1) {
        value = 1;
      }

      setProperties({
        ...properties,
        duration: value,
      });
      setComponentInfo({
        duration: value,
      });
    } else {
      setProperties({
        ...properties,
        duration: 10,
      });
      setComponentInfo({
        duration: 10,
      });
      toast.error(message("editor_number_error_message"));
    }
  };

  const onBlurFn = (event) => {
    let value = event.target.value;
    if (value === "") {
      setProperties({
        ...properties,
        duration: 10,
      });
      setComponentInfo({
        duration: 10,
      });
      toast.error(message("editor_number_error_message"));
    }
  };

  const clearBg = () => {
    setProperties({
      ...properties,
      imgId: "",
      bgImg: "",
      checksum: "",
    });
    setComponentInfo({
      imgId: "",
      bgImg: "",
      checksum: "",
    });
  };

  return (
    <Grid
      sx={{
        width: "100%",
        boxShadow: "0px 0px 6px #00000029",
        borderRadius: "10px",
        backgroundColor: "#ffffff",
        overflow: "hidden",
        minHeight: "200px",
      }}>
      <Grid
        sx={{
          p: 2,
        }}>
        <CustomInput
          label={message("editor_sceneName") + ":"}
          value={properties.title}
          onChange={changeProperties}
          name="title"></CustomInput>

        <Grid
          sx={{
            display: "flex",
            alignItems: "center",
            mt: 1,
          }}>
          <FormLabel sx={{ mr: 2 }}>{message("editor_bgColor")}:</FormLabel>
          <ColorPick
            value={properties.bgColor}
            name="bgColor"
            onChange={changeProperties}></ColorPick>
        </Grid>

        <Grid
          sx={{
            display: "flex",
            alignItems: "center",
            mt: 2,
          }}>
          <FormLabel sx={{ mr: 2 }}>{message("editor_bgImage")}:</FormLabel>

          {properties.bgImg ? (
            <img
              style={{
                height: "30px",
                width: "30px",
              }}
              src={properties.bgImg}></img>
          ) : (
            <ImageIcon
              onClick={() => {
                setOpen(true);
              }}></ImageIcon>
          )}

          {properties.bgImg && (
            <ClearIcon
              sx={{
                "&:hover": {
                  color: "red",
                  cursor: "pointer",
                },
              }}
              onClick={clearBg}></ClearIcon>
          )}
        </Grid>

        <Grid
          sx={{
            mb: 1,
            mt: 2,
            display: "flex",
            alignItems: "center",
          }}>
          <FormLabel sx={{ mr: 2 }}>{message("editor_duration")}</FormLabel>

          <Grid>
            <CustomInput
              sx={{
                width: "60px",
                marginTop: "0px",
                input: {
                  padding: "5px",
                },
              }}
              type="number"
              value={properties.duration}
              onChange={(e) => {
                changeDuration(e);
              }}
              onBlur={(e) => {
                onBlurFn(e);
              }}
              name="duration"></CustomInput>
          </Grid>
          <FormLabel sx={{ ml: 2 }}>{message("editor_second")}</FormLabel>
        </Grid>
      </Grid>

      <BootstrapDialog
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description">
        <DialogTitle sx={{ m: 0, p: 2 }} id="customized-dialog-title">
          {message("editor_select_image")}
        </DialogTitle>
        <IconButton
          aria-label="close"
          onClick={handleClose}
          sx={{
            position: "absolute",
            right: 8,
            top: 8,
            color: (theme) => theme.palette.grey[500],
          }}>
          <CloseIcon />
        </IconButton>
        <DialogContent dividers>
          <Grid
            sx={{
              minWidth: "700px",
            }}>
            <Grid
              sx={{
                pb: 2,
              }}
              container
              rowSpacing={1}
              columnSpacing={1}>
              {imageList.map((item) => (
                <Grid
                  onClick={() => {
                    addImage(item);
                  }}
                  item
                  xs={4}
                  sx={{
                    height: "100px",
                  }}
                  key={item.id}>
                  <img
                    srcSet={`${item.downloadUrl}`}
                    src={`${item.downloadUrl}`}
                    style={{
                      height: "100%",
                      width: "100%",
                    }}
                    loading="lazy"
                  />
                </Grid>
              ))}
            </Grid>
            <TablePagination
              component="div"
              count={imgOptions.total}
              page={imgOptions.current}
              onPageChange={handleChangePage}
              rowsPerPage={imgOptions.size}
              onRowsPerPageChange={handleChangeRowsPerPage}
              rowsPerPageOptions={imgOptions.sizes}
              labelRowsPerPage={<p>{message("editor_rowsPerPage")}:</p>}
            />
          </Grid>
        </DialogContent>
      </BootstrapDialog>
    </Grid>
  );
};
export default SceneComponent;
