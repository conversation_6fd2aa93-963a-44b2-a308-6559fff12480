import React from 'react'
import { <PERSON>, <PERSON>rid, Divider, Stack } from "@mui/material";
import AppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import { styled } from "@mui/material/styles";
import RenderCommon from './RenderCommon'
const RenderAudio = (props) => {
    const info = props.info
    return <RenderCommon {...props}>
        <>
            <svg
                className="music-icon"
                t="1592532095422"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="2906"
                width="128"
                height="128"
            >
                <path
                    d="M742.3 100.3l-25.6 44.3c126.2 73 204.7 208.9 204.7 354.6 0 225.7-183.6 409.3-409.3 409.3S102.8 724.8 102.8 499.1c0-145.7 78.4-281.5 204.7-354.6l-25.6-44.3c-142 82.1-230.2 235-230.2 398.8 0 253.9 206.6 460.5 460.5 460.5S972.6 753 972.6 499.1c0-163.9-88.2-316.7-230.3-398.8z"
                    fill="#1296db"
                    p-id="2907"
                />
                <path
                    d="M464.2 437l-25.6-44.3c-45.3 26.2-73.5 75-73.5 127.3 0 81 65.9 147 147 147s147-65.9 147-147v-6.3L451.2 115.4h164V64.2H366.8l241 461.8c-3.1 50.1-44.8 89.9-95.6 89.9-52.8 0-95.8-43-95.8-95.8-0.1-34.1 18.2-66 47.8-83.1z"
                    fill="#1296db"
                    p-id="2908"
                />
            </svg>
            <audio autoPlay>
                <source src={info.musicUrl} type="audio/mpeg"/>
                Your browser does not support this audio format.
            </audio>
        </>
    </RenderCommon>
}

export default RenderAudio;