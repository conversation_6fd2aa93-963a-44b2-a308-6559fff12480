export default
{
  "menu": {
    "package_subscription": "Package Subscription",
    "subscription": "Subscription",
    "subscription_records": "Subscription Records"
  },
  "subscription": {
    "subscription": "Subscription",
    "trial_package": "Trial",
    "advanced_package": "Advanced",
    "basic_package": "Starter",
    "includes": "Includes",
    "free": "Free",
    "freeTip": "Free for 2 devices for 15 days",
    "getStarted": "Get Started",
    "customerName": "Customer Name",
    "enterCustomerName": "Enter Customer Name",
    "customerAccount": "Customer Account",
    "accountCreatedBy": "Account Created By",
    "contractID": "Contract ID",
    "enterContractID": "Enter Contract ID",
    "contractAmount": "Contract Amount",
    "enterContractAmount": "Enter Contract Amount",
    "subscriptionPackage": "Subscription Package",
    "subscriptionCreationDate": "Subscription Creation Date",
    "expirationDate": "Expiration Date",
    "activeDate": "Activation Date",
    "selectExpirationDate": "Select Expiration Date",
    "selectActiveDate": "Select Activation Date",
    "numberOfDevice": "Number of Devices",
    "enterNumberOfDevice": "Enter Number of Devices",
    "enterNumberOfAddDevice": "Enter the number of newly added devices",
    "accountInformation": "Account Information",
    "customerEmailAddress": "Customer Email Address",
    "enterCustomerEmailAddress": "Enter Customer Email Address",
    "selectCustomer": "Select a Customer",
    "area": "Area",
    "enterArea": "Enter Area",
    "mobileNumber": "Mobile Number",
    "enterMobileNumber": "Enter Mobile Number",
    "contractInformation": "Contract Information",
    "amountUnit": "Amount Unit",
    "selectAmountUnit": "Select Amount Unit",
    "contractFile": "Contract File",
    "uploadContract": "Upload Contract",
    "uploadCompanyLogo": "Upload Company Logo",
    "submit": "Submit",
    "emailAddress": "Email Address",
    "clickSelectCustomer": "Click on the input box to select the Customer",
    "SelectCustomer": "Select a Customer",
    "createCustomer": "Create a Customer",
    "modifyTitle": "Modify Subscription",
    "positive": "Please enter a positive integer",
    "tipMsg": "The number of devices exceeds the package limit, please delete devices and refresh the page first",
    "tipMsg2": "The number of devices exceeds the package limit, please contact the administrator",
    "tipMsg3": "You do not have menu permissions, please contact the administrator",
    payment: 'payment',
    paymentMsg: 'Are you sure to pay for the current order?',
    singleMaterial: "Single content",
    US$4: 'US$4',
    basic_package_tip: 'per device/month(billed annually) US$5 billed monthly',
    areaNameTip: "Must start with a letter or Chinese character and can only contain letters, numbers, Chinese characters, spaces, and underscores",
    payStatus:"Pay Status",
    paid:"Paid",
    unpaid:"Unpaid",
    lengthTip: "The length must not exceed {{length}}.",
    minLengthTip: "The length must be greater than {{length}}.",
    moreThen: "The amount cannot be negative.",
    numberMoreThen: "The amount cannot exceed {{number}}.",
    singleFile: "Only a single file is allowed for upload.",
    dropTip: "Drag and drop here to upload.",
    limtFileSize: "The file size cannot exceed {{tip}}.",
    uploadCompanyLogoTpi: "Please upload the company logo",
    updateSub: 'Update',
    moreDevice: 'More Device',
    extend: 'Extend',
    plsInputMonthNum: 'Please enter the number of months',
    plsInputYearNum: 'Please enter the year and quantity',
    plsInputAddMonthNum: 'Please enter the number of new months added',
    plsInputAddYearNum: 'Please enter the number of new years added',
  }
}
