import React, { forwardRef, useRef, useEffect, useState } from "react";
import { getMerchantSelect } from "@/service/api/merchant";
import {
    Stack,
    Grid,
    Button,
    Typography,
    InputLabel,
    FormHelperText,
    OutlinedInput,
    InputAdornment,
    InputBase,
} from "@mui/material";
import ZKSelect from "@/components/ZKSelect";
import { useTranslation } from "react-i18next";
import { getAreaTree } from "@/service/api/L3Sevice.js";
import Treeselect from "@/components/zktreeselect";

const RegionSelect = forwardRef((props, ref) => {
    //下拉区域树数据
    const [areaId, setAreaId] = useState('');
    const [areaData, setAreaData] = React.useState([]);
    //区分组件缓存key
    const { regionKey } =  props;

    const areaSelectRef = useRef(null);
    const getAreaData = () => {
        getAreaTree().then((res) => {
            if (res.data) {
                setAreaData(res.data);
            }
        });
    };
    useEffect(()=>{
        let areaData = localStorage.getItem(regionKey)
        if(areaData){
            const area = JSON.parse(areaData);
            areaSelectRef.current.setItem({
                id: area.id,
                name: area.name,
              });
        }
    },[])
   
    const [error, setError] = useState('');

    useEffect(() => {
        setError(props.error)
    }, [props.error])

    const { t } = useTranslation();
    const handleChange = (e) => {
        let value = e.id
        setAreaId(value)
        if (props.onChange) {
            localStorage.setItem(regionKey, JSON.stringify(e));
            props.onChange(e)
        }
    }
    const onClear = (e) => {
        setAreaId('')
        if (props.onClear) {
            localStorage.removeItem(regionKey)
            props.onClear('')
        }
    }
    useEffect(() => {
        getAreaData();
    }, []);
    return (<Stack sx={{
        position: 'relative'
    }} spacing={1}>
        {
            props.label && <InputLabel htmlFor="sort-name">
                {props.label}
            </InputLabel>
        }
        <Treeselect
            ref={areaSelectRef}
            data={areaData}
            optionValue="id"
            optionLabel="name"
            name="areaId"
            size="small"
            error={Boolean(error)}
            onClear={onClear}
            placeholder={t("common.common_select_area")}
            onChange={handleChange}
            disableParent={true}
        />
        {error && <FormHelperText sx={{
            position: 'absolute',
            bottom: '-25px'
        }} error>
            {error}
        </FormHelperText>}
    </Stack>)
})
export default RegionSelect;