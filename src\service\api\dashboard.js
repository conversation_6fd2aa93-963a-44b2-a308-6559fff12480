import request from "@/utils/request";
const baseProfixURI = "/store";

/*  地图渲染门店数据接口
 * <AUTHOR>
 * @date 2023-05-18
 */
export const getPrincipaList = () => {
  return request({
    url: `${baseProfixURI}/getPrincipaList`,
    method: "get",
  });
};

/*  获取用户当前的设备总数和在线数信息
 * <AUTHOR>
 * @date 2023-05-18
 */
export const listScreenStatistics = (type, startData, endData) => {
  return request({
    url: `${baseProfixURI}/listScreenStatistics?type=${type}&startDay=${startData}&endDay=${endData}`,
    method: "get",
  });
};

/*  零售商视角查看dashboard页面的设备状态监控
 * <AUTHOR>
 * @date 2023-05-18
 */
export const listScreenPageByRetailer = (params) => {
  return request({
    url: `${baseProfixURI}/listScreenPageByRetailer`,
    method: "get",
    params: params,
  });
};

/*  广告商视角查看dashboard页面的设备状态监控
 * <AUTHOR>
 * @date 2023-05-18
 */
export const listScreenPageByAdvertiser = (params) => {
  return request({
    url: `${baseProfixURI}/listScreenPageByAdvertiser`,
    method: "get",
    params: params,
  });
};
