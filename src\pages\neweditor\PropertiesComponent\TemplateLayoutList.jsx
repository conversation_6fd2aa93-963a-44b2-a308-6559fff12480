import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Link,
  Tooltip,
  Grid,
  TextField,
  InputLabel,
  OutlinedInput,
  IconButton,
  Pagination,
} from "@mui/material";
import { useEffect, useMemo, useRef, useState } from "react";
import { getTemplateList } from "@/service/api/layout";
import { useConfirm } from "@/components/zkconfirm";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import Box from "@mui/material/Box";
import { getIndustry } from "@/service/api/layout";
import { message } from "../common/i18n";

const TemplateLayoutList = (props) => {
  let layoutInfo = props.layoutInfo;
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [pageSize, setPageSize] = useState(20);
  const [industryType, setIndustryType] = useState("");

  const confirmFn = useConfirm();
  const [templateData, setTemplateData] = useState([]);
  const currentPageIndex = props.currentPageIndex;
  const currentComponentId = props.currentComponentId;
  const setLayoutInfo = props.setLayoutInfo;
  const [industryList, setIndustryList] = useState([]);

  const handleChangeIndustryType = (event, newValue) => {
    setIndustryType(newValue);
  };

  useEffect(() => {
    loadList();
    loadTypeList();
  }, []);

  const loadTypeList = () => {
    getIndustry().then((res) => {
      if (res.code == "00000000") {
        setIndustryList([
          {
            label: message("allTempalte"),
            value: "",
          },
          ...res.data,
        ]);
      } else {
        setIndustryList([]);
      }
    });
  };

  const loadList = () => {
    getTemplateList({
      page: page,
      pageSize: pageSize,
      industryType: industryType,
      resolutionWidth: layoutInfo.width,
      resolutionHeight: layoutInfo.height,
      // resolution: `reslotionWIdthx${layoutInfo.height}`,
    }).then((res) => {
      if (res.code == "00000000") {
        let result = res.data;

        setTemplateData(result.data);
        setPage(result.page);
        setTotal(result.total);
      }
    });
  };

  useEffect(() => {
    loadList();
  }, [page, industryType]);

  const initTemp = (item) => {
    let templateJson = item.templateJson;
    let tempObj = JSON.parse(templateJson);
    const { pages, name, ...orther } = tempObj;
    props.setScale(orther.scale);
    props.setPages(pages);
    if (setLayoutInfo) {
      setLayoutInfo({
        ...orther,
        name: layoutInfo.name,
      });
    }
  };

  const userTemp = (item) => {
    let pageInfo = props.pages[currentPageIndex];
    if (pageInfo.isTemplate) {
      let cmpList = pageInfo.tempLayout
        .map((item) => {
          return item.componentList;
        })
        .reduce(function (acc, curr) {
          return acc.concat(curr);
        }, []);
      if (cmpList.length > 0) {
        confirmFn({
          title: message("delete_cmp_tip"),
          confirmationText: message("editor_edit_ok"),
          cancellationText: message("editor_edit_cancel"),
          description: message("useTempTip"),
        }).then(() => {
          initTemp(item);
        });
      } else {
        initTemp(item);
      }
    } else {
      if (pageInfo.componentList.length > 0) {
        confirmFn({
          title: message("delete_cmp_tip"),
          confirmationText: message("editor_edit_ok"),
          cancellationText: message("editor_edit_cancel"),
          description: message("useTempTip"),
        }).then(() => {
          initTemp(item);
        });
      } else {
        initTemp(item);
      }
    }
  };

  return (
    <Grid
      sx={{
        height: "100%",
        display: "flex",
        flexDirection: "column",
      }}>
      <Tabs
        value={industryType}
        onChange={handleChangeIndustryType}
        variant="scrollable"
        scrollButtons="auto"
        aria-label="scrollable auto tabs example">
        {industryList?.map((item) => {
          return <Tab key={item.label} label={item.label} value={item.value} />;
        })}
      </Tabs>

      <Grid
        sx={{
          display: "flex",
          flexWrap: "wrap",
        }}>
        {templateData.map((item) => {
          return (
            <Grid
              sx={{
                width: "50%",
                height: "150px",
                padding: "10px",
                cursor: "pointer",
              }}
              key={item.id}>
              <Grid
                sx={{
                  height: "100%",
                  width: "100%",
                  border: "1px solid #f0ebeb",
                  position: "relative",
                  backgroundColor: "#ffffff",
                  borderRadius: "6px",
                }}>
                <Grid
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    height: "100%",
                    width: "100%",
                    backgroundImage: `url('${item.thumbnailUrl}')`,
                    backgroundSize: "contain",
                    backgroundRepeat: "no-repeat",
                    backgroundPosition: "center",
                  }}></Grid>
                <Grid
                  sx={{
                    display: "flex",
                    position: "absolute",
                    bottom: "0px",
                    height: "40px",
                    alignItems: "center",
                    justifyContent: "space-around",
                    width: "100%",
                    backgroundColor: "#00000040",
                  }}>
                  <Grid
                    onClick={() => {
                      userTemp(item);
                    }}
                    sx={{
                      fontSize: "14px",
                      "&:hover": {
                        color: "#7ac143",
                        cursor: "pointer",
                      },
                    }}>
                    {message("useTempalte")}
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          );
        })}
      </Grid>

      <Grid
        sx={{
          display: "flex",
          justifyContent: "flex-end",
        }}>
        {Math.ceil(total / pageSize) > 1 && (
          <Pagination
            count={Math.ceil(total / pageSize)}
            variant="outlined"
            page={page}
            shape="rounded"
            // siblingCount={3}
            onChange={(event, page) => {
              setPage(page);
            }}
          />
        )}
      </Grid>
    </Grid>
  );
};

export default TemplateLayoutList;
