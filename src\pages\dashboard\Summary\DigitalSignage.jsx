import {
    Grid,
    Typography,
} from "@mui/material";
import React from "react";
import GradientBox from '@/components/GradientBox'
import './summary.less'
import { useTranslation } from "react-i18next";
const DigitalSignage = (props) => {
    const { t } = useTranslation();
    const {signageData} = props

    return <GradientBox style={{flexGrow:1}}>
         <Grid sx={{
            p:1
        }} className="outlet_data_box">
            <Grid className="outlet_info_cla">
                <Typography style={{fontSize:'60px',fontWeight:600}} className="text-gradient">{signageData.total||0}</Typography>
             
                <Typography sx={{mt:2}}>{t('summary.total_devices_installed')}  </Typography>
            </Grid>
            <Grid className="divider_cla"></Grid>
            <Grid className="outlet_info_cla">
                <Typography className="text-gradient signage_text">{signageData.ytdScreen||0}</Typography>
                <Typography sx={{mt:1}}>{t('summary.new_devices_installed_ytd')}</Typography>
              
                <Typography sx={{mt:3}} className="text-gradient signage_text">{signageData.oldScreen||0}</Typography>
                <Typography sx={{mt:1}}>{t('outlet.total_devices_installed')} {new Date().getFullYear()-1} </Typography>
            </Grid>
        </Grid>
    </GradientBox>
}

export default DigitalSignage
