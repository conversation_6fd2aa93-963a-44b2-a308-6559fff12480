/* eslint-disable no-extra-boolean-cast */
import {
  Grid,
  Box,
  FormGroup,
  InputLabel,
  FormControlLabel,
  Checkbox,
} from "@mui/material";
import moment from "moment";

import { useTranslation } from "react-i18next";
import React, { useState, forwardRef, useImperativeHandle, memo } from "react";
import { useCallback } from "react";
//时间按钮选择组件
const TimeButtonSelect = memo(
  forwardRef((props, ref) => {
    const { t } = useTranslation();
    useImperativeHandle(ref, () => ({
      getDaysWeeksAndMonthsDate,
      monthsSelectList,
      weeksSelectList,
      daysSelectList,
      handleSelectedDate,
      handleChangeDaysAble,
    }));
    //排期日期按钮状态集合
    const [daysAbleStatusList, setDaysAbleStatusList] = useState([]);
    //排期星期按钮状态集合
    const [weeksAbleStatusList, setWeeksAbleStatusList] = useState([
      1, 2, 3, 4, 5, 6, 7,
    ]);

    //排期月份按钮已选集合
    const [monthsSelectList, setMonthsSelectList] = useState([]);
    //排期日期按钮已选集合
    const [daysSelectList, setDaysSelectList] = useState([]);
    //排期星期按钮已选集合
    const [weeksSelectList, setWeeksSelectList] = useState([]);
    //定义月份对应星期对应日期Map(跟随播放时间改变)
    const [monthWeekDayMap, setMonthWeekDayMap] = useState(new Map());

    //周
    const weeks = [
      t("common.common_monday"),
      t("common.common_tuesday"),
      t("common.common_wednesday"),
      t("common.common_thursday"),
      t("common.common_friday"),
      t("common.common_saturday"),
      t("common.common_sunday"),
    ];

    const handleSelectedDate = (data) => {
      setMonthsSelectList(data.playMonths);
      setDaysSelectList(data.playDays);
      setWeeksSelectList(data.playWeeksNum);
    };

    //进入编辑页面时，将可选的日期显示出来
    const handleChangeDaysAble = (
      currentSelectWeeks,
      currentSelectMonths,
      monthWeekDay,
      currentSelectDays
    ) => {
      if (currentSelectWeeks.length > 0) {
        // let newDaysAbleStatusList = [];
        // currentSelectMonths.forEach((selectMonth) => {
        //   currentSelectWeeks.forEach((selectWeek) => {
            // if (monthWeekDay.get(selectMonth).has(selectWeek)) {
            //   newDaysAbleStatusList = [
            //     ...newDaysAbleStatusList,
            //     ...Array.from(monthWeekDay.get(selectMonth).get(selectWeek)),
            //   ];
            // }
        //   });
        // });
        //设置日期状态集合
        // setDaysAbleStatusList(newDaysAbleStatusList);
        setDaysAbleStatusList(currentSelectDays);
      }
    };
    //日期
    const days = [
      "01",
      "02",
      "03",
      "04",
      "05",
      "06",
      "07",
      "08",
      "09",
      "10",
      "11",
      "12",
      "13",
      "14",
      "15",
      "16",
      "17",
      "18",
      "19",
      "20",
      "21",
      "22",
      "23",
      "24",
      "25",
      "26",
      "27",
      "28",
      "29",
      "30",
      "31",
    ];
    //月份
    const months = [
      "01",
      "02",
      "03",
      "04",
      "05",
      "06",
      "07",
      "08",
      "09",
      "10",
      "11",
      "12",
    ];

    //构建月份对应星期对应日期双层map集合，第一层map集合key为月份，value为map集合（第二层map集合key为星期，value为日期Set集合，使用set是为了去重）
    const monthAddWeeksAddDays = (monthToWeeksToDaysMap, month, week, day) => {
      //判断map中是否有该月份
      if (monthToWeeksToDaysMap.has(month)) {
        //取出星期对应日期map
        let oldWeeksToDaySetMap = monthToWeeksToDaysMap.get(month);
        //判断map中是否有该星期
        if (oldWeeksToDaySetMap.has(week)) {
          //有直接添加日期
          let oldDaySet = oldWeeksToDaySetMap.get(week);
          oldDaySet.add(day);
        } else {
          //没有则添加map对象
          let daySet = new Set();
          daySet.add(day);
          oldWeeksToDaySetMap.set(week, daySet);
        }
      } else {
        //创建新的星期日期map
        let newWeekToDayMap = new Map();
        let daySet = new Set();
        daySet.add(day);
        newWeekToDayMap.set(week, daySet);
        monthToWeeksToDaysMap.set(month, newWeekToDayMap);
      }
    };

    //根据开始时间和结束时间计算包含日期，星期，月份的map，只在选中播放时间时计算一次，后续状态判断使用双层map
    const getDaysWeeksAndMonthsDate = (
      startDateStr,
      endDateStr,
      clearSelect
    ) => {
      if (
        !Boolean(startDateStr) ||
        startDateStr.length === 0 ||
        !Boolean(endDateStr) ||
        endDateStr.length === 0
      ) {
        return;
      }

      let startDate = moment(startDateStr); //格式YYYY-MM-DD
      let endDate = moment(endDateStr); //格式YYYY-MM-DD
      let currDate = startDate.clone();
      //定义月份对应星期对应日期Map
      let monthToWeeksToDaysMap = new Map();
      //判空
      // console.log(currDate.isSameOrBefore(endDate));
      while (currDate.isSameOrBefore(endDate)) {
        //字符串类型
        let day = currDate.clone().format("DD");
        //字符串类型
        let month = currDate.clone().format("MM");
        //数值类型
        let week = currDate.clone().day();
        switch (week) {
          case 1:
            monthAddWeeksAddDays(monthToWeeksToDaysMap, month, 1, day);
            break;
          case 2:
            monthAddWeeksAddDays(monthToWeeksToDaysMap, month, 2, day);
            break;
          case 3:
            monthAddWeeksAddDays(monthToWeeksToDaysMap, month, 3, day);
            break;
          case 4:
            monthAddWeeksAddDays(monthToWeeksToDaysMap, month, 4, day);
            break;
          case 5:
            monthAddWeeksAddDays(monthToWeeksToDaysMap, month, 5, day);
            break;
          case 6:
            monthAddWeeksAddDays(monthToWeeksToDaysMap, month, 6, day);
            break;
          case 0:
            monthAddWeeksAddDays(monthToWeeksToDaysMap, month, 7, day);
            break;
          default:
            break;
        }
        //时间加一天，继续循环
        currDate.add(1, "day");
      }
      //判断时间是否大于结束时间
      setMonthWeekDayMap(monthToWeeksToDaysMap);
      if (clearSelect) {
        //清空排期日期按钮状态集合
        setDaysAbleStatusList([]);
        //清空排期月份按钮已选集合
        setMonthsSelectList([]);
        //清空排期日期按钮已选集合
        setDaysSelectList([]);
        //清空排期星期按钮已选集合
        setWeeksSelectList([]);
      }
      return monthToWeeksToDaysMap;
    };

    return (
      <>
        {/* 播放月份 */}
        <Grid
          container
          sx={{ marginBottom: "20px" }}
          justifyContent="flex-start"
          alignItems="center"
        >
          <Grid item xs={12}>
            <InputLabel htmlFor="infor-firstName">
              {t("ips.ips_month")}
              <i style={{ color: "red" }}>*</i>
            </InputLabel>
          </Grid>
          <Grid item xs={12}>
            <FormGroup row>
              <FormControlLabel
                sx={{ width: "80px" }}
                control={
                  <Checkbox
                    checked={
                      monthWeekDayMap.size === monthsSelectList.length &&
                      monthWeekDayMap.size != 0
                    }
                    onChange={() => {
                      //判断是否全选
                      if (monthWeekDayMap.size === monthsSelectList.length) {
                        //清空月份选择集合
                        setMonthsSelectList([]);
                        // 清空星期状态集合(星期状态不再手控制)
                        //setWeeksAbleStatusList([]);
                        // 清空日期选择集合
                        setDaysAbleStatusList([]);
                        // 清空星期选择集合
                        setWeeksSelectList([]);
                        // 清空日期选择集合
                        setDaysSelectList([]);
                      } else {
                        //1.先获取播放区间包含的所有月份集合
                        let allMonth = Array.from(monthWeekDayMap.keys());
                        //2.设置全选
                        setMonthsSelectList(allMonth);
                        //定义根据月份获取所有日期可选状态1
                        let newDaysAbleStatusList = [];
                        //定义根据月份获取所有星期下的日期
                        allMonth.forEach((month) => {
                          let daySetList = Array.from(
                            monthWeekDayMap.get(month).values()
                          );

                          daySetList.forEach((daySet) => {
                            if (daySet !== undefined) {
                              newDaysAbleStatusList = [
                                ...newDaysAbleStatusList,
                                ...Array.from(daySet),
                              ];
                            }
                          });
                        });

                        //设置所选月份包含的所有日期
                        setDaysAbleStatusList(newDaysAbleStatusList);
                      }
                    }}
                  />
                }
                label={t("ips.ips_all")}
              />
              {months.map((month, index) => {
                return (
                  <FormControlLabel
                    sx={{ width: "80px" }}
                    key={index}
                    disabled={!monthWeekDayMap.has(month)}
                    name="playMonths"
                    value={index}
                    checked={monthsSelectList.includes(month)}
                    onChange={() => {
                      if (monthsSelectList.includes(month)) {
                        //设置按钮为未选中
                        let newMonthSelectList = [...monthsSelectList];
                        newMonthSelectList.splice(
                          newMonthSelectList.lastIndexOf(month),
                          1
                        );
                        setMonthsSelectList(newMonthSelectList);
                        //月份减少时需要减少对应的日期选中按钮集合和可选状态集合1
                        //定义应该减少的日期状态集合
                        let lessDaysAbleStatusList = [];

                        let daySetList = Array.from(
                          monthWeekDayMap.get(month).values()
                        );

                        daySetList.forEach((daySet) => {
                          lessDaysAbleStatusList = [
                            ...lessDaysAbleStatusList,
                            ...Array.from(daySet),
                          ];
                        });

                        let newDaysAbleStatusList = [...daysAbleStatusList];
                        lessDaysAbleStatusList.forEach((dayValue) => {
                          let dayIndex =
                            newDaysAbleStatusList.lastIndexOf(dayValue);
                          if (dayIndex != -1) {
                            //减少日期可控制按钮
                            newDaysAbleStatusList.splice(dayIndex, 1);
                          }
                        });
                        //设置减少后的日期可选状态集合
                        setDaysAbleStatusList(newDaysAbleStatusList);

                        //减少对应的日期选中按钮的集合
                        let lessDaysSelectList = Array.from(
                          new Set(lessDaysAbleStatusList)
                        );
                        let newDaysSelectList = [...daysSelectList];
                        lessDaysSelectList.forEach((lessDayValue) => {
                          let lessDayIndex =
                            newDaysSelectList.lastIndexOf(lessDayValue);
                          if (lessDayIndex != -1) {
                            //减少日期选中按钮
                            newDaysAbleStatusList.splice(lessDayIndex, 1);
                          }
                        });
                      } else {
                        //设置月份按钮状态为已选中
                        let newMonthSelectList = [...monthsSelectList, month];
                        setMonthsSelectList(newMonthSelectList);
                        let newDaysAbleStatusList = [...daysAbleStatusList];
                        let daySetList = Array.from(
                          monthWeekDayMap.get(month).values()
                        );
                        daySetList.forEach((daySet) => {
                          if (daySet != undefined) {
                            newDaysAbleStatusList = [
                              ...newDaysAbleStatusList,
                              ...Array.from(daySet),
                            ];
                          }
                        });
                        setDaysAbleStatusList(newDaysAbleStatusList);
                      }
                    }}
                    control={<Checkbox />}
                    label={month}
                  />
                );
              })}
            </FormGroup>
          </Grid>
        </Grid>
        {/* 播放周次 */}
        <Grid
          container
          sx={{ marginBottom: "20px" }}
          justifyContent="flex-start"
          alignItems="center"
        >
          <Grid item xs={12}>
            <InputLabel htmlFor="infor-firstName">
              {t("ips.ips_scheduling_playWeeks")}
            </InputLabel>
          </Grid>
          <Grid item xs={12}>
            <FormGroup row>
              <FormControlLabel
                sx={{ width: "80px" }}
                control={
                  <Checkbox
                    checked={
                      weeksSelectList.length ===
                        Array.from(new Set(weeksAbleStatusList)).length &&
                      weeksSelectList.length != 0
                    }
                    onChange={() => {
                      if (
                        weeksSelectList.length ===
                        Array.from(new Set(weeksAbleStatusList)).length
                      ) {
                        //设置按钮为未选中状态
                        setWeeksSelectList([]);
                        //清空日期选择集合
                        setDaysSelectList([]);
                        //清空日期状态集合
                        //setDaysAbleStatusList([]);
                      } else {
                        //设置所有可选按钮为选中状态
                        let newWeeksSelectList = Array.from(
                          new Set(weeksAbleStatusList)
                        );
                        setWeeksSelectList(newWeeksSelectList);
                        //判断日期可选状态（获取选中月份下选中星期对应的所有日期）
                        if (newWeeksSelectList.length > 0) {
                          let newDaysAbleStatusList = [];
                          monthsSelectList.forEach((selectMonth) => {
                            newWeeksSelectList.forEach((selectWeek) => {
                              if (
                                monthWeekDayMap.get(selectMonth).has(selectWeek)
                              ) {
                                newDaysAbleStatusList = [
                                  ...newDaysAbleStatusList,
                                  ...Array.from(
                                    monthWeekDayMap
                                      .get(selectMonth)
                                      .get(selectWeek)
                                  ),
                                ];
                              }
                            });
                          });
                          //设置日期状态集合
                          setDaysAbleStatusList(newDaysAbleStatusList);
                          //设置日期选中集合
                          setDaysSelectList(
                            Array.from(new Set(newDaysAbleStatusList))
                          );
                        }
                      }
                    }}
                  />
                }
                label={t("ips.ips_all")}
              />
              {weeks.map((week, index) => {
                return (
                  <FormControlLabel
                    key={index}
                    disabled={!weeksAbleStatusList.includes(index + 1)}
                    name="playWeeks"
                    value={index}
                    checked={weeksSelectList.includes(index + 1)}
                    onChange={() => {
                      if (weeksSelectList.includes(index + 1)) {
                        //设置该星期按钮为未选中
                        let newWeekSelectList = [...weeksSelectList];
                        newWeekSelectList.splice(
                          newWeekSelectList.lastIndexOf(index + 1),
                          1
                        );
                        setWeeksSelectList(newWeekSelectList);
                        //（需要判断月份的选择）减少对应的日期按钮选择状态集合
                        let lessDayList = [];
                        //已选中星期该删除的日期
                        monthsSelectList.forEach((month) => {
                          if (monthWeekDayMap.get(month).has(index + 1)) {
                            lessDayList = [
                              ...lessDayList,
                              ...Array.from(
                                monthWeekDayMap.get(month).get(index + 1)
                              ),
                            ];
                          }
                        });
                        let newDaysSelectList = [...daysSelectList];
                        lessDayList.forEach((value) => {
                          let valueIndex = newDaysSelectList.lastIndexOf(value);
                          if (valueIndex != -1) {
                            newDaysSelectList.splice(valueIndex, 1);
                          }
                        });

                        setDaysSelectList(newDaysSelectList);
                      } else {
                        //设置按钮为选中状态
                        let newWeekSelectList = [...weeksSelectList];
                        newWeekSelectList.push(index + 1);
                        setWeeksSelectList(newWeekSelectList);
                        //（需要判断月份的选择）添加对应的日期按钮选择状态集合
                        let newDaysSelectList = [...daysSelectList];
                        monthsSelectList.forEach((value) => {
                          if (monthWeekDayMap.get(value).has(index + 1)) {
                            newDaysSelectList = [
                              ...newDaysSelectList,
                              ...Array.from(
                                monthWeekDayMap.get(value).get(index + 1)
                              ),
                            ];
                          }
                        });
                        //需要去重，可能星期对应的日期已经选中
                        setDaysSelectList(
                          Array.from(new Set(newDaysSelectList))
                        );
                      }
                    }}
                    control={<Checkbox />}
                    label={week}
                  />
                );
              })}
            </FormGroup>
          </Grid>
        </Grid>
        {/* 播放日期 */}
        <Grid
          container
          sx={{ marginBottom: "20px" }}
          justifyContent="flex-start"
          alignItems="center"
        >
          <Grid item xs={12}>
            <InputLabel htmlFor="infor-firstName">
              {t("ips.ips_date")}
              <i style={{ color: "red" }}>*</i>
            </InputLabel>
          </Grid>
          <Grid item xs={12}>
            <FormGroup row>
              <FormControlLabel
                sx={{ width: "80px" }}
                control={
                  <Checkbox
                    checked={
                      daysSelectList.length ===
                      Array.from(new Set(daysAbleStatusList)).length &&
                      daysSelectList.length != 0
                    }
                    onChange={() => {
                      if (
                        daysSelectList.length ===
                        Array.from(new Set(daysAbleStatusList)).length
                      ) {
                        //设置按钮状态为未选中
                        setDaysSelectList([]);
                      } else {
                        //设置按钮为选中状态
                        setDaysSelectList(
                          Array.from(new Set(daysAbleStatusList))
                        );
                      }
                    }}
                  />
                }
                label={t("ips.ips_all")}
              />
              {days.map((day, index) => {
                return (
                  <FormControlLabel
                    sx={{ width: "80px" }}
                    key={index}
                    name="playDays"
                    disabled={!daysAbleStatusList?.includes(day)}
                    value={index}
                    checked={daysSelectList?.includes(day)}
                    onChange={() => {
                      if (daysSelectList?.includes(day)) {
                        //设置按钮为未选中
                        let newDaysSelectList = [...daysSelectList];
                        newDaysSelectList.splice(
                          newDaysSelectList.lastIndexOf(day),
                          1
                        );
                        setDaysSelectList(newDaysSelectList);
                      } else {
                        setDaysSelectList([...daysSelectList, day]);
                      }
                    }}
                    control={<Checkbox />}
                    label={day}
                  />
                );
              })}
            </FormGroup>
          </Grid>
        </Grid>
      </>
    );
  })
);

export default TimeButtonSelect;
