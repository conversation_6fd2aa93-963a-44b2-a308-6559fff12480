/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/prop-types */

import React, { useEffect, useMemo, useRef, useState } from "react";
import MaterialReactTable from "material-react-table";
import {
  Button,
  Stack,
  Link,
  Box,
  InputLabel,
  OutlinedInput,
  Select,
  MenuItem,
} from "@mui/material";
// i18n
import { useTranslation } from "react-i18next";
import { tableI18n } from "@/utils/tableLang";
import { getPrincipaList } from "@/service/api/L3Sevice.js";
import StoreDetail from "./storeDetail";
import RegionZKSelect from "./RegionZKSelect"; //百度地图数据
// import RegionZKSelect from "./RegionZKSelecGoogle"; //谷歌地图数据
import { getMerchantSelect } from "@/service/api/merchant";
import ZKSelect from "@/components/ZKSelect";
const Example = () => {
  const [data, setData] = useState({});
  const { t } = useTranslation();
  //存放搜索条件的数据
  const requestParams = useRef(null);
  // 用于hash路由保存到sessionStore拼接
  const [isError, setIsError] = useState(false);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);

  const storeDetailRef = useRef(null);
  // 分页的参数
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });

  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);

  // 构建参数
  const buildParams = () => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      ...requestParams.current,
    };

    return params;
  };

  // 获取数据
  const getTableData = async () => {
    if (!data.length) {
      setIsLoading(true);
    } else {
      setIsRefetching(true);
    }

    await listRetailerByPage(buildParams())
      .then((res) => {
        // 设置数据
        setData(res.data.data);
        // 设置总记录数
        setRowCount(res.data.total);
        setIsLoading(false);
        setIsRefetching(false);
      })
      .catch((err) => {
        setIsError(true);
        setIsLoading(false);
        setIsRefetching(false);
      });
  };

  //查询方法
  const handleGetTableData = (param) => {
    console.log("param", param);
    requestParams.current = param;
    getTableData();
  };
  useEffect(() => {
    // 发请求
    getTableData();
  }, [pagination.pageIndex, pagination.pageSize]);
  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "country",
        header: t("ips.ips_store_country"),
        enableColumnActions: false,
        enableSorting: false,
        minSize: 80,
        maxSize: 120,
        Cell: ({ cell, row }) => {
          return (
            <>
              {row.original.country === "" || row.original.country === null
                ? "-"
                : row.original.country}
            </>
          );
        },
      },
      {
        accessorKey: "province",
        header: t("ips.ips_store_province"),
        enableColumnActions: false,
        minSize: 80,
        maxSize: 120,
        enableSorting: false,
      },
      {
        accessorKey: "city",
        header: t("ips.ips_store_city"),
        enableColumnActions: false,
        minSize: 80,
        maxSize: 120,
        enableSorting: false,
      },
      {
        accessorKey: "region",
        header: t("ips.ips_region"),
        enableColumnActions: false,
        minSize: 80,
        maxSize: 120,
        enableSorting: false,
      },
      {
        accessorKey: "name",
        enableColumnActions: false,
        header: t("ips.ips_store_client_name"),
        minSize: 100,
        maxSize: 120,
        enableSorting: false,
      },
      {
        accessorKey: "storeNumber",
        header: t("ips.ips_outlet_number"),
        enableColumnActions: false,
        minSize: 90,
        maxSize: 120,
        enableSorting: false,
      },
      {
        accessorKey: "screenNumber",
        header: t("ips.ips_signage_number"),
        enableColumnActions: false,
        minSize: 90,
        maxSize: 120,
        enableSorting: false,
      },
      {
        accessorKey: "onlineNum",
        header: t("ips.ips_online_number"),
        enableColumnActions: false,
        minSize: 90,
        maxSize: 120,
        enableSorting: false,
      },
      {
        accessorKey: "offlineNum",
        header: t("ips.ips_offline_number"),
        enableColumnActions: false,
        minSize: 90,
        maxSize: 120,
        enableSorting: false,
      },
    ],
    []
  );
  return (
    <>
      <MaterialReactTable
        // table 状态
        state={{
          // 加载状态
          isLoading,
          // 分页参数
          pagination,
          // 重新拉取
          showProgressBars: isRefetching,
          showAlertBanner: isError,

          density: "compact",
        }}
        // 解决列太多宽度太长问题
        enableColumnResizing
        renderTopToolbar={({ table }) => (
          <Box
            sx={{
              height: "50px",
              display: "flex",
              alignItems: "center",
              justifyContent: "flex-end",
            }}>
            <TableBarSearch handleGetTableData={handleGetTableData} />
          </Box>
        )}
        // 初始化状态
        initialState={{ columnVisibility: { createTime: false } }}
        muiToolbarAlertBannerProps={
          isError
            ? {
                color: "error",
                children: t("table.loading_error"),
              }
            : undefined
        }
        // 列数
        rowCount={rowCount}
        // 固定头部
        enableStickyHeader
        // 处理表格高度
        muiTablePaperProps={{
          elevation: 0,
          sx: {
            borderRadius: "5px",
            border: "1px solid #eaf0f5",
          },
        }}
        muiTableBodyProps={{
          sx: (theme) => ({
            "& tr:nth-of-type(odd)": {
              backgroundColor: "#eaf0f5",
            },
            // 'tr:hover': {
            //     backgroundColor: '#f9fff0'
            // }
          }),
        }}
        // 处理表格高度
        muiTableContainerProps={{ sx: { maxHeight: "600px" } }}
        muiTableHeadCellProps={{
          sx: { color: "#8f9a9e", fontWeight: 500, fontSize: "14px" },
        }}
        // 设置背景颜色
        muiTableBodyCellProps={{ sx: { color: "#61686b", fontSize: "14px" } }}
        muiTableHeadRowProps={{
          sx: { backgroundColor: "#eaf0f5" },
        }}
        muiTableBodyRowProps={{
          sx: { "tr td:hover": { backgroundColor: "red" } },
        }}
        muiTableProps={{ sx: { backgroundColor: "white" } }}
        muiBottomToolbarProps={{ sx: { backgroundColor: "white" } }}
        muiTopToolbarProps={{ sx: { backgroundColor: "white" } }}
        // 分页回调函数
        onPaginationChange={setPagination}
        // 记得开启这些 tips:否则会出现useEffect 反作用函数发起多次请求点击下一页会出问题
        manualFiltering
        manualPagination
        manualSorting
        // 开启分页
        enablePagination
        // 列定义
        columns={columns}
        // 数据
        data={data}
        // 初始化国际化语言  --- todo: i18n key doc address: https://github.com/KevinVandy/material-react-table/blob/main/packages/material-react-table/src/_locales/en.ts
        localization={tableI18n}
        muiLinearProgressProps={({ isTopToolbar }) => ({
          sx: { display: isTopToolbar ? "block" : "none" },
        })}
        // 多选底部提示
        positionToolbarAlertBanner="none"
        // 开启action操作
        enableRowActions
        // action操作位置
        positionActionsColumn="last"
        displayColumnDefOptions={{
          "mrt-row-actions": {
            header: t("common.common_relatedOp"), //change header text
            size: 180, //make actions column wider
          },
        }}
        renderRowActions={(row, index, table) => (
          <Stack direction="row" spacing={1} alignItems="center">
            <Link
              component="button"
              underline="none"
              onClick={() => {
                storeDetailRef.current.handleOpen(
                  row.row.original.storeIdList,
                  row.row.original.name
                );
              }}>
              {t("common.common_op_detail")}
            </Link>
          </Stack>
        )}
      />
      <StoreDetail ref={storeDetailRef} />
    </>
  );
};

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 100,
    },
  },
};
const TableBarSearch = (props) => {
  const [brandCooperate, setBrandCooperate] = useState("");
  const [storeName, setStoreName] = useState("");
  const [merchantList, setMerchantList] = useState([]);
  const { t } = useTranslation();
  //调用搜索
  const { handleGetTableData } = props;
  const regionRef = useRef(null);
  const storeRef = useRef(null);

  //清除搜索条件
  const handleClear = () => {
    setBrandCooperate("");
    setStoreName("");
    regionRef.current.handleClear();
  };
  //获取零售商列表
  const getMerchant = () => {
    getMerchantSelect(1).then((res) => {
      setMerchantList(res.data);
    });
  };
  const merchantNameOnChange = (value) => {
    setBrandCooperate(value);
  };
  //获取输入的门店名称值
  const storeNameOnChange = () => {
    setStoreName(storeRef.current.value);
  };

  useEffect(() => {
    getMerchant();
  }, []);
  return (
    <Box sx={{ paddingRight: "10px" }}>
      <Stack
        spacing={2}
        direction="row"
        justifyContent="flex-start"
        alignItems="center">
        <RegionZKSelect ref={regionRef} />
        <Stack
          direction="row"
          justifyContent="flex-start"
          alignItems="center"
          spacing={1}>
          <InputLabel sx={{ width: 70 }}>
            {t("ips.ips_store_client")}
          </InputLabel>
          <ZKSelect
            placeholder={t("ips.ips_store_tips_select_client")}
            options={merchantList}
            name="merchantName"
            value={brandCooperate}
            onClear={() => {
              setBrandCooperate(undefined);
            }}
            sx={{
              height: 35,
            }}
            onChange={(event) => {
              merchantNameOnChange(event.target.value);
            }}
          />
        </Stack>
        {/* <Stack
          direction="row"
          justifyContent="flex-start"
          alignItems="center"
          spacing={1}
        >
          <InputLabel sx={{ width: 40 }}>门店</InputLabel>
          <OutlinedInput
            size="small"
            type="text"
            name="storeName"
            fullWidth
            placeholder="请输入门店名称"
            value={storeName}
            onChange={storeNameOnChange}
            inputRef={storeRef}
          />
        </Stack> */}
        <Button
          disableElevation
          type="submit"
          variant="contained"
          size="small"
          onClick={() => {
            const regionInfo = regionRef.current.handleGetRegionInfo();
            console.log("regionInfo", regionInfo);
            const searchData = { ...regionInfo, storeName, brandCooperate };
            handleGetTableData(searchData);
          }}>
          {/* 查询 */}
          {t("common.common_table_query")}
        </Button>
        <Button
          variant="outlined"
          color="info"
          size="small"
          sx={{
            minWidth: "90px",
          }}
          onClick={() => {
            handleClear();
            handleGetTableData({});
          }}>
          {/* 重置 */}
          {t("common.common_op_reset")}
        </Button>
      </Stack>
    </Box>
  );
};

export default Example;
