let myCustomLocaleCN = {
  // 按顺序排列的月份列表
  months: [
    "一月",
    "二月",
    "三月",
    "四月",
    "五月",
    "六月",
    "七月",
    "八月",
    "九月",
    "十月",
    "十一月",
    "十二月",
  ],
  // 按顺序排列的星期列表
  weekDays: [
    {
      name: "星期日", // 用于无障碍访问
      short: "日", // 显示在日期的顶部行
      isWeekend: true, // 是否是正式的周末
    },
    {
      name: "星期一",
      short: "一",
    },
    {
      name: "星期二",
      short: "二",
    },
    {
      name: "星期三",
      short: "三",
    },
    {
      name: "星期四",
      short: "四",
    },
    {
      name: "星期五",
      short: "五",
    },
    {
      name: "星期六",
      short: "六",
      isWeekend: true,
    },
  ],
  // 在 0 到 6 之间调整此数字以改变周的开始日
  weekStartingIndex: 0,
  // 返回一个包含年、月和日的对象
  getToday: function (gregorianTodayObject) {
    return gregorianTodayObject;
  },
  // 返回一个原生的 JavaScript 日期对象
  toNativeDate: function (date) {
    return new Date(date.year, date.month - 1, date.day);
  },
  // 返回指定日期的月份长度（天数）
  getMonthLength: function (date) {
    return new Date(date.year, date.month, 0).getDate();
  },
  // 将数字转换为本地的格式
  transformDigit: function (digit) {
    return digit;
  },
  // 日期选择器中的文本
  nextMonth: "下一月",
  previousMonth: "上一月",
  openMonthSelector: "打开月份选择器",
  openYearSelector: "打开年份选择器",
  closeMonthSelector: "关闭月份选择器",
  closeYearSelector: "关闭年份选择器",
  defaultPlaceholder: "选择...",
  // 用于输入范围的文本
  from: "从",
  to: "到",
  // 当选择多个日期时用于输入的文本
  digitSeparator: ",",
  // 如果你提供 -2，年份将显示为两位数
  yearLetterSkip: 0,
  // 你的语言是从右到左 (RTL) 还是从左到右 (LTR)?
  isRtl: false,
};

let myCustomLocaleES = {
  // Lista de meses por orden
  months: [
    "Enero",
    "Febrero",
    "Marzo",
    "Abril",
    "Mayo",
    "Junio",
    "Julio",
    "Agosto",
    "Septiembre",
    "Octubre",
    "Noviembre",
    "Diciembre",
  ],
  // Días de la semana por orden
  weekDays: [
    {
      name: "Domingo", // Utilizado para la accesibilidad
      short: "D", // Se muestra en la parte superior de las filas de días
      isWeekend: true, // ¿Es un fin de semana oficial o no?
    },
    {
      name: "Lunes",
      short: "L",
    },
    {
      name: "Martes",
      short: "M",
    },
    {
      name: "Miércoles",
      short: "X",
    },
    {
      name: "Jueves",
      short: "J",
    },
    {
      name: "Viernes",
      short: "V",
    },
    {
      name: "Sábado",
      short: "S",
      isWeekend: true,
    },
  ],
  // Solo juega con este número entre 0 y 6
  weekStartingIndex: 0,
  // Devuelve un objeto { year: número, month: número, day: número }
  getToday(gregorianTodayObject) {
    return gregorianTodayObject;
  },
  // Devuelve una fecha nativa de JavaScript
  toNativeDate(date) {
    return new Date(date.year, date.month - 1, date.day);
  },
  // Devuelve un número para la longitud del mes de la fecha
  getMonthLength(date) {
    return new Date(date.year, date.month, 0).getDate();
  },
  // Devuelve un dígito transformado a tu idioma
  transformDigit(digit) {
    return digit;
  },
  // Textos en el selector de fecha
  nextMonth: "Próximo Mes",
  previousMonth: "Mes Anterior",
  openMonthSelector: "Abrir Selector de Mes",
  openYearSelector: "Abrir Selector de Año",
  closeMonthSelector: "Cerrar Selector de Mes",
  closeYearSelector: "Cerrar Selector de Año",
  defaultPlaceholder: "Seleccionar...",
  // Para el valor de rango de entrada
  from: "de",
  to: "a",
  // Utilizado para el valor de entrada cuando se seleccionan múltiples fechas
  digitSeparator: ",",
  // Si proporcionas -2, por ejemplo, el año será de 2 dígitos
  yearLetterSkip: 0,
  // ¿Es tu idioma rtl o ltr?
  isRtl: false,
};

export const getCalendarLocales = () => {
  let language = "en";
  let zkBioCloudMediaLang =localStorage.getItem("zkBioCloudMediaLang");
  if (zkBioCloudMediaLang && zkBioCloudMediaLang == "zh") {
    language = "zh";
  } else if (zkBioCloudMediaLang === "es") {
    language = "es";
  }
  if (zkBioCloudMediaLang === "en") {
    return "en";
  } else if (zkBioCloudMediaLang === "zh") {
    return myCustomLocaleCN;
  } else if (zkBioCloudMediaLang === "es") {
    return myCustomLocaleES;
  } else {
    return "en";
  }
};
