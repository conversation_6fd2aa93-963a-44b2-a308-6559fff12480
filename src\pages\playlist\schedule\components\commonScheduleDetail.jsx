/* eslint-disable react/prop-types */
import React, {
  useEffect,
  useState,
  useRef,
  forwardRef,
  useImperativeHandle,
} from "react";
import {
  Grid,
  OutlinedInput,
  Stack,
  InputAdornment,
  InputBase,
  Typography,
  InputLabel,
} from "@mui/material";
import { useFormik } from "formik";
// api
import { getScheduleInfo } from "@/service/api/schedule";
import "@amir04lm26/react-modern-calendar-date-picker/lib/DatePicker.css";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
// i18n
import { useTranslation } from "react-i18next";
import DateSvg from "./DateSvg";
import TimeButtonSelect from "./TimeButtonSelect";
import Container from "@mui/material/Container";
import {
  BootstrapDialog,
  BootstrapContent,
  BootstrapDialogTitle,
} from "@/components/dialog";

const CommonScheduleDetail = forwardRef((props, ref) => {
  const timeButtonSelectRef = useRef(null);
  const { t } = useTranslation();
  const [detailOpen, setDetailOpen] = useState(false);

  const handleClose = () => {
    setDetailOpen(false);
  };

  useImperativeHandle(ref, () => ({
    handleOpen,
  }));

  const handleOpen = (id) => {
    handleGetScheduleInfo(id);
    setDetailOpen(true);
  };

  //获取排期数据
  const handleGetScheduleInfo = async (id) => {
    await getScheduleInfo(id).then((res) => {
      const result = res.data;
      setFormData(result);

      const monthWeekDayMap =
        timeButtonSelectRef.current.getDaysWeeksAndMonthsDate(
          result.startDate,
          result.stopDate
        );
      timeButtonSelectRef.current.handleSelectedDate(result);
      timeButtonSelectRef.current.handleChangeDaysAble(
        result.playWeeksNum,
        result.playMonths,
        monthWeekDayMap
      );
    });
  };

  // 表单赋值
  const setFormData = (data) => {
    scheduleForm.setValues(
      {
        id: data.id,
        name: data.name,
        inventoryList: data.inventoryList,
        startDate: data.startDate,
        stopDate: data.stopDate,
        playWeeks: data.playWeeksNum,
        playDays: data.playDays,
        playMonths: data.playMonths,
        screenIds: data.screenIds,
      },
      true
    );
  };

  const scheduleForm = useFormik({
    initialValues: {
      name: "",
      playDirection: "0",
      startDate: "",
      stopDate: "",
      screenIds: "",
      inventoryList: [],
    },
  });

  //清单选择组件
  function PlayListSelect(props) {
    return (
      <>
        <Grid
          container
          sx={{ marginBottom: "10px" }}
          justifyContent="flex-start"
          alignItems="center"
        >
          <Grid item xs={5.5} sm={5.5} md={5.5}>
            <OutlinedInput
              id="schedule-playListName"
              type="text"
              name="playListName"
              sx={{ width: "100%" }}
              readOnly
              value={
                scheduleForm.values.inventoryList[props.index].playListName
              }
            ></OutlinedInput>
          </Grid>
          <Grid item xs={1}>
            <Typography variant="body1" align="center">
              {t("common.common_from")}
            </Typography>
          </Grid>
          <Grid item xs={5.5} sm={5.5} md={5.5}>
            <OutlinedInput
              value={scheduleForm.values.inventoryList[props.index].stopTime}
              id="infor-firstName"
              type="text"
              name="stopTime"
              readOnly
              sx={{ width: "100%" }}
              placeholder={t("common.common_endTime")}
              startAdornment={
                <InputAdornment position="start" sx={{ width: "120%" }}>
                  <InputBase
                    readOnly
                    endAdornment={<DateSvg />}
                    value={
                      scheduleForm.values.inventoryList[props.index].startTime
                    }
                    name="startTime"
                    // id="quick-area"
                    type="text"
                    placeholder={t("common.common_startTime")}
                    sx={{ width: "100%" }}
                  />
                </InputAdornment>
              }
            />
          </Grid>
        </Grid>
      </>
    );
  }

  return (
    <BootstrapDialog
      fullWidth
      maxWidth="lg"
      onClose={handleClose}
      aria-labelledby="customized-dialog-title"
      open={detailOpen}
    >
      <BootstrapDialogTitle onClose={handleClose}>
        <Typography variant="h4" component="p">
          {t("ips.ips_schedule_detail")}
        </Typography>
      </BootstrapDialogTitle>
      <BootstrapContent>
        <form noValidate>
          <Container maxWidth="sm" sx={{ padding: 3 }}>
            <Grid container spacing={3} sx={{ marginBottom: "5px" }}>
              <Grid item xs={6}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="schedule-name">
                    {t("ips.ips_scheduling")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <OutlinedInput
                    sx={{
                      width: "100%",
                    }}
                    fullWidth
                    id="schedule-name"
                    type="text"
                    name="name"
                    value={scheduleForm.values.name}
                  />
                </Stack>
              </Grid>
              <Grid item xs={6}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="infor-firstName">
                    {t("ips.ips_scheduling_playTime")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <OutlinedInput
                    readOnly
                    autoFocus={false}
                    autoComplete="off"
                    name="startDate"
                    type="text"
                    startAdornment={
                      <InputAdornment position="start">
                        <CalendarMonthIcon />
                      </InputAdornment>
                    }
                    value={scheduleForm.values.startDate}
                    fullWidth
                    endAdornment={
                      <InputAdornment position="end" sx={{ width: "120%" }}>
                        <InputBase
                          readOnly
                          autoComplete="off"
                          startAdornment={<DateSvg></DateSvg>}
                          autoFocus={false}
                          name="stopDate"
                          value={scheduleForm.values.stopDate}
                          sx={{
                            width: "100%",
                          }}
                          type="text"
                          placeholder={t("common.common_startTime")}
                        />
                      </InputAdornment>
                    }
                    sx={{
                      width: "100%",
                    }}
                    placeholder={t("common.common_endTime")}
                  />
                </Stack>
              </Grid>
            </Grid>
            <Grid
              container
              spacing={1}
              direction="row"
              justifyContent="center"
              alignItems="flex-start"
            >
              <Grid
                item
                xs={12}
                justifyContent="center"
                alignItems="flex-start"
              >
                <InputLabel htmlFor="schedule-name">
                  {t("ips.ips_current_playlist")}
                  <i style={{ color: "red" }}>*</i>
                </InputLabel>
              </Grid>
              <Grid
                item
                xs={12}
                justifyContent="center"
                alignItems="flex-start"
              >
                <Grid container>
                  {scheduleForm.values.inventoryList.map(
                    (inventoryOb, index) => (
                      <PlayListSelect
                        key={index}
                        index={index}
                        inventoryOb={inventoryOb}
                      />
                    )
                  )}
                </Grid>
              </Grid>
              <Grid
                container
                xs={12}
                direction="row"
                justifyContent="flex-end"
                alignItems="center"
              ></Grid>
            </Grid>
            {/* 时间选择按钮组 */}
            <TimeButtonSelect ref={timeButtonSelectRef} />
            {/* <Grid
              sx={{ marginTop: "10px" }}
              container
              justifyContent="center"
              alignItems="center"
            >
              <Stack direction="row" spacing={2}>
                <Button
                  size="large"
                  variant="contained"
                  color="secondary"
                  onClick={handleClose}
                >
                  关闭
                </Button>
              </Stack>
            </Grid> */}
          </Container>
        </form>
      </BootstrapContent>
    </BootstrapDialog>
  );
});

export default CommonScheduleDetail;
