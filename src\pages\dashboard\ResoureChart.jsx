/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/prop-types */
import { useEffect, useRef } from "react";
import * as echarts from "echarts";
import { useTranslation } from "react-i18next";
// import { Grid } from "@mui/material";

const ResoureChart = (props) => {
  const { data } = props;
  const chartRef = useRef(null);
  const { t } = useTranslation();

  useEffect(() => {
    let myEcharts = null;
    const initChart = () => {
      // 初始化Echarts图表
      myEcharts = echarts.init(chartRef.current, null, { renderer: "svg" });

      // 设置初始大小
      myEcharts.resize();

      // 监听窗口大小变化，自动调整图表大小
      window.addEventListener("resize", handleResize);
      const options = getOptions();
      myEcharts.setOption(options);
    };

    const handleResize = () => {
      myEcharts.resize();
    };

    // 在组件挂载时进行初始化
    initChart();

    // 在组件卸载时移除事件监听
    return () => {
      window.removeEventListener("resize", handleResize);
      myEcharts.dispose();
    };
  }, [data]);
  const getOptions = () => {
    let option = {
      tooltip: {
        trigger: "item",
      },
      //图条说明
      legend: {
        orient: "vertical", //设置图例的方向
        right: 1,
        top: "center",
        icon: "circle",
        itemWidth: 10, // 宽度
        itemHeight: 10, // 高度
        itemGap: 5, //设置图例的间距
      },
      color: ["#7AC143", "#F12737", "#C9CAEA"],
      // color: colorArray,
      series: [
        {
          type: "pie",
          radius: ["28%", "44%"],
          // avoidLabelOverlap: false,
          // scale: false, //鼠标划入不会变大
          itemStyle: {
            borderColor: "#fff",
            borderWidth: 5,
            // borderRadius: 10,
          },
          label: {
            show: true,
            formatter(param) {
              return param.value;
            },
          },

          labelLine: {
            show: true,
          },
          data: data,
        },
      ],
      graphic: [
        {
          type: "text",
          left: "center",
          top: "center",
          style: {
            text: t("ips.ips_personal_data"),
            textAlign: "center",
            fontFamily: "微软雅黑",
            fill: "#92A5B7",
            fontSize: "0.8rem", // 文字大小
            fontWeight: 400, // 文字粗细
          },
        },
      ],
    };
    return option;
  };

  return <div ref={chartRef} style={{ width: "100%", height: "390px" }}></div>;
};
export default ResoureChart;
