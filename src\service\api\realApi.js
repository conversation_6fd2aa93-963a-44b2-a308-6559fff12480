import request from "@/utils/request";

/**
 *  获取总数
 *
 */
export const getTotals = (params) => {
  return request({
    url: `/v1/demographic/api/statistics/totals`,
    method: "get",
    params: params,
  });
};

export const getMaterials = (params) => {
  return request({
    url: `/v1/demographic/api/statistics/materials`,
    method: "get",
    params: params,
  });
};

/**
 * 年龄段百分比
 * @param {*} params
 * @returns
 */
export const getAgeGroup = (params) => {
  return request({
    url: `/v1/demographic/api/statistics/age-group`,
    method: "get",
    params: params,
  });
};


/**
 *性别百分比
 * @param {*} params
 * @returns
 */
export const getGenderGroup = (params) => {
  return request({
    url: `/v1/demographic/api/statistics/gender-group`,
    method: "get",
    params: params,
  });
};


/**
 * 性别柱状图
 * @param {*} params
 * @returns
 */
export const getGenderGroupBar = (params) => {
  return request({
    url: `/v1/demographic/api/statistics/gender-group-bar`,
    method: "get",
    params: params,
  });
};


/**
 * 年龄折线图
 * @param {*} params
 * @returns
 */
export const getAgeGroupLine = (params) => {
  return request({
    url: `/v1/demographic/api/statistics/age-group-line`,
    method: "get",
    params: params,
  });
};


export const getStayTime = (params) => {
  return request({
    url: `/v1/demographic/api/statistics/stay-time`,
    method: "get",
    params: params,
  });
}