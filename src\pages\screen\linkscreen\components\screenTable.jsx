/* eslint-disable react/prop-types */
import React, { useEffect, useMemo, useState, useRef, forwardRef } from "react";
import MaterialReactTable from "material-react-table";

import { tableI18n } from "@/utils/tableLang";
import { listByPage } from "@/service/api/screen";
// i18n
import { useTranslation } from "react-i18next";
import {
  Button,
  Stack,
  Typography,
  Tooltip,
  Grid,
  InputLabel,
  TextField,
} from "@mui/material";

import ZKSelect from "@/components/ZKSelect";

import { useFormik } from "formik";
import { removeEmpty } from "@/utils/StringUtils";

import { screenStatus, screendirections } from "@/dict/commonDict";
import DictTag from "@/components/DictTag";
const LinkscreenTable = forwardRef((props, ref) => {
  const { t } = useTranslation();

  const [rowSelection, setRowSelection] = useState([]);
  const [isError, setIsError] = useState(false);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  // 表格数据
  const [data, setData] = useState([]);
  // 分页的参数
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);

  const setTableObject = (tableSelectRow) => {
    props.setTableObject(tableSelectRow);
  };

  const requestParams = useRef(null);

  // 构建参数
  const buildParams = () => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      filterDevice: true,
      outletId: props.getData,
      category:"2",
      ...requestParams.current,
    };
    return params;
  };
  // 获取数据
  const getTableData = async () => {
    if (!data.length) {
      setIsLoading(true);
    } else {
      setIsRefetching(true);
    }
    await listByPage(buildParams())
      .then((res) => {
        // 设置数据
        setData(res.data.data);
        // 设置总记录数
        setRowCount(res.data.total);
        setIsLoading(false);
        setIsRefetching(false);
      })
      .catch((err) => {
        setIsError(true);
        setIsLoading(false);
        setIsRefetching(false);
      });
  };
  useEffect(() => {
    // 发请求
    getTableData();
  }, [pagination.pageIndex, pagination.pageSize]);
  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "storeName",
        header: t("ips.ips_store_name"),
        enableColumnActions: false,
        size: 120,
        Cell: ({ cell, row }) => {
          return (
            <Tooltip title={row.original.storeName} placement="top">
              <Typography className="textSpace">
                {row.original.storeName}
              </Typography>
            </Tooltip>
          );
        },
      },
      {
        accessorKey: "name", //access nested data with dot notation
        header: t("ips.ips_device"),
        //列头点击相关事件关闭
        enableColumnActions: false,
        size: 160,
        Cell: ({ cell, row }) => {
          return (
            <Tooltip title={row.original.name} placement="top">
              <Typography className="textSpace">{row.original.name}</Typography>
            </Tooltip>
          );
        },
      },
      // {
      //   accessorKey: "ipAddress",
      //   header: t("system.system_ip"),
      //   enableColumnActions: false,
      //   size: 100,
      // },
      {
        accessorKey: "direction",
        header: t("common.common_direction"),
        enableColumnFilter: false,
        enableColumnActions: false,
        size: 120,
        Cell: ({ cell, row }) => {
          return (
            <DictTag
              dicts={screendirections}
              fieldName={{ value: "value", title: "label", color: "color" }}
              value={row.original.direction}
            />
          );
        },
      },
      {
        accessorKey: "address",
        enableColumnActions: false,
        header: t("common.common_location"),
        size: 120,
        Cell: ({ cell, row }) => {
          return (
            <Tooltip title={row.original.address} placement="top">
              <Typography className="textSpace">
                {row.original.address}
              </Typography>
            </Tooltip>
          );
        },
      },
      {
        accessorKey: "status",
        header: t("common.common_status"),
        enableColumnFilter: false,
        enableColumnActions: false,
        size: 80,
        Cell: ({ cell, row }) => {
          return (
            <DictTag
              dicts={screenStatus}
              fieldName={{ value: "value", title: "label", color: "color" }}
              value={row.original.status}
            />
          );
        },
      },
    ],
    []
  );

  // 查询表单
  const queryFormik = useFormik({
    initialValues: {
      direction: "",
      name: "",
    },
    onSubmit: async (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        // 拷贝value值
        const tempValue = { ...values };
        await removeEmpty(tempValue);
        // setRequestParams(tempValue);
        requestParams.current = tempValue;
        setPagination({
          pageIndex: 0,
          pageSize: 10,
        })
        getTableData();
        // 查询table
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
  });
  // 重置查询
  const resetQuery = () => {
    // 重置表单
    queryFormik.resetForm();
    requestParams.current = null;
    setPagination({
      pageIndex: 0,
      pageSize: 10,
    })
    getTableData();
  };
  return (
    <>
      <MaterialReactTable
        // table 状态
        state={{
          // 加载状态
          //isLoading,
          // 分页参数
          pagination,
          // 重新拉取
          showProgressBars: isRefetching,
          showAlertBanner: isError,

          rowSelection,
        }}
        muiTablePaperProps={{
          elevation: 0,
          sx: {
            borderRadius: "5px",
            // border: "1px solid #f0f0f0",
          },
        }}
        // enableTopToolbar={false}
        muiTableHeadRowProps={{
          sx: { backgroundColor: "#fafafa", boxShadow: "none" },
        }}
        renderToolbarInternalActions={({ table }) => (
          <>
            {/* <Tooltip title="展开或者收起" arrow>
                            <IconButton onClick={() => setShowSearch(!showSearch)}>
                                <SearchIcon />
                            </IconButton>
                        </Tooltip> */}
          </>
        )}
        // 解决列太多宽度太长问题
        enableColumnResizing
        // 初始化状态
        initialState={{ columnVisibility: { createTime: false } }}
        muiToolbarAlertBannerProps={
          isError
            ? {
              color: "error",
              children: t("table.loading_error"),
            }
            : undefined
        }
        // 开启多选
        enableRowSelection
        enableMultiRowSelection={false}
        // 列数
        rowCount={rowCount}
        // 固定头部
        enableStickyHeader
        // 处理表格高度
        muiTableContainerProps={{ sx: { maxHeight: "320px" } }}
        // 设置背景颜色
        muiTableBodyCellProps={{ sx: { backgroundColor: "white" } }}
        muiTableProps={{ sx: { backgroundColor: "white" } }}
        muiBottomToolbarProps={{ sx: { backgroundColor: "white" } }}
        muiTopToolbarProps={{ sx: { backgroundColor: "white" } }}
        //行选中
        onRowSelectionChange={setRowSelection}
        muiTableBodyRowProps={({ row }) => ({
          onClick: row.getToggleSelectedHandler(),
          sx: { cursor: "pointer" },
        })}
        // 分页回调函数
        onPaginationChange={setPagination}
        // 记得开启这些 tips:否则会出现useEffect 反作用函数发起多次请求点击下一页会出问题
        manualFiltering
        manualPagination
        manualSorting
        // 开启分页
        enablePagination
        // 列定义
        columns={columns}
        // 数据
        data={data}
        // 初始化国际化语言
        localization={tableI18n}
        // 自定义表头按钮
        renderTopToolbarCustomActions={({ table }) => {
          setTableObject(table.getSelectedRowModel().rows);
          return (
            <div style={{ width: "100%" }}>
              <form noValidate onSubmit={queryFormik.handleSubmit}>
                <Grid
                  container
                  direction="row"
                  justifyContent="flex-start"
                  alignItems="center"
                  spacing={2}
                >
                  <Grid item xs={12} md={4} lg={3}>
                    <TextField
                      label={t("ips.ips_device")}
                      value={queryFormik.values.name}
                      onChange={queryFormik.handleChange}
                      onBlur={queryFormik.handleBlur}
                      size="small"
                      type="text"
                      name="name"
                      fullWidth
                      placeholder={t("common.common_input_device_name")}
                    />
                  </Grid>
                  <Grid item xs={12} md={4} lg={4}>
                    <ZKSelect
                      size="small"
                      label={t("common.common_direction")}
                      name="direction"
                      value={queryFormik.values.direction}
                      onChange={queryFormik.handleChange}
                      onBlur={queryFormik.handleBlur}
                      options={screendirections}
                      onClear={() => {
                        queryFormik.setFieldValue("direction", "");
                      }}
                      placeholder={t("common.common_plese_screen_direction")}
                    />
                  </Grid>

                  <Grid item xs={12} md={6} lg={3}>
                    <Stack
                      direction="row"
                      justifyContent="flex-start"
                      alignItems="flex-start"
                      spacing={2}
                    >
                      <Button
                        disableElevation
                        type="submit"
                        variant="contained"
                        size="small"
                      >
                        {t("common.common_table_query")}
                      </Button>
                      <Button
                        variant="outlined"
                        onClick={resetQuery}
                        color="info"
                        size="small"
                        sx={{
                            minWidth:'90px'
                          }}
                      >
                        {t("common.common_op_reset")}
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </div>
          );
        }}
        // 多选底部提示
        positionToolbarAlertBanner="none"
        // 开启action操作
        //enableRowActions
        // action操作位置
        positionActionsColumn={false}
        //禁用列过滤
        enableColumnFilters={false}
      />
    </>
  );
});

export default LinkscreenTable;
