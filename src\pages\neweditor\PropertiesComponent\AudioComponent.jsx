import React from "react";
import { Grid } from "@mui/material";
import { styled } from "@mui/material/styles";
import AddIcon from "@mui/icons-material/Add";
import { getComponentId } from "../common/utils";
import { useState } from "react";
import { FormLabel, PrettoSlider } from "./PropertiesComponent";
import CustomInput from "../components/CustomInput";
import { useEffect } from "react";
import musiclist from "./musicList";
import { toast } from "react-toastify";
import { message } from "../common/i18n";

import { isPositiveInteger } from "../common/utils";

let initproperties = {
  title: "editor_audio",
  name: "editor_audio",
  type: "ZKTecoMusic",
  editType: "ZKTecoMusicEdit",
  icon: "icon-music",
  left: 10,
  top: 10,
  width: 200,
  height: 150,
  zIndex: 50,
  interact: {
    type: "",
    site: "",
    apk: "",
    idx: null,
  },
  borderRadius: 0,
  hide: false,
  transparency: 0,
};

const AudioComponent = (props) => {
  const currentPageIndex = props.currentPageIndex;
  const currentComponentId = props.currentComponentId;
  const setCurrentComponentId = props.setCurrentComponentId;
  const activeTempIndex = props.activeTempIndex;
  const pages = props.pages;
  const setPages = props.setPages;
  const setCurrentComponentIndex = props.setCurrentComponentIndex;
  const currentIndex = props.currentComponentIndex;
  const [properties, setProperties] = useState({
    ...initproperties,
  });

  useEffect(() => {
    if (currentIndex !== "") {
      const curretnPage = pages[currentPageIndex];
      if (curretnPage.isTemplate) {
        let componentInfo =
          curretnPage.tempLayout[activeTempIndex]?.componentList[currentIndex];
        setProperties({
          ...componentInfo,
        });
      } else {
        let componentInfo =
          pages[currentPageIndex]?.componentList[currentIndex];
        setProperties({
          ...componentInfo,
        });
      }
    }
  }, [currentPageIndex, currentIndex, activeTempIndex, pages]);

  const [audioList, setAudioList] = useState([...musiclist]);

  const setComponentInfo = (baseInfo) => {
    const newPages = JSON.parse(JSON.stringify(props.pages));
    let currentPage = newPages[currentPageIndex];
    if (currentPage.isTemplate) {
      let oldInfo =
        currentPage.tempLayout[activeTempIndex].componentList[currentIndex];
      let newInfo = {
        ...oldInfo,
        ...baseInfo,
      };
      currentPage.tempLayout[activeTempIndex].componentList[currentIndex] =
        newInfo;
    } else {
      let oldInfo = newPages[currentPageIndex].componentList[currentIndex];
      let newInfo = {
        ...oldInfo,
        ...baseInfo,
      };
      newPages[currentPageIndex].componentList[currentIndex] = newInfo;
    }

    if (props.setPages) {
      props.setPages(newPages);
    }
  };

  const addAudioBox = (item) => {
    const newPages = JSON.parse(JSON.stringify(props.pages));
    let currentPage = newPages[currentPageIndex];
    if (currentPage.isTemplate) {
      if (activeTempIndex === "") {
        toast.success(message("editor_select_template_tip"));
      } else {
        let musicList = currentPage.tempLayout
          .map((cmpList) => {
            return cmpList.componentList.filter((item) => {
              if (item.type === "ZKTecoMusic") {
                return true;
              } else {
                return false;
              }
            });
          })
          .reduce(function (acc, curr) {
            return acc.concat(curr);
          }, []);

        if (musicList.length < 1) {
          let ComponentId = getComponentId(pages);
          let initProps = {
            ...initproperties,
            checksum: item.checksum,
            musicName: item.name,
            musicUrl: item.downloadUrl,
            duration: 30,
            componentId: ComponentId,
            musicId: item.id,
          };
          setProperties({ ...initProps });
          currentPage.tempLayout[activeTempIndex].componentList.push({
            ...initProps,
          });

          setPages([...newPages]);
          let index =
            currentPage.tempLayout[activeTempIndex].componentList.length - 1;
          setCurrentComponentIndex(index);
          setCurrentComponentId(ComponentId);
        } else {
          toast.error(message("editor_audio_error_message"));
        }
      }
    } else {
      let audioSize = pages[currentPageIndex].componentList.filter((item) => {
        if (item.type === "ZKTecoMusic") {
          return true;
        } else {
          return false;
        }
      });
      if (audioSize.length < 1) {
        let ComponentId = getComponentId(pages);
        let initProps = {
          ...initproperties,
          checksum: item.checksum,
          musicName: item.name,
          musicUrl: item.downloadUrl,
          duration: 30,
          componentId: ComponentId,
          musicId: item.id,
        };
        setProperties({ ...initProps });
        pages[currentPageIndex].componentList.push({ ...initProps });
        setPages([...pages]);
        let index = pages[currentPageIndex].componentList.length - 1;
        setCurrentComponentIndex(index);
        setCurrentComponentId(ComponentId);
      } else {
        toast.error(message("editor_audio_error_message"));
      }
    }
  };
  const [value, setValue] = useState("attributes");

  const [open, setOpen] = useState(false);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  const changeProperties = (event) => {
    const name = event.target.name;
    let newInfo = {
      ...properties,
      [name]: event.target.value,
    };
    setComponentInfo(newInfo);
  };

  const changeDuration = (event, index) => {
    let value = event.target.value;
    if (value === "" || isPositiveInteger(value)) {
      if (value > 999999) {
        value = 999999;
      }
      if (value < 1) {
        value = 1;
      }

      properties.audioList[index].duration = value;
      setProperties({
        ...properties,
      });
      let newInfo = {
        ...properties,
      };
      setComponentInfo(newInfo);
    } else {
      properties.audioList[index].duration = 10;
      setProperties({
        ...properties,
      });
      let newInfo = {
        ...properties,
      };
      setComponentInfo(newInfo);
      toast.error(message("editor_number_error_message"));
    }
  };

  // const handleClose = () => {
  //     setOpen(false);
  // };

  // const deleteImage = (index) => {
  //     properties.imgList.splice(index, 1);
  //     let newInfo = {
  //         ...properties
  //     };
  //     setComponentInfo(newInfo);
  // };

  // const handleSliderChange = (event, newValue) => {
  //     let newInfo = {
  //         ...properties,
  //         transparency: newValue
  //     };
  //     setComponentInfo(newInfo);
  // };

  const handleRotationChange = (event, newValue) => {
    let newInfo = {
      ...properties,
      rotate: newValue,
    };
    setComponentInfo(newInfo);
  };

  return (
    <Grid
      sx={{
        display: "flex",
        justifyContent: "center",
        height: "100%",
      }}>
      {currentComponentId ? (
        <Grid
          sx={{
            width: "100%",
            boxShadow: "0px 0px 6px #00000029",
            borderRadius: "10px",
            backgroundColor: "#ffffff",
            overflow: "auto",
          }}>
          <Grid
            sx={{
              p: 2,
            }}>
            <Grid
              sx={{
                display: "flex",
                alignItems: "center",
                mt: 1,
              }}>
              <FormLabel sx={{ mr: 2 }}> {message("editor_rotate")}:</FormLabel>
              <PrettoSlider
                onChange={handleRotationChange}
                size="small"
                min={0}
                max={360}
                step={1}
                color="secondary"
                value={properties.rotate}
                aria-label="Small"
                // valueLabelDisplay="off"
              ></PrettoSlider>
            </Grid>

            <CustomInput
              label={message("editor_abscissa") + ":"}
              value={properties.left}
              onChange={changeProperties}
              name="left"></CustomInput>

            <CustomInput
              label={message("editor_ordinate") + ":"}
              value={properties.top}
              onChange={changeProperties}
              name="top"></CustomInput>

            <CustomInput
              label={message("editor_width") + ":"}
              value={properties.width}
              onChange={changeProperties}
              name="width"></CustomInput>

            <CustomInput
              label={message("editor_height") + ":"}
              value={properties.height}
              onChange={changeProperties}
              name="height"></CustomInput>
          </Grid>
        </Grid>
      ) : (
        <Grid
          sx={{
            width: "100%",
          }}>
          {/* <FormLabel>{message("editor_uploadAudio")}</FormLabel>
          <Grid
            sx={{
              width: "100%",
              height: "80px",
              background: "#ffffff",
              borderRadius: "10px",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              cursor: "pointer",
              mt: 2,
            }}
          >
            {message("editor_uploadAudio")}
            <AddIcon></AddIcon>
          </Grid> */}

          <Grid
            sx={{
              mt: 2,
              pb: 2,
            }}>
            <Grid
              sx={{
                pb: 2,
              }}
              container
              rowSpacing={1}
              columnSpacing={1}>
              {audioList.map((item) => (
                <Grid
                  onClick={() => {
                    addAudioBox(item);
                  }}
                  item
                  xs={12}
                  key={item.id}>
                  <Grid
                    sx={{
                      width: "100%",
                      height: "100%",
                      border: "1px solid #ebeef5",
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                      p: 1,
                      cursor: "pointer",
                      backgroundColor: "#ffff",
                      borderRadius: "5px",
                      "&:hover": {
                        backgroundColor: "#ebeef5",
                      },
                    }}>
                    {item.name}
                  </Grid>
                </Grid>
              ))}
            </Grid>
          </Grid>
        </Grid>
      )}
    </Grid>
  );
};

export default AudioComponent;
