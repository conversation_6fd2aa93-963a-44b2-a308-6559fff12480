import request from "@/utils/request";

/**
 * 查询一年内设备新增数量，按月分组，实现柱状图
 * @param {*} params
 * @returns
 */
export const getScreenByYear = (params) => {
  return request({
    url: `/sd/v1/outlet/screen/year`,
    method: "get",
    params: params,
  });
};

export const getTotalScreen = (params) => {
  return request({
    url: `/sd/v1/outlet/total/screen`,
    method: "get",
    params: params,
  });
};

export const getInstalledRate = (params) => {
  return request({
    url: `/sd/v1/outlet/installed_rate`,
    method: "get",
    params: params,
  });
};

/**
 * 查询总的门店数量以及当年新增的门店数量
 * @param {*} params
 * @returns
 */
export const getOutletTotal = (params) => {
  return request({
    url: `/sd/v1/outlet/total`,
    method: "get",
    params: params,
  });
};

/**
 * 每月已安装门店和预安装门店数量
 * @param {*} params 
 * @returns 
 */
export const getOutletComparison = (params) => {
  return request({
    url: `/sd/v1/outlet/total/comparison`,
    method: "get",
    params: params,
  });
};
