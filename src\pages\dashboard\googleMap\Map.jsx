/* eslint-disable no-undef */
/* eslint-disable react/prop-types */
/* eslint-disable react-hooks/rules-of-hooks */
import React, { useState, useEffect, useRef } from "react";
import GoogleMapReact from "google-map-react";

// import { Marker, InfoWindow } from "@react-google-maps/api";
import { getPrincipaList } from "@/service/api/L3Sevice.js";
import { Paper, Typography, Box } from "@mui/material";
import LocationOnOutlinedIcon from "@material-ui/icons/LocationOnOutlined";
import { useTranslation } from "react-i18next";
import RoomIcon from "@mui/icons-material/Room";
import { Grid } from "@mui/material";
import { Stack } from "@mui/system";
import "./index.less";
function Map({ coordinates, zoom, centerData, addressData }) {
  const { t } = useTranslation();
  const [markers, setMarkers] = useState([]); //存储标记点位置数据
  const [data, setData] = useState([]); //存储数据
  const [selectedMarkerIndex, setSelectedMarkerIndex] = useState(null); //判断点击的是那个卡片
  const [isCardOpen, setIsCardOpen] = useState(false); //是否关闭卡片
  const [currentZoom, setCurrentZoom] = useState(6); // 设置默认的缩放级别
  const [isInfoCardVisible, setIsInfoCardVisible] = useState(true);
  const [paddingLeft, setPaddingLeft] = useState([50, 20, 65, 65]);
  //监听屏幕宽度变化
  const [windowWidth, setWindowWidth] = useState(0);
  const [textHeight, setTextHeight] = useState(0);
  // 地图数据
  const mapStyles = [
    {
      featureType: "all",
      elementType: "labels.text",
      stylers: [
        {
          color: "#878787",
        },
      ],
    },
    {
      featureType: "all",
      elementType: "labels.text.stroke",
      stylers: [
        {
          visibility: "off",
        },
      ],
    },
    {
      featureType: "landscape",
      elementType: "all",
      stylers: [
        {
          color: "#f9f5ed",
        },
      ],
    },
    {
      featureType: "road.highway",
      elementType: "all",
      stylers: [
        {
          color: "#f5f5f5",
        },
      ],
    },
    {
      featureType: "road.highway",
      elementType: "geometry.stroke",
      stylers: [
        {
          color: "#c9c9c9",
        },
      ],
    },
    {
      featureType: "water",
      elementType: "all",
      stylers: [
        {
          color: "#aee0f4",
        },
      ],
    },
  ];
  // const mapRef = useRef(null); //创建地图实例
  //获取数据
  const handlegetPrincipaList = () => {
    getPrincipaList().then((res) => {
      setData(res.data);
      //设置标记点数据
      setMarkers(res.data.storeInfoList);
    });
  };
  const typographyRef = useRef(null);
  // 通过事件对象获取浏览器窗口的高度
  const resizeUpdate = (e) => {
    let getWidth = e.target.innerWidth;
    // console.log("屏幕宽度getWidth", getWidth);
    setWindowWidth(getWidth);
  };
  useEffect(() => {
    // 页面刚加载完成后获取浏览器窗口的大小
    let h = window.innerWidth;
    setWindowWidth(h);
    // 页面变化时获取浏览器窗口的大小
    window.addEventListener("resize", resizeUpdate);
    // Get the height of Typography element
    if (typographyRef.current) {
      setTextHeight(typographyRef.current.clientHeight);
    }
    return () => {
      // 组件销毁时移除监听事件
      window.removeEventListener("resize", resizeUpdate);
    };
  }, []);
  //根据屏幕分辨率调整padding值
  useEffect(() => {
    let newWidth;
    if (windowWidth === 1920) {
      newWidth = -900;
    } else if (windowWidth === 2133) {
      newWidth = -1000;
    } else if (windowWidth === 1745) {
      newWidth = -800;
    } else if (windowWidth === 1536) {
      newWidth = -750;
    } else if (windowWidth === 2400) {
      newWidth = -1100;
    } else {
      newWidth = -900;
    }
    setPaddingLeft(newWidth);
  }, [windowWidth]);

  // Handle marker hover
  const handleMarkerHover = (index) => {
    setSelectedMarkerIndex(index);
    setIsInfoCardVisible(true);
  };

  // Handle marker hover exit
  const handleMarkerHoverExit = () => {
    setSelectedMarkerIndex(null);
    setIsInfoCardVisible(false);
  };

  useEffect(() => {
    handlegetPrincipaList();
  }, [coordinates]);

  return (
    <div
      style={{
        width: "100vw",
        height: "700px",
        position: "relative",
      }}>
      <Box
        // xs={12}
        // sm={6}
        // md={6}
        style={{
          position: "absolute",
          top: "20%",
          left: "20px",
          zIndex: 99999,
          // top: "-300px",
          // left: "-1040px",
        }}>
        <Stack
          sx={{
            color: "#7AC143",
            backgroundColor: "#DEEDDE",
            border: "2px solid #7cb305",
            borderRadius: 2,
            paddingLeft: "10px",
            paddingTop: "15px",
            paddingRight: "20px",
            paddingBottom: "0px",
            marginBottom: "10px",
          }}>
          <Typography variant="h5" gutterBottom>
            {t("ips.ips_total_outlet_num")}
          </Typography>
          <Typography sx={{ fontSize: "17px" }} gutterBottom>
            {data.totalStoreNum}
          </Typography>
        </Stack>

        <Stack
          sx={{
            color: "#7AC143",
            backgroundColor: "#DEEDDE",
            border: "2px solid #7cb305",
            borderRadius: 2,
            paddingLeft: "10px",
            paddingRight: "20px",
            paddingTop: "15px",
            paddingBottom: "0px",
            marginBottom: "10px",
          }}>
          <Typography variant="h5" gutterBottom>
            {t("ips.ips_total_signage_num")}
          </Typography>
          <Typography sx={{ fontSize: "17px" }} gutterBottom>
            {data.totalScreenNum}
          </Typography>
        </Stack>

        <Stack
          sx={{
            color: "#7AC143",
            backgroundColor: "#DEEDDE",
            border: "2px solid #7cb305",
            borderRadius: 2,
            paddingLeft: "10px",
            paddingTop: "15px",
            paddingRight: "20px",
            paddingBottom: "0px",
            marginBottom: "10px",
          }}>
          <Typography variant="h5" gutterBottom>
            {t("ips.ips_online_number")}
          </Typography>
          <Typography sx={{ fontSize: "17px" }} gutterBottom>
            {data.onlineNum}
          </Typography>
        </Stack>

        <Stack
          sx={{
            color: "#7AC143",
            backgroundColor: "#DEEDDE",
            border: "2px solid #7cb305",
            borderRadius: 2,
            paddingLeft: "10px",
            paddingTop: "15px",
            paddingBottom: "0px",
            marginBottom: "10px",
          }}>
          <Typography variant="h5" gutterBottom>
            {t("ips.ips_offline_number")}
          </Typography>
          <Typography sx={{ fontSize: "17px" }} gutterBottom>
            {data.offlineNum}
          </Typography>
        </Stack>
      </Box>
      <GoogleMapReact
        bootstrapURLKeys={{
          key: "AIzaSyA9MaTVJlWIWpINjcgyJl5eS6JDhe60238",
          version: "weekly",
          libraries: ["maps,marker"],
          language: "en",
          id: "map",
        }}
        // mapStyles={{ anchorBLdisplay: "none" }}
        center={coordinates}
        defaultZoom={6}
        zoom={zoom}
        onChange={({ zoom }) => {
          setCurrentZoom(zoom);
          // 根据缩放级别来控制信息卡片的显示和隐藏
          if (zoom < 12) {
            setIsInfoCardVisible(false);
          } else {
            setIsInfoCardVisible(true);
          }
        }}
        margin={[50, 50, 50, 50]}
        options={{
          scrollwheel: true,
          // zoomControl: false,
          // disableDefaultUI: true,
          styles: mapStyles,
        }}>
        {markers.length &&
          markers.map((place, index) => {
            const [lng, lat] = place.value;
            return (
              <Grid
                className="map-container"
                lat={Number(lat).toFixed(6)}
                lng={Number(lng).toFixed(6)}
                key={index}
                onMouseEnter={() => handleMarkerHover(index)}
                onMouseLeave={handleMarkerHoverExit}
                style={{
                  width: "24px",
                  height: "24px",
                }}>
                {selectedMarkerIndex === index && (
                  <Grid
                    sx={{
                      // width: "300px",
                      backgroundColor: "#DEEDDE",
                      border: "2px solid #7cb305",
                      alignItems: "center",
                      marginLeft: "30px",
                      position: "absolute",
                      whiteSpace: "nowrap",
                      borderRadius: "6px",
                      paddingLeft: "10px",
                      paddingRight: "10px",
                      paddingTop: "5px",
                      paddingBottom: "5px",
                      // padding: "10px 15px 10px 15px",
                      transform: "translate(0%, -20%)", // 将位置图标居中
                      zIndex: 1111111111111,
                    }}>
                    <Stack spacing={0}>
                      <Typography
                        variant="h5"
                        gutterBottom
                        sx={{ color: "#7AC143" }}>
                        {place.name}
                      </Typography>
                      <Stack spacing={1} direction="row" alignItems="center">
                        <Typography
                          variant="subtitle1"
                          sx={{ fontWeight: "400" }}>
                          {t("common.common_retailer_name")}
                        </Typography>
                        <Typography
                          variant="subtitle1"
                          sx={{ fontWeight: "400", color: "#7AC143" }}>
                          {place.name}
                        </Typography>
                      </Stack>
                      <Stack spacing={1} direction="row" alignItems="center">
                        <Typography
                          variant="subtitle1"
                          sx={{ fontWeight: "400" }}>
                          {t("ips.ips_operator_name")}
                        </Typography>
                        <Typography
                          variant="subtitle1"
                          sx={{ fontWeight: "400", color: "#7AC143" }}>
                          {place.contacts}
                        </Typography>
                      </Stack>
                      <Stack spacing={1} direction="row" alignItems="center">
                        <Typography
                          variant="subtitle1"
                          sx={{ fontWeight: "400" }}>
                          {t("ips.ips_outlet_address")}
                        </Typography>
                        <Typography
                          variant="subtitle1"
                          sx={{ fontWeight: "400", color: "#7AC143" }}>
                          {place.address}
                        </Typography>
                      </Stack>
                      <Stack spacing={1} direction="row" alignItems="center">
                        <Typography
                          variant="subtitle1"
                          sx={{ fontWeight: "400" }}>
                          {t("ips.ips_operator_email")}
                        </Typography>
                        <Typography
                          variant="subtitle1"
                          sx={{ fontWeight: "400", color: "#7AC143" }}>
                          {place.email}
                        </Typography>
                      </Stack>
                    </Stack>
                  </Grid>
                )}
                <RoomIcon className="marker" color="primary" />
                <div class="ripple"></div>
                <div class="ripple1"></div>
                {/* <div class="ripple2"></div> */}
              </Grid>
            );
          })}

        {isInfoCardVisible &&
          centerData &&
          centerData.lat !== null &&
          centerData.lng !== null && (
            <InfoCard
              addressData={addressData}
              lat={centerData.lat}
              lng={centerData.lng}
            />
          )}
      </GoogleMapReact>
    </div>
  );
}
// 自定义标记点组件
const Marker = () => (
  <div class="ripple-container">
    <div class="ripple ripple-1"></div>
    <div class="ripple ripple-2"></div>
    <div class="ripple ripple-3"></div>
    <div class="ripple ripple-4"></div>
  </div>
);
function InfoCard({ lat, lng, addressData }) {
  const { t } = useTranslation();
  return (
    <div>
      <Paper
        style={{
          backgroundColor: "#DEEDDE",
          border: "3px solid #7cb305",
          alignItems: "center",
          marginLeft: "30px",
          position: "absolute",
          whiteSpace: "nowrap",
          borderRadius: "10px",
          padding: "10px 10px 10px 10px",
          transform: "translate(0%, -20%)", // 将位置图标居中

          // transform: `translate(${lng}px, ${lat}px)`,
          zIndex: 1000,
        }}>
        <Typography variant="subtitle2">
          {t("common.common_search_info")}
        </Typography>
        <Typography variant="body2">
          {t("common.common_location_e")} {addressData}
        </Typography>
        <Typography variant="body2">
          {t("common.common_latitude")} : {lat}
        </Typography>
        <Typography variant="body2">
          {t("common.common_longitude")} : {lng}
        </Typography>
        {/* Add more information here */}
      </Paper>
    </div>
  );
}

export default Map;
