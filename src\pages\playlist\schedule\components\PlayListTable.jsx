/* eslint-disable react/prop-types */
import React, { useEffect, useMemo, useState, useRef } from "react";
import MaterialReactTable from "material-react-table";
import {
  Stack,
  Typography,
  Tooltip,
  Button,
  Grid,
  TextField,
} from "@mui/material";
// api
import { listPlayListByPage } from "@/service/api/playList";
import MainCard from "@/components/MainCard";
import ZKSelect from "@/components/ZKSelect";
import { useFormik } from "formik";
import { useLocation } from "react-router-dom";
// i18n
import { tableI18n } from "@/utils/tableLang";
import { useTranslation } from "react-i18next";
import { removeEmpty } from "@/utils/StringUtils";
import { playListType, directions, screendirections } from "@/dict/commonDict";
import DictTag from "@/components/DictTag";

const PlayListTable = (props) => {
  const { isLayout } = props;
  const { t } = useTranslation();
  const [rowSelection, setRowSelection] = useState([]);
  const location = useLocation();
  const setTableObject = (tableSelectRow) => {
    if (tableSelectRow[0]) {
      props.setTableObject(tableSelectRow[0].original);
    }
  };

  const [isError, setIsError] = useState(false);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  // 表格数据
  const [data, setData] = useState([]);
  // 分页的参数
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 5,
  });
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);
  const requestParams = useRef({ name: "", duration: "" });
  // 构建参数
  const buildParams = () => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      //   type: props.playListType,
      // fromType: "schedule",
      isLayout: isLayout,
      ...requestParams.current,
    };

    return params;
  };
  // 获取数据
  const getTableData = async () => {
    if (!data.length) {
      setIsLoading(true);
    } else {
      setIsRefetching(true);
    }
    // 开启加载
    setIsLoading(true);
    setIsRefetching(true);
    await listPlayListByPage(buildParams())
      .then((res) => {
        // 设置数据
        setData(res.data.data);
        // 设置总记录数
        setRowCount(res.data.total);
        setIsLoading(false);
        setIsRefetching(false);
      })
      .catch((err) => {
        setIsError(true);
        setIsLoading(false);
        setIsRefetching(false);
      });
  };
  useEffect(() => {
    // 发请求
    getTableData();
  }, [pagination.pageIndex, pagination.pageSize, location]);
  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "name", //access nested data with dot notation
        header: t("common.common_name"),
        //列头点击相关事件关闭
        enableColumnActions: false,
        enableSorting: false,
        // size: '200',
        Cell: ({ cell, row }) => {
          return (
            <Tooltip title={row.original.name} placement="top">
              <Typography className="textSpace">{row.original.name}</Typography>
            </Tooltip>
          );
        },
      },
      {
        accessorKey: "duration", //access nested data with dot notation
        header: t("ips.ips_playList_count_duration") + "(s)",
        enableColumnActions: false,
        enableSorting: false,
      },
      {
        accessorKey: "type",
        header: t("ips.ips_playlist_type"),
        size: 100,
        enableColumnActions: false,
        enableSorting: false,
        Cell: ({ cell, row }) => {
          return (
            <DictTag
              dicts={playListType}
              value={row.original.type}
              fieldName={{ title: "label" }}
            />
          );
        },
      },
      {
        accessorKey: "direction", //access nested data with dot notation
        header: t("common.common_direction"),
        enableColumnActions: false,
        enableSorting: false,
        Cell: ({ cell, row }) => {
          return (
            <DictTag
              dicts={screendirections}
              fieldName={{
                value: "value",
                title: "label",
                color: "color",
              }}
              value={row.original.direction}
            />
          );
        },
      },
    ],
    []
  );
  // 查询表单
  const queryFormik = useFormik({
    initialValues: {
      direction: "",
      name: "",
    },
    onSubmit: async (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        // 拷贝value值
        const tempValue = { ...values };
        await removeEmpty(tempValue);
        requestParams.current = tempValue;
        setPagination({
          pageIndex: 0,
          pageSize: 10,
        });
        getTableData();
        // 查询table
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
  });
  // 重置查询
  const resetQuery = () => {
    // 重置表单
    queryFormik.resetForm();
    requestParams.current = null;
    setPagination({
      pageIndex: 0,
      pageSize: 10,
    });
    getTableData();
  };

  return (
    <>
      <MainCard style={{ marginBottom: "10px" }}>
        <form noValidate onSubmit={queryFormik.handleSubmit}>
          <Grid
            container
            direction="row"
            justifyContent="flex-start"
            alignItems="center"
            spacing={2}>
            <Grid item xs={3}>
              {/* <Stack
                direction="row"
                justifyContent="flex-start"
                alignItems="center"
                spacing={1}
              > */}
              <TextField
                label={t("ips.ips_playlist_name")}
                value={queryFormik.values.name}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                size="small"
                name="name"
                type="text"
                fullWidth
                placeholder={t("ips.ips_playlist_name")}
              />
              {/* </Stack> */}
            </Grid>

            <Grid item xs={3}>
              <ZKSelect
                size="small"
                name="direction"
                value={queryFormik.values.direction}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                options={directions}
                onClear={() => {
                  queryFormik.setFieldValue("direction", "");
                }}
                placeholder={t("ips.ips_please_select_direction")}
                menuWidth={250}
              />
            </Grid>
            <Grid item justifyContent="flex-end">
              <Stack
                direction="row"
                justifyContent="flex-start"
                alignItems="flex-start"
                spacing={2}>
                <Button
                  disableElevation
                  type="submit"
                  variant="contained"
                  size="small">
                  {t("common.common_table_query")}
                </Button>
                <Button
                  disableElevation
                  variant="outlined"
                  onClick={resetQuery}
                  color="info"
                  size="small"
                  sx={{
                    minWidth: "90px",
                  }}>
                  {t("common.common_op_reset")}
                </Button>
              </Stack>
            </Grid>
          </Grid>
        </form>
      </MainCard>
      <MaterialReactTable
        state={{
          // 分页参数
          pagination,
          // 重新拉取
          showProgressBars: isRefetching,
          showAlertBanner: isError,
          enableSorting: true,
          // sorting,
          rowSelection,
        }}
        muiTablePaperProps={{
          elevation: 0,
          sx: {
            borderRadius: "5px",
            border: "1px solid #f0f0f0",
          },
        }}
        renderToolbarInternalActions={({ table }) => <></>}
        muiTableHeadRowProps={{
          sx: { backgroundColor: "#fafafa", boxShadow: "none" },
        }}
        // 关闭过滤搜素
        enableColumnFilters={true}
        // 关闭排序
        enableSorting={false}
        // 布局方式
        layoutMode="grid"
        enableColumnActions={false}
        enableMultiRowSelection={false}
        // 开启列对齐
        muiTableHeadCellProps={{
          sx: {
            "& .Mui-TableHeadCell-Content": {
              justifyContent: "space-between",
            },
          },
        }}
        // 解决列太多宽度太长问题
        enableColumnResizing
        // enablePinning
        // 初始化状态
        initialState={{ columnVisibility: { createTime: true } }}
        muiToolbarAlertBannerProps={
          isError
            ? {
                color: "error",
                children: "Error loading data",
              }
            : undefined
        }
        //行选中
        onRowSelectionChange={setRowSelection}
        muiTableBodyRowProps={({ row }) => ({
          onClick: row.getToggleSelectedHandler(),
          sx: { cursor: "pointer" },
        })}
        // 开启多选
        enableRowSelection
        getRowId={(row) => row.id}
        // 列数
        rowCount={rowCount}
        // 固定头部
        enableStickyHeader
        // 处理表格高度
        muiTableContainerProps={{ sx: { maxHeight: "620px" } }}
        // 设置背景颜色
        muiTableBodyCellProps={{ sx: { backgroundColor: "white" } }}
        muiTableProps={{
          sx: { backgroundColor: "white", tableLayout: "fixed" },
        }}
        muiBottomToolbarProps={{ sx: { backgroundColor: "white" } }}
        muiTopToolbarProps={{ sx: { backgroundColor: "white" } }}
        // 分页回调函数
        onPaginationChange={setPagination}
        // 记得开启这些 tips:否则会出现useEffect 反作用函数发起多次请求点击下一页会出问题
        manualFiltering
        manualPagination
        manualSorting
        // 排序
        // onSortingChange={setSorting}
        // 开启分页
        enablePagination
        // 列定义
        columns={columns}
        // 数据
        data={data}
        // 初始化国际化语言  --- todo: i18n key doc address: https://github.com/KevinVandy/material-react-table/blob/main/packages/material-react-table/src/_locales/en.ts
        localization={tableI18n}
        // 自定义表头按钮
        renderTopToolbarCustomActions={({ table }) => {
          setTableObject(table.getSelectedRowModel().rows);
        }}
        // 多选底部提示
        positionToolbarAlertBanner="none"
      />
    </>
  );
};
export default PlayListTable;
