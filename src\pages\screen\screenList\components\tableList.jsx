/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/prop-types */
import React, { useEffect, useMemo, useState, useRef } from "react";
import {
  Button,
  Stack,
  Typography,
  Tooltip,
  Link,
  Grid,
  IconButton,
  TextField,
} from "@mui/material";
import TipsAndUpdatesIcon from "@mui/icons-material/TipsAndUpdates";
import { CloseOutlined } from "@material-ui/icons";
import CancelIcon from "@mui/icons-material/Cancel";
import InfoIcon from "@mui/icons-material/Info";
import SyncIcon from "@mui/icons-material/Sync";
import DictTag from "@/components/DictTag";
// api
import {
  listByPage,
  rebootScreen,
  removeScreen,
  doScreenShot,
  screenTimeZone,
  scheduleDeviceShutdown,
  scheduleDeviceSwitch,
  shutdownNow,
  clearShutdown,
  clearSwitchTime,
  cleanSchedule,
} from "@/service/api/screen";
import { useNavigate, useLocation } from "react-router-dom";
import { tableI18n } from "@/utils/tableLang";
import ExportDialog from "@/components/ExportDialog";
// 消息提示
import { toast } from "react-toastify";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import { useTranslation } from "react-i18next";
import { useConfirm } from "@/components/zkconfirm";
import MaterialReactTable from "material-react-table";
import UploadUpgrade from "./UploadUpgrade";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import { useFormik } from "formik";
import { removeEmpty } from "@/utils/StringUtils";
import MainCard from "@/components/MainCard";
import ZKSelect from "@/components/ZKSelect";
import AuthButton from "@/components/AuthButton";
import {
  screenStatus,
  screendirections,
  deviceTypies,
} from "@/dict/commonDict";
import { download } from "@/utils/downloadFile";
import DropdownMenu from "@/components/dropdownMenu";
import CloudUploadOutlinedIcon from "@mui/icons-material/CloudUploadOutlined";
import SyncAltOutlinedIcon from "@mui/icons-material/SyncAltOutlined";
import EditIcon from "@mui/icons-material/Edit";
import RestartAltIcon from "@mui/icons-material/RestartAlt";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
import ContentPasteSearchOutlinedIcon from "@mui/icons-material/ContentPasteSearchOutlined";
import ScreenLogList from "./screenLogList";
import ToastContent from "@/components/@extended/ToastContent";
import UpgradeIcon from "@mui/icons-material/Upgrade";
// import TimedShutdown from './TimedShutdown';
import HighlightOffIcon from "@mui/icons-material/HighlightOff";
import Shutdown from "./switchTime";
import UpgradeList from "./upgradeList";
import { useBoolean } from "ahooks";
const ProgramScheduleTableList = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();
  const confirm = useConfirm();
  const [rowSelection, setRowSelection] = useState([]);
  const [isError, setIsError] = useState(false);
  const upload = useRef(null);
  const screenLogRef = useRef(null);
  const [open, setOpen] = useState(false);
  const [openScreenLog, setOpenScreenLog] = useState(false);
  const [exportOpen, setExportOpen] = useState(false);
  const [timedShutdownOpen, setTimedShutdownOpen] = useState(false);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  // 表格数据
  const [data, setData] = useState([]);
  // 分页的参数
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);
  // 显示搜索
  const [showSearch, setShowSearch] = useState(true);
  //设备id集合
  const [screenIdList, setScreenIdList] = useState([]);
  //屏幕表id
  const [screenId, setScreenId] = useState([]);
  // 查询参数
  const requestParams = useRef(null);
  const ctrlAbout = useRef(null);
  const [upgradeOpenList, { setTrue, setFalse }] = useBoolean(false);
  const [currentScreens, setCurrentScreens] = useState(undefined);
  // 构建参数
  const buildParams = () => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      filterDevice: false,
      category: "2",
      ...requestParams.current,
    };
    return params;
  };

  // 关闭
  const handlCloseCancel = () => {
    setOpen(false);
    setScreenIdList([]);
  };
  const handlCloseLogCancel = () => {
    setOpenScreenLog(false);
    setScreenId([]);
  };
  // 获取数据
  const getTableData = async () => {
    if (!data.length) {
      setIsLoading(true);
    } else {
      setIsRefetching(true);
    }
    // // 开启加载
    setIsLoading(true);
    // setIsRefetching(true);
    await listByPage(buildParams())
      .then((res) => {
        // 设置数据
        setData(res.data.data);
        // 设置总记录数
        setRowCount(res.data.total);
        setIsLoading(false);
        setIsRefetching(false);
      })
      .catch((err) => {
        setIsError(true);
        setIsLoading(false);
        setIsRefetching(false);
      });
  };
  useEffect(() => {
    getTableData();
    setRowSelection([]);
  }, [pagination.pageIndex, pagination.pageSize, location]);
  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "name",
        header: t("ips.ips_device"),
        size: 180,
        Cell: ({ cell, row }) => {
          return (
            <Tooltip title={row.original.name} placement="top">
              <Typography className="textSpace">{row.original.name}</Typography>
            </Tooltip>
          );
        },
      },
      {
        accessorKey: "sn",
        header: t("ips.ips_device_sn"),
        size: 100,
      },
      // {
      //     accessorKey: 'screenType',
      //     header: t('ips.ips_type'),
      //     size: 70
      // },
      {
        accessorKey: "screenModel",
        header: t("ips.ips_screen_model"),
        size: 80,
      },
      {
        accessorKey: "resolution",
        header: t("ips.ips_resolution"),
        size: 80,
      },
      {
        accessorKey: "fwVersion",
        header: t("ips.ips_screen_app_version"),
        size: 120,
      },
      {
        accessorKey: "merchantName",
        header: t("ips.ips_store_client_name"),
        size: 140,
        Cell: ({ cell, row }) => {
          return (
            <Tooltip title={row.original.merchantName} placement="top">
              <Typography className="textSpace">
                {row.original.merchantName}
              </Typography>
            </Tooltip>
          );
        },
      },
      {
        accessorKey: "outletName",
        header: t("common.common_outlet_owner"),
        size: 140,
        Cell: ({ cell, row }) => {
          return (
            <Tooltip title={row.original.storeName} placement="top">
              <Typography className="textSpace">
                {row.original.outletName}
              </Typography>
            </Tooltip>
          );
        },
      },

      // {
      //   accessorKey: "wide",
      //   header: t("common.common_resolution_ratio"),
      //   Cell: ({ cell, row }) => {
      //     return (
      //       <>
      //         {row.original.wide && row.original.high ? (
      //           <>
      //             <Stack direction="row" spacing={1} alignItems="center">
      //               <Typography>
      //                 {row.original.wide} x {row.original.high}
      //               </Typography>
      //             </Stack>
      //           </>
      //         ) : (
      //           <>-</>
      //         )}
      //       </>
      //     );
      //   },
      // },
      {
        accessorKey: "status",
        header: t("ips.ips_device_online"),
        size: 120,
        Cell: ({ cell, row }) => {
          return (
            <DictTag
              dicts={screenStatus}
              fieldName={{
                value: "value",
                title: "label",
                color: "color",
              }}
              value={row.original.status}
            />
          );
        },
      },
      // {
      //   accessorKey: "deviceType",
      //   header: t("common.common_deviceType"),
      //   size: 100,
      //   Cell: ({ cell, row }) => {
      //     return (
      //       // <DictTag
      //       //   dicts={deviceTypies}
      //       //   fieldName={{
      //       //     value: "value",
      //       //     title: "label",
      //       //   }}
      //       //   value={row.original?.deviceType?.trim()}
      //       // />
      //     );
      //   },
      // },
      {
        accessorKey: "direction",
        header: t("common.common_direction"),
        size: 100,
        Cell: ({ cell, row }) => {
          return (
            <DictTag
              dicts={screendirections}
              fieldName={{
                value: "value",
                title: "label",
                color: "color",
              }}
              value={row.original.direction}
            />
          );
        },
      },
      // {
      //     accessorKey: 'ipAddress',
      //     header: t('ips.ips_device_ip'),
      //     enableColumnFilter: false,
      //     size: 100
      // },

      {
        accessorKey: "address",
        header: t("common.common_location"),
        Cell: ({ cell, row }) => {
          return (
            <Tooltip title={row.original.address} placement="top">
              <Typography className="textSpace">
                {row.original.address}
              </Typography>
            </Tooltip>
          );
        },
      },
    ],
    []
  );

  //设备截图
  const handeScreenShot = (id) => {
    doScreenShot(id).then((res) => {
      toast.success(res.message);
    });
  };
  // // 重启设备
  // const handelRebootScreen = (ids, names) => {
  //   if (ids.length > 1) {
  //     toast.error(t("common.common_select_one_operation"));
  //     return;
  //   }
  //   confirm({
  //     title: t("message.messageBox_title"),
  //     confirmationText: t("common.common_edit_ok"),
  //     cancellationText: t("common.common_edit_cancel"),
  //     description: t("common.common_confirm_reboot_device", {
  //       ids: names,
  //     }),
  //   }).then(() => {
  //     rebootScreen(ids).then((res) => {
  //       toast.success(res.message);
  //       // 重新请求数据
  //       getTableData();
  //       //重置选中行框
  //       setRowSelection([]);
  //       // setIsRefetching(true);
  //     });
  //   });
  // };
  // 删除屏幕
  const handleRemoveScreen = (ids, names) => {
    confirm({
      title: t("message.messageBox_title"),
      confirmationText: t("common.common_edit_ok"),
      cancellationText: t("common.common_edit_cancel"),
      description: t("common.common_confirm_link_delete_device", {
        ids: names,
      }),
    }).then(() => {
      removeScreen(ids)
        .then((res) => {
          toast.success(res.message);
          // 重新请求数据
          getTableData();
          //重置选中行框
          setRowSelection([]);
          // setIsRefetching(true);
        })
        .catch((error) => {});
    });
  };
  // 同步设备时区
  const handleSyncTimeZone = (id, name) => {
    confirm({
      title: t("message.messageBox_title"),
      confirmationText: t("common.common_edit_ok"),
      cancellationText: t("common.common_edit_cancel"),
      description: t("ips.ips_confirm_sync_store_timezone", {
        ids: name,
      }),
    }).then(() => {
      screenTimeZone(id)
        .then((res) => {
          toast.success(res.message);
          // 重新请求数据
          getTableData();
          //重置选中行框
          setRowSelection([]);
        })
        .catch((error) => {});
    });
  };

  // 查询表单
  const queryFormik = useFormik({
    initialValues: {
      name: "",
      sn: "",
      status: "",
      address: "",
      storeName: "",
      wide: "",
      high: "",
      merchantName: "",
      deviceType: "",
    },
    onSubmit: async (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        // 拷贝value值
        const tempValue = { ...values };
        await removeEmpty(tempValue);
        // setRequestParams(tempValue);
        requestParams.current = tempValue;
        setPagination({
          pageIndex: 0,
          pageSize: 10,
        });
        getTableData();
        // 查询table
        // setColumnFilters(tempValue);
        // handelSaveSubmit(values);
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
  });
  // 重置查询
  const resetQuery = () => {
    // 重置表单
    queryFormik.resetForm();
    requestParams.current = null;
    setPagination({
      pageIndex: 0,
      pageSize: 10,
    });
    getTableData();
  };

  const handleExport = (value) => {
    // 处理数据
    const params = {
      ...value,
      ...buildParams(),
    };
    return new Promise((resolve, reject) => {
      download(
        `/screen/export`,
        params,
        `${t("common.common_export_screen_title")}.xlsx`
      )
        .then(() => {
          resolve();
          setExportOpen(false);
        })
        .catch(() => {
          reject();
        });
    });
  };
  const handleOnActions = (key, original) => {
    switch (key) {
      case "delete":
        handleRemoveScreen(original.id, original.name);
        break;
      case "modify":
        navigate(`/screen/screenList/editScreen?id=${original.id}`);
        break;
      case "reboot":
        handleReboot(original, "one");
        break;
      case "updateTimeZone":
        handleSyncTimeZone(original.id, original.name);
        break;
      case "upgrade":
        // setOpen(true);
        // setScreenIdList([original.id]);
        handleOpenUpgradeList(original);
        break;
      case "screenLog":
        setOpenScreenLog(true);
        setScreenId(original.id);
        break;
      case "clearShutdown":
        //清除定时关机
        handleClearShoutdown(original, "one");
        break;
      case "cleanSchedule":
        //清空所有计划
        handleCleanSchedule(original, "one");
        break;
      case "shutdown":
        //设备关机
        handleShoutdown(original, "one");
        break;
    }
  };
  const handleUpgrade = (rows) => {
    let idList = [];
    let screens = [];
    rows.map((row) => {
      idList.push(row.original.id);
      screens.push(row.original);
    });
    setTrue();
    setCurrentScreens(screens);
    setTrue();
  };
  const handleOpenUpgradeList = (screen) => {
    setTrue();
    setCurrentScreens([screen]);
  };
  const handleReboot = (rows, type = "list") => {
    const names = [];
    const ids = [];
    let loadingToast;
    if (type === "list") {
      rows.forEach((element) => {
        names.push(element.original.name);
        ids.push(element.original.id);
      });
    } else {
      names.push(rows.name);
      ids.push(rows.id);
    }
    confirm({
      title: t("message.messageBox_title"),
      confirmationText: t("common.common_edit_ok"),
      cancellationText: t("common.common_edit_cancel"),
      description: `${t("screen.reboot_devices", {
        names: names,
      })}`,
    }).then(() => {
      loadingToast = toast.loading((toast) => (
        <ToastContent text={t("screen.rebooting")} t={toast} />
      ));
      rebootScreen(ids)
        .then((res) => {
          toast.dismiss(loadingToast);
          toast.success((t) => <ToastContent text={res?.message} t={t} />);
        })
        .catch((err) => {
          toast.dismiss(loadingToast);
        });
    });

    // toast.error(t("common.common_select_one_operation"));
  };
  const handleShoutdown = (rows, type = "list") => {
    const ids = [];
    if (type === "list") {
      rows.forEach((element) => {
        ids.push(element.original.id);
      });
    } else {
      ids.push(rows.id);
    }
    setScreenId(ids);
    setTimedShutdownOpen(true);
  };
  const handleCleanSchedule = (rows, type = "list") => {
    const names = [];
    const ids = [];
    let loadingToast;
    if (type === "list") {
      rows.forEach((element) => {
        names.push(element.original.name);
        ids.push(element.original.id);
      });
    } else {
      names.push(rows.name);
      ids.push(rows.id);
    }
    //  `${t('screen.reboot_devices',{
    //   names:names
    // })} <br/>123`

    confirm({
      title: t("message.messageBox_title"),
      confirmationText: t("common.common_edit_ok"),
      cancellationText: t("common.common_edit_cancel"),
      description: ` <div>
          ${t("screen.clean_schedule_screen", {
            names: names,
          })}
          <br />
          <b> ${t("screen.tips")}</b>
          ${t("screen.clean_schedule_desc_tips")}
        </div>`,
    }).then(() => {
      loadingToast = toast.loading((toast) => (
        <ToastContent text={t("screen.clean_schedules")} t={toast} />
      ));
      cleanSchedule(ids)
        .then((res) => {
          toast.dismiss(loadingToast);
          // toast((t) => <ToastContent text={res?.message} t={t} />,{
          //     icon:<InfoIcon sx={{color:"#1890ff"}}/>
          // });
          const successCount = res?.data?.success;
          const errorCount = res?.data?.fail;
          toast(
            (t1) => (
              <CustomToastComponent
                successCount={successCount}
                errorCount={errorCount}
                t1={t1}
              />
            ),
            {
              icon: (
                <CustomerToastIcon
                  successCount={successCount}
                  errorCount={errorCount}
                />
              ),
            }
          );
        })
        .catch((err) => {
          toast.dismiss(loadingToast);
        });
    });
  };
  // //计划清空SSE通知
  // const handleClearShoutdown = (rows,type = "list")=>{
  // }
  const checkDeviceModal = (rows) => {
    // 初始化一个变量来存储第一个设备类型
    let firstScreenModel = null;

    // 遍历每一项
    for (let i = 0; i < rows.length; i++) {
      const item = rows[i];

      if (item.original.status === 1) {
        toast.error(t("screen.check_device_status_error"));
        return false; // 立即返回 false
      }

      // 第一次迭代时记录设备类型
      if (firstScreenModel === null) {
        firstScreenModel = item.original.screenModel;
      } else if (item.original.screenModel !== firstScreenModel) {
        return false; // 设备类型不一致，立即返回 false
      }
    }

    return true; // 所有设备类型一致
  };

  const handleOnDeviceActions = (key, rows) => {
    switch (key) {
      case "upgrade":
        //设备升级
        if (!checkDeviceModal(rows)) {
          toast.error("设备型号不一致");
          break;
        }
        handleUpgrade(rows);
        break;
      case "reboot":
        //设备重启
        handleReboot(rows);
        break;
      case "shutdown":
        //设备关机
        handleShoutdown(rows);
        break;
      case "clearShutdown":
        //清除定时关机
        handleClearShoutdown(rows);
        break;
      case "cleanSchedule":
        //清空所有计划
        handleCleanSchedule(rows);
        break;
    }
  };

  const handleSwitchTimeSubmit = async (switchTime) => {
    return new Promise((resolve, reject) => {
      scheduleDeviceSwitch(screenId, {
        switchTime: switchTime,
      })
        .then((res) => {
          const successCount = res?.data?.success;
          const errorCount = res?.data?.fail;
          toast(
            (t1) => (
              <CustomToastComponent
                successCount={successCount}
                errorCount={errorCount}
                t1={t1}
              />
            ),
            {
              icon: (
                <CustomerToastIcon
                  successCount={successCount}
                  errorCount={errorCount}
                />
              ),
            }
          );
          setScreenId([]);
          setTimedShutdownOpen(false);
          resolve();
        })
        .catch((err) => {
          reject();
        });
    });
  };

  const handleShoutdownSubmit = async (cron) => {
    return new Promise((resolve, reject) => {
      scheduleDeviceShutdown(screenId, {
        cron: cron,
      })
        .then((res) => {
          const successCount = res?.data?.success;
          const errorCount = res?.data?.fail;
          toast(
            (t1) => (
              <CustomToastComponent
                successCount={successCount}
                errorCount={errorCount}
                t1={t1}
              />
            ),
            {
              icon: (
                <CustomerToastIcon
                  successCount={successCount}
                  errorCount={errorCount}
                />
              ),
            }
          );
          setScreenId([]);
          setTimedShutdownOpen(false);
          resolve();
        })
        .catch((err) => {
          reject();
        });
    });
  };

  const handleNowShoutdownSubmit = async () => {
    return new Promise((resolve, reject) => {
      shutdownNow(screenId)
        .then((res) => {
          const successCount = res?.data?.success;
          const errorCount = res?.data?.fail;
          toast(
            (t1) => (
              <CustomToastComponent
                successCount={successCount}
                errorCount={errorCount}
                t1={t1}
              />
            ),
            {
              icon: (
                <CustomerToastIcon
                  successCount={successCount}
                  errorCount={errorCount}
                />
              ),
            }
          );
          setScreenId([]);
          setTimedShutdownOpen(false);
          resolve();
        })
        .catch((err) => {
          reject();
        });
    });
  };
  const handleClearShoutdown = (rows, type = "list") => {
    const names = [];
    const ids = [];
    let loadingToast;
    if (type === "list") {
      rows.forEach((element) => {
        names.push(element.original.name);
        ids.push(element.original.id);
      });
    } else {
      names.push(rows.name);
      ids.push(rows.id);
    }
    confirm({
      title: t("message.messageBox_title"),
      confirmationText: t("common.common_edit_ok"),
      cancellationText: t("common.common_edit_cancel"),
      description: `${t("common.clean_time_power_device", { names: names })}`,
    }).then(() => {
      loadingToast = toast.loading((toast) => (
        <ToastContent text={t("screen.clean_shutdowning")} t={toast} />
      ));
      clearSwitchTime(ids)
        .then((res) => {
          toast.dismiss(loadingToast);
          const successCount = res?.data?.success;
          const errorCount = res?.data?.fail;
          toast(
            (t1) => (
              <CustomToastComponent
                successCount={successCount}
                errorCount={errorCount}
                t1={t1}
              />
            ),
            {
              icon: (
                <CustomerToastIcon
                  successCount={successCount}
                  errorCount={errorCount}
                />
              ),
            }
          );
        })
        .catch((err) => {
          toast.dismiss(loadingToast);
        });
    });
  };

  const CustomToastComponent = ({ successCount, errorCount, t1 }) => {
    return (
      <div
        style={{
          maxWidth: 500, // Corrigido para maxWidth ao invés de maxWeight
          minWidth: 300,
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}>
        <div>
          {successCount > 0 && errorCount == 0
            ? t("screen.executed_success_all")
            : errorCount > 0 && successCount == 0
            ? t("screen.executed_error_all")
            : t("screen.execution_tips_section", {
                success: successCount,
                error: errorCount,
              })}
        </div>
        <IconButton onClick={() => toast.dismiss(t1.id)}>
          <CloseOutlined />
        </IconButton>
      </div>
    );
  };

  const CustomerToastIcon = ({ successCount, errorCount }) => {
    return (
      <>
        {successCount > 0 && errorCount === 0 ? (
          <CheckCircleIcon sx={{ color: "#61d345" }} />
        ) : errorCount > 0 && successCount === 0 ? (
          <CancelIcon sx={{ color: "#ff4b4b" }} />
        ) : (
          <InfoIcon sx={{ color: "#1890ff" }} />
        )}
      </>
    );
  };

  return (
    <>
      <MainCard style={{ marginBottom: "10px" }}>
        <form noValidate onSubmit={queryFormik.handleSubmit}>
          <Grid
            container
            direction="row"
            justifyContent="flex-start"
            alignItems="center"
            spacing={5}>
            <Grid item xs={12} md={4} lg={2}>
              <TextField
                label={t("ips.ips_device")}
                value={queryFormik.values.name}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                size="small"
                type="text"
                name="name"
                fullWidth
                placeholder={t("common.common_please_input")}
              />
            </Grid>
            <Grid item xs={12} md={4} lg={2}>
              <TextField
                name="merchantName"
                label={t("ips.ips_store_client_name")}
                value={queryFormik.values.merchantName}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                size="small"
                type="text"
                fullWidth
                placeholder={t("common.common_please_input")}
              />
            </Grid>
            <Grid item xs={12} md={4} lg={2}>
              <TextField
                name="sn"
                label={t("ips.ips_device_sn")}
                value={queryFormik.values.sn}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                size="small"
                type="text"
                fullWidth
                placeholder={t("common.common_please_input")}
              />
            </Grid>
            <Grid item xs={12} md={4} lg={2}>
              <TextField
                name="storeName"
                label={t("ips.ips_store_name")}
                value={queryFormik.values.storeName}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                size="small"
                type="text"
                fullWidth
                placeholder={t("common.common_please_input")}
              />
            </Grid>

            {!showSearch && (
              <>
                <Grid item xs={12} sm={4} md={2}>
                  {/* <TextField
                    name="screenType"
                    label={t("ips.ips_type")}
                    value={queryFormik.values.screenNumber}
                    onChange={queryFormik.handleChange}
                    onBlur={queryFormik.handleBlur}
                    size="small"
                    type="text"
                    fullWidth
                  /> */}
                  <TextField
                    label={t("ips.ips_screen_model")}
                    size="small"
                    type="text"
                    name="screenModel"
                    fullWidth
                    value={queryFormik.values.contacts}
                    onChange={queryFormik.handleChange}
                    onBlur={queryFormik.handleBlur}
                  />
                </Grid>
                <Grid item xs={12} sm={4} md={2}>
                  <ZKSelect
                    size="small"
                    name="deviceType"
                    value={queryFormik.values.deviceType}
                    placeholder={t("common.common_please_type")}
                    options={deviceTypies}
                    onChange={queryFormik.handleChange}
                    onBlur={queryFormik.handleBlur}
                    onClear={() => {
                      queryFormik.setFieldValue("deviceType", undefined);
                    }}
                    error={Boolean(
                      queryFormik.touched.deviceType &&
                        queryFormik.errors.deviceType
                    )}
                  />
                </Grid>
                <Grid item xs={12} md={4} lg={2}>
                  <ZKSelect
                    size="small"
                    label={t("ips.ips_device_online")}
                    name="status"
                    value={queryFormik.values.status}
                    onChange={queryFormik.handleChange}
                    onBlur={queryFormik.handleBlur}
                    options={screenStatus}
                    onClear={() => {
                      queryFormik.setFieldValue("status", "");
                    }}
                    placeholder={t("common.common_select_status")}
                    menuWidth={250}
                  />
                </Grid>
                <Grid item xs={12} md={4} lg={2}>
                  <TextField
                    label={t("common.common_location")}
                    name="address"
                    size="small"
                    type="text"
                    fullWidth
                    placeholder={t("common.common_please_input")}
                    value={queryFormik.values.address}
                    onChange={queryFormik.handleChange}
                    onBlur={queryFormik.handleBlur}
                  />
                </Grid>
                <Grid item xs={12} md={4} lg={2}>
                  <TextField
                    label={t("ips.ips_resolution_wide")}
                    name="wide"
                    size="small"
                    type="text"
                    fullWidth
                    placeholder={t("common.common_please_input")}
                    value={queryFormik.values.wide}
                    onChange={queryFormik.handleChange}
                    onBlur={queryFormik.handleBlur}
                  />
                </Grid>
                <Grid item xs={12} md={4} lg={2}>
                  <TextField
                    label={t("ips.ips_resolution_high")}
                    name="high"
                    size="small"
                    type="text"
                    fullWidth
                    placeholder={t("common.common_please_input")}
                    value={queryFormik.values.high}
                    onChange={queryFormik.handleChange}
                    onBlur={queryFormik.handleBlur}
                  />
                </Grid>
              </>
            )}

            <Grid item xs={12} md={4} lg={2}>
              <Stack
                direction="row"
                justifyContent="flex-start"
                alignItems="flex-start"
                spacing={2}>
                <Button
                  disableElevation
                  type="submit"
                  variant="contained"
                  size="small">
                  {t("common.common_table_query")}
                </Button>
                <Button
                  variant="outlined"
                  onClick={resetQuery}
                  color="info"
                  sx={{
                    minWidth: "100px",
                  }}
                  size="small">
                  {t("common.common_op_reset")}
                </Button>
                <IconButton
                  onClick={() => setShowSearch(!showSearch)}
                  size="middle">
                  {showSearch ? (
                    <>
                      <ExpandMoreIcon sx={{ mt: -0.5 }} />
                      <Typography sx={{ mt: -0.5 }}>
                        {t("common.common_form_unfold")}
                      </Typography>
                    </>
                  ) : (
                    <>
                      <ExpandLessIcon sx={{ mt: -0.5 }} />
                      <Typography sx={{ mt: -0.5 }}>
                        {t("common.common_form_packup")}
                      </Typography>
                    </>
                  )}
                </IconButton>
              </Stack>
            </Grid>
          </Grid>
        </form>
      </MainCard>
      <MaterialReactTable
        // table 状态
        state={{
          // 加载状态
          isLoading,
          // 分页参数
          pagination,
          // 重新拉取
          showProgressBars: isRefetching,
          columnPinning: { right: ["mrt-row-actions"] },
          showAlertBanner: isError,
          // columnPinning: { right: ["action"] },
          rowSelection,
        }}
        renderToolbarInternalActions={({ table }) => (
          <Stack direction={"row"} spacing={1}>
            <AuthButton button="sd:screen:screen:export">
              <Button
                variant="contained"
                onClick={() => {
                  setExportOpen(true);
                }}>
                {t("common.common_op_export")}
              </Button>
            </AuthButton>
            <Tooltip
              arrow
              placement="top"
              title={t("common.common_op_refresh")}>
              <IconButton aria-label="sync" onClick={getTableData}>
                <SyncIcon />
              </IconButton>
            </Tooltip>
          </Stack>
        )}
        muiTablePaperProps={{
          elevation: 0,
          sx: {
            borderRadius: "5px",
            border: "1px solid #f0f0f0",
          },
        }}
        muiTableHeadRowProps={{
          sx: { backgroundColor: "#fafafa", boxShadow: "none" },
        }}
        // 关闭过滤搜素
        enableColumnFilters={true}
        // 关闭排序
        enableSorting={false}
        // 布局方式
        layoutMode="grid"
        enableColumnActions={false}
        // 开启列对齐
        muiTableHeadCellProps={{
          sx: {
            "& .Mui-TableHeadCell-Content": {
              justifyContent: "space-between",
            },
          },
        }}
        // 解决列太多宽度太长问题
        enableColumnResizing
        // enablePinning
        // 初始化状态

        initialState={{
          columnVisibility: { createTime: true },
          columnPinning: { right: ["action"] },
        }}
        muiToolbarAlertBannerProps={
          isError
            ? {
                color: "error",
                children: "Error loading data",
              }
            : undefined
        }
        //行选中
        onRowSelectionChange={setRowSelection}
        muiTableBodyRowProps={({ row }) => ({
          onClick: row.getToggleSelectedHandler(),
          sx: { cursor: "pointer" },
        })}
        // 开启多选
        enableRowSelection
        // 列数
        rowCount={rowCount}
        // 固定头部
        enableStickyHeader
        // 处理表格高度
        muiTableContainerProps={{ sx: { maxHeight: "620px" } }}
        // 设置背景颜色
        muiTableBodyCellProps={{ sx: { backgroundColor: "white" } }}
        muiTableProps={{
          sx: { backgroundColor: "white", tableLayout: "fixed" },
        }}
        muiBottomToolbarProps={{ sx: { backgroundColor: "white" } }}
        muiTopToolbarProps={{ sx: { backgroundColor: "white" } }}
        // 分页回调函数
        onPaginationChange={setPagination}
        // 记得开启这些 tips:否则会出现useEffect 反作用函数发起多次请求点击下一页会出问题
        manualFiltering
        manualPagination
        manualSorting
        // 开启分页
        enablePagination
        // 列定义
        columns={columns}
        // 数据
        data={data}
        localization={tableI18n}
        // 自定义表头按钮
        renderTopToolbarCustomActions={({ table }) => {
          return (
            <Stack direction="row" spacing={1} alignItems="center">
              <AuthButton button="sd:screen:screen:save">
                <Button
                  variant="contained"
                  onClick={() => {
                    navigate("/screen/screenList/add");
                  }}>
                  {t("common.common_add_screen")}
                </Button>
              </AuthButton>
              <AuthButton button="sd:screen:screen:delete">
                <Button
                  variant="contained"
                  color="secondary"
                  disabled={
                    !table.getIsSomeRowsSelected() &&
                    !table.getIsAllRowsSelected()
                  }
                  onClick={() => {
                    const ids = [];
                    const names = [];
                    table.getSelectedRowModel().rows.map((row) => {
                      ids.push(row.original.id);
                      names.push(row.original.name);
                    });
                    handleRemoveScreen(ids, names);
                  }}>
                  {t("common.common_op_batch_del")}
                </Button>
              </AuthButton>
              {/* 
              
              
                 <AuthButton button="sd:screen:screen:upgrade">
                <Button
                  variant="contained"
                  disabled={
                    !table.getIsSomeRowsSelected() &&
                    !table.getIsAllRowsSelected()
                  }
                  onClick={() => {
                    let idList = [];
                    table.getSelectedRowModel().rows.map((row) => {
                      idList.push(row.original.id);
                    });
                    setScreenIdList(idList);
                    setOpen(true);
                  }}>
                  {t("common.common_batch_upgrade")}
                </Button>
              </AuthButton>

              
              */}

              <DropdownMenu
                tagger="button"
                btnText={t("screen.device_operater")}
                options={[
                  {
                    text: t("menu.device_reboot"),
                    key: "reboot",
                    icon: <RestartAltIcon sx={{ fontSize: "18px" }} />,
                    // authCode: "screen:screen:reboot",
                  },
                  {
                    text: t("common.common_batch_upgrade"),
                    key: "upgrade",
                    icon: <CloudUploadOutlinedIcon sx={{ fontSize: "18px" }} />,
                    authCode: "screen:screen:upgrade",
                  },
                  {
                    text: t("menu.time_power_on_off"),
                    key: "shutdown",
                    icon: <AccessTimeIcon sx={{ fontSize: "18px" }} />,
                    // authCode: "screen:screen:shutdown",
                  },
                  {
                    text: t("menu.clean_time_power_on_off"),
                    key: "clearShutdown",
                    icon: <HighlightOffIcon sx={{ fontSize: "18px" }} />,
                    // authCode: "screen:screen:cleanShutdown",
                  },
                  {
                    text: t("screen.clean_schedule_btn"),
                    key: "cleanSchedule",
                    icon: <AccessTimeIcon sx={{ fontSize: "18px" }} />,
                    // authCode: "screen:screen:cleanSchedule",
                  },
                ]}
                onActions={(key) => {
                  if (
                    !table.getIsSomeRowsSelected() &&
                    !table.getIsAllRowsSelected()
                  ) {
                    // toast.custom(<div>请选择设备在进行操作</div>)
                    toast.error((toast) => (
                      <ToastContent
                        text={t("screen.please_select_device")}
                        t={toast}
                      />
                    ));
                    return;
                  }
                  handleOnDeviceActions(key, table.getSelectedRowModel().rows);
                }}
              />
            </Stack>
          );
        }}
        // 多选底部提示
        positionToolbarAlertBanner="none"
        // action操作位置
        positionActionsColumn="last"
        displayColumnDefOptions={{
          "mrt-row-actions": {
            size: 180,
          },
        }}
        // 开启action操作
        enableRowActions={true}
        renderRowActions={({ row, index, table }) => (
          <Stack direction="row" spacing={1} alignItems="center">
            <AuthButton button="sd:screen:screen:get">
              <Link
                component="button"
                underline="none"
                onClick={() => {
                  navigate(
                    `/screen/screenList/detail?id=${row.original.id}&&status=${row.original.status}&&name=${row.original.name}`
                  );
                }}>
                {t("common.common_op_detail")}
              </Link>
            </AuthButton>

            <DropdownMenu
              btnText={t("common.common_operation")}
              options={[
                {
                  text: t("common.common_op_modify"),
                  key: "modify",
                  icon: <EditIcon sx={{ fontSize: "16px" }} />,
                  authCode: "screen:screen:edit",
                },
                {
                  text: t("common.common_upgrade"),
                  icon: <CloudUploadOutlinedIcon sx={{ fontSize: "16px" }} />,
                  key: "upgrade",
                  authCode: "screen:screen:upgrade",
                },
                {
                  text: t("ips.ips_sync_store_timezone"),
                  icon: <SyncAltOutlinedIcon sx={{ fontSize: "16px" }} />,
                  key: "updateTimeZone",
                  authCode: "screen:screen:timezone",
                },
                {
                  text: t("menu.device_reboot"),
                  key: "reboot",
                  icon: <RestartAltIcon sx={{ fontSize: "16px" }} />,
                  authCode: "screen:screen:reboot",
                },
                {
                  text: t("menu.time_power_on_off"),
                  key: "shutdown",
                  icon: <AccessTimeIcon sx={{ fontSize: "16px" }} />,
                  authCode: "screen:screen:shutdown",
                },
                {
                  text: t("menu.clean_time_power_on_off"),
                  key: "clearShutdown",
                  icon: <HighlightOffIcon sx={{ fontSize: "16px" }} />,
                  authCode: "screen:screen:cleanShutdown",
                },
                {
                  text: t("screen.clean_schedule_btn"),
                  key: "cleanSchedule",
                  icon: <AccessTimeIcon sx={{ fontSize: "18px" }} />,
                  authCode: "screen:screen:cleanSchedule",
                },
                {
                  text: t("menu.media_deviceLog"),
                  icon: (
                    <ContentPasteSearchOutlinedIcon sx={{ fontSize: "16px" }} />
                  ),
                  key: "screenLog",
                  authCode: "screen:screen:logList",
                },
                {
                  text: t("common.common_op_del"),
                  icon: <DeleteOutlinedIcon sx={{ fontSize: "16px" }} />,
                  key: "delete",
                  authCode: "screen:screen:delete",
                },
              ]}
              onActions={(key) => handleOnActions(key, row.original)}
            />
          </Stack>
        )}
      />
      <UploadUpgrade
        ref={upload}
        open={open}
        onCancel={handlCloseCancel}
        screenIdList={screenIdList}
      />
      <ScreenLogList
        ref={screenLogRef}
        openScreenLog={openScreenLog}
        onCancel={handlCloseLogCancel}
        screenId={screenId}
      />
      <ExportDialog
        title={t("common.common_export_screen_title")}
        open={exportOpen}
        columns={columns}
        sx={{}}
        onExport={handleExport}
        onClose={() => {
          setExportOpen(false);
        }}
      />
      <Shutdown
        open={timedShutdownOpen}
        onCancel={() => {
          setTimedShutdownOpen(false);
        }}
        onNowShutodwn={async () => {
          return await handleNowShoutdownSubmit();
        }}
        onSubmit={async (cron) => {
          return await handleSwitchTimeSubmit(cron);
        }}
      />

      <UpgradeList
        open={upgradeOpenList}
        onClose={() => {
          setFalse();
          setCurrentScreens(undefined);
        }}
        screens={currentScreens}
      />
    </>
  );
};
export default ProgramScheduleTableList;
