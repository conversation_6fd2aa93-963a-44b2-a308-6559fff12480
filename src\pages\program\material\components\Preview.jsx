import React from 'react'
/* eslint-disable react/prop-types */
import  { useState,forwardRef } from "react";
// material-ui
// 消息提示
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Container,
  Stack,
  Pagination,
  IconButton,
} from "@mui/material";
// import envConfig from '@/config/env.config';
import ReactPlayer from "react-player";
import CloseIcon from "@mui/icons-material/Close";
import ZoomInIcon from '@material-ui/icons/ZoomIn';
import ZoomOutIcon from '@material-ui/icons/ZoomOut';
import AddCircleOutlineOutlinedIcon from "@mui/icons-material/AddCircleOutlineOutlined";
import RemoveCircleOutlineOutlinedIcon from "@mui/icons-material/RemoveCircleOutlineOutlined";
import { useTheme } from "@mui/material/styles";
// import { Document, Page } from 'react-pdf';
// i18n
import { useTranslation } from "react-i18next";
const Preview = forwardRef((props, ref) => {
  const { t } = useTranslation();
  const options = {
    cMapUrl: "/cmaps/",
    cMapPacked: true,
    standardFontDataUrl: "/standard_fonts/",
  };
  const theme = useTheme();
  const typeObj = {
    image: "image",
    file: "file",
    media: "media",
    audio: "audio",
    layout: "layout",
  };
  const type = props.type.trimRight();
  const url = props.url;
  // 素材预览弹窗
  const [open, setOpen] = React.useState(false);
  //视频播放开关
  const [playing, setPlaying] = React.useState(true);
  //pdf页数
  const [numPages, setNumPages] = React.useState(0);
  const [currentPage, setCurrentPage] = React.useState(1);
  const [scale, setScale] = React.useState(0.8);
  // const onDocumentLoadSuccess = (pages) => {
  //     setNumPages(pages._pdfInfo.numPages);
  // };

  React.useImperativeHandle(ref, () => ({
    handleClose,
    handleOpen,
  }));
  const handleClose = () => {
    setCurrentPage(1);
    setZoomLevel(1);
    setOpen(false);
  };
  const handleOpen = () => {
    setOpen(true);
  };
  const handleChange = (event, value) => {
    setCurrentPage(value);
  };
  const [zoomLevel, setZoomLevel] = useState(1);
  //放大按钮
  const handleZoomIn = () =>{
    setZoomLevel(zoomLevel + 0.1);
  };
  //缩小按钮
  const handleZoomOut = () =>{
    setZoomLevel(Math.max(zoomLevel - 0.1, 0.5));
  }
  return (
    <>
      <Dialog
        {...props}
        open={open}
        fullWidth={true}
        onClose={handleClose}
        aria-describedby="alert-dialog-slide-description"
      >
        <DialogTitle>
          {t("common.common_op_preview")}
          <IconButton
            aria-label="close"
            onClick={handleZoomIn}
            sx={{
              position: "absolute",
              right: 85,
              top: 8,
              color: (theme) => theme.palette.grey[500],
            }}
          >
            <ZoomInIcon />
          </IconButton>
          <IconButton
            aria-label="close"
            onClick={handleZoomOut}
            sx={{
              position: "absolute",
              right: 45,
              top: 8,
              color: (theme) => theme.palette.grey[500],
            }}
          >
            <ZoomOutIcon />
          </IconButton>
          <IconButton
            aria-label="close"
            onClick={handleClose}
            sx={{
              position: "absolute",
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500],
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Container maxWidth="lg">
            <Stack direction="row" justifyContent="center" alignItems="center" sx={{transform: `scale(${zoomLevel})`,transformOrigin: 'center'}}>
              {(type == typeObj.image || type == typeObj.layout)  && (
                <>
                  <img
                    loading="lazy"
                    style={{ width: "100%" }}
                    alt=""
                    src={url}
                  />
                </>
              )}
              {type == typeObj.audio && (
                <>
                  <audio src={url} autoplay={true} controls={true}></audio>
                </>
              )}
              {/* {type == typeObj.file && (
                                <>
                                    <Stack direction="column" justifyContent="center" alignItems="center" spacing={2}>
                                        <Document file={url} onLoadSuccess={onDocumentLoadSuccess} loading="Please wait!" options={options}>
                                            <Page
                                                scale={scale}
                                                renderAnnotationLayer={false}
                                                renderTextLayer={false}
                                                pageNumber={currentPage}
                                            />
                                        </Document>
                                    </Stack>
                                </>
                            )} */}
              {type == typeObj.media && (
                <>
                  <ReactPlayer url={url} playing={playing} controls={true} />
                </>
              )}
            </Stack>
          </Container>
        </DialogContent>
        {type == typeObj.file && (
          <DialogActions>
            <IconButton
              onClick={() => {
                if (scale < 2) {
                  setScale(scale + 0.1);
                }
              }}
            >
              <AddCircleOutlineOutlinedIcon
                sx={{ color: theme.palette.info.main }}
              />
            </IconButton>
            <IconButton
              onClick={() => {
                if (scale > 0.3) {
                  setScale(scale - 0.1);
                }
              }}
            >
              <RemoveCircleOutlineOutlinedIcon
                sx={{ color: theme.palette.info.main }}
              />
            </IconButton>

            <Pagination
              count={numPages}
              variant="outlined"
              shape="rounded"
              showFirstButton
              showLastButton
              onChange={handleChange}
            ></Pagination>
          </DialogActions>
        )}
      </Dialog>
    </>
  );
});

export default Preview;
