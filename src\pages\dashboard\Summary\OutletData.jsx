import {
    Grid,
    Card,
    InputAdornment,
    IconButton,
    Button,
    TextField,
    Typography,
    Stack,
    CircularProgress,
    Box,
} from "@mui/material";
import React, { forwardRef, useRef, useEffect, useState } from "react";
import GradientBox from '@/components/GradientBox'
import PercentCircle from '@/components/PercentCircle'
import './summary.less'
import { useTranslation } from "react-i18next";
const OutletData = (props) => {
    const { t } = useTranslation();
    let { outletData } = props
    return <GradientBox style={{ flexGrow: 1 }}>

        <Grid sx={{
            p: 1
        }} style={{ height: '100%' }} className="outlet_data_box">
            <Grid className="outlet_info_cla">
                <Grid className="text-gradient">
                    <PercentCircle showInner={true} textStyle={{ fontSize: '12px' }} strokeWidth={10} width={100} Percent={outletData.realScale?(outletData.realScale * 100).toFixed(2) : 0} />
                </Grid>
                <Grid className="outlet_data_box" ><Typography>{outletData.totalOutletInstall || 0}</Typography><Typography>/{outletData.total || 0}</Typography></Grid>
                <Typography>{t('summary.outlets_installed')} </Typography>
            </Grid>
            <Grid className="divider_cla"></Grid>
            <Grid className="outlet_info_cla">
                <Typography className="text-gradient outlets_text">{outletData.ytdOutlet || 0}</Typography>
                <Typography sx={{ mt: 2 }}>{ t('outlet.new_outlets_installed2')}</Typography>
                <Typography>{ t('outlet.year_to_date')} </Typography>
            </Grid>
        </Grid>
    </GradientBox>
}

export default OutletData