import React, { useRef, useEffect, useState } from "react";
import {
  <PERSON>rid,
  Card,
  CardContent,
  Typography,
  IconButton,
  Box,
} from "@mui/material";
import ArrowBackIosNewIcon from "@mui/icons-material/ArrowBackIosNew";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import DoneIcon from '@mui/icons-material/Done';
const HorizontalCardList = (props) => {
  const {
    materials = [],
    currentMaterialId = "",
    selectItem = () => {},
  } = props;
  const scrollContainerRef = useRef(null);
  const [showScrollButtons, setShowScrollButtons] = useState(false);

  const checkOverflow = () => {
    if (scrollContainerRef.current) {
      const isOverflowing =
        scrollContainerRef.current.scrollWidth >
        scrollContainerRef.current.clientWidth;
      setShowScrollButtons(isOverflowing);
    }
  };

  useEffect(() => {
    checkOverflow();
    window.addEventListener("resize", checkOverflow);
    return () => window.removeEventListener("resize", checkOverflow);
  }, []);

  useEffect(() => {
    checkOverflow();
  }, [materials]);

  const scroll = (scrollOffset) => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollLeft += scrollOffset;
    }
  };

  return (
    <Box
      sx={{
        position: "relative",
        width: "100%",
        display: "flex",
        alignItems: "center",
        overflow: "hidden",
        backgroundColor: "#ffffff",
        borderRadius: "4px",
        padding: showScrollButtons ? "0px 40px" : "0px",
      }}
    >
      {showScrollButtons && (
        <IconButton
          sx={{
            position: "absolute",
            left: 0,
            top: "50%",
            transform: "translateY(-50%)",
            zIndex: 1,
          }}
          onClick={() => scroll(-200)}
        >
          <ArrowBackIosNewIcon fontSize="12px" />
        </IconButton>
      )}
      <Grid
        container
        ref={scrollContainerRef}
        sx={{
          display: "flex",
          flexWrap: "nowrap",
          overflowX: "hidden",
          scrollBehavior: "smooth",
          "&::-webkit-scrollbar": { display: "none" },
          msOverflowStyle: "none",
          scrollbarWidth: "none",
        }}
      >
        {materials.map((item) => (
          <Grid item key={item.materialId} sx={{ flexShrink: 0, width: 184, m: 1 }}>
            <Card

              onClick={() => {
                selectItem(item.materialId);
              }}
              sx={{
                height: "110px",
                boxShadow: "none",
                borderRadius: "4px",
                padding: "10px",
                boxSizing: "border-box",
                backgroundColor: currentMaterialId === item.materialId?'#ffffff':"#F1F1F1",
                cursor: "pointer",
                position: "relative",
                border:
                  currentMaterialId === item.materialId
                    ? "2px solid #52A6D8"
                    : "none",
              }}
            >
              <Grid
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  height: "100%",
                  width: "100%",
                  backgroundImage: `url('${item.url}')`,
                  backgroundSize: "contain",
                  position: "relative",
                  backgroundRepeat: "no-repeat",
                  backgroundPosition: "center",
                }}
              >
                {currentMaterialId === item.materialId && (
                  <Grid
                    sx={{
                      width: "34px",
                      height: "34px",
                      borderRadius: "50%",
                      margin: "auto",
                      background: "#FFFFFF 0% 0% no-repeat padding-box",
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                      position: "absolute",
                      top: "-5px",
                      right: "-5px",
                    }}
                  >
                    <Grid
                      sx={{
                        width: "24px",
                        height: "24px",
                        borderRadius: "50%",
                        margin: "auto",
                        display: 'flex',
                        justifyContent: "center",
                        alignItems: "center",
                        background:
                          "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
                      }}
                    >
                      <DoneIcon sx={{ color: '#ffffff', fontSize: 18  }}></DoneIcon>
                    </Grid>
                  </Grid>
                )}
              </Grid>
            </Card>
            <Grid
              sx={{
                textAlign: "center",
                marginTop: "4px",
                font: "normal normal normal 14px/19px Roboto",
                fontSize: "14px",
                letterSpacing: "0px",
                color: "#474B4F",
                textTransform: "capitalize",
                opacity: 1,
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
                lineClamp: 1,
                padding: "0px 10px",
              }}
            >
              {item.name}
            </Grid>
          </Grid>
        ))}
      </Grid>
      {showScrollButtons && (
        <IconButton
          sx={{
            position: "absolute",
            right: 0,
            top: "50%",
            transform: "translateY(-50%)",
            zIndex: 1,
          }}
          onClick={() => scroll(200)}
        >
          <ArrowForwardIosIcon fontSize="12px" />
        </IconButton>
      )}
    </Box>
  );
};

export default HorizontalCardList;
