import { useEffect, useRef, useState } from "react";
import * as echarts from "echarts";
import { useTranslation } from "react-i18next";
const VisitorsGender = (props) => {
  const chartRef = useRef(null);
  const { t } = useTranslation();
  const myChartRef = useRef(null);
  const initChart = () => {
    let chart = echarts.init(chartRef.current, null, { renderer: "svg" });
    myChartRef.value = chart;
    // 设置初始大小
    chart.resize();
    // 监听窗口大小变化，自动调整图表大小
    window.addEventListener("resize", handleResize);
    const options = getOptions(props.gender);
    chart.setOption(options);
  };

  const handleResize = () => {
    if (myChartRef.value) {
      myChartRef.value.resize();
    }
  };

  //   realTime:{
  //     female:'女',
  //     male:'男'
  //   }

  useEffect(() => {
    // 在组件挂载时进行初始化
    initChart();
    return () => {
      window.removeEventListener("resize", handleResize);
      if (myChartRef.value) {
        myChartRef.value.dispose();
        myChartRef.value = null;
      }
    };
  }, []);

  useEffect(() => {
    if (myChartRef.value === null) {
      initChart();
    } else {
      const options = getOptions(props.gender);
      myChartRef.value.setOption(options);
    }
  }, [props.gender]);

  const getOptions = (data) => {
    let result = data
      ?.filter((item) => {
        if (item.number > 0) {
          return true;
        } else {
          return false;
        }
      })
      ?.map((item) => {
        let name =
          item.fieldName === "1" ? t("realTime.male") : t("realTime.female");
        return {
          name: name,
          value: item.number,
          itemStyle:
            item.fieldName === "1"
              ? {
                  // 设置柱状图的颜色
                  color: "rgb(0,180,255)",
                }
              : {
                  // 设置柱状图的颜色
                  color: "rgb(244,164,218)",
                },
        };
      });
    if (!result) {
      result = [];
    }

    return {
      title: {
        text: t("realTime.genderTitle"),
        textStyle: {
          color: "#86909c",
          fontStyle: "normal",
          fontWeight: "normal",
          fontFamily: "Arial",
          fontSize: 14,
        },
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "5%",
        containLabel: true,
      },
      tooltip: {
        trigger: "item",
        formatter: t("realTime.genderTooltip"),
      },
      legend: {
        orient: "vertical",
        left: "left",
        top: "middle",
        itemGap: 15,
      },
      series: [
        {
          name: data.length > 0 ? "VisitorsGender" : "",
          type: "pie",
          radius: "90%",
          center: ["50%", "50%"],
          labelLine: {
            show: false, // 不显示标签的连线
          },
          label: {
            show: true,
            position: "inside", // 设置标签位置为饼图内部
            formatter: "{d}%", // 标签文本格式器，这里显示名称、数值和百分比
          },
          data: result,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: "rgba(0, 0, 0, 0.5)",
            },
          },
        },
      ],
    };
  };
  return (
    <div
      style={{
        width: "100%",
        boxSizing: "border-box",
        position: "relative",
        height: "100%",
        padding: "10px",
        border: "1px solid #e5e6eb",
        borderRadius: "20px",
        backgroundColor: "#ffffff",
      }}
    >
      <div
        style={{
          width: "100%",
          height: "100%",
        }}
        ref={chartRef}
      ></div>
    </div>
  );
};

export default VisitorsGender;
