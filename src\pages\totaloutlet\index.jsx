import React from "react";
/* eslint-disable react-hooks/rules-of-hooks */
import { useEffect, useMemo, useState, useRef } from "react";
import MaterialReactTable from "material-react-table";
import {
  Button,
  Stack,
  IconButton,
  Typography,
  Link,
  Grid,
  TextField,
} from "@mui/material";
// import { getPrincipaList } from "@/service/api/L3Sevice.js";
import {
  listByPage,
  save,
  update,
  batchRemove,
} from "@/service/api/totaloutlet";
import { tableI18n } from "@/utils/tableLang";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
// i18n
import { useConfirm } from "@/components/zkconfirm";
// 消息提示
import { toast } from "react-toastify";
import { useTranslation } from "react-i18next";
import { useFormik } from "formik";
import { removeEmpty } from "@/utils/StringUtils";
import MainCard from "@/components/MainCard";
import ZKSelect from "@/components/ZKSelect";
import AuthButton from "@/components/AuthButton";
import TotaloutletForm from "./component/totaloutletForm";

import Treeselect from "@/components/zktreeselect";
import { getPrincipaList } from "@/service/api/L3Sevice.js";
const TotalOutlet = () => {
  const [open, setOpen] = useState(false);
  const treeSelectRef = React.useRef(null);
  const confirm = useConfirm();
  const { t } = useTranslation();
  const [rowSelection, setRowSelection] = useState([]);
  const [isError, setIsError] = useState(false);
  const [merchantOptions, setMerchantOptions] = useState([]);
  const [areaData, setAreaData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [idValue, setIdValue] = useState(undefined);
  // 显示搜索
  const [showSearch, setShowSearch] = useState(true);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  // 表格数据
  const [data, setData] = useState([]);
  // 分页的参数
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);

  // 删除总计
  const handelRemoveTotalOutlet = (ids) => {
    confirm({
      title: t("message.messageBox_title"),
      confirmationText: t("common.common_edit_ok"),
      cancellationText: t("common.common_edit_cancel"),
      description: t("common.common_delete_confirm_description"),
    }).then(() => {
      batchRemove(ids).then((res) => {
        toast.success(res.message);
        // 重新请求数据
        getTableData();
        //重置选中行框
        setRowSelection([]);
        // setIsRefetching(true);
      });
    });
  };

  // 查询参数
  const requestParams = useRef(null);

  // 构建参数
  const buildParams = () => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      ...requestParams.current,
    };

    return params;
  };
  // 获取数据
  const getTableData = async () => {
    if (!data.length) {
      setIsLoading(true);
    } else {
      setIsRefetching(true);
    }

    await listByPage(buildParams())
      .then((res) => {
        // 设置数据
        setData(res.data.data);
        // 设置总记录数
        setRowCount(res.data.total);
        setIsLoading(false);
        setIsRefetching(false);
      })
      .catch((err) => {
        setIsError(true);
        setIsLoading(false);
        setIsRefetching(false);
      });
  };
  useEffect(() => {
    handleRequestMerchant();
  }, []);
  useEffect(() => {
    // const params = buildParams();
    // 发请求
    setRowSelection([]);
    getTableData();
  }, [pagination.pageIndex, pagination.pageSize]);
  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "departmentName",
        header: t("common.common_los_merchant_name"),
        enableColumnActions: false,
        enableSorting: false,
      },
      {
        accessorKey: "areaName",
        header: t("common.common_los_area_name"),
        enableColumnActions: false,
        enableSorting: false,
      },
      {
        accessorKey: "year",
        header: t("common.common_name_year"),
        enableColumnActions: false,
        enableSorting: false,
      },
      {
        accessorKey: "month",
        header: t("common.common_name_month"),
        enableColumnActions: false,
        enableSorting: false,
      },
      {
        accessorKey: "totalOutlet",
        header: t("common.common_total"),
        enableColumnActions: false,
        enableSorting: false,
      },
    ],
    []
  );
  // 查询表单
  const queryFormik = useFormik({
    initialValues: {
      name: "",
      areaName: "",
      year: "",
      month: "",
    },
    onSubmit: async (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        // 拷贝value值
        const tempValue = { ...values };
        await removeEmpty(tempValue);
        // setRequestParams(tempValue);
        requestParams.current = tempValue;
        setPagination({
          pageIndex: 0,
          pageSize: 10,
        });
        getTableData();
        // 查询table
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
  });
  // 重置查询
  const resetQuery = () => {
    handleClearArea();
    // 重置表单
    queryFormik.resetForm();
    setPagination({
      pageIndex: 0,
      pageSize: 10,
    });
    requestParams.current = null;
    getTableData();
  };
  // 表单弹出
  const handleOpen = (type) => {
    if (type === "add") {
      // 新增
      setOpen(true);
    }
  };
  // 请求商户数据
  const handleRequestMerchant = () => {
    getPrincipaList("1").then((res) => {
      setMerchantOptions(res.data);
    });
  };

  // 保存
  const handleAddTotalOutlet = async (values) => {
    setLoading(true);
    save(values)
      .then((res) => {
        toast.success(res.message);
        setLoading(false);
        handleRest();
      })
      .catch((err) => {
        setLoading(false);
      });
  };
  // 编辑
  const handleEditTotalOutlet = (values) => {
    setLoading(true);
    update(values)
      .then((res) => {
        toast.success(res.message);
        setLoading(false);
        handleRest();
      })
      .catch((err) => {
        setLoading(false);
      });
  };
  // 重置表格
  const handleRest = () => {
    setOpen(false);
    setIdValue(undefined);
    getTableData();
  };

  const handleClearArea = () => {
    treeSelectRef?.current?.clear();
    queryFormik.setFieldValue("areaName", "");
    queryFormik.setFieldValue("areaId", "");
  };

  return (
    <>
      <MainCard style={{ marginBottom: "10px" }}>
        <form noValidate onSubmit={queryFormik.handleSubmit}>
          <Grid
            container
            direction="row"
            justifyContent="flex-start"
            alignItems="center"
            spacing={5}>
            <Grid item xs={6} sm={4} md={3}>
              <ZKSelect
                name="departmentId"
                size="small"
                value={queryFormik.values.departmentId}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                options={merchantOptions}
                onClear={() => {
                  queryFormik.setFieldValue("departmentId", "");
                }}
                placeholder={t("ips.ips_select_merchant")}
                menuWidth={200}
              />
            </Grid>
            {/* <Grid item xs={6} sm={4} md={3}> */}
            {/* <Treeselect
                ref={treeSelectRef}
                data={areaData}
                isClear={true}
                optionValue="id"
                optionLabel="name"
                placeholder={t("ips.ips_enter_region")}
                onChange={(valuas) => {
                  queryFormik.values.areaId = valuas.id;
                }}
                onClear={handleClearArea}
                disableParent={true}
              /> */}
            {/* <TextField
                label={t("common.common_area_name")}
                name="areaName"
                value={queryFormik.values.areaName}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                size="small"
                type="text"
                fullWidth
                placeholder={t("common.common_please_input")}
              /> */}
            {/* </Grid> */}
            <Grid item xs={6} sm={4} md={3}>
              <TextField
                label={t("common.common_name_year")}
                value={queryFormik.values.year}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                size="small"
                type="text"
                name="year"
                fullWidth
                placeholder={t("common.common_please_enter_year")}
              />
            </Grid>
            {!showSearch && (
              <Grid item xs={6} sm={4} md={3}>
                <TextField
                  label={t("common.common_name_month")}
                  value={queryFormik.values.month}
                  onChange={queryFormik.handleChange}
                  onBlur={queryFormik.handleBlur}
                  size="small"
                  type="text"
                  name="month"
                  fullWidth
                  placeholder={t("common.common_please_enter_month")}
                />
              </Grid>
            )}

            <Grid
              item
              xs={6}
              sm={4}
              md={3}
              direction="row"
              justifyContent="flex-end">
              <Stack
                direction="row"
                justifyContent="flex-start"
                alignItems="flex-start"
                spacing={2}>
                <Button
                  disableElevation
                  type="submit"
                  variant="contained"
                  size="small">
                  {t("common.common_table_query")}
                </Button>

                <Button
                  variant="outlined"
                  onClick={resetQuery}
                  color="info"
                  size="small"
                  sx={{
                    minWidth: "90px",
                  }}>
                  {t("common.common_op_reset")}
                </Button>
                <IconButton
                  onClick={() => setShowSearch(!showSearch)}
                  size="middle">
                  {showSearch ? (
                    <>
                      <ExpandMoreIcon sx={{ mt: -0.5 }} />
                      <Typography sx={{ mt: -0.5 }}>
                        {t("common.common_form_unfold")}
                      </Typography>
                    </>
                  ) : (
                    <>
                      <ExpandLessIcon sx={{ mt: -0.5 }} />
                      <Typography sx={{ mt: -0.5 }}>
                        {t("common.common_form_packup")}
                      </Typography>
                    </>
                  )}
                </IconButton>
              </Stack>
            </Grid>
          </Grid>
        </form>
      </MainCard>
      <MaterialReactTable
        // table 状态
        state={{
          // 加载状态
          isLoading,
          // 分页参数
          pagination,
          // 重新拉取
          showProgressBars: isRefetching,
          showAlertBanner: isError,

          rowSelection,
        }}
        renderToolbarInternalActions={({ table }) => <></>}
        muiTablePaperProps={{
          elevation: 0,
          sx: {
            borderRadius: "5px",
            border: "1px solid #f0f0f0",
          },
        }}
        muiTableHeadRowProps={{
          sx: { backgroundColor: "#fafafa", boxShadow: "none" },
        }}
        // 关闭过滤搜素
        enableColumnFilters={true}
        // 关闭排序
        enableSorting={false}
        // 布局方式
        layoutMode="grid"
        // 开启列对齐
        muiTableHeadCellProps={{
          sx: {
            "& .Mui-TableHeadCell-Content": {
              justifyContent: "space-between",
            },
          },
        }}
        // 解决列太多宽度太长问题
        enableColumnResizing
        // enablePinning
        // 初始化状态
        initialState={{ columnVisibility: { createTime: true } }}
        muiToolbarAlertBannerProps={
          isError
            ? {
                color: "error",
                children: "Error loading data",
              }
            : undefined
        }
        //行选中
        onRowSelectionChange={setRowSelection}
        muiTableBodyRowProps={({ row }) => ({
          onClick: row.getToggleSelectedHandler(),
          sx: { cursor: "pointer" },
        })}
        // 开启多选
        enableRowSelection
        // 列数
        rowCount={rowCount}
        // 固定头部
        enableStickyHeader
        // 处理表格高度
        muiTableContainerProps={{ sx: { maxHeight: "570px" } }}
        // 设置背景颜色
        muiTableBodyCellProps={{ sx: { backgroundColor: "white" } }}
        muiTableProps={{
          sx: { backgroundColor: "white", tableLayout: "fixed" },
        }}
        muiBottomToolbarProps={{ sx: { backgroundColor: "white" } }}
        muiTopToolbarProps={{ sx: { backgroundColor: "white" } }}
        // 分页回调函数
        onPaginationChange={setPagination}
        // 记得开启这些 tips:否则会出现useEffect 反作用函数发起多次请求点击下一页会出问题
        manualFiltering
        manualPagination
        manualSorting
        // 开启分页
        enablePagination
        // 列定义
        columns={columns}
        // 数据
        data={data}
        // 初始化国际化语言  --- todo: i18n key doc address: https://github.com/KevinVandy/material-react-table/blob/main/packages/material-react-table/src/_locales/en.ts
        localization={tableI18n}
        // 自定义表头按钮
        renderTopToolbarCustomActions={({ table }) => {
          return (
            <Stack direction="row" spacing={1} alignItems="center">
              <AuthButton button="sd:system:total_outlet:save">
                <Button
                  variant="contained"
                  onClick={() => {
                    handleOpen("add");
                  }}>
                  {t("common.common_btn_add_outlet")}
                </Button>
              </AuthButton>
              <AuthButton button="sd:system:total_outlet:delete">
                <Button
                  variant="contained"
                  color="secondary"
                  disabled={
                    !table.getIsSomeRowsSelected() &&
                    !table.getIsAllRowsSelected()
                  }
                  onClick={() => {
                    const ids = [];
                    // const names = [];
                    table.getSelectedRowModel().rows.map((row) => {
                      ids.push(row.original.id);
                      // names.push(row.original.name);
                    });
                    handelRemoveTotalOutlet(ids);
                  }}>
                  {t("common.common_op_batch_del")}
                </Button>
              </AuthButton>
            </Stack>
          );
        }}
        // 多选底部提示
        positionToolbarAlertBanner="none"
        // 开启action操作
        enableRowActions
        // action操作位置
        positionActionsColumn="last"
        displayColumnDefOptions={{
          "mrt-row-actions": {
            header: t("common.common_relatedOp"), //change header text
            size: 120, //make actions column wider
          },
        }}
        renderRowActions={(row, index, table) => (
          <Stack direction="row" spacing={1} alignItems="center">
            <AuthButton button="sd:system:total_outlet:update">
              <Link
                component="button"
                underline="none"
                onClick={() => {
                  setOpen(true);
                  setIdValue(row.row.original.id);
                }}>
                {t("common.common_op_modify")}
              </Link>
            </AuthButton>
            <AuthButton button="sd:system:total_outlet:delete">
              <Link
                component="button"
                underline="none"
                color="error"
                onClick={() =>
                  handelRemoveTotalOutlet(
                    row.row.original.id
                    // row.row.original.name
                  )
                }>
                {t("common.common_op_del")}
              </Link>
            </AuthButton>
          </Stack>
        )}
      />
      {/* 表单 */}
      <TotaloutletForm
        areaOption={areaData}
        merchantOptions={merchantOptions}
        id={idValue}
        loading={loading}
        open={open}
        onSave={async (values) => {
          if (values.id) {
            // 编辑
            handleEditTotalOutlet(values);
          } else {
            // 新增
            handleAddTotalOutlet(values);
          }
        }}
        onCancel={() => {
          setIdValue(undefined);
          setOpen(false);
        }}
      />
    </>
  );
};
export default TotalOutlet;
