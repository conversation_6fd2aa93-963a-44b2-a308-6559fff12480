import request from "@/utils/request";

const baseProfixURI = "/screenLog";

/**
 *  分页查询
 * <AUTHOR>
 * @date 2024-02-27 15:29
 */
export const listByPage = (params) => {
  return request({
    url: `${baseProfixURI}/page`,
    method: "get",
    params: params,
  });
};

/**
 * 拉取日志
 * <AUTHOR>
 * @date 2024-02-27 15:29
 */
export const pullScreenLog = (screenId) => {
  return request({
      url: `${baseProfixURI}/pull/${screenId}`,
      method: "get",
  });
};
