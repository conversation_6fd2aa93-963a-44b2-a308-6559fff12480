import React from 'react'
// 弹窗
import  { useEffect, useMemo, useState, forwardRef } from "react";

import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import Radio from "@mui/material/Radio";
import RadioGroup from "@mui/material/RadioGroup";
import FormControlLabel from "@mui/material/FormControlLabel";
import {
  InputLabel,
  Grid,
  OutlinedInput,
  Select,
  MenuItem,
  FormHelperText,
  Stack,
  Box,
  Button,
} from "@mui/material";
import * as Yup from "yup";
import { useFormik } from "formik";
import { setProgramValue, setRouter } from "@/utils/editorStore";
import { useLocation } from "react-router-dom";
import { getTreeSelect } from "service/api/group";
import Treeselect from "@/components/zktreeselect";
import { useNavigate } from "react-router-dom";
import { getStoreLang } from "@/utils/langUtils";
// i18n
import { useTranslation } from "react-i18next";
const productDialog = forwardRef((props, ref) => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [open, setOpen] = React.useState(false);
  const location = useLocation();
  // 显示自定义分辨率
  const [showCustom, setShowCustom] = React.useState(false);
  // 自定义转换宽高
  const [canvasWidth, setCanvasWidth] = React.useState(150);
  const [canvasHeight, setCanvasHeight] = React.useState(100);

  const [customWidth, setCustomWidth] = React.useState(200);
  const [customHeight, setCustomHeight] = React.useState(100);
  //下拉树数据
  const [treeData, setTreeData] = React.useState([]);

  // 用于hash路由保存到sessionStore拼接
  const hashConstant = "/#";

  React.useImperativeHandle(ref, () => ({
    handleClose,
    handleOpen,
  }));
  // 关闭弹窗
  const handleClose = () => {
    setOpen(false);
  };

  // 打开弹窗
  const handleOpen = () => {
    console.log(location.pathname);
    setOpen(true);
  };
  // 计算宽高
  const calculateWidthHeight = (val) => {
    if (val === "custom") {
      setCanvasWidth(200);
      setCanvasHeight(100);
    } else {
      let arr = val.split("*");
      let width = arr[0];
      let height = arr[1];
      if (width / height > 200 / 100) {
        setCanvasWidth(200);
        setCanvasHeight((200 / width) * height);
      } else {
        setCanvasWidth((100 / height) * width);
        setCanvasHeight(100);
      }
    }
  };
  // 提交表单跳转到编辑器
  const handleCreateProgram = (value) => {};
  // 表单
  const programFormik = useFormik({
    initialValues: {
      programType: "0",
      name: "",
      groupId: "",
      resolution: "1920*1080",
      customWidth: 200,
      customHeight: 100,
      isTemplate: "0",
    },
    onSubmit: (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        handleCreateProgram(values);
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
    validationSchema: Yup.object().shape({
      programType: Yup.string().required(
        t("common.common_select_program_type")
      ),
      name: Yup.string().required(t("common.common_input_programs_name")),
      groupId: Yup.string().required(t("common.common_select_group")),
      resolution: Yup.string().required(
        t("common.common_input_resolution_ratio")
      ),
      customWidth: Yup.number().when("resolution", {
        is: "custom", // alternatively: (val) => val == true
        then: (schema) => schema.required(t("common.common_input_width")),
        // otherwise: (schema) => schema.min(0)
      }),
      customHeight: Yup.number().when("resolution", {
        is: "custom",
        then: (schema) => schema.required(t("common.common_input_height")),
      }),
      // .oneOf([Yup.ref('resolution'), 'custom'])
      // .required('请输入宽度')
    }),
  });

  const getTreeData = () => {
    getTreeSelect().then((res) => {
      if (res.code == 0) {
        setTreeData(res.data);
      }
    });
  };

  useEffect(() => {
    getTreeData();
  }, []);

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle sx={{ fontSize: "18px", padding: "5px 25px 5px 25px" }}>
        <p>{t("common.common_add_program")}</p>
      </DialogTitle>
      <DialogContent>
        <form noValidate onSubmit={programFormik.handleSubmit}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Stack spacing={6} direction="row" alignItems="center">
                <InputLabel htmlFor="programType">
                  {t("ips.ips_program_type")} <i style={{ color: "red" }}>*</i>
                </InputLabel>
                <RadioGroup
                  name="programType"
                  onChange={programFormik.handleChange}
                  value={programFormik.values.programType}
                  // defaultValue={programFormik.values.programType}
                  style={{
                    marginTop: "0px",
                    paddingLeft: "0px",
                    marginLeft: getStoreLang() === "es" ? "37px" : "49px",
                  }}
                >
                  <Stack
                    direction="row"
                    justifyContent="flex-start"
                    alignItems="center"
                    spacing={getStoreLang() === "es" ? 3 : 8}
                  >
                    <FormControlLabel
                      sx={{
                        border: "1px solid #dcdfe6",
                        paddingRight: "50px",
                        borderRadius: "4px",
                        marginRight: "0px",
                      }}
                      value="0"
                      control={<Radio />}
                      label={t("dictData.dict_ordinary_program")}
                    />
                    <FormControlLabel
                      sx={{
                        border: "1px solid #dcdfe6",
                        paddingRight: "50px",
                        borderRadius: "4px",
                      }}
                      value="1"
                      control={<Radio />}
                      label={t("dictData.dict_acc_program")}
                    />
                    <FormControlLabel
                      sx={{
                        border: "1px solid #dcdfe6",
                        paddingRight: "50px",
                        borderRadius: "4px",
                      }}
                      value="2"
                      control={<Radio />}
                      label={t("dictData.dict_car_program")}
                    />
                  </Stack>
                </RadioGroup>
              </Stack>
            </Grid>
            <Grid item xs={12}>
              <Stack
                spacing={getStoreLang() == "zh" ? 4 : 9}
                direction="row"
                alignItems="center"
              >
                <InputLabel htmlFor="programname-name">
                  {t("ips.ips_program")}{" "}
                  <i style={{ color: "red" }}>
                    * {getStoreLang() == "en" ? "" : <>&nbsp;</>}
                  </i>
                </InputLabel>
                <div style={{ width: "80%" }}>
                  <OutlinedInput
                    sx={{
                      width: "100%",
                    }}
                    id="programname-name"
                    type="text"
                    fullWidth
                    value={programFormik.values.name}
                    name="name"
                    placeholder={t("common.common_input_programs_name")}
                    onBlur={programFormik.handleBlur}
                    onChange={programFormik.handleChange}
                    error={Boolean(
                      programFormik.touched.name && programFormik.errors.name
                    )}
                  />
                  {programFormik.touched.name && programFormik.errors.name && (
                    <FormHelperText error id="name-error">
                      {programFormik.errors.name}
                    </FormHelperText>
                  )}
                </div>
              </Stack>
            </Grid>
            <Grid item xs={12}>
              <Stack
                spacing={getStoreLang() == "zh" ? 5 : 6}
                direction="row"
                alignItems="center"
              >
                <InputLabel htmlFor="groupId">
                  {t("common.common_group")} <i style={{ color: "red" }}>*</i>
                </InputLabel>
                <div style={{ width: "80%" }}>
                  <Treeselect
                    error={Boolean(
                      programFormik.touched.groupId &&
                        programFormik.errors.groupId
                    )}
                    data={treeData}
                    placeholder={t("common.common_select_group")}
                    optionValue="id"
                    optionLabel="name"
                    onChange={(values) => {
                      if (values != null && values != undefined) {
                        programFormik.values.groupId = values.id;
                      }
                    }}
                    disableParent={true}
                  />
                  {programFormik.touched.groupId &&
                    programFormik.errors.groupId && (
                      <FormHelperText error id="groupId-error">
                        {programFormik.errors.groupId}
                      </FormHelperText>
                    )}
                </div>
              </Stack>
            </Grid>
            <Grid item xs={12}>
              {/* 8 */}
              <Stack
                spacing={getStoreLang() == "zh" ? 6 : 8}
                direction="row"
                alignItems="center"
              >
                <InputLabel htmlFor="code-loginaa">
                  {t("common.common_resolution_ratio")}
                  <i style={{ color: "red" }}>
                    *{getStoreLang() == "en" ? "" : <>&nbsp;&nbsp;</>}
                  </i>
                </InputLabel>
                <div style={{ width: "80%" }}>
                  <Select
                    sx={{
                      width: "230px",
                    }}
                    labelId="code-loginaa"
                    onChange={(event) => {
                      // 显示自定义宽高表单
                      const value = event.target.value;
                      if (value === "custom") {
                        // 是否显示自定义的输入框
                        setShowCustom(true);
                      } else {
                        setShowCustom(false);
                      }
                      // 计算宽高
                      calculateWidthHeight(value);
                      //传递值给form表单
                      programFormik.handleChange(event);
                    }}
                    value={programFormik.values.resolution}
                    onBlur={programFormik.handleBlur}
                    id="company-type-create"
                    name="resolution"
                    defaultValue={programFormik.values.resolution}
                    input={<OutlinedInput />}
                    error={Boolean(
                      programFormik.touched.resolution &&
                        programFormik.errors.resolution
                    )}
                  >
                    <MenuItem value="1920*1080">1920*1080</MenuItem>
                    <MenuItem value="1080*1920">1080*1920</MenuItem>
                    <MenuItem value="1366*768">1366*768</MenuItem>
                    <MenuItem value="1280*720">1280*720</MenuItem>
                    <MenuItem value="720*1280">720*1280</MenuItem>
                    <MenuItem value="custom">
                      {t("common.common_custom")}
                    </MenuItem>
                  </Select>
                  {programFormik.touched.resolution &&
                    programFormik.errors.resolution && (
                      <FormHelperText error id="resolution-error">
                        {programFormik.errors.resolution}
                      </FormHelperText>
                    )}
                </div>
              </Stack>
            </Grid>
            {showCustom ? (
              <Grid item xs={12}>
                <Grid container spacing={3} direction="row" alignItems="center">
                  <Grid item xs={6}>
                    <Stack
                      spacing={getStoreLang() == "zh" ? 8 : 12}
                      direction="row"
                      alignItems="center"
                    >
                      {/* <Stack spacing={getStoreLang() == 'en' ? 11 : 12} direction="row" alignItems="center"> */}
                      <InputLabel
                        htmlFor="customWidth"
                        sx={{
                          overflow: "initial",
                          // paddingRight: '23px'
                        }}
                      >
                        {t("common.common_param_width")} &nbsp; &nbsp;
                      </InputLabel>
                      <div style={{ width: "60%" }}>
                        <OutlinedInput
                          sx={{ width: "100%" }}
                          value={programFormik.values.customWidth}
                          id="customWidth"
                          type="number"
                          name="customWidth"
                          onBlur={programFormik.handleBlur}
                          error={Boolean(
                            programFormik.touched.customWidth &&
                              programFormik.errors.customWidth
                          )}
                          onChange={(event) => {
                            const width = event.target.value;
                            setCustomWidth(width);
                            if (width / customHeight > 200 / 100) {
                              setCanvasWidth(200);
                              setCanvasHeight((200 / width) * customHeight);
                            } else {
                              setCanvasWidth((100 / customHeight) * width);
                              setCanvasHeight(100);
                            }
                            programFormik.handleChange(event);
                          }}
                        />
                        {programFormik.touched.customWidth &&
                          programFormik.errors.customWidth && (
                            <FormHelperText error id="customWidth-error">
                              {programFormik.errors.customWidth}
                            </FormHelperText>
                          )}
                      </div>
                    </Stack>
                  </Grid>
                  <Grid item xs={6}>
                    <Stack spacing={3} direction="row" alignItems="center">
                      <InputLabel htmlFor="customHeight">
                        {t("common.common_param_height")}
                      </InputLabel>
                      <div style={{ width: "60%" }}>
                        <OutlinedInput
                          value={programFormik.values.customHeight}
                          id="customHeight"
                          name="customHeight"
                          sx={{ width: "100%" }}
                          onBlur={programFormik.handleBlur}
                          error={Boolean(
                            programFormik.touched.customHeight &&
                              programFormik.errors.customHeight
                          )}
                          onChange={(event) => {
                            const height = event.target.value;
                            setCustomHeight(height);
                            if (customWidth / height > 200 / 100) {
                              setCanvasWidth(200);
                              setCanvasHeight((200 / customWidth) * height);
                            } else {
                              setCanvasWidth((200 / height) * customWidth);
                              setCanvasHeight(100);
                            }
                            programFormik.handleChange(event);
                          }}
                          type="number"
                        />
                        {programFormik.touched.customHeight &&
                          programFormik.errors.customHeight && (
                            <FormHelperText error id="customHeight-error">
                              {programFormik.errors.customHeight}
                            </FormHelperText>
                          )}
                      </div>
                    </Stack>
                  </Grid>
                </Grid>
              </Grid>
            ) : null}
          </Grid>

          <Grid item xs={12}>
            <Stack
              spacing={3}
              direction="column"
              alignItems="flex-start"
              sx={{
                paddingLeft: getStoreLang() == "zh" ? "110px" : "150px",
                marginTop: "10px",
              }}
            >
              <span style={{ color: "#c0c4cc", marginBottom: "-15px" }}>
                {t("common.common_width")} * {t("common.common_height")}
              </span>
              <Box
                sx={{
                  width: 200,
                  height: 100,
                  backgroundColor: "#d8d8d8",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <Box
                  sx={{
                    width: canvasWidth,
                    height: canvasHeight,
                    backgroundColor: "primary.main",
                  }}
                />
              </Box>
            </Stack>
          </Grid>
          <DialogActions>
            <Button color="info" variant="outlined" onClick={handleClose}>
              {t("common.common_edit_cancel")}
            </Button>
            <Button
              disableElevation
              disabled={programFormik.isSubmitting}
              type="submit"
              variant="contained"
              color="primary"
            >
              {t("common.common_edit_ok")}
            </Button>
            {programFormik.errors.submit && (
              <Grid item xs={12}>
                <FormHelperText error>
                  {programFormik.errors.submit}
                </FormHelperText>
              </Grid>
            )}
          </DialogActions>
        </form>
      </DialogContent>

      {/* </DialogActions> */}
    </Dialog>
  );
});

export default productDialog;
