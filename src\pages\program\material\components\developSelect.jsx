import React from 'react'
/* eslint-disable react/prop-types */
import  {
  forwardRef,
  useRef,
  useState,
  useImperativeHandle,
} from "react";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
  Stack,
  Button,
  Typography,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
// i18n
import { useTranslation } from "react-i18next";
import DevelopTable from "./developTable";
import {
  BootstrapDialog,
  BootstrapDialogTitle,
  BootstrapContent,
  BootstrapActions,
} from "@/components/dialog";
const DevelopSelect = forwardRef((props, ref) => {
  const { setFormValues = () => {} } = props;
  const { t } = useTranslation();
  const developTableRef = useRef(null);
  // 广告商选择弹窗
  const [open, setOpen] = React.useState(false);

  useImperativeHandle(ref, () => ({
    handleClose,
    handleOpen,
  }));

  const handleDevelop = (id, name) => {
    setFormValues(id, name);
  };
  //表单选择对象
  const [tableObject, setTableObject] = useState([]);

  //重新加载广告商列表
  // const reloadMerchantList = () => {
  //   advertiserTableRef.current.getTableData();
  // };
  const handleClose = () => {
    setOpen(false);
  };
  const handleOpen = () => {
    setOpen(true);
  };

  return (
    <>
      <BootstrapDialog
        open={open}
        fullWidth
        onClose={handleClose}
        aria-describedby="alert-dialog-slide-description"
      >
        <BootstrapDialogTitle onClose={handleClose}>
          <Typography variant="h4" component="p">
            {t("ips.ips_select_auditor")}
          </Typography>
        </BootstrapDialogTitle>
        <BootstrapContent dividers>
          <DevelopTable
            ref={developTableRef}
            setTableObject={setTableObject}
          />
        </BootstrapContent>
        
        <Stack
          direction="row"
          justifyContent="flex-end"
          sx={{ margin: "15px" }}
          spacing={2}
        >
          <Button
            variant="contained"
            color="primary"
            disabled={tableObject.length > 1 || tableObject.length === 0}
            onClick={() => {
              handleDevelop(
                tableObject[0].original.userId,
                tableObject[0].original.firstName
              );
              handleClose();
            }}
          >
            {t("common.common_edit_ok")}
          </Button>
        </Stack>
      </BootstrapDialog>
    </>
  );
});

export default DevelopSelect;
