import axios from "axios";
import { setToken } from "@/utils/auth";
import { toast } from "react-toastify";
import { clearDashboard } from './cache';
import { doRefreshToken } from "@/service/api/user";
import { getStoreLang, converLang } from "@/utils/langUtils";
let isShowLogoutMessage = false;
// 是否正在刷新的标记
let isRefreshing = false;
let tryRequestQueue = [];
axios.defaults.headers["Content-Type"] = "application/json;charset=utf-8";
axios.defaults.headers["Accept-Language"] = converLang(getStoreLang());
const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  // baseURL: config.BASE_URL,
  baseURL: import.meta.env.VITE_API_URL,
  // 超时
  timeout: 8 * 6000, //请求超时时间（5秒后还未接收到数据，就需要再次发送请求）
  retry: 3, //设置全局重试请求次数（最多重试几次请求）
  retryDelay: 1500, //设置全局请求间隔
});
export default function loadExpireReload(res, dataObj) {
  console.log("过期了");
  // token 失效重新返回登录
  if (!isRefreshing) {
    isRefreshing = true;
    return doRefreshToken()
      .then((tokenRes) => {
        if (null === tokenRes.data) {
          if (isShowLogoutMessage) {
            toast.error(dataObj.message);
            isShowLogoutMessage = true;
          }

          window.location.href = "/#/login";
          isRefreshing = false;
          return Promise.reject("error");
        }
        const token = tokenRes.data.access_token;
        // 刷新token成功，将最新的token更新到header中
        setToken(token);
        // 获取当前失败的请求
        const config = res.config;
        // 重置一下配置

        config.headers["Authorization"] = token;
        // 已经刷新了token，将所有队列中的请求进行重试
        tryRequestQueue.forEach((cb) => cb(token));
        tryRequestQueue = [];

        // 重试当前请求并返回promise
        return service(config);
      })
      .catch((tokenRes) => {
        toast.error(dataObj.message);
        clearDashboard()
        window.location.href = "/#/login";
        isRefreshing = false;
        return Promise.reject("error");
      })
      .finally(() => {
        isRefreshing = false;
      });
  } else {
    console.log("加入等待队列");
    // 正在刷新token，返回一个未执行resolve的promise
    return new Promise((resolve) => {
      // 将resolve放进队列，用一个函数形式来保存，等token刷新后直接执行
      tryRequestQueue.push((token) => {
        // config.baseURL = '';
        res.config.headers["Authorization"] = token;
        resolve(service(res.config));
      });
    });
  }
}

