import React from 'react'
import { <PERSON><PERSON>, But<PERSON> } from "@mui/material";
import { useRef, useEffect, useState } from "react";
import RenderText from "./CenterComponent/RenderText";
import RenderPhotos from "./CenterComponent/RenderPhotos";
import RenderVideo from "./CenterComponent/RenderVideo";
import RenderAudio from "./CenterComponent/RenderAudio";
import RenderTime from "./CenterComponent/RenderTime";
import RenderNews from "./CenterComponent/RenderNews";
import RenderWeather from "./CenterComponent/RenderWeather";
import RemoveIcon from "@mui/icons-material/Remove";
import AddIcon from "@mui/icons-material/Add";
import { PrettoSlider } from "./PrettoSlider";
import CloseIcon from "@mui/icons-material/Close";
import { message } from "./common/i18n";
const RenderCompon = (props) => {
  let info = props.info;
  let type = info.type;
  let component = null;
  switch (type) {
    case "ZKTecoText":
      component = <RenderText isPre={true} {...props}></RenderText>;
      break;
    case "ZKTecoSlideShowImg":
      component = <RenderPhotos isPre={true} {...props}></RenderPhotos>;
      break;
    case "ZKTecoVideo":
      component = <RenderVideo isPre={true} {...props}></RenderVideo>;
      break;
    case "ZKTecoMusic":
      component = <RenderAudio isPre={true} {...props}></RenderAudio>;
      break;
    case "ZKTecoWeather":
      component = <RenderWeather isPre={true} {...props}></RenderWeather>;
      break;

    case "ZKTecoTime":
      component = <RenderTime isPre={true} {...props}></RenderTime>;
      break;

    case "ZKTecoNews":
      component = <RenderNews isPre={true} {...props}></RenderNews>;
      break;
    default:
      component = null;
      break;
  }

  return component;
};

const PreView = (props) => {
  let programData = props.programData;
  let hideClose = props.hideClose;
  const preRef = useRef(null);
  //场景缩放
  const [scale, setScale] = useState(1);
  const showScale = props.showScale;
  const [pageIndex, setPageIndex] = useState(0);
  const [currentPage, setCurrentPage] = useState({});
  const [pageWidth, setPageWidth] = useState(1000);
  const [pageHeight, setPageHeight] = useState(1000);

  useEffect(() => {
    if (props.scale) {
      setScale(props.scale);
    }
  }, [props.scale]);

  useEffect(() => {
    let pageInfo = programData.pages[pageIndex];
    setCurrentPage({
      ...pageInfo,
    });
  }, [pageIndex]);

  useEffect(() => {
    if (preRef !== null) {
      let offsetHeight = preRef.current.offsetHeight;
      let offsetWidth = preRef.current.offsetWidth;
      let width = parseInt(programData.width);
      let height = parseInt(programData.height);
      setPageWidth(width);
      setPageHeight(height);
      if (width > offsetWidth || height > offsetHeight) {
        let widtScale = (offsetWidth - 40) / width;
        let heightScale = (offsetHeight - 40) / height;
        if (widtScale > heightScale) {
          let result = (heightScale - 0.01).toFixed(2);
          setScale(result);
        } else {
          let result = (widtScale - 0.01).toFixed(2);
          setScale(result);
        }
      }
    }
  }, []);

  const plusScale = () => {
    if (scale < 2) {
      let result = (scale + 0.01).toFixed(2);
      setScale(parseFloat(result));
    }
  };

  const minusScale = () => {
    if (props.scale > 0.01) {
      let result = (scale - 0.01).toFixed(2);
      setScale(parseFloat(result));
    }
  };

  const handleSliderChange = (event, newValue) => {
    let result = (newValue + 0.01).toFixed(2);
    setScale(result);
  };

  const prePage = () => {
    setPageIndex(pageIndex - 1);
  };

  const nexPage = () => {
    setPageIndex(pageIndex + 1);
  };

  const closeBack = () => {
    if (props.onClose) {
      props.onClose();
    }
  };

  return (
    <Grid
      ref={preRef}
      sx={{
        width: "100%",
        height: "100%",
        minWidth: props.minWidth || "60vw",
        minHeight: props.minHeight || "80vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        position: "relative",
        overflow: "hidden",
        paddingBottom: "40px",
        paddingTop: "40px",
      }}
    >
      {!hideClose && (
        <Grid
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            paddingTop: "5px",
            paddingRight: "5px",
            position: "absolute",
            top: "0px",
            width: "100%",
            height: "40px",
            alignItems: "center",
            backgroundColor: "#ffffff",
          }}
        >
          <CloseIcon
            sx={{
              "&:hover": {
                color: "#ce2626",
                cursor: "pointer",
              },
              zIndex: 999,
            }}
            onClick={closeBack}
          ></CloseIcon>
        </Grid>
      )}

      <Grid
        id="PreView"
        sx={{
          position: "absolute",
          transform: `scale(${scale})`,
          width: programData.width + "px",
          height: programData.height + "px",
          backgroundColor: currentPage.bgColor
            ? currentPage.bgColor
            : "#ffffff",
          boxShadow: "0px 0px 7px 1px #ebeef5",
          overflow: "hidden",
        }}
      >
        {currentPage.isTemplate && (
          <Grid
            sx={{
              width: "100%",
              height: "100%",
              zIndex: 10,
              position: "absolute",
              backgroundColor: currentPage.bgColor,
            }}
          >
            {currentPage.bgImg && (
              <img
                style={{
                  zIndex: 0,
                  position: "absolute",
                  width: "100%",
                  height: "100%",
                }}
                crossOrigin="anonymous"
                src={currentPage.bgImg + "?_aa=" + new Date().getTime()}
              ></img>
            )}

            {currentPage.tempLayout.map((item, index) => {
              return (
                <Grid
                  key={index}
                  sx={{
                    width: item.width + "px",
                    height: item.height + "px",
                    position: "absolute",
                    top: item.top + "px",
                    left: item.left + "px",
                    backgroundColor: item.bgColor,
                    overflow: "hidden",
                    boxShadow:
                      props.activeTempIndex === index
                        ? "0px 0px 5px #7ac143"
                        : "0px 0px 2px #00000029",
                  }}
                >
                  {item.bgImg && (
                    <img
                      style={{
                        zIndex: 0,
                        position: "absolute",
                        width: "100%",
                        height: "100%",
                      }}
                      crossOrigin="anonymous"
                      src={item.bgImg + "?_aa=" + new Date().getTime()}
                    ></img>
                  )}

                  {item.componentList.map((comItem, componentIndex) => {
                    if (comItem.hide) {
                      return "";
                    } else {
                      return (
                        <RenderCompon
                          scale={scale}
                          key={comItem.componentId}
                          componentIndex={componentIndex}
                          info={comItem}
                          tempIndex={index}
                          {...props}
                        ></RenderCompon>
                      );
                    }
                  })}
                </Grid>
              );
            })}
          </Grid>
        )}

        {currentPage.bgImg && !currentPage.isTemplate && (
          <img
            style={{
              zIndex: 0,
              position: "absolute",
              width: "100%",
              height: "100%",
            }}
            crossOrigin="anonymous"
            src={currentPage.bgImg + "?_aa=" + new Date().getTime()}
          ></img>
        )}

        {currentPage?.componentList?.map((item, componentIndex) => {
          if (item.hide) {
            return "";
          } else {
            return (
              <RenderCompon
                centerWidth={pageWidth}
                centerHeight={pageHeight}
                scale={scale}
                key={item.componentId}
                setContextMenu={() => {}}
                componentIndex={componentIndex}
                info={item}
                {...props}
              ></RenderCompon>
            );
          }
        })}
      </Grid>
      <Grid
        sx={{
          position: "absolute",
          bottom: "0px",
          width: "100%",
          height: "40px",
          alignItems: "center",
          display: "flex",
          justifyContent: "space-around",
          backgroundColor: "#ffffff",
        }}
      >
        {showScale && (
          <Grid
            sx={{
              width: "150px",
              display: "flex",
              alignItems: "center",
              mr: 3,
            }}
          >
            <RemoveIcon
              sx={{
                fontSize: "12px",
                marginRight: "10px",
                color: "#323232",
              }}
              onClick={minusScale}
            ></RemoveIcon>
            <PrettoSlider
              onChange={handleSliderChange}
              size="small"
              min={0.01}
              max={2}
              step={0.01}
              color="secondary"
              value={scale}
              aria-label="Small"
              valueLabelDisplay="on"
            />
            <AddIcon
              sx={{
                fontSize: "12px",
                marginLeft: "10px",
                color: "#323232",
              }}
              onClick={() => {
                plusScale();
              }}
            ></AddIcon>
          </Grid>
        )}

        <Grid
          sx={{
            display: "flex",
            minWidth: "150px",
          }}
        >
          {pageIndex !== 0 && (
            <Button
              sx={{
                cursor: "pointer",
              }}
              onClick={prePage}
              variant="text"
            >
              {message("editor_page_previous")}
            </Button>
          )}
          {pageIndex !== programData.pages.length - 1 && (
            <Button
              sx={{
                cursor: "pointer",
              }}
              onClick={nexPage}
              variant="text"
            >
              {message("editor_page_next")}
            </Button>
          )}
        </Grid>
      </Grid>
    </Grid>
  );
};

export default PreView;
