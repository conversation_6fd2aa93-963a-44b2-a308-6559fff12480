import React from 'react'
import Moveable from "react-moveable";
import  { useEffect, useState, useRef } from "react";
import { debounce } from "@mui/material/utils";
const CustomTemplate = (props) => {
  const targetRef = useRef(null);
  const moveableRef = useRef(null);

  const { item, width, height, currentComponentId, isPre, tempIndex } = props;
  const setTemalateInfo = (baseInfo) => {
    const currentPageIndex = props.currentPageIndex;
    const pages = JSON.parse(JSON.stringify(props.pages));
    const currentPage = pages[currentPageIndex];
    let oldInfo = currentPage.tempLayout[props.activeTempIndex];
    let newInfo = {
      ...oldInfo,
      ...baseInfo,
    };
    currentPage.tempLayout[props.activeTempIndex] = newInfo;
    if (props.setPages) {
      props.setPages(pages);
    }
  };
  const validationDebounced = debounce(setTemalateInfo,200);
  const validationDebounced2 = debounce(setTemalateInfo,300);

  let [dropBounds, setDropBounds] = useState({
    top: 0,
    left: 0,
    right: parseInt(width),
    bottom: parseInt(height),
  });
  useEffect(() => {
    targetRef.current.style.width = item.width + "px";
    targetRef.current.style.height = item.height + "px";
    targetRef.current.style.transform = `translate(${item.left}px, ${
      item.top
    }px) rotate(${item.rotate ? item.rotate : 0}deg)`;
    if (!isPre) {
      moveableRef?.current?.moveable?.updateRect();
    }
  }, [item]);

  return (
    <>
      <div
        onClick={(e) => {
          props.clickTemp(e, tempIndex);
        }}
        ref={targetRef}
        style={{
          width: item.width + "px",
          height: item.height + "px",
          transform: `translate(${item.left}px, ${item.top}px) rotate(${
            item.rotate ? item.rotate : 0
          }deg)`,
          position: "absolute",
        }}
      >
        {props.children}
      </div>

      {props.activeTempIndex!==''&&currentComponentId === "" && props.activeTempIndex == tempIndex && (
        <Moveable
          ref={moveableRef}
          target={targetRef}
          snappable={true}
          draggable={
            currentComponentId === "" && props.activeTempIndex == tempIndex
          }
          throttleDrag={1}
          edgeDraggable={false}
          startDragRotate={0}
          resizable={true}
          throttleResize={1}
          origin={false}
          bounds={dropBounds} // 边界点
          renderDirections={["se"]}
          onDrag={(e) => {
            e.target.style.transform = e.transform;
            let left = Math.floor(e.translate[0]);
            let top = Math.floor(e.translate[1]);
            validationDebounced2({
              left: left < 0 ? 0 : left,
              top: top < 0 ? 0 : top,
            });
          }}
          onResize={(e) => {
            let translate = e.drag.translate;
            let width = Math.floor(e.width);
            let height = Math.floor(e.height);
            let left = Math.floor(translate[0]);
            let top = Math.floor(translate[1]);
            e.target.style.width = `${width}px`;
            e.target.style.height = `${height}px`;
            e.target.style.transform = e.drag.transform;
            validationDebounced({
              width: width,
              height: height,
              left: left < 0 ? 0 : left,
              top: top < 0 ? 0 : top,
            });
          }}
          onRotate={(e) => {
            e.target.style.transform = e.drag.transform;
          }}
        ></Moveable>
      )}
    </>
  );
};

export default CustomTemplate;
