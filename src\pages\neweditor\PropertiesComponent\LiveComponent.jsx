import React from "react";
import { <PERSON>, <PERSON>rid, <PERSON>vide<PERSON>, <PERSON><PERSON>, But<PERSON> } from "@mui/material";
import AppBar from "@mui/material/AppBar";
import Toolbar from "@mui/material/Toolbar";
import { styled } from "@mui/material/styles";
import AddIcon from "@mui/icons-material/Add";
import { getComponentId } from "../common/utils";
import { useState } from "react";
import {
  AntTab,
  AntTabs,
  FormLabel,
  PrettoSlider,
} from "./PropertiesComponent";
import CustomInput from "../components/CustomInput";
import CustomSelect from "../components/CustomSelect";
import CustomGroupSelect from "../components/CustomGroupSelect";
import { useEffect } from "react";
import {
  fontSizeList,
  fontList,
  formatList,
  animationList,
} from "../common/utils";
import ColorPick from "../components/ColorPick";
import { message } from "../common/i18n";
import { isPositiveInteger } from "../common/utils";
import { toast } from "react-toastify";
import { pageDuration } from "../common/utils";
const LiveComponent = (props) => {
  const currentPageIndex = props.currentPageIndex;
  const currentIndex = props.currentComponentIndex;
  const pages = props.pages;
  const activeTempIndex = props.activeTempIndex;

  const [properties, setProperties] = useState({
    title: "editor_Live", //标题
    name: "editor_Live", //名称
    type: "ZKTecoLive", //组件类型
    hide: false,
    borderRadius: "0",
    duration: 3600,
    rotate: 0,
    transparency: 1,
    left: 12,
    top: 15,
    width: 350,
    height: 130,
    zIndex: 30,
    url: "",
  });

  useEffect(() => {
    if (currentIndex !== "") {
      const curretnPage = pages[currentPageIndex];
      if (curretnPage.isTemplate) {
        let componentInfo =
          curretnPage.tempLayout[activeTempIndex]?.componentList[currentIndex];
        setProperties({
          ...componentInfo,
        });
      } else {
        let componentInfo = pages[currentPageIndex].componentList[currentIndex];
        setProperties({
          ...componentInfo,
        });
      }
    }
  }, [currentPageIndex, currentIndex, activeTempIndex, pages]);

  const setComponentInfo = (baseInfo) => {
    const newPages = JSON.parse(JSON.stringify(props.pages));
    let currentPage = newPages[currentPageIndex];
    if (currentPage.isTemplate) {
      let oldInfo =
        currentPage.tempLayout[activeTempIndex].componentList[currentIndex];
      let newInfo = {
        ...oldInfo,
        ...baseInfo,
      };
      try {
        let durationArry = [
          newPages[currentPageIndex].duration,
          properties.duration,
        ];
        if (durationArry.length > 0) {
          const maxNum = Math.max(...durationArry);
          currentPage.tempLayout[activeTempIndex].duration = maxNum;
        }
      } catch (error) {
        console.log(error);
      }
      currentPage.tempLayout[activeTempIndex].componentList[currentIndex] =
        newInfo;
    } else {
      let oldInfo = newPages[currentPageIndex].componentList[currentIndex];
      let newInfo = {
        ...oldInfo,
        ...baseInfo,
      };
      try {
        const maxNum = Math.max(
          newPages[currentPageIndex].duration,
          properties.duration
        );
        newPages[currentPageIndex].duration = maxNum;
      } catch (error) {
        console.log(error);
      }
      newPages[currentPageIndex].componentList[currentIndex] = newInfo;
    }

    if (props.setPages) {
      props.setPages(pageDuration(newPages));
    }
  };

  const changeProperties = (event) => {
    const name = event.target.name;
    let newInfo = {
      ...properties,
      [name]: event.target.value,
    };
    setComponentInfo(newInfo);
  };

  const handleRotationChange = (event, newValue) => {
    let newInfo = {
      ...properties,
      rotate: newValue,
    };
    setComponentInfo(newInfo);
  };

  const handleSliderChange = (event, newValue) => {
    let newInfo = {
      ...properties,
      transparency: newValue,
    };
    setComponentInfo(newInfo);
  };

  const changeDuration = (event) => {
    let value = event.target.value;
    if (isPositiveInteger(value)) {
      if (value > 999999) {
        value = 999999;
      }
      if (value < 1) {
        value = 1;
      }
      let newInfo = {
        ...properties,
        duration: value,
      };
      setComponentInfo(newInfo);
    } else {
      let newInfo = {
        ...properties,
        duration: 3600,
      };
      setComponentInfo(newInfo);
      toast.error(message("editor_number_error_message"));
    }
  };

  const onBlurFn = (event) => {
    let value = event.target.value;
    if (value === "") {
      let newInfo = {
        ...properties,
        duration: 3600,
      };
      setComponentInfo(newInfo);
      toast.error(message("editor_number_error_message"));
    }
  };

  return (
    <Grid
      sx={{
        width: "100%",
        boxShadow: "0px 0px 6px #00000029",
        borderRadius: "10px",
        backgroundColor: "#ffffff",
        overflow: "hidden",
        minHeight: "200px",
        padding: "20px",
      }}>
      <Grid>
        <Grid
          sx={{
            display: "flex",
            alignItems: "center",
            mt: 1,
          }}>
          <CustomInput
            label={message("editor_layerName") + ":"}
            value={properties.name}
            onChange={changeProperties}
            name="name"></CustomInput>
        </Grid>

        <Grid
          sx={{
            display: "flex",
            alignItems: "center",
            mt: 1,
          }}>
          <CustomInput
            label={"URL"}
            value={properties.url}
            onChange={changeProperties}
            name="url"></CustomInput>
        </Grid>

        <Grid
          sx={{
            mt: 2,
            display: "flex",
            alignItems: "center",
          }}>
          <Grid>{message("editor_duration")}</Grid>
          <Grid>
            <CustomInput
              sx={{
                marginTop: "0px",
                input: {
                  padding: "5px",
                },
              }}
              type="number"
              value={properties.duration}
              onChange={(e) => {
                changeDuration(e);
              }}
              onBlur={(e) => {
                onBlurFn(e);
              }}
              name="duration"></CustomInput>
          </Grid>
          <Grid
            sx={{
              ml: 1,
            }}>
            {message("editor_second")}
          </Grid>
        </Grid>

        <Grid
          sx={{
            display: "flex",
            alignItems: "center",
            mt: 1,
          }}>
          <FormLabel sx={{ mr: 2 }}>{message("editor_rotate")}:</FormLabel>
          <PrettoSlider
            onChange={handleRotationChange}
            size="small"
            min={0}
            max={360}
            step={1}
            color="secondary"
            value={properties.rotate}
            aria-label="Small"
            // valueLabelDisplay="off"
          ></PrettoSlider>
        </Grid>

        <Grid
          sx={{
            display: "flex",
            alignItems: "center",
            mt: 1,
          }}>
          <FormLabel sx={{ mr: 2 }}>{message("editor_diaphaneity")}:</FormLabel>
          <PrettoSlider
            onChange={handleSliderChange}
            size="small"
            min={0}
            max={1}
            step={0.1}
            color="secondary"
            value={properties.transparency}
            aria-label="Small"
            // valueLabelDisplay="off"
          ></PrettoSlider>
        </Grid>

        <CustomInput
          label={message("editor_abscissa") + ":"}
          value={properties.left}
          onChange={changeProperties}
          name="left"></CustomInput>

        <CustomInput
          label={message("editor_ordinate") + ":"}
          value={properties.top}
          onChange={changeProperties}
          name="top"></CustomInput>

        <CustomInput
          label={message("editor_width") + ":"}
          value={properties.width}
          onChange={changeProperties}
          name="width"></CustomInput>

        <CustomInput
          label={message("editor_height") + ":"}
          value={properties.height}
          onChange={changeProperties}
          name="height"></CustomInput>
      </Grid>
    </Grid>
  );
};

export default LiveComponent;
