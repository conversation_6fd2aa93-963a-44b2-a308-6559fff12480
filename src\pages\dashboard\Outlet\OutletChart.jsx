import { useEffect, useRef, useState } from "react";
import * as echarts from "echarts";
import { useTranslation } from "react-i18next";

const OutletChart = (props) => {
  const chartRef = useRef(null);
  const [myEcharts, setMyEcharts] = useState(null);
  const { i18n, t } = useTranslation();
  const language = i18n.language;

  const initChart = () => {
    let chart = echarts.init(chartRef.current, null, { renderer: "svg" });
    setMyEcharts(chart);
    // 设置初始大小
    chart.resize();
    // 监听窗口大小变化，自动调整图表大小
    window.addEventListener("resize", handleResize);
    const options = getOptions(props.chartData);
    chart.setOption(options);
  };

  const handleResize = () => {
    if (myEcharts) {
      myEcharts.resize();
    }
  };

  useEffect(() => {
    initChart();
    return () => {
      window.removeEventListener("resize", handleResize);
      if (myEcharts) {
        myEcharts.dispose();
        setMyEcharts(null);
      }
    };
  }, []);

  useEffect(() => {
    if (myEcharts === null) {
      initChart();
    } else {
      const options = getOptions(props.chartData);
      myEcharts.setOption(options);
    }
  }, [props.chartData]);

  const getOptions = (data) => {
    let xAxis = [];
    let dataArry1 = [];
    let dataArry2 = [];
    data.forEach((item) => {
      let total = item.total;
      xAxis.push(item.fieldName);
      dataArry1.push(total[0]);
      dataArry2.push(total[1]);
    });

    return {
      title: {
        text: t("outlet.total_outlets_installed"),
        subtext: t("outlet.vs_total_outlets"),
        left: language === "es" ? "1%" : "5%",
        textStyle: {
          fontSize: language === "es" ? 16 : 20,
        },
        top: "30%",
      },
      tooltip: {
        trigger: "axis",
      },
      legend: {
        data: [
          {
            name: t("outlet.outlets_installed"),
            icon: "circle",
          },
          {
            name: t("outlet.total_outlets"),
            icon: "circle",
          },
        ],
        align: "left",
        orient: "vertical",
        left: "5%",
        top: "45%",
        icon: "circle",
        itemWidth: 40,
        itemHeight: 27,
        itemGap: 20, //图例间距
        textStyle: {
          fontSize: language === "es" ? "16px" : "20px",
          fontWeight: "bold",
          lineHeight: 30,
          rich: {
            a: {
              align: "left",
              color: "#7a7a7a",
              fontSize: language === "es" ? "14px" : "20px",
            },
            b: {
              color: "#8c8c8c",
              fontSize: language === "es" ? "12px" : "15px",
              lineH: "10px",
            },
          },
        },
        formatter: function (name) {
          return `{a| ${name} }\n{b|${t("outlet.month_to_date")}}`;
        },
      },
      grid: {
        left: "25%",
      },
      xAxis: {
        type: "category",
        data: xAxis,
      },
      yAxis: {
        type: "value",
        axisTick: {
          // 轴刻度
          show: true,
        },
        axisLabel: {
          // 轴文字
          show: true,
          color: "#000000",
          fontSize: 12,
        },
        axisLine: {
          // 轴线
          show: true,
          color: "#268C8C",
        },
      },
      series: [
        {
          name: t("outlet.outlets_installed"),
          type: "bar",
          data: dataArry1,
          barWidth: "20px",
          itemStyle: {
            normal: {
              color: "#85bc56",
            },
          },
        },
        {
          name: t("outlet.total_outlets"),
          type: "bar",
          data: dataArry2,
          barWidth: "20px",
          itemStyle: {
            normal: {
              color: "#c9e0b6",
            },
          },
        },
      ],
    };
  };
  return <div style={{ width: "100%", height: "100%" }} ref={chartRef}></div>;
};

export default OutletChart;
