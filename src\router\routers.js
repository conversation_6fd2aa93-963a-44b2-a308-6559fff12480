import bizRoutes from "./menus/index";
const routes = [
  {
    path: "/",
    redirect: "/dashboard/Summary",
  },
  {
    path: "/redirect",
    redirect: "/dashboard/Summary",
  },
  ...bizRoutes,

  {
    path: "/neweditor",
    component: () => import("@/pages/neweditor/index"),
    meta: {
      title: "编辑器",
      idEditor: true,
      i18n: "editor"
    },
  },

  {
    path: "/403",
    component: () => import("@/pages/notfount/403"),
    meta: {
      title: "403",
      i18n: "media_403",
      needLogin: false,
    },
  },
  {
    path: "/404",
    component: () => import("@/pages/notfount/404"),
    meta: {
      requiresAuth: false,
      i18n: "media_404",
      title: "404页面",
    },
  },
  {
    path: "*",
    component: () => import("@/pages/notfount/404"),
  },
];

export default routes;
