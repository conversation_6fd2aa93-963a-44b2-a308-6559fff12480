export default {
  adverStatistics: {
    materialName: "Material Name",
    materialGroupName: "Material group name",
    storeName: "Store Name",
    screenName: "Screen Name",
    merchantName: "Principal Name",
    playCount: "Play Count",
    countTime: "Statistical time",
    dateRange: "dateRange",
    exportStatistic: "Export Statistics",
    exportName:
      "{{startTime}}~{{endTime}} Time range Statistics on playback of materials",
  },
  menu: {
    material_report_export: "Export",
    real_time_viewing: "Demographic",
    screen_shutdown: "Scheduled shutdown",
    screen_clean_shutdown: "Clear scheduled shutdown",
    screen_clean_schedule: "Clear playback schedule",
    screen_remote_diagnosis: "Remote diagnosis",
  },
  realTime: {
    ageTitle: "Percentage of Visitors Age Range",
    ageTooltip: "No. of Visitors {c}",
    ageLgen: "age {{name}}",
    female: "female",
    male: "male",
    genderTitle: "Percentage of Visitors Gender",
    genderTooltip: "No. of Visitors {c}",
    entryTrendTitle: " Entry Flow Trend",
    one: "Age 0-11",
    two: "Age 12-17",
    three: "Age 18-34",
    four: "Age 35-54",
    plus: "Age 55+",
    demographic: "Visitors Demographic",
    genderDistriBution: "Gender Distribution",
    realTimeVisitors:"Real Time Visitors",
    contentList:"Content List"
  },
  upload:{
    warnMsg:"Please check whether the content of the uploaded file contains illegal characters!",
  },
  playlist:{
    durationRange:"The duration ranges from 1 to 999999"
  },
  screen:{
    device_operater:"Device operation",
    please_select_device:"Please select the device to operate",
    reboot_devices:"Devices to reboot: {{names}}",
    rebooting:"Rebooting...",
    time_shutdown:"Scheduled Shutdown",
    clean_shutdown: "Cleaning devices scheduled for shutdown: {{names}}",
    clean_shutdowning: "Cleaning in progress...",
    clean_shutdown_btn: "Clean Shutdown",
    tips: "Tips:",
    clean_schedule_desc_tips: "When attempting to clear schedules, verify if devices were cleaned successfully. Reasons for failure may include: 1. Schedule data does not exist, 2. Device is currently downloading resources.",
    clean_schedule_screen: "Devices cleared: {{names}}",
    clean_schedules: "Cleaning...",
    clean_schedule_btn: "Clear Playback Schedule",
    executed_success_all: "All executed successfully",
    executed_error_all: "All executed with errors",
    execution_tips_section: "Partially executed successfully: {{success}}, failed: {{error}}",
    screen_shutdown_now:"Immediate shutdown",
  },
  cron:{
    everyday:"Everyday",
    everyweek:"Every Week",
    everymonth:"Every Month",
    customer:"Custom",
    corn_expression:"Expression is:",
    copy_success:"Copy Success",
    month:"Month",
    weeks:"Weeks",
    day:"Day",
    hao:"Number",
    hour:"Hour",
    minute:"Minute",
    week_1:"Monday",
    week_2:"Tuesday",
    week_3:"Wednesday",
    week_4:"Thursday",
    week_5:"Friday",
    week_6:"Saturday",
    week_7:"Sunday",
    please_select_week:"Please select week",
    please_select_month:"Please select month",
    please_select_day:"Please select day",
    please_select_hour:"Please select hour",
    please_select_minute:"Please select minute",
    month_1:"January",
    month_2:"February",
    month_3:"March",
    month_4:"April",
    month_5:"May",
    month_6:"June",
    month_7:"July",
    month_8:"August",
    month_9:"September",
    month_10:"October",
    month_11:"November",
    month_12:"December",
    },
    detail: {
      device_name: "Device:",
      device_status: "Status:",
      label_detail: "Device Details",
      label_remote_diagnosis: "Remote Diagnosis",
      current_remote_diagnosis: "Diagnosing",
      diagnosis_result: "Diagnosis Result",
      total_memory_free: "Total Memory / Free Memory",
      cpu_usage: "Current CPU Usage",
      total_storage_free: "Total Storage / Free Storage",
      update_time: "Update Time",
      current_schedule_status: "Current Schedule Status",
      current_schedule_status_unknown: "Unknown",
      current_schedule_status_normal: "Currently Playing Schedule",
      current_schedule_status_not: "No Schedule",
      device_rediagnosis: "Rediagnose",
      device_start_rediagnosis: "Start Diagnosis",
      current_remote_diagnosis_finsh: "Diagnosis Finished"
    }

};
