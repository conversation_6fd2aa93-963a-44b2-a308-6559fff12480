import React, { forwardRef, useRef, useEffect, useState } from "react";
import {
  Grid,
  Card,
  InputAdornment,
  IconButton,
  Button,
  TextField,
  Typography,
  Stack,
  CircularProgress,
  Box,
} from "@mui/material";
import MerchantSelect from "@/components/MerchantSelect";
import OutletDetails from "./Outlet/OutletDetails";
import DigitalSignage from "./Outlet/DigitalSignage";
import { useTranslation } from "react-i18next";
const Outlet = (props) => {
  const [retailClientId, setRetailClientId] = useState("");
  const [merchantError, setMerchantError] = useState("");
  const [showType, setShowType] = useState("outlet");
  const { t } = useTranslation();
  useEffect(() => {
    let retailData = localStorage.getItem("outletSelectedRetailValue");
    if (retailData) {
      setRetailClientId(retailData);
    }
  }, []);
  const retailClientChange = (v) => {
    setRetailClientId(v);
    setMerchantError("");
  };
  const retailClear = () => {
    setRetailClientId("");
    setMerchantError("");
  };
  let activeStyle = {
    background: "rgb(68,68,68)",
    color: "#ffffff",
    borderColor: "rgb(68,68,68)",
  };
  let unActiveStyle = {
    background: "#ffffff",
    color: "rgb(68,68,68)",
    borderColor: "rgb(68,68,68)",
  };
  return (
    <Grid>
      <Grid
        sx={{
          justifyContent: "space-between",
        }}
        container>
        <Grid sx={{ ml: 2, mb: 2 }}>
          <div
            style={{
              margin: "10px",
            }}>
            {t("dashboard.retail")}{" "}
          </div>
          <MerchantSelect
            retailKey="outletSelectedRetailValue"
            onClear={retailClear}
            error={merchantError}
            onChange={retailClientChange}
          />
        </Grid>
        <Grid>
          <Button
            onClick={() => {
              setShowType("outlet");
            }}
            size="small"
            style={showType === "outlet" ? activeStyle : unActiveStyle}
            variant={showType === "outlet" ? "contained" : "outlined"}>
            {t("outlet.outlet")}
          </Button>
          <Button
            onClick={() => {
              setShowType("signage");
            }}
            sx={{ ml: 2 }}
            size="small"
            style={showType === "signage" ? activeStyle : unActiveStyle}
            variant={showType === "signage" ? "contained" : "outlined"}>
            {t("outlet.signage")}
          </Button>
        </Grid>
      </Grid>
      <Grid>
        {showType === "outlet" && (
          <OutletDetails retailClientId={retailClientId}></OutletDetails>
        )}
        {showType === "signage" && (
          <DigitalSignage retailClientId={retailClientId}></DigitalSignage>
        )}
      </Grid>
    </Grid>
  );
};
export default Outlet;
