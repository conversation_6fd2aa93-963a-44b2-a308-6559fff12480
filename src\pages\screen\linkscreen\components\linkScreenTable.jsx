/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/prop-types */
import React, { useEffect, useMemo, useState, useRef } from "react";
import MaterialReactTable from "material-react-table";
import {
  Button,
  Stack,
  Typography,
  Tooltip,
  Link,
  InputAdornment,
  Grid,
  InputBase,
  IconButton,
  TextField,
} from "@mui/material";
// api
import {
  listByPage,
  removeLinkScreen,
  cleanSchedule,
  cleanPlayerSchedule,
} from "@/service/api/linkScreen";
import { useTranslation } from "react-i18next";
import { useConfirm } from "@/components/zkconfirm";
// 消息提示
import { toast } from "react-toastify";
import { useNavigate, useLocation } from "react-router-dom";
import { tableI18n } from "@/utils/tableLang";
import { useFormik } from "formik";
import { removeEmpty } from "@/utils/StringUtils";
import MainCard from "@/components/MainCard";
import AuthButton from "@/components/AuthButton";
import DictTag from "@/components/DictTag";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import CancelIcon from "@mui/icons-material/Cancel";
import InfoIcon from "@mui/icons-material/Info";
import ToastContent from "@/components/@extended/ToastContent";
import { CloseOutlined } from "@material-ui/icons";

const LinkScreenTableList = () => {
  const { t } = useTranslation();
  const confirm = useConfirm();
  const navigate = useNavigate();
  const location = useLocation();
  const [isError, setIsError] = useState(false);
  const [rowSelection, setRowSelection] = useState([]);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  // 表格数据
  const [data, setData] = useState([]);
  // 分页的参数
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);

  // 显示搜索
  const [showSearch, setShowSearch] = useState(true);
  // 查询参数
  const requestParams = useRef(null);
  // 构建参数
  const buildParams = () => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      ...requestParams.current,
    };
    return params;
  };
  // 获取数据
  const getTableData = async () => {
    if (!data.length) {
      setIsLoading(true);
    } else {
      setIsRefetching(true);
    }
    // // 开启加载
    // setIsLoading(true);
    // setIsRefetching(true);
    await listByPage(buildParams())
      .then((res) => {
        // 设置数据
        setData(res.data.data);
        // 设置总记录数
        setRowCount(res.data.total);
        setIsLoading(false);
        setIsRefetching(false);
      })
      .catch((err) => {
        setIsError(true);
        setIsLoading(false);
        setIsRefetching(false);
      });
  };
  useEffect(() => {
    // const params = buildParams();
    // 发请求
    getTableData();
  }, [pagination.pageIndex, pagination.pageSize, location]);
  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "name",
        header: t("common.common_link_screen_name"),
        Cell: ({ cell, row }) => {
          return (
            <Tooltip title={row.original.name} placement="top">
              <Typography className="textSpace">{row.original.name}</Typography>
            </Tooltip>
          );
        },
      },
      {
        accessorKey: "num",
        header: t("common.common_link_screen_num"),
      },
      {
        enableColumnFilter: false,
        header: t("common.common_link_screen_specifca"),
        Cell: ({ cell, row }) => {
          return (
            <Typography className="textSpace">
              {row.original.line}*{row.original.columns}
            </Typography>
          );
        },
      },
      {
        accessorKey: "outletName",
        header: t("ips.ips_store_name"),
        Cell: ({ cell, row }) => {
          return (
            <Tooltip title={row.original.outletName} placement="top">
              <Typography className="textSpace">
                {row.original.outletName}
              </Typography>
            </Tooltip>
          );
        },
      },
      {
        accessorKey: "address",
        header: t("common.common_location"),
        Cell: ({ cell, row }) => {
          return (
            <Tooltip title={row.original.address} placement="top">
              <Typography className="textSpace">
                {row.original.address}
              </Typography>
            </Tooltip>
          );
        },
      },
    ],
    []
  );
  // 删除联屏
  const handelRemoveLinkScreen = (ids, names) => {
    confirm({
      title: t("message.messageBox_title"),
      confirmationText: t("common.common_edit_ok"),
      cancellationText: t("common.common_edit_cancel"),
      description: t("common.common_confirm_link_delete_link_screen", {
        ids: names,
      }),
    }).then(() => {
      removeLinkScreen(ids)
        .then((res) => {
          toast.success(res.message);
          // 重新请求数据
          getTableData();
          //重置选中行框
          setRowSelection([]);
          // setIsRefetching(true);
        })
        .catch((error) => {});
    });
  };
  // 查询表单
  const queryFormik = useFormik({
    initialValues: {
      name: "",
      num: "",
      direction: "",
      address: "",
      storeName: "",
      columns: "",
      line: "",
    },
    onSubmit: async (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        // 拷贝value值
        const tempValue = { ...values };
        await removeEmpty(tempValue);
        // setRequestParams(tempValue);
        requestParams.current = tempValue;
        setPagination({
          pageIndex: 0,
          pageSize: 10,
        });
        getTableData();
        // 查询table
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
  });
  // 重置查询
  const resetQuery = () => {
    // 重置表单
    queryFormik.resetForm();
    requestParams.current = null;
    setPagination({
      pageIndex: 0,
      pageSize: 10,
    });
    getTableData();
  };
  const CustomerToastIcon = ({ successCount, errorCount }) => {
    return (
      <>
        {successCount > 0 && errorCount === 0 ? (
          <CheckCircleIcon sx={{ color: "#61d345" }} />
        ) : errorCount > 0 && successCount === 0 ? (
          <CancelIcon sx={{ color: "#ff4b4b" }} />
        ) : (
          <InfoIcon sx={{ color: "#1890ff" }} />
        )}
      </>
    );
  };

  const handleCleanSchedule = (rows, type = "list") => {
    const names = [];
    const ids = [];
    let loadingToast;
    if (type === "list") {
      rows.forEach((element) => {
        names.push(element.original.name);
        ids.push(element.original.id);
      });
    } else {
      names.push(rows.name);
      ids.push(rows.id);
    }
    //  `${t('screen.reboot_devices',{
    //   names:names
    // })} <br/>123`

    let aStr = t("screen.clean_schedule_screen", {
      names: names,
    });

    let bStr = t("screen.tips");
    let cStr = t("screen.clean_schedule_desc_tips");

    confirm({
      title: t("message.messageBox_title"),
      confirmationText: t("common.common_edit_ok"),
      cancellationText: t("common.common_edit_cancel"),
      description: ` <div>
          ${aStr}
          <br />
          <b> ${bStr}</b>
          ${cStr}
        </div>`,
    }).then(() => {
      loadingToast = toast.loading((toast) => (
        <ToastContent text={t("screen.clean_schedules")} t={toast} />
      ));
      cleanPlayerSchedule(ids)
        .then((res) => {
          toast.dismiss(loadingToast);
          // toast((t) => <ToastContent text={res?.message} t={t} />,{
          //     icon:<InfoIcon sx={{color:"#1890ff"}}/>
          // });
          const successCount = res?.data?.success;
          const errorCount = res?.data?.fail;
          toast(
            (t1) => (
              <CustomToastComponent
                successCount={successCount}
                errorCount={errorCount}
                t1={t1}
              />
            ),
            {
              icon: (
                <CustomerToastIcon
                  successCount={successCount}
                  errorCount={errorCount}
                />
              ),
            }
          );
        })
        .catch((err) => {
          toast.dismiss(loadingToast);
        });
    });
  };
  const CustomToastComponent = ({ successCount, errorCount, t1 }) => {
    return (
      <div
        style={{
          maxWidth: 500, // Corrigido para maxWidth ao invés de maxWeight
          minWidth: 300,
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}>
        <div>
          {successCount > 0 && errorCount == 0
            ? t("screen.executed_success_all")
            : errorCount > 0 && successCount == 0
            ? t("screen.executed_error_all")
            : t("screen.execution_tips_section", {
                success: successCount,
                error: errorCount,
              })}
        </div>
        <IconButton onClick={() => toast.dismiss(t1.id)}>
          <CloseOutlined />
        </IconButton>
      </div>
    );
  };

  return (
    <>
      <MainCard style={{ marginBottom: "10px" }}>
        <form noValidate onSubmit={queryFormik.handleSubmit}>
          <Grid
            container
            direction="row"
            justifyContent="flex-start"
            alignItems="center"
            spacing={5}>
            <Grid
              sx={{
                minWidth: "300px",
              }}
              item
              md={8}
              lg={2}
              xs={12}>
              <TextField
                label={t("common.common_link_screen_name")}
                value={queryFormik.values.name}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                size="small"
                type="text"
                name="name"
                fullWidth
                placeholder={t("common.common_please_input")}
              />
            </Grid>
            <Grid
              sx={{
                minWidth: "300px",
              }}
              item
              md={4}
              lg={2}
              xs={12}>
              <TextField
                label={t("common.common_link_screen_num")}
                name="num"
                value={queryFormik.values.num}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                size="small"
                type="number"
                fullWidth
                placeholder={t("common.common_please_input")}
              />
            </Grid>
            <Grid item md={4} lg={2} xs={12}>
              <TextField
                label={t("ips.ips_store_name")}
                name="storeName"
                value={queryFormik.values.storeName}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                size="small"
                type="text"
                fullWidth
                placeholder={t("common.common_please_input")}
              />
            </Grid>
            {/*
            <Grid item md={4} lg={2} xs={12}>
              <ZKSelect
                name="direction"
                size="small"
                value={queryFormik.values.direction}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                options={screendirections}
                onClear={() => {
                  queryFormik.setFieldValue("direction", "");
                }}
                placeholder={t("ips.ips_screen_direction")}
              />
            </Grid> */}
            <Grid item md={4} lg={2} xs={12}>
              <TextField
                label={t("common.common_link_screen_specifca")}
                id="columns"
                type="number"
                name="columns"
                value={queryFormik.values.columns}
                fullWidth
                size="small"
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                placeholder={t("common.common_link_column")}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <InputBase
                        value={queryFormik.values.line}
                        id="line"
                        name="line"
                        type="number"
                        sx={{
                          background: "white",
                          width: "100%",
                        }}
                        onChange={queryFormik.handleChange}
                        onBlur={queryFormik.handleBlur}
                        placeholder={t("common.common_link_row")}
                        endAdornment={<>*</>}
                      />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            {
              <>
                {/* <Grid item md={4} lg={2} xs={12}></Grid> */}
                <Grid item md={4} lg={2} xs={12}>
                  <TextField
                    label={t("common.common_location")}
                    name="address"
                    size="small"
                    type="text"
                    fullWidth
                    placeholder={t("ips.ips_enter_location")}
                    value={queryFormik.values.address}
                    onChange={queryFormik.handleChange}
                    onBlur={queryFormik.handleBlur}
                  />
                </Grid>
              </>
            }

            <Grid item md={4} lg={2} xs={12}>
              <Stack
                direction="row"
                justifyContent="flex-start"
                alignItems="flex-start"
                spacing={2}>
                <Button
                  disableElevation
                  type="submit"
                  variant="contained"
                  size="small">
                  {t("common.common_table_query")}
                </Button>
                <Button
                  variant="outlined"
                  onClick={resetQuery}
                  color="info"
                  size="small"
                  sx={{
                    minWidth: "90px",
                  }}>
                  {t("common.common_op_reset")}
                </Button>
                {/* 展开、收起icon */}
                {/* <IconButton
                  onClick={() => setShowSearch(!showSearch)}
                  size="middle"
                >
                  {showSearch ? (
                    <>
                      <ExpandMoreIcon sx={{ mt: -0.5 }} />
                      <Typography sx={{ mt: -0.5 }}>
                        {t("common.common_form_unfold")}
                      </Typography>
                    </>
                  ) : (
                    <>
                      <ExpandLessIcon sx={{ mt: -0.5 }} />
                      <Typography sx={{ mt: -0.5 }}>
                        {t("common.common_form_packup")}
                      </Typography>
                    </>
                  )}
                </IconButton> */}
              </Stack>
            </Grid>
          </Grid>
        </form>
      </MainCard>
      <MaterialReactTable
        // table 状态
        state={{
          // 加载状态
          isLoading,
          // 分页参数
          pagination,
          // 重新拉取
          showProgressBars: isRefetching,
          showAlertBanner: isError,

          rowSelection,
        }}
        renderToolbarInternalActions={({ table }) => <></>}
        muiTablePaperProps={{
          elevation: 0,
          sx: {
            borderRadius: "5px",
            border: "1px solid #f0f0f0",
          },
        }}
        muiTableHeadRowProps={{
          sx: { backgroundColor: "#fafafa", boxShadow: "none" },
        }}
        // 关闭过滤搜素
        enableColumnFilters={true}
        // 关闭排序
        enableSorting={false}
        // 布局方式
        layoutMode="grid"
        enableColumnActions={false}
        // 开启列对齐
        muiTableHeadCellProps={{
          sx: {
            "& .Mui-TableHeadCell-Content": {
              justifyContent: "space-between",
            },
          },
        }}
        // 解决列太多宽度太长问题
        enableColumnResizing
        // enablePinning
        // 初始化状态
        initialState={{ columnVisibility: { createTime: true } }}
        muiToolbarAlertBannerProps={
          isError
            ? {
                color: "error",
                children: "Error loading data",
              }
            : undefined
        }
        //行选中
        onRowSelectionChange={setRowSelection}
        muiTableBodyRowProps={({ row }) => ({
          onClick: row.getToggleSelectedHandler(),
          sx: { cursor: "pointer" },
        })}
        // 开启多选
        enableRowSelection
        // 列数
        rowCount={rowCount}
        // 固定头部
        enableStickyHeader
        // 处理表格高度
        muiTableContainerProps={{ sx: { maxHeight: "620px" } }}
        // 设置背景颜色
        muiTableBodyCellProps={{ sx: { backgroundColor: "white" } }}
        muiTableProps={{
          sx: { backgroundColor: "white", tableLayout: "fixed" },
        }}
        muiBottomToolbarProps={{ sx: { backgroundColor: "white" } }}
        muiTopToolbarProps={{ sx: { backgroundColor: "white" } }}
        // 分页回调函数
        onPaginationChange={setPagination}
        // 记得开启这些 tips:否则会出现useEffect 反作用函数发起多次请求点击下一页会出问题
        manualFiltering
        manualPagination
        manualSorting
        // 开启分页
        enablePagination
        // 列定义
        columns={columns}
        // 数据
        data={data}
        // 初始化国际化语言  --- todo: i18n key doc address: https://github.com/KevinVandy/material-react-table/blob/main/packages/material-react-table/src/_locales/en.ts
        localization={tableI18n}
        // 自定义表头按钮
        renderTopToolbarCustomActions={({ table }) => {
          // const handleDeactivate = () => {
          //   table.getSelectedRowModel().flatRows.map((row) => {
          //     alert("deactivating " + row.getValue("name"));
          //   });
          // };

          // const handleActivate = () => {
          //   table.getSelectedRowModel().flatRows.map((row) => {
          //     alert("activating " + row.getValue("name"));
          //   });
          // };

          // const handleContact = () => {
          //   table.getSelectedRowModel().flatRows.map((row) => {
          //     alert("contact " + row.getValue("name"));
          //   });
          // };

          return (
            <Stack direction="row" spacing={1} alignItems="center">
              <AuthButton button="sd:screen:link_screen:save">
                <Button
                  variant="contained"
                  onClick={() => {
                    navigate("/screen/linkscreen/add");
                  }}>
                  {t("server.server_add_link_screen")}
                </Button>
              </AuthButton>
              <AuthButton button="sd:screen:link_screen:cleanSchedule">
                <Button
                  color="warning"
                  variant="contained"
                  disabled={
                    !table.getIsSomeRowsSelected() &&
                    !table.getIsAllRowsSelected()
                  }
                  onClick={() => {
                    handleCleanSchedule(table.getSelectedRowModel().rows);
                  }}>
                  {t("screen.clean_schedule_btn")}
                </Button>
              </AuthButton>
              <AuthButton button="sd:screen:link_screen:delete">
                <Button
                  variant="contained"
                  color="secondary"
                  disabled={
                    !table.getIsSomeRowsSelected() &&
                    !table.getIsAllRowsSelected()
                  }
                  onClick={() => {
                    const ids = [];
                    const names = [];
                    table.getSelectedRowModel().rows.map((row) => {
                      ids.push(row.original.id);
                      names.push(row.original.name);
                    });
                    handelRemoveLinkScreen(ids, names);
                  }}>
                  {t("common.common_op_batch_del")}
                </Button>
              </AuthButton>
            </Stack>
          );
        }}
        // 多选底部提示
        positionToolbarAlertBanner="none"
        // 开启action操作
        enableRowActions
        // action操作位置
        positionActionsColumn="last"
        displayColumnDefOptions={{
          "mrt-row-actions": {
            size: 120, //make actions column wider
          },
        }}
        renderRowActions={({ row, table }) => (
          <Stack direction="row" spacing={2} alignItems="center">
            <AuthButton button="sd:screen:link_screen:update">
              <Link
                component="button"
                underline="none"
                onClick={() =>
                  navigate(`/screen/linkscreen/edit?id=${row.original.id}`)
                }>
                {t("common.common_op_modify")}
              </Link>
            </AuthButton>
            <AuthButton button="sd:screen:link_screen:cleanSchedule">
              <Link
                component="button"
                color="warning"
                underline="none"
                onClick={() => handleCleanSchedule(row.original, "")}>
                {t("system.system_clean")}
                {/* {t("common.common_op_modify")} */}
              </Link>
            </AuthButton>
            <AuthButton button="sd:screen:link_screen:delete">
              <Link
                color={"error"}
                component="button"
                underline="none"
                onClick={() =>
                  handelRemoveLinkScreen(row.original.id, row.original.name)
                }>
                {t("common.common_op_del")}
              </Link>
            </AuthButton>
          </Stack>
        )}
      />
    </>
  );
};

export default LinkScreenTableList;
