@font-face {
  font-family: 'MyriadPro-Black';
  src: url("https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/ScreenDirect/Web/Font/v1/MyriadPro-Black.ttf");
}


@font-face {
  font-family: 'MyriadPro-Light';
  src: url("https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/ScreenDirect/Web/Font/v1/MyriadPro-Light.ttf");
}

@font-face {
  font-family: 'MyriadPro-Regular';
  src: url("https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/ScreenDirect/Web/Font/v1/MyriadPro-Regular.otf");
}


@font-face {
  font-family: 'MyriadPro-Semibold';
  src: url("https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/ScreenDirect/Web/Font/v1/MyriadPro-Semibold.ttf");
}

 @font-face {
  font-family: 'Aa剑豪体';
  src: url("https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/ScreenDirect/Web/Font/v1/Aa剑豪体.ttf");
}



@font-face {
  font-family: '庞门正道标题体';
  src: url("https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/ScreenDirect/Web/Font/v1/庞门正道标题体.ttf");
}

@font-face {
  font-family: '演示佛系体';
  src: url("https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/ScreenDirect/Web/Font/v1/演示佛系体.ttf");
}

@font-face {
  font-family: '阿里妈妈东方大楷';
  src: url("https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/ScreenDirect/Web/Font/v1/阿里妈妈东方大楷.ttf");
}

@font-face {
  font-family: '鸿雷行书简体';
  src: url("https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/ScreenDirect/Web/Font/v1/鸿雷行书简体.otf");
}


@font-face {
  font-family: 'Aa厚底黑';
  src: url("https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/ScreenDirect/Web/Font/v1/Aa厚底黑.ttf");
}


@font-face {
  font-family: 'Roboto Bold';
  src: url("https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/ScreenDirect/Web/Font/v1/Roboto Bold.ttf");
}

@font-face {
  font-family: '思源宋体 Regular';
  src: url("https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/ScreenDirect/Web/Font/v1/思源宋体 Regular.otf");
}


@font-face {
  font-family: '演示夏行楷';
  src: url("https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/ScreenDirect/Web/Font/v1/演示夏行楷.ttf");
}

@font-face {
  font-family: '阿里妈妈刀隶体';
  src: url("https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/ScreenDirect/Web/Font/v1/阿里妈妈刀隶体.ttf");
}


@font-face {
  font-family: '字体圈欣意冠黑体';
  src: url("https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/ScreenDirect/Web/Font/v1/字体圈欣意冠黑体.ttf");
}

@font-face {
  font-family: '思源黑体 Light';
  src: url("https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/ScreenDirect/Web/Font/v1/思源黑体 Light.otf");
}


@font-face {
  font-family: '演示悠然小楷';
  src: url("https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/ScreenDirect/Web/Font/v1/演示悠然小楷.ttf");
}

@font-face {
  font-family: '阿里妈妈数黑体';
  src: url("https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/ScreenDirect/Web/Font/v1/阿里妈妈数黑体.ttf");
}

@font-face {
  font-family: 'Roboto+Bold';
  src: url("https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/ScreenDirect/Web/Font/v1/Roboto+Bold.ttf");
}



@font-face {
  font-family: '字体圈欣意吉祥宋';
  src: url("https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/ScreenDirect/Web/Font/v1/字体圈欣意吉祥宋.ttf");
}

@font-face {
  font-family: '斗鱼追光体';
  src: url("https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/ScreenDirect/Web/Font/v1/斗鱼追光体.otf");
}

@font-face {
  font-family: '演示秋鸿楷';
  src: url("https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/ScreenDirect/Web/Font/v1/演示秋鸿楷.ttf");
}


@font-face {
  font-family: '字制区喜脉体';
  src: url("https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/ScreenDirect/Web/Font/v1/字制区喜脉体.ttf");
}

@font-face {
  font-family: '沐瑶软笔手写体';
  src: url("https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/ScreenDirect/Web/Font/v1/沐瑶软笔手写体.ttf");
}



@font-face {
  font-family: '沐瑶软笔手写体';
  src: url("https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/ScreenDirect/Web/Font/v1/沐瑶软笔手写体.ttf");
}


@font-face {
  font-family: '站酷庆科黄油体';
  src: url("https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/ScreenDirect/Web/Font/v1/站酷庆科黄油体.ttf");
}

@font-face {
  font-family: '阿里巴巴普惠体 Regular';
  src: url("https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/ScreenDirect/Web/Font/v1/阿里巴巴普惠体 3.0 55 Regular.ttf");
}


@font-face {
  font-family: '阿里巴巴普惠体 Heav';
  src: url("https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/ScreenDirect/Web/Font/v1/阿里巴巴普惠体 3.0 105 Heavy.ttf");
}

