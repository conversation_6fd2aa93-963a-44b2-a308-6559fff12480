/* eslint-disable react/prop-types */
/* eslint-disable react-hooks/rules-of-hooks */
import { useEffect, useState } from "react";
import { Select, MenuItem } from "@mui/material";
import { makeStyles } from "@material-ui/core/styles";

import {
  getCounty,
  getGoogleProvince,
  getGoogleCity,
} from "@/service/api/bmap";
import { useTranslation } from "react-i18next";

const useStyles = makeStyles((theme) => ({
  select: {
    minWidth: 120,
    margin: theme.spacing(1),
  },
}));

function SearchSelect({
  label,
  value,
  options,
  onChange,
  placeholder,
  currentSelected,
}) {
  const { t } = useTranslation();
  const classes = useStyles();
  const [open, setOpen] = useState(false);

  const handleOpen = () => {
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
  };
  const handleChange = (event) => {
    onChange(event.target.value);
  };

  return (
    <>
      <Select
        displayEmpty
        className={classes.select}
        labelId={`${label}-label`}
        id={label}
        open={open}
        align="left"
        onClose={handleClose}
        onOpen={handleOpen}
        onChange={handleChange}
        value={value}
        style={{ marginBottom: "23px" }}
        sx={{ width: 100, height: 40 }}
        renderValue={(selected) => {
          if (
            null === selected ||
            undefined === selected ||
            selected.length === 0 ||
            "0" === currentSelected
          ) {
            return <>{placeholder}</>;
          }
          return <>{selected}</>;
        }}
        MenuProps={{
          // getContentAnchorEl: null,
          anchorOrigin: {
            vertical: "bottom",
            horizontal: "left",
          },
          transformOrigin: {
            vertical: "top",
            horizontal: "left",
          },
          style: {
            maxHeight: 300, // 修改下拉菜单的最大高度
          },
        }}
      >
        {options.map((option) => (
          <MenuItem key={option.label} value={option}>
            {option.label}
          </MenuItem>
        ))}
      </Select>
    </>
  );
}

const CitySelect = (props) => {
  const { changeSelectAddress, search, setZoom } = props;
  //下拉框展示数据
  const [county, setCounty] = useState([]);
  const [province, setProvince] = useState([]);
  const [city, setCity] = useState([]);
  const { t } = useTranslation();
  //选中的省市地址
  const [countyAddress, setCountyAddress] = useState("");
  const [provinceAddress, setProvinceAddress] = useState("");
  const [cityAddress, setCityAddress] = useState("");
  //用于当上级选择的变化后，下级选中的置空
  const [provinceSelected, setProvinceSelected] = useState("0");
  const [citySelected, setCitySelected] = useState("0");

  useEffect(() => {
    getCountyData();
  }, []);

  //获取国家数据
  const getCountyData = () => {
    getCounty().then((res) => {
      setCounty(res.data);
    });
  };

  //省份数据
  const handleCountyChange = (option) => {
    setCountyAddress(option.label);
    if (option.level === 0) {
      setZoom(6);
    }
    setCountyAddress(option.label);
    getGoogleProvince(option.value).then((res) => {
      setProvince(res.data);
    });

    if ("" != provinceAddress && countyAddress != option.label) {
      setProvinceSelected("0");
      setCitySelected("0");
    }
    search(option.label, option.value, option.label);
  };

  //省份数据
  const handleProvinceChange = (option) => {
    setProvinceAddress(option.label);
    if (option.level === 1) {
      setZoom(12);
    }
    getGoogleCity(option.value).then((res) => {
      console.log("res", res.data);
      setCity(res.data);
    });
    setProvinceSelected("1");
    if ("" != cityAddress && provinceAddress != option.label) {
      setCitySelected("0");
    }
    search(provinceAddress + option.label, option.value, option.label);
  };

  //城市数据
  const handlerCityChange = (option) => {
    setCityAddress(option.label);
    setCitySelected("1");
    changeSelectAddress(countyAddress + provinceAddress + cityAddress);
    // console.log("cityAddress", cityAddress);
    if (option.level === 2) {
      setZoom(16);
    }
    search(
      countyAddress + provinceAddress + cityAddress,
      option.value,
      option.label
    );
  };

  return (
    <>
      <SearchSelect
        label="County"
        value={countyAddress ? countyAddress : ""}
        options={county}
        onChange={handleCountyChange}
        placeholder={t("ips.ips_store_tips_select_country")}
      />
      {county && (
        <SearchSelect
          label="Province"
          value={provinceAddress ? provinceAddress : ""}
          options={province}
          onChange={handleProvinceChange}
          placeholder={t("ips.ips_select_province")}
          currentSelected={provinceSelected}
        />
      )}
      {county && province && (
        <SearchSelect
          label="city"
          value={cityAddress ? cityAddress : ""}
          options={city}
          onChange={handlerCityChange}
          placeholder={t("ips.ips_store_tips_select_city")}
          currentSelected={citySelected}
        />
      )}
    </>
  );
};
export default CitySelect;
