/* eslint-disable no-const-assign */
/* eslint-disable no-undef */
import React, {
  useRef,
  useState,
  useEffect,
  useMemo,
  forwardRef,
  useCallback,
} from "react";
import LoadingButton from "@mui/lab/LoadingButton";
import {
  Stack,
  Alert,
  Grid,
  Checkbox,
  InputBase,
  Button,
  Popover,
  IconButton,
  RadioGroup,
  InputAdornment,
  OutlinedInput,
  FormControlLabel,
  FormGroup,
  TextField,
  Radio,
  Typography,
  InputLabel,
} from "@mui/material";
import { Delete as DeleteIcon } from "@mui/icons-material";
import DateSvg from "./DateSvg";

import { toast } from "react-toastify";
import TimePickert from "@/components/zktime";
import dayjs from "dayjs";
import isBetween from "dayjs/plugin/isBetween";
import ClearIcon from "@mui/icons-material/Clear";
import { tableI18n } from "@/utils/tableLang";
import AddPlayList from "./AddPlayList";
import { useConfirm } from "@/components/zkconfirm";
import { useForm, Controller, useFieldArray } from "react-hook-form";
import {
  BootstrapDialog,
  BootstrapDialogTitle,
  BootstrapContent,
  BootstrapActions,
} from "@/components/dialog";
import MaterialReactTable from "material-react-table";
import { useTranslation } from "react-i18next";
dayjs.extend(isBetween);

const AdvancedDialog = forwardRef((props, ref) => {
  const confirm = useConfirm();
  const { t } = useTranslation();
  const { data, onSubmit, open, onClose } = props;
  const [copyOpen, setCopyOpen] = useState(false);
  const [copyList, setCopyList] = useState([]);
  const [loading, setLoading] = useState(false);
  const handleClose = () => {
    onClose();
  };
  const handleCopySubmit = (value) => {
    const data = [];
    console.log(copyList);
    let copyStartTime = dayjs(
      "2022-06-01T" + copyList[copyList.length - 1]?.stopTime,
      "YYYY-MM-DDTHH:mm:ss"
    );
    copyList.forEach((item) => {
      data.push({
        playListId: item?.playListId,
        playListName: item?.playListName,
        startTime: copyStartTime.format("HH:mm:ss"),
        stopTime: copyStartTime.add(value, "minute").format("HH:mm:ss"),
        checkBox: true,
      });
      copyStartTime = copyStartTime.add(value, "minute");
    });
    let formData = getValues()?.playLists;
    const temps = [...formData, ...data];
    setValue("playLists", temps);
  };

  // function isTimeOverlap(time1, time2) {
  //     const maxStartTime = Math.max(time1.startTime, time2.startTime);
  //     const minStopTime = Math.min(time1.stopTime, time2.stopTime);

  //     return maxStartTime < minStopTime;
  // }

  // function hasOverlapTime(times) {
  //     times.sort((a, b) => a.startTime.localeCompare(b.startTime));

  //     for (let i = 0; i < times.length - 1; i++) {
  //         const time1 = times[i];

  //         for (let j = i + 1; j < times.length; j++) {
  //             const time2 = times[j];

  //             if (isTimeOverlap(time1, time2)) {
  //                 return true; // 存在重叠时间段
  //             }
  //         }
  //     }

  //     return false; // 不存在重叠时间段
  // }

  function isOverlap(startTime1, stopTime1, startTime2, stopTime2) {
    const start1 = Date.parse(`2000-01-01T${startTime1}Z`);
    let stop1 = Date.parse(`2000-01-01T${stopTime1}Z`);
    const start2 = Date.parse(`2000-01-01T${startTime2}Z`);
    let stop2 = Date.parse(`2000-01-01T${stopTime2}Z`);

    // 处理第一个时间段结束时间在第二天的情况
    if (stop1 < start1) stop1 += 24 * 60 * 60 * 1000;
    // 处理第二个时间段结束时间在第二天的情况
    if (stop2 < start2) stop2 += 24 * 60 * 60 * 1000;

    return (
      Math.max(start1, start2) < Math.min(stop1, stop2) ||
      Math.max(start1, start2 - 24 * 60 * 60 * 1000) <
        Math.min(stop1, stop2 - 24 * 60 * 60 * 1000)
    );
  }

  function hasOverlapTime(times) {
    for (let i = 0; i < times.length - 1; i++) {
      const time1 = times[i];

      for (let j = i + 1; j < times.length; j++) {
        const time2 = times[j];

        if (
          isOverlap(
            time1.startTime,
            time1.stopTime,
            time2.startTime,
            time2.stopTime
          )
        ) {
          return true; // 存在重叠时间段
        }
      }
    }

    return false; // 不存在重叠时间段
  }

  // 新表单组件
  const {
    control,
    handleSubmit,
    setValue,
    watch,
    register,
    reset,
    getValues,
    formState: { errors },
  } = useForm({
    mode: "all",
  });
  // 清单列表，用于动态创建渲染
  const { fields, append, remove } = useFieldArray({
    control,
    name: "playLists",
  });
  useEffect(() => {
    handeOpenChange();
  }, [data]);

  const PlayListFromField = (props) => {
    const addPlayListRef = useRef(null);
    //设置清单id和名称
    const setPlayListFormValues = (playListId, playListName, index) => {
      setValue(`playLists[${index}].playListId`, playListId);
      setValue(`playLists[${index}].playListName`, playListName);
    };

    const [anchorTime, setAnchorTime] = useState(null);
    const [timeIndex, setTimeIndex] = useState();
    const handleTimeClick = (event, index) => {
      setTimeIndex(index);
      setAnchorTime(event.currentTarget);
    };
    const handleTimeClose = () => {
      setAnchorTime(null);
    };
    const openTimeRange = Boolean(anchorTime);
    const timeRangeId = openTimeRange ? "time-popover" : undefined;
    return (
      <>
        <Popover
          elevation={3}
          id={timeRangeId}
          open={openTimeRange}
          anchorEl={anchorTime}
          onClose={handleTimeClose}
          anchorOrigin={{
            vertical: "bottom",
            horizontal: "left",
          }}>
          <TimePickert
            endTimeText={t("common.common_endTime")}
            startTimeText={t("common.common_startTime")}
            confirmText={t("common.common_edit_ok")}
            onChange={(time) => {
              //时间段是否存在重叠标识
              let flag = false;
              //已选播放清单
              let timePeriodList = [...getValues().playLists];
              //开始时间
              let startTime = new Date("2022-06-01T" + time.startTime);
              //结束时间
              let endTime = new Date("2022-06-01T" + time.endTime);

              if (startTime.getTime() >= endTime.getTime()) {
                toast.error(t("ips.ips_starttime_greater_endtime"));
                return;
              }

              timePeriodList.forEach((timePeriod, index) => {
                //排除自身与其它时间段比较
                if (timeIndex != index) {
                  //排除未选择的播放时间段
                  if (timePeriod.startTime != "" && timePeriod.stopTime != "") {
                    //该时间段开始时间
                    let thisStartTime = new Date(
                      "2022-06-01T" + timePeriod.startTime
                    );
                    //该时间段结束时间
                    let thisStopTime = new Date(
                      "2022-06-01T" + timePeriod.stopTime
                    );
                    //交集判断结果
                    let result =
                      (startTime.getTime() < thisStartTime.getTime() &&
                        thisStartTime.getTime() < endTime.getTime()) ||
                      (startTime.getTime() < thisStopTime.getTime() &&
                        thisStopTime.getTime() < endTime.getTime()) ||
                      (startTime.getTime() >= thisStartTime.getTime() &&
                        endTime.getTime() <= thisStopTime.getTime()) ||
                      (startTime.getTime() <= thisStartTime.getTime() &&
                        endTime.getTime() >= thisStopTime.getTime());

                    if (result) {
                      //时间段存在交集
                      flag = true;
                      return;
                    }
                  }
                }
              });

              if (flag) {
                toast.error(t("ips.ips_overlap_play_time"));
                return;
              } else {
                setValue(`playLists[${timeIndex}].startTime`, time.startTime);
                setValue(`playLists[${timeIndex}].stopTime`, time.endTime);
                handleTimeClose();
              }
            }}
          />
        </Popover>
        <AddPlayList
          ref={addPlayListRef}
          setPlayListFormValues={setPlayListFormValues}
          playListType={"0"}
          isLayout={"1"}
        />
        {fields.map((field, index) => {
          return (
            <Grid
              key={field.id}
              container
              sx={{ marginBottom: "10px" }}
              justifyContent="flex-start"
              alignItems="center">
              <Grid item xs={12}>
                <InputLabel htmlFor="listrangeTime">
                  <Stack direction="row" spacing={0.1} alignItems="center">
                    <Controller
                      name={`playLists[${index}].checkBox`}
                      control={control}
                      defaultValue={field.checkBox}
                      render={({ field }) => {
                        return (
                          <Checkbox
                            checked={field?.value || false}
                            onClick={(e) => handleCheckboxChange(index, e)}
                            {...field}
                          />
                        );
                      }}
                    />
                    <Typography>
                      {t("menu.play_list")}-{t("ips.ips_scheduling_playTime")}
                    </Typography>
                    <i style={{ color: "red" }}>*</i>
                  </Stack>
                </InputLabel>
              </Grid>
              <Grid item xs={5.5} sm={5.5} md={5.5}>
                <Controller
                  name={`playLists[${index}].playListName`}
                  control={control}
                  defaultValue={field.playListName}
                  render={({ field }) => (
                    <OutlinedInput
                      {...field}
                      id="schedule-playListName"
                      type="text"
                      name="playListName"
                      sx={{ width: "100%" }}
                      readOnly
                      // value={
                      //   scheduleForm.values.inventoryList[props.index]
                      //     .playListName
                      // }
                      // onBlur={scheduleForm.handleBlur}
                      // onChange={scheduleForm.handleChange}
                      placeholder={t("ips.ips_please_select_playlist")}
                      onClick={() => {
                        addPlayListRef.current.handleOpen();
                        addPlayListRef.current.setIndex(index);
                      }}></OutlinedInput>
                  )}
                />
              </Grid>
              <Grid item xs={1}>
                <Typography variant="body1" align="center">
                  {t("common.common_from")}
                </Typography>
              </Grid>
              <Grid item xs={5.5} sm={5.5} md={5.5}>
                <Controller
                  name={`playLists[${index}].stopTime`}
                  control={control}
                  defaultValue={field.stopTime}
                  render={({ field }) => (
                    <OutlinedInput
                      {...field}
                      onClick={(event) => {
                        handleTimeClick(event, index);
                      }}
                      id="infor-firstName"
                      type="text"
                      // readOnly
                      sx={{ width: "100%" }}
                      placeholder={t("common.common_endTime")}
                      startAdornment={
                        <InputAdornment position="start" sx={{ width: "120%" }}>
                          <Controller
                            name={`playLists[${index}].startTime`}
                            control={control}
                            defaultValue={field.startTime}
                            render={({ field }) => (
                              <InputBase
                                {...field}
                                // readOnly
                                endAdornment={<DateSvg />}
                                type="text"
                                placeholder={t("common.common_startTime")}
                                sx={{
                                  width: "100%",
                                }}
                              />
                            )}
                          />
                        </InputAdornment>
                      }
                    />
                  )}
                />
              </Grid>
            </Grid>
          );
        })}
      </>
    );
  };

  // 导入到表单中
  const handleImport = () => {
    console.log(JSON.stringify(getValues("playLists")));
    if (getValues("playLists").length === 0) {
      toast.error(t("common.common_copy_erro_null"));
      return;
    }
    if (hasOverlapTime(getValues("playLists"))) {
      toast.error(t("ips.ips_overlap_play_time"));
      return;
    }
    confirm({
      title: t("message.messageBox_title"),
      confirmationText: t("common.common_edit_ok"),
      cancellationText: t("common.common_edit_cancel"),
      description: t("common.common_copy_playlist_tips"),
    }).then(() => {
      setLoading(true);
      //导入到表单中
      onSubmit(getValues("playLists"))
        .then(() => {
          setLoading(false);
          onClose();
        })
        .catch((err) => {
          setLoading(false);
        });
    });
  };
  const handeOpenChange = () => {
    reset({ playLists: [] });
    if (data.length > 0) {
      console.log(data);
      data.forEach((item) => {
        append(item);
      });
    } else {
      append({ playListId: "", playListName: "", startTime: "", stopTime: "" });
    }
  };
  const handleCopyRow = () => {
    const playLists = getValues()?.playLists;
    //查询是否有勾选
    let checkBoxs = playLists.filter((item) => item?.checkBox === true);
    if (checkBoxs.length === 0) {
      toast.error(t("common.common_please_select_row_copy"));
      return;
    }
    // //校验时间
    let flag = false;
    checkBoxs.forEach((item) => {
      if (!item?.stopTime || !item?.startTime) {
        flag = true;
        return;
      }
      if (!item?.playListId || !item?.playListName) {
        flag = true;
      }
    });
    if (flag) {
      toast.error(t("common.common_copy_row_null_playlist"));
      return;
    }
    setCopyList(checkBoxs);
    setCopyOpen(true);
  };
  const handleDelRow = () => {
    const flag = getValues().playLists.some((item) => item?.checkBox);
    if (!flag) {
      toast.error(t("common.common_screen_copy_del_error"));
      return;
    }
    for (let i = getValues().playLists.length - 1; i >= 0; i--) {
      if (getValues().playLists[i].checkBox) {
        remove(i);
      }
    }
  };

  const watchedChecked = watch("playLists", []);
  const [selectAll, setSelectAll] = useState(
    watchedChecked.length > 0 && watchedChecked.every((item) => item?.checkBox)
  );
  // 添加新的一行
  const handleAddRow = () => {
    const newData = {
      playListId: "",
      playListName: "",
      startTime: "",
      stopTime: "",
      checkBox: true,
    };
    append(newData);
  };
  const handleCheckboxChange = (index, e) => {
    const { checked } = e.target;
    const fieldName = `playLists[${index}].checkBox`;
    setValue(fieldName, checked);
    const allChecked = watchedChecked.every((item) => item?.checkBox);
    console.log("allChecked", getValues());
    setSelectAll(allChecked);
  };
  useEffect(() => {
    if (watchedChecked) {
      const flag = watchedChecked.some((item) => item?.checkBox);
      if (!flag) {
        setSelectAll(false);
      } else {
        setSelectAll(true);
      }
    }
  }, [watchedChecked]);

  const handleSelectAll = (e) => {
    console.log(e.target.checked);
    const valueData = getValues().playLists;
    let data = valueData.map((item) => {
      item.checkBox = e.target.checked;
      return item;
    });
    setSelectAll(e.target.checked);
    console.log(data);
    reset({ playLists: data });
  };

  return (
    <>
      <BootstrapDialog
        BackdropClick={false}
        fullWidth
        maxWidth="md"
        onClose={handleClose}
        aria-labelledby="customized-dialog-title"
        open={open}>
        <BootstrapDialogTitle onClose={handleClose}>
          <Typography variant="h4" component="p">
            {t("common.common_advanced_menu_title")}
          </Typography>
        </BootstrapDialogTitle>
        <BootstrapContent dividers>
          <Grid container>
            <Grid item xs={12} sx={{ padding: "10px 30px 10px 30px" }}>
              <Stack direction="row" spacing={1} alignItems="center">
                <Button variant="contained" onClick={handleCopyRow}>
                  {t("common.common_copy_row")}
                </Button>
                <Button variant="contained" onClick={handleAddRow}>
                  {t("common.common_please_add_new_row")}
                </Button>
                <Button variant="contained" color="info" onClick={handleDelRow}>
                  {t("common.common_op_batch_del")}
                </Button>
              </Stack>
            </Grid>
            <Grid item xs={12} sx={{ paddingLeft: "30px" }}>
              <FormControlLabel
                control={<Checkbox value={selectAll} checked={selectAll} />}
                label={t("ips.ips_all")}
                onChange={handleSelectAll}
              />
            </Grid>
            <Grid item xs={12} sx={{ padding: "10px 30px 10px 30px" }}>
              <PlayListFromField />
            </Grid>
          </Grid>
        </BootstrapContent>
        <BootstrapActions>
          <Button color="info" variant="outlined" onClick={handleClose}>
            {t("common.common_edit_cancel")}
          </Button>
          <LoadingButton
            disableElevation
            onClick={handleImport}
            loading={loading}
            type="submit"
            variant="contained"
            color="primary">
            {t("common.common_edit_ok")}
          </LoadingButton>
        </BootstrapActions>
      </BootstrapDialog>
      <CopyPlayListTimeRange
        open={copyOpen}
        onClose={() => {
          setCopyList([]);
          setCopyOpen(false);
        }}
        onSubmit={handleCopySubmit}
      />
    </>
  );
});

export const CopyPlayListTimeRange = ({ open, onClose, onSubmit }) => {
  const [value, setValue] = React.useState("15");
  const [customerValue, setCustomerValue] = React.useState("");
  const { t } = useTranslation();
  const handleSubmit = () => {
    let duration = 0;
    if (value === "customer") {
      if (!customerValue) {
        toast.error(t("common.common_input_interval_time"));
        return;
      }
      duration = Number(customerValue);
    } else {
      duration = Number(value);
    }

    onSubmit(duration);
    onClose();
  };

  const handleChange = (event) => {
    setValue(event.target.value);
  };

  return (
    <BootstrapDialog open={open} width="xs" fullWidth>
      <BootstrapDialogTitle textAlign="center">
        <Typography variant="h5" component="p">
          {t("common.common_copy_input_info")}
        </Typography>
      </BootstrapDialogTitle>
      <BootstrapContent>
        <Stack spacing={2}>
          <Alert severity="warning">{t("common.common_copy_tips_info")}</Alert>
          <form onSubmit={(e) => e.preventDefault()}>
            <Stack spacing={1}>
              <InputLabel htmlFor="infor-dateRange">
                {t("common.common_interval_time")}
                <i style={{ color: "red" }}>*</i>
              </InputLabel>
              <RadioGroup
                row
                value={value}
                onChange={handleChange}
                aria-labelledby="demo-controlled-radio-buttons-group"
                name="controlled-radio-buttons-group">
                <FormControlLabel
                  value="15"
                  control={<Radio />}
                  label={t("common.common_interval_time_15_minute")}
                />
                <FormControlLabel
                  value="30"
                  control={<Radio />}
                  label={t("common.common_interval_time_30_minute")}
                />
                <FormControlLabel
                  value="60"
                  control={<Radio />}
                  label={t("common.common_interval_time_60_minute")}
                />
                <FormControlLabel
                  value="customer"
                  control={<Radio />}
                  label={t("common.common_interval_time_customer_minute")}
                />
              </RadioGroup>
            </Stack>
            {value === "customer" && (
              <Stack spacing={2}>
                <InputLabel htmlFor="infor-dateRange">
                  {t("common.common_duration_minute")}
                  <i style={{ color: "red" }}>*</i>
                </InputLabel>
                <OutlinedInput
                  value={customerValue}
                  onChange={(e) => setCustomerValue(e.target.value)}
                  sx={{ width: "300px" }}
                  type="number"
                />
              </Stack>
            )}
          </form>
        </Stack>
      </BootstrapContent>
      <BootstrapActions sx={{ p: "1.25rem" }}>
        <Button onClick={onClose} variant="outlined" color="info">
          {t("common.common_edit_cancel")}
        </Button>
        <Button color="primary" onClick={handleSubmit} variant="contained">
          {t("common.common_copy_ok")}
        </Button>
      </BootstrapActions>
    </BootstrapDialog>
  );
};

export default AdvancedDialog;
