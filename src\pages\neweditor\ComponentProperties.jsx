import React from 'react'
import {  Grid} from "@mui/material";
import typeList from "./ComponentConfig";
import WidgetComponent from "./PropertiesComponent/WidgetComponent";
import TemplateSetting from "./PropertiesComponent/TemplateSetting";

const ComponentProperties = (props) => {
  const { currentType, ...rest } = props;
  return (
    <Grid
      sx={{
        height: "100%",
        overflow: "auto",
        p: 1.5,
      }}
    >
      {typeList.map((item) => {
        if (currentType === item.type) {
          return (
            <item.Component
              currentType={currentType}
              {...rest}
              key={item.type}
            ></item.Component>
          );
        } else if (
          item.type === "widget" &&
          (currentType === "ZKTecoNews" ||
            currentType === "ZKTecoTime" ||
            currentType === "ZKTecoLive" ||
            currentType === "ZKTecoWeather")
        ) {
          return (
            <WidgetComponent
              currentType={currentType}
              {...rest}
              key={item.type}
            ></WidgetComponent>
          );
        } else if (item.type === "scene" && currentType === "templateSetting") {
          return (
            <TemplateSetting
              currentType={currentType}
              {...rest}
              key={item.type}
            ></TemplateSetting>
          );
        } else {
          return "";
        }
      })}
    </Grid>
  );
};

export default ComponentProperties;
