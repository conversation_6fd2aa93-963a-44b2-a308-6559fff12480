import request from "@/utils/request";
/**
 *  分页查询可用设备列表
 */
export const list = (params) => {
    return request({
        url: `/firmware/list`,
        method: "get",
        params: params,
    });
};



export const saveFirmware = (data)=>{
  return request({
    url: `/firmware/save`,
    method: "post",
    data
});
}


export const featchUpgrade= (ids,version)=>{
  return request({
    url: `/firmware/upgrade/${ids}`,
    method: "post",
    params:{
      version:version
    }
});
}
