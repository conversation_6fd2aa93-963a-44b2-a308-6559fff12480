import React from "react";
import { Grid, TablePagination, Button } from "@mui/material";
import { styled } from "@mui/material/styles";
import AddIcon from "@mui/icons-material/Add";
import { getComponentId } from "../common/utils";
import { useState } from "react";
import { fetchEventSource } from "@microsoft/fetch-event-source";
import { getToken } from "@/utils/auth";
import {
  AntTab,
  AntTabs,
  FormLabel,
  PrettoSlider,
} from "./PropertiesComponent";
import CustomInput from "../components/CustomInput";
import { isPositiveInteger } from "../common/utils";
import { useEffect, useRef } from "react";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import VideoPlay from "./VideoPlay";
import UploadMaterial from "@/pages/program/material/components/UploadMaterial";
import { getResource } from "@/service/api/layout";
import { toast } from "react-toastify";
import { message } from "../common/i18n";
const BootstrapDialog = styled(Dialog)(({ theme }) => ({
  "& .MuiDialogContent-root": {
    padding: theme.spacing(2),
  },
  "& .MuiDialogActions-root": {
    padding: theme.spacing(1),
  },
}));

let initproperties = {
  title: "editor_video",
  name: "editor_video",
  type: "ZKTecoVideo", //视频组件
  editType: "ZKTecoVideoEdit", //视频组件
  icon: "icon-video",
  left: 50,
  top: 50,
  width: 150,
  height: 150,
  zIndex: 50,
  anim: "",
  interact: {
    type: "",
    site: "",
    apk: "",
    idx: null,
  },
  borderRadius: 0,
  hide: false,
  videoList: [],
  transparency: 1, //透明度
  rotate: 0,
  duration: 30, //时长
  componentId: "",
};

const VideoComponent = (props) => {
  const currentPageIndex = props.currentPageIndex;
  const currentComponentId = props.currentComponentId;
  const setCurrentComponentId = props.setCurrentComponentId;
  const activeTempIndex = props.activeTempIndex;
  const pages = props.pages;
  const setPages = props.setPages;
  const setCurrentComponentIndex = props.setCurrentComponentIndex;
  const currentIndex = props.currentComponentIndex;
  const [properties, setProperties] = useState({
    ...initproperties,
  });

  const [showUpload, setShowUpload] = useState(false);
  const [videoOptions, setVideoOptions] = useState({
    current: 0,
    size: 10,
    total: 0,
    sizes: [6, 10, 24, 60, 100],
  });

  const addUploadMaterial = useRef(null);
  const [videoList, setVideoList] = useState([]);

  const [pageNumber, setPageNumber] = useState(0);
  const [pageSize, setPageSize] = useState(10);

  // 得到视频列表
  const getVideoList = (page) => {
    let currentPage = page === undefined ? pageNumber + 1 : page + 1;
    getResource({
      page: currentPage,
      pageSize: videoOptions.size,
      showAudited: true,
      type: "media",
      status: 2,
    }).then((res) => {
      let resData = res.data;
      let list = resData.data.map((it) => {
        it.url = it.downloadUrl;
        return it;
      });

      setVideoList(list);
      if (videoOptions.total !== resData.total) {
        setVideoOptions({
          ...videoOptions,
          current: currentPage - 1,
          total: resData.total,
        });
      }
    });
  };

  useEffect(() => {
    getVideoList();
  }, [pageSize, pageNumber]);

  const ctrlAbout = useRef(null);
  useEffect(() => {
    return () => {
      closeEES();
    };
  }, []);

  const initEES = () => {
    if (ctrlAbout && ctrlAbout.current) {
      return false;
    }
    ctrlAbout.current = new AbortController();
    // fetchEventSource(import.meta.env.VITE_APP_BASE_API + "/sse/connect", {
    //   method: "POST",
    //   headers: {
    //     Accept: "text/event-stream",
    //     Authorization: import.meta.env.VITE_TOKEN_HEADER + getToken(),
    //   },
    //   signal: ctrlAbout.current.signal,
    //   body: JSON.stringify({}),
    //   onmessage(msg) {
    //     let data = msg.data;
    //     data = JSON.parse(data);

    //     if (data.code === "M00000") {
    //       getVideoList(0);
    //     }
    //   },
    //   onerror() {
    //     // 服务异常
    //     console.log("服务异常");
    //   },
    //   onclose() {
    //     // 服务关闭
    //     console.log("服务关闭");
    //   },
    // });
  };
  const closeEES = () => {
    if (ctrlAbout && ctrlAbout.current) {
      ctrlAbout.current.abort();
      ctrlAbout.current = null;
    }
  };

  useEffect(() => {
    setPageNumber(videoOptions.current);
    setPageSize(videoOptions.size);
  }, [videoOptions]);

  useEffect(() => {
    if (currentIndex !== "") {
      const curretnPage = pages[currentPageIndex];
      if (curretnPage.isTemplate) {
        let componentInfo =
          curretnPage.tempLayout[activeTempIndex]?.componentList[currentIndex];
        setProperties({
          ...componentInfo,
        });
      } else {
        let componentInfo = pages[currentPageIndex].componentList[currentIndex];
        setProperties({
          ...componentInfo,
        });
      }
    }
  }, [currentPageIndex, currentIndex, activeTempIndex, pages]);

  const setComponentInfo = (baseInfo) => {
    const newPages = JSON.parse(JSON.stringify(props.pages));
    let currentPage = newPages[currentPageIndex];
    if (currentPage.isTemplate) {
      let oldInfo =
        currentPage.tempLayout[activeTempIndex].componentList[currentIndex];
      let newInfo = {
        ...oldInfo,
        ...baseInfo,
      };
      currentPage.tempLayout[activeTempIndex].componentList[currentIndex] =
        newInfo;

      try {
        let imageDuration = pages[currentPageIndex].tempLayout
          .map((cmpList) => {
            return cmpList.componentList
              .filter((item) => {
                if (item.type === "ZKTecoSlideShowImg") {
                  return true;
                } else {
                  return false;
                }
              })
              .map((item) => {
                return item.imgList
                  ?.map((imgItem) => {
                    try {
                      return parseInt(imgItem.duration);
                    } catch (e) {
                      return 0;
                    }
                  })
                  .reduce(
                    (accumulator, currentValue) => accumulator + currentValue
                  );
              });
          })
          .reduce(function (acc, curr) {
            return acc.concat(curr);
          }, []);

        let videoDuration = pages[currentPageIndex].tempLayout
          .map((cmpList) => {
            return cmpList.componentList
              .filter((item) => {
                if (item.type === "ZKTecoVideo") {
                  return true;
                } else {
                  return false;
                }
              })
              .map((item) => {
                return item.videoList
                  ?.map((imgItem) => {
                    try {
                      return parseInt(imgItem.duration);
                    } catch (e) {
                      return 0;
                    }
                  })
                  .reduce(
                    (accumulator, currentValue) => accumulator + currentValue
                  );
              });
          })
          .reduce(function (acc, curr) {
            return acc.concat(curr);
          }, []);
        let durationArry = [...imageDuration, ...videoDuration];
        if (durationArry.length > 0) {
          const maxNum = Math.max(...durationArry);
          currentPage.tempLayout[activeTempIndex].duration = maxNum;
        }
      } catch (e) {
        console.log(e);
      }
    } else {
      let oldInfo = newPages[currentPageIndex].componentList[currentIndex];
      let newInfo = {
        ...oldInfo,
        ...baseInfo,
      };
      newPages[currentPageIndex].componentList[currentIndex] = newInfo;
      try {
        let imageDuration = pages[currentPageIndex].componentList
          .filter((item) => {
            if (item.type === "ZKTecoSlideShowImg") {
              return true;
            } else {
              return false;
            }
          })
          .map((item) => {
            return item.imgList
              ?.map((imgItem) => {
                try {
                  return parseInt(imgItem.duration);
                } catch (e) {
                  return 0;
                }
              })
              .reduce(
                (accumulator, currentValue) => accumulator + currentValue
              );
          });

        let videoSize = pages[currentPageIndex].componentList.filter((item) => {
          if (item.type === "ZKTecoVideo") {
            return true;
          } else {
            return false;
          }
        });
        if (videoSize.length === 1) {
          const sum = videoSize[0].videoList
            ?.map((item) => {
              try {
                return parseInt(item.duration);
              } catch (e) {
                return 0;
              }
            })
            .reduce((accumulator, currentValue) => accumulator + currentValue);
          imageDuration.push(sum);
        }
        if (imageDuration.length > 0) {
          const maxNum = Math.max(...imageDuration);
          newPages[currentPageIndex].duration = maxNum;
        }
      } catch (e) {
        console.log(e);
      }
    }

    if (props.setPages) {
      props.setPages(newPages);
    }
  };

  const addVideoBox = (item) => {
    const newPages = JSON.parse(JSON.stringify(props.pages));
    let currentPage = newPages[currentPageIndex];
    if (currentPage.isTemplate) {
      if (activeTempIndex === "") {
        toast.success(message("editor_select_template_tip"));
      } else {
        let videoList = currentPage.tempLayout[
          activeTempIndex
        ].componentList.filter((item) => {
          if (item.type === "ZKTecoVideo") {
            return true;
          } else {
            return false;
          }
        });

        if (videoList.length < 1) {
          let ComponentId = getComponentId(pages);
          let initProps = {
            ...initproperties,
            componentId: ComponentId,
            videoList: [
              {
                duration: 30,
                url: item.downloadUrl,
                videoId: item.id,
                coverImage: item.coverImage,
                checksum: item.checksum,
              },
            ],
          };
          setProperties({ ...initProps });
          currentPage.tempLayout[activeTempIndex].componentList.push({
            ...initProps,
          });
          setPages([...newPages]);
          let index =
            currentPage.tempLayout[activeTempIndex].componentList.length - 1;
          setCurrentComponentIndex(index);
          setCurrentComponentId(ComponentId);
        } else {
          toast.error(message("editor_template_addvideo_tip"));
        }
      }
    } else {
      let videoSize = pages[currentPageIndex].componentList.filter((item) => {
        if (item.type === "ZKTecoVideo") {
          return true;
        } else {
          return false;
        }
      });
      if (videoSize.length < 1) {
        let ComponentId = getComponentId(pages);
        let initProps = {
          ...initproperties,
          componentId: ComponentId,
          videoList: [
            {
              duration: 30,
              url: item.downloadUrl,
              videoId: item.id,
              coverImage: item.coverImage,
              checksum: item.checksum,
            },
          ],
        };
        setProperties({ ...initProps });
        pages[currentPageIndex].componentList.push({ ...initProps });
        setPages([...pages]);
        let index = pages[currentPageIndex].componentList.length - 1;
        setCurrentComponentIndex(index);
        setCurrentComponentId(ComponentId);
      } else {
        toast.error(message("editor_video_error_message"));
      }
    }
  };
  const [value, setValue] = useState("attributes");

  const [open, setOpen] = useState(false);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  const changeProperties = (event) => {
    const name = event.target.name;
    let newInfo = {
      ...properties,
      [name]: event.target.value,
    };
    setComponentInfo(newInfo);
  };

  const changeDuration = (event, index) => {
    let value = event.target.value;

    if (value === "" || isPositiveInteger(value)) {
      if (value > 999999) {
        value = 999999;
      }
      if (value < 1) {
        value = 1;
      }

      properties.videoList[index].duration = value;
      setProperties({
        ...properties,
      });
      let newInfo = {
        ...properties,
      };
      setComponentInfo(newInfo);
    } else {
      properties.imgList[index].duration = 10;
      setProperties({
        ...properties,
      });
      let newInfo = {
        ...properties,
      };
      setComponentInfo(newInfo);
      toast.error(message("editor_number_error_message"));
    }
  };

  const onBlurFn = (event, index) => {
    let value = event.target.value;
    if (value === "") {
      properties.videoList[index].duration = 10;
      setProperties({
        ...properties,
      });
      let newInfo = {
        ...properties,
      };
      setComponentInfo(newInfo);
      toast.error(message("editor_number_error_message"));
    }
  };

  const handleClose = () => {
    setOpen(false);
  };

  const addVideo = (item) => {
    properties.videoList.push({
      duration: 5,
      url: item.downloadUrl,
      videoId: item.id,
      coverImage: item.coverImage,
      checksum: item.checksum,
    });
    let newInfo = {
      ...properties,
    };
    setOpen(false);
    setComponentInfo(newInfo);
  };

  const deleteImage = (index) => {
    properties.videoList.splice(index, 1);
    let newInfo = {
      ...properties,
    };
    setComponentInfo(newInfo);
  };

  const handleSliderChange = (event, newValue) => {
    let newInfo = {
      ...properties,
      transparency: newValue,
    };
    setComponentInfo(newInfo);
  };

  const handleRotationChange = (event, newValue) => {
    let newInfo = {
      ...properties,
      rotate: newValue,
    };
    setComponentInfo(newInfo);
  };

  const handleChangePage = (e, v) => {
    setVideoOptions({
      ...videoOptions,
      current: v,
    });
  };

  const handleChangeRowsPerPage = (e, v) => {
    let value = e.target.value;
    setVideoOptions({
      ...videoOptions,
      current: 0,
      size: value,
    });
  };

  const uploadCallback = () => {
    setVideoOptions({
      ...videoOptions,
      current: 0,
    });
  };

  return (
    <Grid
      sx={{
        display: "flex",
        justifyContent: "center",
        height: "100%",
      }}>
      {currentComponentId ? (
        <Grid
          sx={{
            width: "100%",
            boxShadow: "0px 0px 6px #00000029",
            borderRadius: "10px",
            backgroundColor: "#ffffff",
            overflow: "auto",
          }}>
          <AntTabs
            value={value}
            onChange={handleChange}
            aria-label="ant example">
            <AntTab value="attributes" label={message("editor_attribute")} />
            <AntTab value="style" label={message("editor_style")} />
          </AntTabs>
          <Grid>
            {value === "attributes" && (
              <Grid
                sx={{
                  p: 2,
                }}>
                {/* <Grid
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                    width: "100%",
                  }}
                >
                  <Grid
                    sx={{
                      mb: 1,
                    }}
                  >
                    {message("editor_swicthEffect")}
                  </Grid>
                  <CustomGroupSelect
                    sx={{
                      width: "100%",
                      mt: 2,
                    }}
                    label=""
                    name="anim"
                    onChange={changeProperties}
                    value={properties.anim}
                    items={animationList}
                  ></CustomGroupSelect>
                </Grid> */}

                <Grid>
                  {properties.videoList?.map((item, index) => {
                    return (
                      <Grid key={item.url + index}>
                        <Grid
                          sx={{
                            position: "relative",
                          }}>
                          <VideoPlay
                            posterUrl={item.coverImage}
                            url={item.url}></VideoPlay>
                          {properties.videoList.length > 1 && (
                            <Grid
                              onClick={() => {
                                deleteImage(index);
                              }}
                              sx={{
                                position: "absolute",
                                top: "50%",
                                right: "-10px",
                                zIndex: "1000",
                                backgroundColor: "#ffffff",
                                border: "1px solid gray",
                                padding: "3px",
                                height: "20px",
                                width: "20px",
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center",
                                borderRadius: "5px",
                              }}>
                              X
                            </Grid>
                          )}
                        </Grid>
                        <Grid
                          sx={{
                            mb: 1,
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                          }}>
                          <Grid>{message("editor_duration")}</Grid>
                          <Grid>
                            <CustomInput
                              sx={{
                                width: "60px",
                                marginTop: "0px",
                                input: {
                                  padding: "5px",
                                },
                              }}
                              value={item.duration}
                              type="number"
                              onChange={(e) => {
                                changeDuration(e, index);
                              }}
                              onBlur={(e) => {
                                onBlurFn(e, index);
                              }}
                              name="duration"></CustomInput>
                          </Grid>
                          <Grid
                            sx={{
                              ml: 1,
                            }}>
                            {message("editor_second")}
                          </Grid>
                        </Grid>
                      </Grid>
                    );
                  })}
                  <Grid
                    sx={{
                      display: "flex",
                      justifyContent: "center",
                    }}>
                    <Button
                      onClick={() => {
                        setOpen(true);
                      }}>
                      {message("editor_addVideo")}
                    </Button>
                  </Grid>
                </Grid>
              </Grid>
            )}

            {value === "style" && (
              <Grid sx={{ p: 2, pt: 0 }}>
                <CustomInput
                  label={message("editor_layerName") + ":"}
                  value={properties.name}
                  onChange={changeProperties}
                  name="name"></CustomInput>

                <Grid
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    mt: 1,
                  }}>
                  <FormLabel sx={{ mr: 2 }}>
                    {message("editor_diaphaneity")}:
                  </FormLabel>
                  <PrettoSlider
                    onChange={handleSliderChange}
                    size="small"
                    min={0}
                    max={1}
                    step={0.1}
                    color="secondary"
                    value={properties.transparency}
                    aria-label="Small"
                    // valueLabelDisplay="off"
                  ></PrettoSlider>
                </Grid>

                <Grid
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    mt: 1,
                  }}>
                  <FormLabel sx={{ mr: 2 }}>
                    {message("editor_rotate")}:
                  </FormLabel>
                  <PrettoSlider
                    onChange={handleRotationChange}
                    size="small"
                    min={0}
                    max={360}
                    step={1}
                    color="secondary"
                    value={properties.rotate}
                    aria-label="Small"
                    // valueLabelDisplay="off"
                  ></PrettoSlider>
                </Grid>

                <CustomInput
                  label={message("editor_abscissa") + ":"}
                  value={properties.left}
                  onChange={changeProperties}
                  name="left"></CustomInput>

                <CustomInput
                  label={message("editor_ordinate") + ":"}
                  value={properties.top}
                  onChange={changeProperties}
                  name="top"></CustomInput>

                <CustomInput
                  label={message("editor_width") + ":"}
                  value={properties.width}
                  onChange={changeProperties}
                  name="width"></CustomInput>

                <CustomInput
                  label={message("editor_height") + ":"}
                  value={properties.height}
                  onChange={changeProperties}
                  name="height"></CustomInput>
              </Grid>
            )}
          </Grid>
        </Grid>
      ) : (
        <Grid>
          <FormLabel> {message("editor_uploadVideo")}</FormLabel>
          <Grid
            onClick={() => {
              setShowUpload(true);
              initEES();
            }}
            sx={{
              width: "100%",
              height: "80px",
              background: "#ffffff",
              borderRadius: "10px",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              cursor: "pointer",
              mt: 2,
            }}>
            {message("editor_uploadVideo")}
            <AddIcon></AddIcon>
          </Grid>

          <Grid
            sx={{
              mt: 2,
              pb: 2,
            }}>
            <Grid
              sx={{
                pb: 2,
              }}
              container
              rowSpacing={1}
              columnSpacing={1}>
              {videoList.map((item) => (
                <Grid
                  onClick={() => {
                    addVideoBox(item);
                  }}
                  item
                  xs={6}
                  sx={{
                    height: "100px",
                  }}
                  key={item.url}>
                  <img
                    srcSet={`${item.coverImage}`}
                    src={`${item.coverImage}`}
                    style={{
                      height: "100%",
                      width: "100%",
                    }}
                    alt=""
                    loading="lazy"
                  />
                </Grid>
              ))}
            </Grid>
            <TablePagination
              component="div"
              count={videoOptions.total}
              page={videoOptions.current}
              onPageChange={handleChangePage}
              rowsPerPage={videoOptions.size}
              onRowsPerPageChange={handleChangeRowsPerPage}
              rowsPerPageOptions={videoOptions.sizes}
              labelRowsPerPage={<p>{message("editor_rowsPerPage")}:</p>}
            />
          </Grid>
        </Grid>
      )}

      <BootstrapDialog
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description">
        <DialogTitle
          sx={{ m: 0, p: 3 }}
          id="customized-dialog-title"></DialogTitle>
        <IconButton
          aria-label="close"
          onClick={handleClose}
          sx={{
            position: "absolute",
            right: 8,
            top: 8,
            color: (theme) => theme.palette.grey[500],
          }}>
          <CloseIcon />
        </IconButton>
        <DialogContent dividers>
          <Grid
            sx={{
              minWidth: "700px",
            }}>
            <Grid
              sx={{
                pb: 2,
              }}
              container
              rowSpacing={1}
              columnSpacing={1}>
              {videoList.map((item) => (
                <Grid
                  onClick={() => {
                    addVideo(item);
                  }}
                  item
                  xs={4}
                  sx={{
                    height: "100px",
                  }}
                  key={item.coverImage}>
                  <img
                    srcSet={`${item.coverImage}`}
                    src={`${item.coverImage}`}
                    style={{
                      height: "100%",
                      width: "100%",
                    }}
                    alt={item.coverImage}
                    loading="lazy"
                  />
                </Grid>
              ))}
            </Grid>
          </Grid>
        </DialogContent>

        <TablePagination
          component="div"
          count={videoOptions.total}
          page={videoOptions.current}
          onPageChange={handleChangePage}
          rowsPerPage={videoOptions.size}
          onRowsPerPageChange={handleChangeRowsPerPage}
          rowsPerPageOptions={videoOptions.sizes}
          labelRowsPerPage={<p>{message("editor_rowsPerPage")}:</p>}
        />
      </BootstrapDialog>

      <UploadMaterial
        ref={addUploadMaterial}
        open={showUpload}
        onCancel={() => {
          uploadCallback();
          setShowUpload(false);
        }}
      />
    </Grid>
  );
};

export default VideoComponent;
