/* eslint-disable react-refresh/only-export-components */
/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/prop-types */
import React, { useEffect, useState, useRef } from "react";
import {
  BootstrapActions,
  BootstrapContent,
  BootstrapDialog,
  BootstrapDialogTitle,
} from "@/components/dialog";
import PropTypes from "prop-types";
import {
  Button,
  Stack,
  Typography,
  Grid,
  Box,
  OutlinedInput,
} from "@mui/material";
import { toast } from "react-toastify";
import i18n from "i18next";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import "dayjs/locale/es";
import "dayjs/locale/en";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { TimePicker } from "@mui/x-date-pickers/TimePicker";
import { LoadingButton } from "@mui/lab";
const CornTabPanel = (props) => {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}>
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

//定时关机
const shutdown = (props) => {
  const { t } = useTranslation();
  const { open, onCancel, onSubmit, onNowShutodwn } = props;
  const [startTiming, setStartTiming] = useState("");
  const [shutTiming, setShutTiming] = useState("");
  const [loading, setLoading] = useState(false);
  const [nowLoading, setNowLoading] = useState(false);
  const handleClose = () => {
    //清除表达式
    setStartTiming("");
    setShutTiming("");
    onCancel();
  };
  const handleSave = async () => {
    setLoading(true);
    await onSubmit(startTiming + "," + shutTiming)
      .then(() => {
        setLoading(false);
        setStartTiming("");
        setShutTiming("");
      })
      .catch(() => {
        setLoading(false);
      });
  };

  const handleNowShutdown = async () => {
    setNowLoading(true);
    await onNowShutodwn()
      .then(() => {
        setNowLoading(false);
        setStartTiming("");
        setShutTiming("");
      })
      .catch(() => {
        setNowLoading(false);
      });
  };

  return (
    <BootstrapDialog open={open} fullWidth maxWidth={"xs"}>
      <BootstrapDialogTitle onClose={handleClose}>
        <Typography variant="h4" component="p">
          {t("menu.time_power_on_off")}
        </Typography>
      </BootstrapDialogTitle>
      <BootstrapContent>
        <Box>
          <Stack sx={{ marginBottom: 2 }}>
            {t("common.common_switcher_startTiming")}
            <CornTimePicker
              expression={startTiming}
              onResult={(val) => {
                setStartTiming(val);
              }}
            />
          </Stack>
          <Stack sx={{ marginBottom: 2 }}>
            {t("common.common_switcher_shutTiming")}
            <CornTimePicker
              expression={shutTiming}
              onResult={(val) => {
                setShutTiming(val);
              }}
            />
          </Stack>
        </Box>
      </BootstrapContent>
      <BootstrapActions>
        <Grid
          container
          direction="row"
          justifyContent={"space-between"}
          alignItems="center">
          <Grid item>
            <LoadingButton
              variant="contained"
              color="warning"
              loading={nowLoading}
              onClick={handleNowShutdown}>
              {t("screen.screen_shutdown_now")}
            </LoadingButton>
          </Grid>
          <Grid item>
            <Stack spacing={1} direction="row" justifyContent={"end"}>
              <Button color="info" variant="outlined" onClick={handleClose}>
                {t("common.common_edit_cancel")}
              </Button>
              <LoadingButton
                variant="contained"
                color="primary"
                loading={loading}
                disabled={
                  !startTiming || !shutTiming || startTiming === shutTiming
                }
                onClick={() => handleSave()}>
                {t("common.common_edit_ok")}
              </LoadingButton>
            </Stack>
          </Grid>
        </Grid>
      </BootstrapActions>
    </BootstrapDialog>
  );
};

const CornTimePicker = ({ expression, defaultTimeType, onResult }) => {
  const [value, setValue] = useState(undefined);
  const handleChangeTime = (value) => {
    // const timeStr = dayjs(value).format("H:m");
    const timeStr = dayjs(value).format("HH:mm");
    const hours = timeStr.split(":")[0];
    const minutes = timeStr.split(":")[1];
    let result = null;
    if (!Number.isNaN(Number(hours)) && !Number.isNaN(Number(minutes))) {
      // result = `${minutes} ${hours} * * *`;
      result = timeStr;
    }
    if (result !== null) {
      onResult(result);
    }
  };
  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <TimePicker
        timeSteps={{
          minutes: 1,
        }}
        value={value}
        onChange={handleChangeTime}
        ampm={false}
        renderInput={(props) => <OutlinedInput {...props} />}
      />
    </LocalizationProvider>
  );
};

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};

CornTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired,
};
export default shutdown;
