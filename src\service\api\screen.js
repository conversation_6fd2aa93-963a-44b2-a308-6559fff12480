/**
 * 屏幕管理接口定义
 */
import request from "@/utils/request";
const baseProfixURI = "/sd/v1/screen";

/**
 * 分页查询屏幕
 * <AUTHOR>
 */
export const listByPage = (params) => {
  return request({
    url: `/global/v1/device/query/page`,
    method: "get",
    params: params,
  });
};

/**
 *  注册屏幕
 * <AUTHOR>
 * @date 2023-01-10 18:09
 */
export const registerScreen = (params) => {
  return request({
    url: `/global/v1/device/pre_register/${sn}`,
    method: "PUT",
    data: params,
  });
};

/**
 *  删除屏幕
 * @param ids string 类型id用逗号隔开
 * <AUTHOR>
 * @date 2022-12-23 11:20
 */
export const removeScreen = (ids) => {
  return request({
    url: `/global/v1/device/${ids}`,
    method: "delete",
  });
};

/**
 *  更新屏幕时区
 * @param id
 * <AUTHOR>
 * @date 2024-05-23 11:20
 */
export const screenTimeZone = (id) => {
  return request({
    url: `${baseProfixURI}/screenTimeZone/${id}`,
    method: "get",
  });
};

/**
 *  重启屏幕
 * @param id
 * <AUTHOR>
 * @date 2023-04-18 11:20
 */
export const rebootScreen = (ids) => {
  return request({
    url: `${baseProfixURI}/reboot/${ids}`,
    method: "post",
  });
};

/**
 * 获取屏幕详情
 * <AUTHOR>
 * @returns
 */
export const getScreenInfo = (id) => {
  return request({
    url: `/global/v1/device/query/${id}`,
    method: "get",
  });
};

/**
 * 获取屏幕详情
 * <AUTHOR>
 * @returns
 */
export const updateScreen = (data) => {
  return request({
    url: `${baseProfixURI}/updateScreen`,
    method: "post",
    data: data,
  });
};

/**
 * 通过门店id查询设备列表
 * <AUTHOR>
 * @returns
 */
export const listScreenByStoreId = (params) => {
  return request({
    url: `${baseProfixURI}/listScreenByStoreId?storeIdList=${params}`,
    method: "get",
  });
};

/**
 * 设备截图
 * <AUTHOR>
 * @returns
 */
export const doScreenShot = (id) => {
  return request({
    url: `${baseProfixURI}/doScreenShot/${id}`,
    method: "post",
  });
};

/**
 *  ota升级
 * <AUTHOR>
 */
export const uploadUpgrade = (params) => {
  return request({
    url: `${baseProfixURI}/uploadUpgrade`,
    method: "post",
    params: params,
    headers: {
      isRetry: false,
      "Content-Type": "multipart/form-data",
    },
  })
};

//根据门店id获取设备列表
export const getScreenListByStoreId = (storeId) => {
  return request({
    url: `${baseProfixURI}/option/scope?storeId=${storeId}`,
    method: "get",
  });
};

/**
 *  根据deviceId获取设备信息
 * @param deviceId
 * <AUTHOR>
 * @date 2024-05-27 11:20
 */
export const getScreenByDeviceId = (deviceId) => {
  return request({
    url: `${baseProfixURI}/getScreenByDeviceId/${deviceId}`,
    method: "get",
  });
};

//重启设备
export const scheduleDeviceShutdown = (id, params) => {
  return request({
    url: `${baseProfixURI}/reboot/${id}`,
    method: 'POST',
    params
  });
}

//设备开关机
export const scheduleDeviceSwitch = (id, params) => {
  return request({
    url: `${baseProfixURI}/switch_device_time/${id}`,
    method: 'POST',
    params
  });
}

export const shutdownNow = (id) => {
  return request({
    url: `${baseProfixURI}/shutdown/${id}/now`,
    method: 'POST',
  });
}

//获取远程诊断信息
export const getRemoteDiagnosis = (id) => {
  return request({
    url: `${baseProfixURI}/get/diagnosis/${id}`,
    method: 'GET',
  });
}
//下发远程诊断
export const remoteDiagnosis = (id) => {
  return request({
    url: `${baseProfixURI}/diagnosis/${id}`,
    method: 'POST',
  });
}

export const removeSSE = (id) => {
  return request({
    url: `/sse/remove/diagnosis/${id}`,
    method: 'DELETE',
  });
}

export const clearShutdown = (ids) => {
  return request({
    url: `${baseProfixURI}/clean/shutdown/${ids}`,
    method: 'DELETE',
  });
}

export const clearSwitchTime = (ids) => {
  return request({
    url: `${baseProfixURI}/clean_switch_time/${ids}`,
    method: 'DELETE',
  });
}

export const cleanSchedule = (ids) => {
  return request({
    url: `${baseProfixURI}/clear_data/${ids}`,
    method: 'DELETE',
  });
}

export const screenAlert = (params) => {
  return request({
    url: `${baseProfixURI}/event/alert`,
    method: 'GET',
    params
  });
}
