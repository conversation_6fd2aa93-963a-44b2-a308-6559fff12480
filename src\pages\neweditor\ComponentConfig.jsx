import React from 'react'
import TextComponent from './PropertiesComponent/TextComponent';
import PhotosComponent from './PropertiesComponent/PhotosComponent';
import VideoComponent from './PropertiesComponent/VideoComponent';
import AudioComponent from './PropertiesComponent/AudioComponent';
import WidgetComponent from './PropertiesComponent/WidgetComponent';
import SceneComponent from './PropertiesComponent/SceneComponent';
import TemplateComponent from './PropertiesComponent/TemplateComponent'
import TemplateLayoutList from './PropertiesComponent/TemplateLayoutList'
import text from './icon/text.png';
import photos from './icon/photos.png';
import video from './icon/Video.png';
import audio from './icon/Audio.png';
import widget from './icon/widget.png';
import template from './icon/template.png'
import { message } from './common/i18n';

export default [
  {
    name:  message('tempalte'),
    type: 'templateLayout',
    width: '30px',
    height: '30px',
    icon: template,
    Component: TemplateLayoutList
},
    {
        name: message('editor_template'),
        type: 'ZKTecoTemplate',
        width: '30px',
        height: '30px',
        icon: template,
        Component: TemplateComponent
    },
    {
        name: message('editor_text'),
        type: 'ZKTecoText',
        width: '30px',
        height: '30px',
        isMobile:true,
        icon: text,
        Component: TextComponent
    },
    {
        name: message('editor_img'),
        icon: photos,
        width: '40px',
        height: '40px',
        isMobile:true,
        type: 'ZKTecoSlideShowImg',
        Component: PhotosComponent
    },
    {
        name: message('editor_video'),
        width: '40px',
        height: '40px',
        type: 'ZKTecoVideo',
        icon: video,
        Component: VideoComponent
    },
    {
        name: message('editor_audio'),
        icon: audio,
        width: '40px',
        height: '40px',
        type: 'ZKTecoMusic',
        Component: AudioComponent
    },
    {
        name: message('editor_widget'),
        type: 'widget',
        width: '40px',
        height: '40px',
        icon: widget,
        Component: WidgetComponent
    },
    {
        name: message('editor_scene'),
        type: 'scene',
        Component: SceneComponent
    }
];
