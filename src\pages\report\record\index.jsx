// /* eslint-disable react/jsx-key */
// /* eslint-disable react-hooks/rules-of-hooks */
// import {
//   Dropzone,
//   FileMosaic,
//   FullScreen,
//   ImagePreview,
// } from "@files-ui/react";
// import React, { useState } from "react";
// import {
//   Button,
//   Stack,
//   Typography,
//   Tooltip,
//   Link,
//   IconButton,
//   Grid,
//   InputLabel,
//   Box,
// } from "@mui/material";
// import ZKSearchSelect from "@/components/ZKSearchSelect";
// const record = () => {
//   const [files, setFiles] = useState([
//     {
//       id: "fileId-2",
//       size: 28 * 1024 * 1024,
//       type: "image/gif",
//       uploadMessage: "Upload was aborted by the user",
//       imageUrl: "https://cdn.wallpapersafari.com/0/95/1zms6H.jpg",
//     },
//     {
//       id: "fileId-3",
//       size: 28 * 1024 * 1024,
//       type: "image/jpeg",
//       uploadMessage:
//         "File couldn't be uploaded to Files-ui earthquakes. File was too big.",
//       imageUrl: "https://cdn.wallpapersafari.com/0/95/1zms6H.jpg",
//     },
//     {
//       id: "fileId-4",
//       size: 28 * 1024 * 1024,
//       type: "image/png",
//       uploadMessage: "File was uploaded correctly to Files-ui earthquakes",
//       imageUrl: "https://cdn.wallpapersafari.com/0/95/1zms6H.jpg",
//     },
//   ]);
//   const updateFiles = (incommingFiles) => {
//     //do something with the files
//     setFiles(incommingFiles);
//     //even your own upload implementation
//   };
//   const removeFile = (id) => {
//     setFiles(files.filter((x) => x.id !== id));
//   };
//   const sxGridContainer = React.useMemo(
//     () => ({
//       display: "flex",
//       flexWrap: "wrap",
//       width: "100%",
//       gap: 5,
//     }),
//     []
//   );
//   return (
//     <>
//       <Grid container spacing={2}>
//         <Grid item xs={12}>
//           <Stack
//             justifyContent="flex-start"
//             alignItems="flex-start"
//             spacing={1}
//           >
//             {/* <InputLabel htmlFor="username-login">文件上传</InputLabel> */}
//             <Dropzone
//               footer={false}
//               header={false}
//               onChange={updateFiles}
//               value={files}
//               maxFileSize={1 * 1024 * 1024 * 1024}
//               // maxFiles={5}
//               accept={"image/png, image/jpeg"}
//             >
//               <Stack
//                 direction="column"
//                 justifyContent="center"
//                 alignItems="center"
//                 spacing={1}
//               >
//                 <svg
//                   t="1688974869714"
//                   class="icon"
//                   viewBox="0 0 1024 1024"
//                   version="1.1"
//                   xmlns="http://www.w3.org/2000/svg"
//                   p-id="2826"
//                   width="80"
//                   height="80"
//                 >
//                   <path
//                     d="M1024 640.192C1024 782.912 919.872 896 787.648 896h-512C123.904 896 0 761.6 0 597.504 0 451.968 94.656 331.52 226.432 302.976 284.16 195.456 391.808 128 512 128c152.32 0 282.112 108.416 323.392 261.12C941.888 413.44 1024 519.04 1024 640.192z m-341.312-139.84L512 314.24 341.312 500.48h341.376z m-213.376 0v256h85.376v-256H469.312z"
//                     fill="#bfbfbf"
//                     p-id="2827"
//                   ></path>
//                 </svg>
//                 <Typography>
//                   Drag and drop or<span> browse</span>
//                 </Typography>
//               </Stack>
//               {/* {files.map((file) => (
//                     <FileMosaic key={file.id} {...file} onDelete={removeFile} info />
//                 ))} */}
//             </Dropzone>
//           </Stack>
//         </Grid>
//         <Grid item xs={12}>
//           <Stack
//             justifyContent="flex-start"
//             alignItems="flex-start"
//             spacing={1}
//           >
//             <Typography>预览</Typography>
//             <Button
//               onClick={() => {
//                 console.log(files);
//               }}
//             >
//               getFile
//             </Button>
//             <Box sx={sxGridContainer}>
//               {files.map((file, index) => (
//                 <Box>
//                   <FileMosaic
//                     {...file}
//                     key={file.id}
//                     onDelete={removeFile}
//                     preview
//                   />
//                   {/* <FileMosaic onDelete={removeFile} info /> */}
//                 </Box>
//               ))}
//             </Box>
//           </Stack>
//         </Grid>
//         <ZKSearchSelect />
//       </Grid>
//     </>
//   );
// };
// export default record;
const Record = () => {
  return <>12312</>;
};
export default Record;
