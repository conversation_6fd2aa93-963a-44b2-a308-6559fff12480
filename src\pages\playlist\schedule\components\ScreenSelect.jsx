/* eslint-disable react/prop-types */
import React, { useEffect, useMemo, useState, useRef } from "react";
import MaterialReactTable, {
  MRT_ShowHideColumnsButton,
} from "material-react-table";
import {
  Stack,
  Typography,
  Tooltip,
  Button,
  Grid,
  TextField,
} from "@mui/material";
import { tableI18n } from "@/utils/tableLang";

// api
import { listByPage as screenPage } from "@/service/api/screen";
import { getOutletType } from "@/service/api/L3Sevice.js";
// i18n
import { useTranslation } from "react-i18next";
import { removeEmpty } from "@/utils/StringUtils";
import ZKSelect from "@/components/ZKSelect";
import { screenStatus, screendirections } from "@/dict/commonDict";
import DictTag from "@/components/DictTag";

const ScreenSelect = (props) => {
  const { t } = useTranslation();
  const { pageSize = 10 } = props;
  const [isError, setIsError] = useState(false);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  // 表格数据
  const [data, setData] = useState([]);
  // 分页的参数
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: pageSize,
  });

  const [allRowSelection, setAllRowSelection] = useState([]);

  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);

  const tableInstanceRef = useRef(null);
  const requestParams = useRef({
    name: "",
    storeName: "",
    status: "",
    layoutType: "",
  });
  const setTableObject = (tableSelectRow) => {
    props.setTableObject(tableSelectRow);
  };
  const [rowSelection, setRowSelection] = useState([]);
  const [locationTypes, setLocationTypes] = useState([]);
  const getLocationType = () => {
    getOutletType().then((res) => {
      setLocationTypes(res.data);
    });
  };
  // 构建参数
  const buildParams = () => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      category: "2",

      ...requestParams.current,
    };

    return params;
  };
  // 获取数据
  const getTableData = async () => {
    if (!data.length) {
      setIsLoading(true);
    } else {
      setIsRefetching(true);
    }
    // 开启加载
    setIsLoading(true);
    setIsRefetching(true);
    await screenPage(buildParams())
      .then((res) => {
        // 设置数据
        setData(res.data.data);
        // 设置总记录数
        setRowCount(res.data.total);
        setIsLoading(false);
        setIsRefetching(false);
      })
      .catch((err) => {
        setIsError(true);
        setIsLoading(false);
        setIsRefetching(false);
      });
  };
  useEffect(() => {
    getLocationType();
    // 换取表单数据
    getTableData();
  }, [pagination.pageIndex, pagination.pageSize]);

  useEffect(() => {
    let ids = data.map((item) => {
      return item.id;
    });
    //其他页面选中的记录
    let ortherPageSelectRow = allRowSelection.filter((item) => {
      if (ids.indexOf(item.id) > -1) {
        return false;
      } else {
        return true;
      }
    });

    let currentPageSelectRows =
      tableInstanceRef.current.getSelectedRowModel().rows;

    let all = [...ortherPageSelectRow, ...currentPageSelectRows];
    setAllRowSelection(all);
    //设置表格中所有选中的行
    setTableObject(all);
  }, [rowSelection]);

  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "storeName",
        header: t("ips.ips_store_outlet_name"),
        enableColumnActions: false,
        enableSorting: false,
      },
      {
        accessorKey: "name",
        header: t("ips.ips_device"),
        enableColumnActions: false,
        enableSorting: false,
      },
      // {
      //   accessorKey: "ipAddress",
      //   header: t("ips.ips_device_ip"),
      //   enableColumnActions: false,
      //   enableSorting: false,
      // },
      {
        accessorKey: "locationTypeName",
        header: t("ips.ips_location_type"),
        enableColumnActions: false,
        enableSorting: false,
      },
      {
        accessorKey: "direction",
        header: t("common.common_direction"),
        enableColumnActions: false,
        enableSorting: false,
        Cell: ({ cell, row }) => {
          return (
            <DictTag
              dicts={screendirections}
              fieldName={{ value: "value", title: "label", color: "color" }}
              value={row.original.direction}
            />
          );
        },
      },
      {
        accessorKey: "address",
        header: t("common.common_location"),
        enableColumnActions: false,
        enableSorting: false,
        Cell: ({ cell, row }) => {
          return (
            <Tooltip title={row.original.address} placement="top">
              <Typography className="textSpace">
                {row.original.address}
              </Typography>
            </Tooltip>
          );
        },
      },
      {
        accessorKey: "status",
        header: t("common.common_status"),
        enableColumnActions: false,
        enableSorting: false,
        Cell: ({ cell, row }) => {
          return (
            <DictTag
              dicts={screenStatus}
              fieldName={{ value: "value", title: "label", color: "color" }}
              value={row.original.status}
            />
          );
        },
      },
    ],
    []
  );
  // 查询
  const handleQuery = async () => {
    const tempValue = { ...requestParams.current };
    await removeEmpty(tempValue);
    requestParams.current = tempValue;
    setPagination({
      pageSize: pageSize,
      pageIndex: 0,
    });
    getTableData();
  };
  const resetQuery = async () => {
    requestParams.current = {
      name: "",
      storeName: "",
      status: "",
      layoutType: "",
    };
    setDeviceStatus("");
    setName("");
    setStoreName("");
    setLayoutTypeVal("");
    getTableData();
  };
  const [layoutTypeVal, setLayoutTypeVal] = React.useState("");
  const [deviceStatus, setDeviceStatus] = React.useState("");
  const [name, setName] = React.useState("");
  const [storeName, setStoreName] = React.useState("");
  return (
    <>
      <div style={{ width: "100%", marginBottom: "10px" }}>
        <Grid
          container
          direction="row"
          justifyContent="flex-start"
          alignItems="center"
          spacing={2}>
          <Grid item xs={12} md={4} lg={2}>
            <TextField
              label={t("ips.ips_digital_signage_name")}
              onChange={(e) => {
                requestParams.current.name = e.target.value;
                setName(e.target.value);
              }}
              size="small"
              name="name"
              value={name}
              type="text"
              fullWidth
              placeholder={t("common.common_input_device_name")}
            />
          </Grid>
          <Grid item xs={12} md={4} lg={2}>
            <TextField
              label={t("ips.ips_store_name")}
              onChange={(e) => {
                requestParams.current.storeName = e.target.value;
                setStoreName(e.target.value);
              }}
              size="small"
              value={storeName}
              name="storeName"
              type="text"
              fullWidth
              placeholder={t("common.common_please_input_store_name")}
            />
          </Grid>
          <Grid item xs={12} md={4} lg={2}>
            <ZKSelect
              id="layoutType"
              name="layoutType"
              size="small"
              value={layoutTypeVal}
              options={locationTypes}
              onClear={() => {
                setLayoutTypeVal(undefined);
                requestParams.current.layoutType = "";
              }}
              onChange={(event) => {
                let {
                  target: { value },
                } = event;
                requestParams.current.layoutType = value;
                setLayoutTypeVal(value);
              }}
              type="text"
              placeholder={t("ips.ips_select_location_type")}
            />
          </Grid>
          <Grid item xs={12} md={4} lg={2}>
            <ZKSelect
              id="type"
              name="status"
              size="small"
              value={deviceStatus}
              options={screenStatus}
              onClear={() => {
                setDeviceStatus(undefined);
                requestParams.current.status = "";
              }}
              onChange={(event) => {
                let {
                  target: { value },
                } = event;
                requestParams.current.status = value;
                setDeviceStatus(value);
              }}
              type="text"
              placeholder={t("ips.ips_select_signage_status")}
            />
          </Grid>
          <Grid item xs={12} md={4} lg={2}>
            <Stack
              direction="row"
              justifyContent="flex-start"
              alignItems="flex-start"
              spacing={2}>
              <Button
                disableElevation
                onClick={() => {
                  handleQuery();
                }}
                type="submit"
                variant="contained"
                size="small">
                {t("common.common_table_query")}
              </Button>
              <Button
                disableElevation
                variant="outlined"
                onClick={resetQuery}
                color="info"
                size="small"
                sx={{
                  minWidth: "90px",
                }}>
                {t("common.common_op_reset")}
              </Button>
            </Stack>
          </Grid>
        </Grid>
      </div>
      <MaterialReactTable
        // table 状态
        state={{
          // 加载状态
          isLoading,
          // 分页参数
          pagination,
          // 重新拉取
          showProgressBars: isRefetching,
          showAlertBanner: isError,

          rowSelection,
        }}
        renderToolbarInternalActions={({ table }) => (
          <>
            <MRT_ShowHideColumnsButton table={table} />
            {/* <MRT_ToggleDensePaddingButton table={table} /> */}
            {/* <MRT_FullScreenToggleButton table={table} /> */}
          </>
        )}
        tableInstanceRef={tableInstanceRef}
        // 解决列太多宽度太长问题
        enableColumnResizing
        // enablePinning
        // 初始化状态
        initialState={{ columnVisibility: { createTime: false } }}
        // muiSelectAllCheckboxProps={(row) => {
        //     console.log(row);
        // }}
        muiToolbarAlertBannerProps={
          isError
            ? {
                color: "error",
                children: t("table.loading_error"),
              }
            : undefined
        }
        enableTopToolbar={false}
        muiTablePaperProps={{
          elevation: 0,
          sx: {
            borderRadius: "5px",
            // border: '1px solid #f0f0f0'
          },
        }}
        muiTableHeadRowProps={{
          sx: { backgroundColor: "#fafafa", boxShadow: "none" },
        }}
        //行选中
        muiTableBodyRowProps={({ row }) => ({
          onClick: row.getToggleSelectedHandler(),
          sx: { cursor: "pointer" },
        })}
        // 开启多选
        enableRowSelection={(row) => row.original.status != "0"}
        onRowSelectionChange={setRowSelection}
        getRowId={(row) => row.id}
        // 列数
        rowCount={rowCount}
        // 固定头部
        enableStickyHeader
        // 处理表格高度
        muiTableContainerProps={{ sx: { maxHeight: "620px" } }}
        // 设置背景颜色
        muiTableBodyCellProps={{ sx: { backgroundColor: "white" } }}
        muiTableProps={{ sx: { backgroundColor: "white" } }}
        muiBottomToolbarProps={{ sx: { backgroundColor: "white" } }}
        muiTopToolbarProps={{ sx: { backgroundColor: "white" } }}
        // 分页回调函数
        onPaginationChange={setPagination}
        // 记得开启这些 tips:否则会出现useEffect 反作用函数发起多次请求点击下一页会出问题
        manualFiltering
        manualPagination
        manualSorting
        // 开启分页
        enablePagination
        // 列定义
        columns={columns}
        // 数据
        data={data}
        // 初始化国际化语言  --- todo: i18n key doc address: https://github.com/KevinVandy/material-react-table/blob/main/packages/material-react-table/src/_locales/en.ts
        localization={tableI18n}
        muiLinearProgressProps={({ isTopToolbar }) => ({
          sx: { display: isTopToolbar ? "block" : "none" },
        })}
        // 多选底部提示
        positionToolbarAlertBanner="none"
      />
    </>
  );
};

export default ScreenSelect;
