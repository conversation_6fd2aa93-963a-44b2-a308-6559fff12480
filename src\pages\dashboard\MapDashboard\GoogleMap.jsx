/* eslint-disable no-undef */
import { useEffect, useState } from "react"
import {
    Grid,
    Card,
    Typography,
} from "@mui/material";
import { Loader } from "@googlemaps/js-api-loader"
import pin_orange from "@/assets/images/icons/pin_orange.svg";
import pin_green from "@/assets/images/icons/pin_green.svg";
import pin_blue from "@/assets/images/icons/pin_blue.svg";
import { useTranslation } from "react-i18next";
let MarkerObj = null
const GoogleMap = (props) => {
    let center = props.center
    let position = { lat: 24.608866, lng: 118.036190 };
    const [map, setMap] = useState(null);
    const [infoWindow, setInfoWindow] = useState(null);

    const [dataList, setDataList] = useState([]);
    const [showType, setShowType] = useState(['1', '2', '3']);
    const [showData, setShowData] = useState([]);
    const [markers, setMarkers] = useState([]);
    const { t } = useTranslation();




    const initMap = () => {
        let language = 'en'
        let zkBioCloudMediaLang = sessionStorage.getItem('zkBioCloudMediaLang')
        if (zkBioCloudMediaLang && zkBioCloudMediaLang == 'zh') {
            language = 'zh'
        }
        const loader = new Loader({
            apiKey: "AIzaSyA9MaTVJlWIWpINjcgyJl5eS6JDhe60238",
            version: "weekly",
            libraries: ["maps,marker"],
            language: language,
            id: 'map'
        });
        loader.load().then(async () => {
            // eslint-disable-next-line no-undef
            const { Map } = await google.maps.importLibrary("maps");
            const { Marker } = await google.maps.importLibrary("marker");
            MarkerObj = Marker
            // eslint-disable-next-line no-undef
            let map = new Map(document.getElementById("map"), {
                center: position,
                mapTypeControl: false,
                mapTypeId: 'roadmap',
                maxZoom: 24,
                minZoom: 5,
                fullscreenControl: true,
                streetViewControl: false,
                zoom: 10,
                mapId: "DEMO_MAP_ID",
            });

            const infoWindow = new google.maps.InfoWindow({
                content: "",
                disableAutoPan: true,
            });

            setInfoWindow(infoWindow)
            // const legend = document.getElementById("legend");
            // map.controls[google.maps.ControlPosition.RIGHT_BOTTOM].push(legend);


            const controlBox = document.createElement('div');
            controlBox.style.boxShadow = "0px 2px 1px -1px rgba(0,0,0,0.2), 0px 1px 1px 0px rgba(0,0,0,0.14), 0px 1px 3px 0px rgba(0,0,0,0.12)"
            controlBox.style.padding = "10px 15px"
            controlBox.style.borderRadius = '5px'
            controlBox.style.background = '#ffffff'
            controlBox.style.marginBottom = '20px'
            const legendBox = document.createElement('div');
            legendBox.style.display = "flex"

            // Create the control.
            const control1 = createCenterControl('1');
            // Append the control to the DIV.
            legendBox.appendChild(control1);

            const control2 = createCenterControl('2');
            legendBox.appendChild(control2);

            const control3 = createCenterControl('3');
            legendBox.appendChild(control3);

            const title1 = document.createElement('div');
            title1.textContent = t('mapDashboard.additional_information')//  'Additional Information'
            title1.style.fontWeight = 600;
            title1.style.fontSize = '18px'

            const title2 = document.createElement('div');
            title2.textContent = t('mapDashboard.digital_signage_usage_duration')
            title2.style.fontWeight = 400;
            title2.style.fontSize = '14px'
            title2.style.marginTop = '10px'

            const title3 = document.createElement('div');
            title3.textContent = t('mapDashboard.markers_number_tip')
            title3.style.fontWeight = 400;
            title3.style.fontSize = '12px'
            title3.style.marginTop = '10px'
            title3.style.marginBottom = '10px'

            controlBox.appendChild(title1);
            controlBox.appendChild(title2);
            controlBox.appendChild(title3);
            controlBox.appendChild(legendBox);
            map.controls[google.maps.ControlPosition.BOTTOM_RIGHT].push(controlBox);
            setMap(map)
        });
    }

    const getSvgMarker = (Leve = '1') => {
        let fillColor = ""
        if (Leve === '1') {
            fillColor = "rgb(86,166,59)"
        } else if (Leve === '2') {
            fillColor = "rgb(107,149,238)"
        } else if (Leve === '3') {
            fillColor = "rgb(234,143,64)"
        }
        return {
            path: "M0 0 C6.85580569 4.2076404 11.26833084 10.1340168 13.19140625 17.921875 C14.30442032 25.65439383 12.65614117 31.93420041 8.5625 38.5 C6.22259831 41.41280605 3.67502836 44.08167006 1.0625 46.75 C0.39863281 47.44351563 -0.26523437 48.13703125 -0.94921875 48.8515625 C-4.07317781 52.10646596 -7.2310006 55.32621998 -10.4375 58.5 C-13.82416654 57.03169836 -16.05414718 55.14074491 -18.66015625 52.54296875 C-19.83868164 51.36831055 -19.83868164 51.36831055 -21.04101562 50.16992188 C-21.85248047 49.35072266 -22.66394531 48.53152344 -23.5 47.6875 C-24.31533203 46.87990234 -25.13066406 46.07230469 -25.97070312 45.24023438 C-26.75509766 44.45326172 -27.53949219 43.66628906 -28.34765625 42.85546875 C-29.41705444 41.78268677 -29.41705444 41.78268677 -30.50805664 40.68823242 C-36.24409544 34.18284007 -36.86254633 28.32958628 -36.77734375 19.89453125 C-36.00567055 12.18666904 -32.25404279 6.5262427 -26.37890625 1.65625 C-18.97059305 -2.75594458 -7.92679982 -3.50578622 0 0 Z",
            fillColor,
            fillOpacity: 1,
            strokeWeight: 0,
            rotation: 0,
            scale: 0.6,
            anchor: new google.maps.Point(-10, 65),
            labelOrigin: new google.maps.Point(-10, 25)
        };
    }

    const addMarker = (position, element) => {
        const marker = new MarkerObj({
            map: map,
            position: position,
            title: element.address + ' ' + element.storeName,
            icon: getSvgMarker(element.yearsType),
            label: {
                text: element.total + '',
                color: '#ffffff'
            },
        });

        marker.addListener("click", () => {
            map.setZoom(12);
            map.setCenter(marker.getPosition());
        });

        return marker;
    }

    useEffect(() => {
        if (map === null) {
            initMap()
        }
    }, [])

    useEffect(() => {
        if (center) {
            let location = center.split(',')
            if (location.length === 2) {
                position = { lat: Number(location[1]), lng: Number(location[0]) };
                map?.panTo(position)
            }
        }
    }, [center])

    const renderMakrk = async () => {
        if (showData.length > 0) {
            if (MarkerObj === null) {
                const { Marker } = await google.maps.importLibrary("marker");
                MarkerObj = Marker
            }
            markers.map((item) => {
                item.setMap(null)
                item = null
            })
            setMarkers([])
            let markerList = showData.map((element, index) => {
                let local = element.location.split(',')
                let location = { lat: Number(local[1]), lng: Number(local[0]) };
                if (!center && index === 0) {
                    map?.panTo(location)
                }
                return addMarker(location, element)
            });
            setMarkers(markerList)
        } else {
            markers.map((item) => {
                item.setMap(null)
                item = null
            })
            setMarkers([])
        }
    }

    useEffect(() => {
        renderMakrk()
    }, [showData])


    const filterData = (list) => {
        let temp = {}
        list.forEach((item) => {
            let id = item.id
            let obj = temp[id]
            if (!obj) {
                temp[id] = item
            } else {
                let current = Number(item.yearsType)
                let tempValue = Number(obj.yearsType)
                let total = item.total + obj.total
                if (current > tempValue) {
                    item.total = total
                    temp[id] = item
                }
            }
        })
        let result = Object.keys(temp).map((key) => {
            return temp[key]
        })
        return result;
    }

    useEffect(() => {
        let dataList = [...(props.mapData?.zeroToThreeYears || []), ...(props.mapData?.threeToSixYears || []), ...(props.mapData?.sixPlusYears || [])]
        let result = filterData(dataList)
        setDataList(result)
        let list = dataList?.filter((item) => {
            let index = showType.indexOf(item.yearsType)
            if (index > -1) {
                return true
            } else {
                return false
            }
        })
        setShowData(list)
    }, [props.mapData])


    useEffect(() => {
        if (dataList.length > 1) {
            var bounds = new google.maps.LatLngBounds();
            dataList.forEach((item) => {
                let location = item.location.split(',')
                if (location.length === 2) {
                    let p = { lat: Number(location[1]), lng: Number(location[0]) };
                    var latlng = new google.maps.LatLng(p.lat, p.lng);
                    bounds.extend(latlng);
                }
            })
            if (map) {
                //    var myLatlng = bounds.getCenter();
                //    map?.panTo(myLatlng)
                map.fitBounds(bounds);
            }
        } else if (dataList.length === 1) {
            try {
                let location = dataList[0].location.split(',')
                if (location.length === 2) {
                    let p = { lat: Number(location[1]), lng: Number(location[0]) };
                    map?.panTo(p)
                }
            } catch (e) {
                console.log(e)
            }
        }
    }, [dataList])

    useEffect(() => {
        let list = dataList?.filter((item) => {
            let index = showType.indexOf(item.yearsType)
            if (index > -1) {
                return true
            } else {
                return false
            }
        })
        setShowData(list)
        sessionStorage.setItem('showType', showType.toString())
    }, [showType])

    const showTypeClick = (type) => {
        let showTypeStr = sessionStorage.getItem('showType')
        let tempArray = []
        if (showTypeStr) {
            tempArray = showTypeStr.split(',')
        }
        let index = tempArray.indexOf(type)
        if (index > -1) {
            tempArray.splice(index, 1)
            setShowType(tempArray)
        } else {
            let temp = [...tempArray, type]
            setShowType(temp)
        }
    }

    const createCenterControl = (yearsType) => {
        const controlButton = document.createElement('div');
        controlButton.style.display = 'flex'
        controlButton.style.alignItems = 'center'
        controlButton.style.marginRight = '10px'
        let icon = ''
        if (yearsType === '1') {
            icon = pin_green
        } else if (yearsType === '2') {
            icon = pin_blue
        } else if (yearsType === '3') {
            icon = pin_orange
        }
        const img = document.createElement('img');
        img.src = icon
        img.style.width = "20px"
        img.style.height = "20px"

        let label = ''
        if (yearsType === '1') {
            label = '0-3Yr'
        } else if (yearsType === '2') {
            label = '3-6Yr'
        } else if (yearsType === '3') {
            label = '6+Yr'
        }
        const pNode = document.createElement('p');
        pNode.textContent = label
        pNode.style.fontSize = "15px"
        pNode.style.marginLeft = '5px'

        controlButton.appendChild(img);
        controlButton.appendChild(pNode);
        controlButton.addEventListener('click', (e) => {
            if (img.style.opacity === '0.5') {
                img.style.opacity = 1
            } else {
                img.style.opacity = 0.5
            }
            showTypeClick(yearsType)
        });
        return controlButton;

    }

    return <div style={{ width: '100%', height: '70vh', position: 'relative' }} >
        <div style={{ width: '100%', height: '100%' }} id="map"></div>
    </div>
}

export default GoogleMap