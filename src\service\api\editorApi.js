import request from '@/utils/request';

/**
 *  获取国家列表
 */
export const getNewsLocals = () => {
    return request({
        url: `/news/locals`,
        method: 'get'
    });
};



/**
 *  获取新闻类别
 */
export const getNewsCategories = () => {
    return request({
        url: `/news/categories`,
        method: 'get'
    });
};



export const getNewsLanguages = () => {
    return request({
        url: `/news/languages`,
        method: 'get'
    });
};

export const getNewsByPage = (params) => {
    return request({
        url: `/news/query`,
        method: 'get',
        params: params
    });
};


export const getWeather = (params) => {
    return request({
        url: `/weather/query/location`,
        method: 'get',
        params: params
    });
};


export const getIp = (params) => {
    return request({
        url: `http://checkip.amazonaws.com`,
        method: 'get',
        params: params
    });
};


