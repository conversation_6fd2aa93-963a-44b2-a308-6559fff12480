import React from 'react'
import { Grid } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";

import { message } from "../common/i18n";
import { useConfirm } from "@/components/zkconfirm";

const templateConfig = [
    {
        templateConfig: [1, 1, 1],
        templateLeftConfig: [1, 1, 1],
        bgColor: ["#CECECE", "#ffffff", "#CECECE"],
        templateType: "col",
    },
    {
        templateConfig: [1, 1],
        templateLeftConfig: [1, 1],
        bgColor: ["#CECECE", "#ffffff"],
        templateType: "col",
    },

    {
        templateConfig: [1, 1],
        templateLeftConfig: [1, 1],
        bgColor: ["#CECECE", "#ffffff"],
        templateType: "row",
    },

    {
        templateConfig: [1, 2],
        templateLeftConfig: [1, 2],
        bgColor: ["#CECECE", "#ffffff"],
        templateType: "col",
    },
    {
        templateConfig: [2, 1],
        templateLeftConfig: [2, 1],
        bgColor: ["#ffffff", "#CECECE"],
        templateType: "col",
    },
    {
        templateConfig: [6, "100px"],
        templateLeftConfig: [1, "30px"],
        bgColor: ["#ffffff", "#CECECE"],
        templateType: "row",
    },
    {
        templateConfig: ["100px", 1],
        templateLeftConfig: ["30px", 6],
        bgColor: ["#CECECE", "#ffffff"],
        templateType: "row",
    },
    {
        templateConfig: [1, 1, 1, 1, 1, 1],
        templateLeftConfig: [1, 1, 1, 1, 1, 1],
        bgColor: [
            "#CECECE",
            "#ffffff",
            "#CECECE",
            "#ffffff",
            "#CECECE",
            "#ffffff",
            "#CECECE",
            "#ffffff",
            "#CECECE",
            "#ffffff",
        ],
        templateType: "col",
    },
    {
        templateConfig: [1, 1, 1, 1, 1],
        templateLeftConfig: [1, 1, 1, 1, 1],
        bgColor: [
            "#CECECE",
            "#ffffff",
            "#CECECE",
            "#ffffff",
            "#CECECE",
            "#ffffff",
            "#CECECE",
            "#ffffff",
            "#CECECE",
        ],
        templateType: "col",
    },
    {
        templateType: "custom",
    },
];

const WidgetComponent = (props) => {
    const layoutInfo = props.layoutInfo;
    const currentPageIndex = props.currentPageIndex;
    const currentComponentId = props.currentComponentId;
    const confirmFn = useConfirm();
    const isString = (value) => {
        return typeof value === "string";
    };

    const initTemp = (item) => {
        const newPages = JSON.parse(JSON.stringify(props.pages));
        let pageInfo = newPages[currentPageIndex];
        pageInfo.isTemplate = true;
        pageInfo.tempLayout = [];
        pageInfo.componentList = [];
        if (item.templateType === "col") {
            pageInfo.customTemplate = false
            let regular = 0;
            let fr = 0;
            item.templateConfig.forEach((element) => {
                if (isString(element)) {
                    let result = element.replace("px", "");
                    regular += parseInt(result);
                } else {
                    fr += element;
                }
            });

            let frwidth = parseInt((parseInt(layoutInfo.width) - regular) / fr);

            item.templateConfig.reduce((prev, cur, index, arr) => {
                if (isString(cur)) {
                    let result = parseInt(cur.replace("px", ""));
                    pageInfo.tempLayout.push({
                        left: prev,
                        top: 0,
                        width: result,
                        height: parseInt(layoutInfo.height),
                        duration: 10,
                        bgColor: "#ffffff",
                        bgImg: "",
                        checksum: "",
                        key: new Date().getTime(),
                        componentList: [],
                    });
                    return prev + result;
                } else {
                    pageInfo.tempLayout.push({
                        left: prev,
                        top: 0,
                        width: cur * frwidth,
                        height: parseInt(layoutInfo.height),
                        key: new Date().getTime(),
                        duration: 10,
                        bgColor: "#ffffff",
                        bgImg: "",
                        checksum: "",
                        componentList: [],
                    });
                    return prev + cur * frwidth;
                }
            }, 0);
        } else if (item.templateType === "row") {
            pageInfo.customTemplate = false
            let regular = 0;
            let fr = 0;
            item.templateConfig.forEach((element) => {
                if (isString(element)) {
                    let result = element.replace("px", "");
                    regular += parseInt(result);
                } else {
                    fr += element;
                }
            });

            let frHeight = parseInt((parseInt(layoutInfo.height) - regular) / fr);
            item.templateConfig.reduce((prev, cur, index, arr) => {
                if (isString(cur)) {
                    let result = parseInt(cur.replace("px", ""));
                    pageInfo.tempLayout.push({
                        left: 0,
                        top: prev,
                        width: parseInt(layoutInfo.width),
                        height: result,
                        key: new Date().getTime(),
                        duration: 10,
                        bgColor: "#ffffff",
                        bgImg: "",
                        checksum: "",
                        componentList: [],
                    });
                    return prev + result;
                } else {
                    pageInfo.tempLayout.push({
                        left: 0,
                        top: prev,
                        width: parseInt(layoutInfo.width),
                        height: cur * frHeight,
                        duration: 10,
                        key: new Date().getTime(),
                        bgColor: "#ffffff",
                        bgImg: "",
                        checksum: "",
                        componentList: [],
                    });
                    return prev + cur * frHeight;
                }
            }, 0);
        } else if (item.templateType === "custom") {
            pageInfo.tempLayout.push({
                left: 0,
                top: 0,
                width: 100,
                height: 100,
                duration: 10,
                key: new Date().getTime(),
                bgColor: "#ffffff",
                bgImg: "",
                checksum: "",
                componentList: [],
            });
            pageInfo.customTemplate = true;
        }
        if (props.setPages) {
            props.setPages(newPages);
        }
        if (props.setActiveTempIndex) {
            props.setActiveTempIndex(0);
        }
    };

    const addTemp = (item) => {
        let pageInfo = props.pages[currentPageIndex];
        if (pageInfo.isTemplate) {
            let cmpList = pageInfo.tempLayout
                .map((item) => {
                    return item.componentList;
                })
                .reduce(function (acc, curr) {
                    return acc.concat(curr);
                }, []);
            if (cmpList.length > 0) {
                confirmFn({
                    title: message("delete_cmp_tip"),
                    confirmationText: message("editor_edit_ok"),
                    cancellationText: message("editor_edit_cancel"),
                    description: message("editor_change_layout_tip"),
                }).then(() => {
                    initTemp(item);
                });
            } else {
                initTemp(item);
            }
        } else {
            if (pageInfo.componentList.length > 0) {
                confirmFn({
                    title: message("delete_cmp_tip"),
                    confirmationText: message("editor_edit_ok"),
                    cancellationText: message("editor_edit_cancel"),
                    description: message("editor_change_layout_tip"),
                }).then(() => {
                    initTemp(item);
                });
            } else {
                initTemp(item);
            }
        }
    };

    return (
        <Grid>
            {currentComponentId ? (
                <Grid
                    sx={{
                        width: "100%",
                    }}
                ></Grid>
            ) : (
                <Grid
                    sx={{
                        pb: 2,
                        width: "100%",
                    }}
                    container
                    rowSpacing={1}
                    columnSpacing={1}
                >
                    {templateConfig.map((item, index) => {
                        if (item.templateType === "row") {
                            return (
                                <Grid item xs={6} key={index}>
                                    <Grid
                                        onClick={() => {
                                            addTemp(item);
                                        }}
                                        sx={{
                                            height: "100px",
                                            display: "flex",
                                            flexDirection: "column",
                                            justifyContent: "center",
                                            alignItems: "stretch",
                                        }}
                                    >
                                        {item.templateLeftConfig.map((tempItem, configIndex) => {
                                            let bgColor = item.bgColor[configIndex];

                                            let sx = {};
                                            if (isString(tempItem)) {
                                                sx = {
                                                    height: tempItem,
                                                    backgroundColor: bgColor,
                                                };
                                            } else {
                                                sx = {
                                                    flexGrow: tempItem,
                                                    backgroundColor: bgColor,
                                                };
                                            }

                                            return <Grid sx={sx} key={configIndex}></Grid>;
                                        })}
                                    </Grid>
                                </Grid>
                            );
                        } else if (item.templateType === "col") {
                            return (
                                <Grid item xs={6} key={index}>
                                    <Grid
                                        onClick={() => {
                                            addTemp(item);
                                        }}
                                        sx={{
                                            height: "100px",
                                            display: "flex",
                                            justifyContent: "center",
                                        }}
                                    >
                                        {item.templateLeftConfig.map((itemConfig, configIndex) => {
                                            let bgColor = item.bgColor[configIndex];
                                            let sx = {};
                                            if (isString(itemConfig)) {
                                                sx = {
                                                    width: itemConfig,
                                                    backgroundColor: bgColor,
                                                };
                                            } else {
                                                sx = {
                                                    flexGrow: itemConfig,
                                                    backgroundColor: bgColor,
                                                };
                                            }

                                            return <Grid sx={sx} key={configIndex}></Grid>;
                                        })}
                                    </Grid>
                                </Grid>
                            );
                        } else if (item.templateType === "custom") {
                            return (
                                <Grid item xs={6} key={index}>
                                    <Grid
                                        onClick={() => {
                                            addTemp(item);
                                        }}
                                        sx={{
                                            height: "100px",
                                            display: "flex",
                                            justifyContent: "center",
                                            alignItems: "center",
                                            backgroundColor:"#ffffff"
                                        }}
                                    >
                                        <AddIcon></AddIcon>
                                        {message('editor_custom')}
                                    </Grid>
                                </Grid>
                            );
                        }
                    })}
                </Grid>
            )}
        </Grid>
    );
};

export default WidgetComponent;
