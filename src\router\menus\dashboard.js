// 看板
const dashboardRoute = [
  // {
  //   path: "/dashboard",
  //   component: () => import("@/pages/dashboard"),
  //   meta: {
  //     title: "运维看板",
  //     needLogin: true,
  //     i18n: "media_dashboard",
  //     roles: ["DevOpt", "admin"],
  //   },
  // },

  {
    path: "/dashboard/summary",
    component: () => import("@/pages/dashboard/Summary"),
    meta: {
      title: "Summary",
      id:'516489808121577346',
      i18n: "summary_dashboard"
    },
  },
  {
    path: "/dashboard/MapDashboard",
    component: () => import("@/pages/dashboard/MapDashboard"),
    meta: {
      title: "Map",
      id: "316489808121577346",
      i18n: "map_dashboard"
    },
  },
  {
    path: "/dashboard/outlet",
    component: () => import("@/pages/dashboard/Outlet"),
    meta: {
      title: "Outlet",
      id: "316481208121577346",
      i18n: "outlet_dashboard"
    },
  },
  {
    path: "/dashboard/RealTime",
    component: () => import("@/pages/dashboard/RealTime"),
    meta: {
      title: "RealTime",
      id: "543658692738809856",
      i18n: "real_time_viewing"
    },
  },
  // {
  //   path: "/retailer/dashboard",
  //   component: () => import("@/pages/dashboard/retailer"),
  //   meta: {
  //     title: "零售商看板",
  //     i18n: "media_retailer_dashboard",
  //     needLogin: true,
  //     roles: ["brand"],
  //   },
  // },
  // {
  //   path: "/advertiser/dashboard",
  //   component: () => import("@/pages/dashboard/advertiser"),
  //   meta: {
  //     title: "广告商看板",
  //     i18n: "media_advertiser_dashboard",
  //     needLogin: true,
  //     roles: ["advertiser"],
  //   },
  // },
];

export default dashboardRoute;
