import React from 'react'
import  { forwardRef, useRef, useEffect, useState } from "react";
import {
  Stack,
  InputLabel,
  OutlinedInput,
  Select,
  MenuItem,
} from "@mui/material";
import { IconButton } from "@mui/material";
import ClearIcon from "@mui/icons-material/Clear";
const CustomSelect = forwardRef((props, ref) => {
  const {
    onChange,
    value,
    label,
    required,
    items,
    labelKey,
    disabled,
    isClear = false,
    valueKey,
    disableArray,
    ...rest
  } = props;
  const [haveValue, setHaveValue] = useState(false);
  const handleChangeSelect = (event) => {
    if (onChange) {
      onChange(event);
    }
  };

  useEffect(() => {
    if (
      value === "" ||
      value === null ||
      value === undefined ||
      (Array.isArray(value) && value.length === 0)
    ) {

      setHaveValue(false);
    } else {

      setHaveValue(true);
    }
  }, [value]);

  return (
    <Stack
      sx={{
        mb: 2,
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
      }}
      spacing={0}
    >
      <InputLabel
        sx={{ color: "#707070", fontSize: "14px", mr: 2, whiteSpace: "nowrap" }}
      >
        {label} {required && <i style={{ color: "red" }}>*</i>}
      </InputLabel>
      <Select
        style={{
          marginTop: "0px",
        }}
        sx={{
          minWidth: 80,
          ".MuiSelect-select": {
            padding: "5px",
          },
        }}
        displayEmpty
        {...rest}
        value={props.value}
        onChange={handleChangeSelect}
        input={
          <OutlinedInput
            endAdornment={
              isClear
                ? haveValue &&
                  !disabled && (
                    <IconButton
                    sx={{
                        right:'10px'
                      }}
                      onClick={(event) => {
                        event.target.name = props.name;
                        event.target.value = "";
                        handleChangeSelect(event);
                      }}
                    >
                      <ClearIcon
                        fontSize="small"
                        sx={{
                          color: "#757575",
                          cursor: "pointer",
                        }}
                      />
                    </IconButton>
                  )
                : null
            }
          />
        }
        inputProps={{ "aria-label": "Without label" }}
      >

        {(items || []).map((item) => {
          let isObject = typeof item === "object";
          if (isObject) {
            let disableResult = false
            if (disableArray && disableArray.length > 0) {
              let value = item[valueKey || "value"]
              disableResult= disableArray.indexOf(value)>-1
            }
            return (
              <MenuItem
              disabled={disableResult}
                key={item[valueKey || "value"]}
                value={item[valueKey || "value"]}
              >
                {item[labelKey || "label"]}
              </MenuItem>
            );
          } else {

            let disableResult = false

            if (disableArray&&disableArray.length>0) {
              disableResult= disableArray.indexOf(item)>-1
            }

            return (
              <MenuItem disabled={disableResult} key={item} value={item}>
                {item}
              </MenuItem>
            );
          }
        })}
      </Select>
    </Stack>
  );
});

export default CustomSelect;
