/**
 * 联屏管理接口定义
 */
import request from '@/utils/request';
const baseProfixURI = '/sd/v1/link_screen';

/**
 * 分页查询
 * <AUTHOR> yu
 * date 2023-04-26 15:09
 */
export const listByPage = (params) => {
    return request({
        url: `${baseProfixURI}/page`,
        method: 'get',
        params: params
    });
};

/**
 *  新增联屏
 * <AUTHOR> yu
 * @date 2023-04-26 15:10
 */
export const addLinkScreen = (params) => {
    return request({
        url: `${baseProfixURI}`,
        method: 'POST',
        data: params
    });
};

/**
 * 修改联屏
 * <AUTHOR> yu
 * @date 2023-05-04 11:15
 */
export const updateSplicingScreen = (params) => {
    return request({
        url: `${baseProfixURI}`,
        method: 'PUT',
        data: params
    });
};

/**
 *  根据ID获取联屏信息
 * <AUTHOR> yu
 * @date 2023-05-04 11:10
 */
export const getSplicingScreenById = (id) => {
    return request({
        url: `${baseProfixURI}/${id}`,
        method: 'get'
    });
};

/**
 *  删除联屏
 *
 * @param ids string 类型id用逗号隔开
 * <AUTHOR> yu
 * @date 2023-04-26 15:12
 */
export const removeLinkScreen = (ids) => {
    return request({
        url: `${baseProfixURI}/${ids}`,
        method: 'delete'
    });
};

/**
 * 分页查询
 * <AUTHOR> yu
 * date 2023-04-26 15:09
 */
export const listByPageScreen = (params) => {
    return request({
        url: `${baseProfixURI}/page/screen`,
        method: 'get',
        params: params
    });
};

/**
 *  根据ID获取联屏是否在线
 * <AUTHOR>
 * @date 2023-06-08 11:29
 */
export const getGroupScreenById = (id) => {
    return request({
        url: `${baseProfixURI}/${id}`,
        method: 'get'
    });
};



export const cleanSchedule = (ids) => {
    return request({
        url: `${baseProfixURI}/clear/data/${ids}`,
        method: 'DELETE',
    });
}


export const cleanPlayerSchedule = (ids) => {
    return request({
        url: `/sd/v1/schedule/${ids}`,
        method: 'DELETE',
    });
}
