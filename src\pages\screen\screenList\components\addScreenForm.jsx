import React, { forwardRef, useState, useRef, useEffect } from "react";
import * as Yup from "yup";
import { useFormik } from "formik";
import LoadingButton from "@mui/lab/LoadingButton";
import {
  <PERSON><PERSON>,
  Grid,
  <PERSON>ert,
  <PERSON>ert<PERSON><PERSON>le,
  Button,
  InputLabel,
  Typography,
  FormHelperText,
  OutlinedInput,
  Tooltip,
} from "@mui/material";
import { toast } from "react-toastify";
import { useNavigate } from "react-router-dom";
import MainCard from "@/components/MainCard";
import ZKSelect from "@/components/ZKSelect";
import InfoIcon from "@mui/icons-material/Info";
import { getOutletList } from "@/service/api/L3Sevice.js";
import { registerScreen } from "@/service/api/screen";
import { screendirections, deviceTypies } from "@/dict/commonDict";
import HelpOutlineIcon from "@mui/icons-material/HelpOutline";
import { useTranslation } from "react-i18next";
import ZKAutocomplete from "@/components/ZKAutocomplete";
import { getPrincipaList } from "@/service/api/L3Sevice.js";

const addScreenForm = forwardRef((props, ref) => {
  const [merchantOptions, setMerchantOptions] = useState([]);
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [storeOptions, setStoreOptions] = useState([]);
  const [checkbox, setCheckBox] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [store, setStore] = useState(null);
  // 请求商户数据
  const handleRequestMerchant = () => {
    getPrincipaList("1").then((res) => {
      setMerchantOptions(res.data);
    });
  };
  const getOption = (merchantId) => {
    getOutletList(merchantId).then((res) => {
      setStoreOptions(res.data);
    });
  };
  useEffect(() => {
    handleRequestMerchant();
  }, []);

  const getAddress = (value) => {
    const target = storeOptions.find((item) => {
      return item.value === value;
    });
    setStore(target);
  };
  const handelSaveSubmit = (values) => {
    setIsLoading(true);
    registerScreen(values)
      .then((res) => {
        toast.success(res.message);
        setIsLoading(false);
        navigate(-1);
      })
      .catch((error) => {
        setIsLoading(false);
      });
  };

  //  表单
  const screenFormik = useFormik({
    initialValues: {
      name: "",
      sn: "",
      direction: "",
      storeId: "",
      wide: "",
      merchantId: "",
      high: "",
      deviceType: "",
    },
    onSubmit: (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        handelSaveSubmit(values);
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
    validationSchema: Yup.object().shape({
      name: Yup.string()
        .required(t("common.common_input_device_name"))
        .min(0, t("common.common_rule_device_name"))
        .max(20, t("common.common_rule_device_name_length_max")),
      sn: Yup.string()
        .required(t("common.common_plese_screen_sn"))
        .min(0, t("common.common_screen_sn_notnull"))
        .max(50, t("common.common_screen_sn_length_max")),
      direction: Yup.string().required(
        t("common.common_plese_screen_direction")
      ),
      deviceType: Yup.string().required(t("common.common_please_type")),
      storeId: Yup.string().required(
        t("common.common_plese_scrren_onwer_outlet")
      ),
      merchantId: Yup.string().required(t("ips.ips_select_merchant")),
      wide: Yup.string()
        .matches(/^[0-9]*$/, {
          message: t("ips.ips_resolution_input_number"),
          excludeEmptyString: true,
        })
        .required(t("common.common_input_width")),
      high: Yup.string()
        .matches(/^[0-9]*$/, {
          message: t("ips.ips_resolution_input_number"),
          excludeEmptyString: true,
        })
        .required(t("common.common_input_height")),
    }),
  });

  return (
    <>
      <MainCard
        title={
          <Typography variant="h4" component="p">
            {t("common.common_add_screen")}
          </Typography>
        }
        border={false}>
        <form noValidate onSubmit={screenFormik.handleSubmit}>
          <Grid container spacing={3} sx={{ padding: "20px" }}>
            <Grid item xs={12} md={4} lg={4}>
              <Stack spacing={1}>
                <InputLabel htmlFor="screen-name">
                  {t("ips.ips_device")} <i style={{ color: "red" }}>*</i>
                </InputLabel>
                <OutlinedInput
                  id="dictLabel"
                  value={screenFormik.values.name}
                  type="text"
                  fullWidth
                  name="name"
                  error={Boolean(
                    screenFormik.touched.name && screenFormik.errors.name
                  )}
                  onBlur={screenFormik.handleBlur}
                  onChange={screenFormik.handleChange}
                  placeholder={t("common.common_please_input")}
                />
                {screenFormik.touched.name && screenFormik.errors.name && (
                  <FormHelperText error id="name-error">
                    {screenFormik.errors.name}
                  </FormHelperText>
                )}
              </Stack>
            </Grid>
            <Grid item xs={12} md={4} lg={4}>
              <Stack spacing={1}>
                <InputLabel htmlFor="screen-sn">
                  <Stack
                    direction="row"
                    justifyContent="flex-start"
                    alignItems="center"
                    spacing={0.3}>
                    <Typography>{t("ips.ips_device_sn")}</Typography>
                    <Tooltip
                      title={t("common.common_show_screen_sn")}
                      placement="top"
                      arrow>
                      <HelpOutlineIcon fontSize="small" />
                      {/* <IconButton size="small" sx={{ width: 20, height: 20 }}>
                        <QuestionMarkIcon />
                      </IconButton> */}
                    </Tooltip>
                    <i style={{ color: "red" }}>*</i>
                  </Stack>
                </InputLabel>
                <OutlinedInput
                  value={screenFormik.values.sn}
                  id="sn"
                  type="text"
                  fullWidth
                  name="sn"
                  error={Boolean(
                    screenFormik.touched.sn && screenFormik.errors.sn
                  )}
                  onBlur={screenFormik.handleBlur}
                  onChange={screenFormik.handleChange}
                  placeholder={t("common.common_please_input")}
                />
                {screenFormik.touched.sn && screenFormik.errors.sn && (
                  <FormHelperText error id="sn-error">
                    {screenFormik.errors.sn}
                  </FormHelperText>
                )}
              </Stack>
            </Grid>
            <Grid item xs={12} md={4} lg={4}>
              <Stack spacing={1}>
                <InputLabel htmlFor="deviceType">
                  {t("common.common_deviceType")}
                  <i style={{ color: "red" }}>*</i>
                </InputLabel>
                <ZKSelect
                  displayEmpty
                  name="deviceType"
                  value={screenFormik.values.deviceType}
                  placeholder={t("common.common_plese_select")}
                  options={deviceTypies}
                  onChange={screenFormik.handleChange}
                  onBlur={screenFormik.handleBlur}
                  onClear={() => {
                    screenFormik.setFieldValue("deviceType", undefined);
                  }}
                  error={Boolean(
                    screenFormik.touched.deviceType &&
                      screenFormik.errors.deviceType
                  )}
                />
                {screenFormik.touched.deviceType &&
                  screenFormik.errors.deviceType && (
                    <FormHelperText error id="direction-error">
                      {screenFormik.errors.deviceType}
                    </FormHelperText>
                  )}
              </Stack>
            </Grid>
            <Grid item xs={12} md={4} lg={4}>
              <Stack spacing={1}>
                <InputLabel htmlFor="direction">
                  {t("ips.ips_screen_direction")}
                  <i style={{ color: "red" }}>*</i>
                </InputLabel>
                <ZKSelect
                  displayEmpty
                  name="direction"
                  value={screenFormik.values.direction}
                  placeholder={t("common.common_plese_select")}
                  options={screendirections}
                  onChange={screenFormik.handleChange}
                  onBlur={screenFormik.handleBlur}
                  onClear={() => {
                    screenFormik.setFieldValue("direction", undefined);
                  }}
                  error={Boolean(
                    screenFormik.touched.direction &&
                      screenFormik.errors.direction
                  )}
                />
                {screenFormik.touched.direction &&
                  screenFormik.errors.direction && (
                    <FormHelperText error id="direction-error">
                      {screenFormik.errors.direction}
                    </FormHelperText>
                  )}
              </Stack>
            </Grid>
            <Grid item xs={12} md={4} lg={4}>
              <Stack spacing={1}>
                <InputLabel htmlFor="store-name">
                  {t("common.common_los_merchant_name")}{" "}
                  <i style={{ color: "red" }}>*</i>
                </InputLabel>
                <ZKSelect
                  name="merchantId"
                  size="small"
                  value={screenFormik.values.merchantId}
                  onChange={(e) => {
                    //调用获取门店
                    // console.log(e.target.value);
                    getOption(e.target.value);
                    screenFormik.handleChange(e);
                  }}
                  onBlur={screenFormik.handleBlur}
                  options={merchantOptions}
                  onClear={() => {
                    setStoreOptions([]);
                    screenFormik.setFieldValue("storeId", "");
                    screenFormik.setFieldValue("merchantId", "");
                  }}
                  error={Boolean(
                    screenFormik.touched.merchantId &&
                      screenFormik.errors.merchantId
                  )}
                  placeholder={t("ips.ips_select_merchant")}
                />
                {screenFormik.touched.merchantId &&
                  screenFormik.errors.merchantId && (
                    <FormHelperText error id="merchantId-error">
                      {screenFormik.errors.merchantId}
                    </FormHelperText>
                  )}
              </Stack>
            </Grid>
            <Grid item xs={12} md={4} lg={4}>
              <Stack spacing={1}>
                <InputLabel htmlFor="store-name">
                  {t("common.common_outlet_owner")}{" "}
                  <i style={{ color: "red" }}>*</i>
                </InputLabel>
                <ZKAutocomplete
                  name="storeId"
                  onClear={() => {
                    screenFormik.setFieldValue("storeId", undefined);

                    setStore(null);
                  }}
                  value={screenFormik.values.storeId}
                  id="store-id"
                  onChange={(e, newValue) => {
                    screenFormik.handleChange(e);
                    screenFormik.setFieldValue("storeId", newValue.value);
                    setStore(newValue);
                  }}
                  labelField="name"
                  valueField="id"
                  onBlur={screenFormik.handleBlur}
                  data={storeOptions}
                  placeholder={t("ips.ips_select_a_outlet")}
                  error={Boolean(
                    screenFormik.touched.storeId && screenFormik.errors.storeId
                  )}
                />
                {screenFormik.touched.storeId &&
                  screenFormik.errors.storeId && (
                    <FormHelperText error id="storeId-error">
                      {screenFormik.errors.storeId}
                    </FormHelperText>
                  )}
              </Stack>
            </Grid>
            <Grid item xs={12} md={4} lg={4}>
              <Stack spacing={1}>
                <InputLabel htmlFor="address">
                  {t("common.common_location")}
                  <i style={{ color: "red" }}>*</i>
                </InputLabel>
                <OutlinedInput
                  id="address"
                  placeholder={t("common.common_loading_select_outlet")}
                  disabled={true}
                  value={null !== store ? store.remark : ""}
                  type="text"
                  fullWidth
                  name="address"
                />
              </Stack>
            </Grid>
            <Grid item xs={12} md={4} lg={4}>
              <Stack spacing={1}>
                <InputLabel htmlFor="resolution_wide">
                  {t("ips.ips_resolution_wide")}{" "}
                  <i style={{ color: "red" }}>*</i>
                </InputLabel>
                <OutlinedInput
                  id="wide"
                  value={screenFormik.values.wide}
                  type="text"
                  fullWidth
                  name="wide"
                  error={Boolean(
                    screenFormik.touched.wide && screenFormik.errors.wide
                  )}
                  onBlur={screenFormik.handleBlur}
                  onChange={screenFormik.handleChange}
                  placeholder={t("common.common_please_input")}
                />
                {screenFormik.touched.wide && screenFormik.errors.wide && (
                  <FormHelperText error id="wide-error">
                    {screenFormik.errors.wide}
                  </FormHelperText>
                )}
              </Stack>
            </Grid>
            <Grid item xs={12} md={4} lg={4}>
              <Stack spacing={1}>
                <InputLabel htmlFor="resolution_high">
                  {t("ips.ips_resolution_high")}{" "}
                  <i style={{ color: "red" }}>*</i>
                </InputLabel>
                <OutlinedInput
                  id="high"
                  value={screenFormik.values.high}
                  type="text"
                  fullWidth
                  name="high"
                  error={Boolean(
                    screenFormik.touched.high && screenFormik.errors.high
                  )}
                  onBlur={screenFormik.handleBlur}
                  onChange={screenFormik.handleChange}
                  placeholder={t("common.common_please_input")}
                />
                {screenFormik.touched.high && screenFormik.errors.high && (
                  <FormHelperText error id="high-error">
                    {screenFormik.errors.high}
                  </FormHelperText>
                )}
              </Stack>
            </Grid>
            <Grid item xs={12}>
              <Alert severity="success" icon={<InfoIcon />}>
                <AlertTitle>
                  {t("common.common_add_screen_alert_title")}
                </AlertTitle>
                <div
                  dangerouslySetInnerHTML={{
                    __html: t("common.common_add_screen_alert_desc"),
                  }}
                />
              </Alert>
            </Grid>
            <Grid item xs={12}>
              <Stack
                direction="row"
                justifyContent="flex-end"
                alignItems="center"
                spacing={2}>
                <Button
                  color="info"
                  variant="outlined"
                  onClick={() => {
                    navigate(-1);
                  }}>
                  {t("common.common_op_return")}
                </Button>
                <LoadingButton
                  loading={isLoading}
                  disableElevation
                  type="submit"
                  variant="contained"
                  color="primary">
                  {t("common.common_submit")}
                </LoadingButton>
              </Stack>
            </Grid>
          </Grid>
        </form>
      </MainCard>
    </>
  );
});

export default addScreenForm;
