import React, { useEffect, useState } from "react"
import PercentCircle from '@/components/PercentCircle'
import { getInstalledRate } from '@/service/api/dashboardOutlet'
const OutletPercent = (props) => {
    const [percent, setPercent] = useState(0)
    useEffect(() => {
        if (props.retailClientId) {
            getInstalledRate({
                retailClientId:props.retailClientId
            }).then((res) => {
                if(res.code===0){
                    let data = res.data
                    setPercent(data)
                }else{
                    setPercent(0)
                }
            })
        } else {
            setPercent(0)
        }
    }, [props.retailClientId])
    return <PercentCircle textStyle={{fontSize:'18px'}} width={100} Percent={percent} />
}
export default OutletPercent;