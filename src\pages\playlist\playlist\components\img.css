.img {
  width: 65px;
  height: 50px;
}

.disappear {
  width: 0px;
  height: 0px;
}
.react-player-wrapper {
  position: relative;
  padding-top: 56.25%; /* 视频宽高比为16:9时，容器宽高比为9/16=0.5625 */
}

.preview-img {
  /* width: 100%; */
  height: 500px;
}

.preview-audio {
  width: 92%;
  height: 50px;
  position: absolute;
  bottom: 10%;
}

.svg-img {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* .textSpace {
  display: flex;
  flex-wrap: wrap;
  gap: 3px;
} */
