import React, { useEffect, useState, useMemo, useRef } from "react";
import Box from "@mui/material/Box";
import { InputAdornment, IconButton, TextField } from "@mui/material";
import Autocomplete from "@mui/material/Autocomplete";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import Grid from "@mui/material/Grid";
import SearchIcon from "@mui/icons-material/Search";
import Typography from "@mui/material/Typography";
import parse from "autosuggest-highlight/parse";
import { debounce } from "@mui/material/utils";
import { useTranslation } from "react-i18next";

// This key was created specifically for the demo in mui.com.
// You need to create a new one for your application.
const GOOGLE_MAPS_API_KEY = "AIzaSyA9MaTVJlWIWpINjcgyJl5eS6JDhe60238";

function loadScript(src, position, id) {
  console.log("position", position);
  if (!position) {
    return;
  }

  const script = document.createElement("script");
  script.setAttribute("async", "");
  script.setAttribute("id", id);
  script.src = src;
  position.appendChild(script);
}

const autocompleteService = { current: null };

export default function GoogleMaps(props) {
  const {
    setZoom,
    setCoordinates,
    detailAddress,
    onCoordinatesChange,
    onDetailAddress,
  } = props;
  const { t } = useTranslation();

  const [value, setValue] = React.useState(null);
  const [inputValue, setInputValue] = React.useState("");
  const [options, setOptions] = React.useState([]);
  const loaded = React.useRef(null);

  const fetch = React.useMemo(
    () =>
      debounce((request, callback) => {
        autocompleteService?.current?.getPlacePredictions(request, callback);
      }, 400),
    []
  );

  React.useEffect(() => {

    if (
      window.localStorage.getItem("localVersion") === 'EN'
    ) {
      let active = true;

      if (!autocompleteService?.current && window?.google) {
        autocompleteService.current =
          new window.google.maps.places.AutocompleteService();
      }
      if (!autocompleteService?.current) {
        return undefined;
      }

      if (inputValue === "") {
        setOptions(value ? [value] : []);
        return undefined;
      }

      fetch({ input: inputValue }, (results) => {
        // console.log("inputValue", inputValue);
        // console.log("results", results);
        if (active) {
          let newOptions = [];

          if (value) {
            newOptions = [value];
          }

          if (results) {
            newOptions = [...newOptions, ...results];
          }

          setOptions(newOptions);
        }
      });

      return () => {
        active = false;
      };
    }

  }, [value, inputValue, fetch]);
  const handleSearch = (value) => {
    // console.log("输入的地点为", value);

    const geocoder = new window.google.maps.Geocoder();
    geocoder.geocode({ address: value }, (results, status) => {
      if (status === "OK") {
        const position = results[0].geometry.location;
        const selectedLatLng = {
          lat: position.lat(), // 纬度
          lng: position.lng(), // 经度
        };

        setCoordinates(selectedLatLng);
        // 调用父组件传递的函数，传递经纬度
        onCoordinatesChange(selectedLatLng);
        setZoom(20);
      } else {
        console.error(
          "Geocode was not successful for the following reason:",
          status
        );
      }
    });
    onDetailAddress(value);
  };

  return (
    <Autocomplete
      id="google-map-search"
      label="address"
      size="small"
      disablePortal
      disableClearable //清除按钮
      freeSolo
      getOptionLabel={(option) =>
        typeof option === "string" ? option : option.description
      }
      filterOptions={(x) => x}
      options={options}
      autoComplete
      includeInputInList
      filterSelectedOptions
      value={value}
      noOptionsText={t("common.common_input_location_search")}
      onChange={(event, newValue) => {
        setOptions(newValue ? [newValue, ...options] : options);
        setValue(newValue);
      }}
      onInputChange={(event, newInputValue) => {
        setInputValue(newInputValue);
        setValue(newInputValue);
      }}
      renderInput={(params) => (
        <TextField
          {...params}
          label="Add a location"
          fullWidth
          sx={{ width: 200, marginTop: 1, height: 60 }}
          InputProps={{
            ...params.InputProps,
            type: "search",
            endAdornment: (
              <InputAdornment position="end">
                <IconButton onClick={() => handleSearch(inputValue)}>
                  <SearchIcon />
                </IconButton>
              </InputAdornment>
            ),
          }}
        />
      )}
      renderOption={(props, option) => {
        const matches =
          option && option.structured_formatting
            ? option.structured_formatting.main_text_matched_substrings || []
            : [];

        const parts = parse(
          option && option.structured_formatting
            ? option.structured_formatting.main_text
            : "",
          matches.map((match) => [match.offset, match.offset + match.length])
        );

        return (
          <li {...props}>
            <Grid container alignItems="center">
              <Grid item sx={{ display: "flex", width: 44 }}>
                <LocationOnIcon sx={{ color: "text.secondary" }} />
              </Grid>
              <Grid
                item
                sx={{ width: "calc(100% - 44px)", wordWrap: "break-word" }}
              >
                {parts.map((part, index) => (
                  <Box
                    key={index}
                    component="span"
                    sx={{ fontWeight: part.highlight ? "bold" : "regular" }}
                  >
                    {part.text}
                  </Box>
                ))}
                <Typography variant="body2" color="text.secondary">
                  {option && option.structured_formatting
                    ? option.structured_formatting.secondary_text
                    : ""}
                </Typography>
              </Grid>
            </Grid>
          </li>
        );
      }}
    />
  );
}
