/* eslint-disable react/prop-types */
/* eslint-disable react-hooks/rules-of-hooks */
import React, { useRef, useEffect } from 'react';

const VideoThumbnail = (props) => {
    const videoRef = useRef(null);
    const canvasRef = useRef(null);

    useEffect(() => {
        const video = videoRef.current;
        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');

        video.addEventListener('loadedmetadata', () => {
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;

            // 绘制视频第一帧到canvas中
            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
        });

        video.src = props.videoSrc;
    }, []);

    return (
        <>
            <video ref={videoRef} className="img" />
            <canvas ref={canvasRef} className="disappear" />
        </>
    );
};

export default VideoThumbnail;
