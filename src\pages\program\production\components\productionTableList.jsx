import React from "react";
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/prop-types */
import { useEffect, useMemo, useState } from "react";
import MaterialReactTable, {
  MRT_ToggleDensePaddingButton,
  MRT_FullScreenToggleButton,
  MRT_ShowHideColumnsButton,
  MRT_GlobalFilterTextField,
  MRT_ToggleFiltersButton,
} from "material-react-table";
import TableCell, { tableCellClasses } from "@mui/material/TableCell";
import {
  Button,
  Stack,
  Typography,
  Link,
  MenuItem,
  Menu,
  Box,
  TextField,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import { useLocation } from "react-router-dom";
import { setProgramValue, setRouter } from "@/utils/editorStore";
// api
import { listByPage, removeProgram } from "@/service/api/program";
import { useConfirm } from "@/components/zkconfirm";
// 消息提示
import { toast } from "react-toastify";
import ProductDialog from "./productDialog";
// i18n
import { useTranslation } from "react-i18next";
import { tableI18n } from "@/utils/tableLang";
const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: theme.palette.common.black,
    color: theme.palette.common.white,
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 14,
  },
}));

const StyledMenu = styled((props) => (
  <Menu
    elevation={0}
    anchorOrigin={{
      vertical: "bottom",
      horizontal: "right",
    }}
    transformOrigin={{
      vertical: "top",
      horizontal: "right",
    }}
    {...props}
  />
))(({ theme }) => ({
  "& .MuiPaper-root": {
    borderRadius: 6,
    marginTop: theme.spacing(1),
    minWidth: 180,
    color:
      theme.palette.mode === "light"
        ? "rgb(55, 65, 81)"
        : theme.palette.grey[300],
    boxShadow:
      "rgb(255, 255, 255) 0px 0px 0px 0px, rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px",
    "& .MuiMenu-list": {
      padding: "4px 0",
    },
    "& .MuiMenuItem-root": {
      "& .MuiSvgIcon-root": {
        fontSize: 18,
        color: theme.palette.text.secondary,
        marginRight: theme.spacing(1.5),
      },
      "&:active": {
        backgroundColor: alpha(
          theme.palette.primary.main,
          theme.palette.action.selectedOpacity
        ),
      },
    },
  },
}));

const Example = () => {
  const { t } = useTranslation();
  const confirm = useConfirm();
  // 用于hash路由保存到sessionStore拼接
  const hashConstant = "/#";
  const [isError, setIsError] = useState(false);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  // 表格数据
  const [data, setData] = useState([]);
  // 分页的参数
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);
  // 过滤参数
  const [globalFilter, setGlobalFilter] = useState("");
  // 排序参数
  const [sorting, setSorting] = useState([{ id: "createTime", desc: true }]);
  // 列过滤
  const [columnFilters, setColumnFilters] = useState([]);
  // 构建参数
  const buildParams = () => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      orderProp: sorting[0]?.id,
      keyWard: globalFilter,
      orderBy: sorting[0]?.desc == true ? "DESC" : "ASC",
    };
    // 列过滤
    columnFilters.forEach((item) => {
      params[item.id] = item.value;
    });
    console.log(params);
    return params;
  };
  // 获取数据
  const getTableData = async () => {
    if (!data.length) {
      setIsLoading(true);
    } else {
      setIsRefetching(true);
    }
    // // 开启加载
    // setIsLoading(true);
    // setIsRefetching(true);
    await listByPage(buildParams())
      .then((res) => {
        // 设置数据
        setData(res.data.data);
        // 设置总记录数
        setRowCount(res.data.total);
        setIsLoading(false);
        setIsRefetching(false);
      })
      .catch((err) => {
        setIsError(true);
        setIsLoading(false);
        setIsRefetching(false);
      });
  };
  useEffect(() => {
    // const params = buildParams();
    // 发请求
    getTableData();
  }, [
    columnFilters,
    globalFilter,
    sorting,
    pagination.pageIndex,
    pagination.pageSize,
  ]);
  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "name", //access nested data with dot notation
        header: t("ips.ips_program"),
        enableSorting: false,
        // enableColumnFilter: false
      },
      {
        accessorKey: "type", //normal accessorKey
        header: t("ips.ips_program_type"),
        enableSorting: false,
        Cell: ({ cell, row }) => {
          let color;
          let title;
          switch (row.original.type) {
            case "0":
              color = "warning";
              title = t("dictData.dict_ordinary_program");
              break;
            case "1":
              color = "success";
              title = t("dictData.dict_acc_program");
              break;
            case "2":
              color = "error";
              title = t("dictData.dict_car_program");
              break;
            default:
              color = "primary";
              title = t("common.common_unknown");
          }

          return (
            <Stack direction="row" spacing={1} alignItems="center">
              {/* <Dot color={color} /> */}
              <Typography>{title}</Typography>
            </Stack>
          );
        },
        Filter: ({ header }) => (
          <TextField
            onChange={(e) =>
              header.column.setFilterValue(e.target.value || undefined)
            }
            select
            value={header.column.getFilterValue() ?? ""}
            margin="none"
            // placeholder="Filter"
            placeholder={t("common.common_select_program_type")}
            variant="standard"
            fullWidth>
            <MenuItem value="0">{t("dictData.dict_ordinary_program")}</MenuItem>
            <MenuItem value="1">{t("dictData.dict_acc_program")}</MenuItem>
            <MenuItem value="2">{t("dictData.dict_car_program")}</MenuItem>
          </TextField>
        ),
      },
      {
        accessorKey: "height", //normal accessorKey
        header: t("common.common_resolution_ratio"),
        enableSorting: false,
        // enableColumnFilter: false,
        Cell: ({ cell, row }) => {
          return (
            <Stack direction="row" spacing={1} alignItems="center">
              <Typography>
                {row.original.width} * {row.original.height}
              </Typography>
            </Stack>
          );
        },
      },
      {
        accessorKey: "scene", //normal accessorKey
        header: t("ips.ips_program_scene_num"),
        enableColumnFilter: false,
        enableSorting: false,
      },
      {
        accessorKey: "duration", //normal accessorKey
        header: t("ips.ips_program_durations"),
        enableSorting: false,
        enableColumnFilter: false,
        Cell: ({ cell, row }) => {
          return (
            <Stack direction="row" spacing={1} alignItems="center">
              <Typography>{row.original.duration}s</Typography>
            </Stack>
          );
        },
      },
      {
        accessorKey: "createTime",
        header: t("common.common_createdTime"),
        // size: '260'
      },
    ],
    []
  );
  const upload = React.useRef(null);
  const addProgramRef = React.useRef(null);
  // 路由获取
  const location = useLocation();
  // 编辑节目
  const handleEditProgram = (row) => {
    if (row.type == "0" || row.type == "1" || row.type == "2") {
      // 保存路由信息
      setRouter(hashConstant + location.pathname);
      setProgramValue(JSON.stringify(row));
      setTimeout(() => {
        // 跳转到编辑器
        window.open("editor.html", "_self");
      }, 50);
    }
  };

  // <WarningIcon color="warning" />
  // <Typography variant="h4" gutterBottom>
  //     提示
  // </Typography>
  // 删除节目
  const handleRemove = (ids, names) => {
    confirm({
      title: t("message.messageBox_title"),
      confirmationText: t("common.common_edit_ok"),
      cancellationText: t("common.common_edit_cancel"),
      description: t("common.common_confirm_delete_program", { ids: names }),
    }).then(() => {
      removeProgram(ids).then((res) => {
        toast.success(res.message);
        // 重新请求数据
        getTableData();
        // setIsRefetching(true);
      });
    });
  };
  return (
    <>
      <MaterialReactTable
        // table 状态
        state={{
          // 加载状态
          isLoading,
          // 分页参数
          pagination,
          // 重新拉取
          showProgressBars: isRefetching,
          showAlertBanner: isError,
          columnFilters,
          globalFilter,
          sorting,
        }}
        // 解决列太多宽度太长问题
        enableColumnResizing
        // enablePinning
        // 初始化状态
        initialState={{ columnVisibility: { createTime: true } }}
        muiToolbarAlertBannerProps={
          isError
            ? {
                color: "error",
                children: t("table.loading_error"),
              }
            : undefined
        }
        // 开启多选
        enableRowSelection
        // 列数
        rowCount={rowCount}
        // 固定头部
        enableStickyHeader
        // 处理表格高度
        muiTableContainerProps={{ sx: { maxHeight: "620px" } }}
        // 设置背景颜色
        muiTableBodyCellProps={{ sx: { backgroundColor: "white" } }}
        muiTableProps={{ sx: { backgroundColor: "white" } }}
        muiBottomToolbarProps={{ sx: { backgroundColor: "white" } }}
        muiTopToolbarProps={{ sx: { backgroundColor: "white" } }}
        // 分页回调函数
        onPaginationChange={setPagination}
        // 记得开启这些 tips:否则会出现useEffect 反作用函数发起多次请求点击下一页会出问题
        manualFiltering
        manualPagination
        manualSorting
        // 列过滤
        onColumnFiltersChange={setColumnFilters}
        // 全局过滤
        onGlobalFilterChange={setGlobalFilter}
        // 排序
        onSortingChange={setSorting}
        // 开启分页
        enablePagination
        // 列定义
        columns={columns}
        // muiTablePaperProps={{
        //     sx: {
        //         maxWidth: '1580px',
        //         m: 'auto'
        //     }
        // }}
        // 数据
        data={data}
        // 初始化国际化语言  --- todo: i18n key doc address: https://github.com/KevinVandy/material-react-table/blob/main/packages/material-react-table/src/_locales/en.ts
        localization={tableI18n}
        muiLinearProgressProps={({ isTopToolbar }) => ({
          sx: { display: isTopToolbar ? "block" : "none" },
        })}
        // 表头右上角
        // renderToolbarInternalActions={({ table }) => (
        //     <Box>
        //         {/* <IconButton
        //             onClick={() => {
        //                 window.print();
        //             }}
        //         >
        //             <PrintIcon />
        //         </IconButton> */}
        //         <MRT_ShowHideColumnsButton table={table} />
        //         <MRT_ToggleFiltersButton table={table} />
        //         <MRT_ToggleDensePaddingButton table={table} />
        //         <MRT_FullScreenToggleButton table={table} />
        //     </Box>
        // )}
        // 自定义表头按钮
        renderTopToolbarCustomActions={({ table }) => {
          // const handleDeactivate = () => {
          //     table.getSelectedRowModel().flatRows.map((row) => {
          //         alert('deactivating ' + row.getValue('name'));
          //     });
          // };

          // const handleActivate = () => {
          //     table.getSelectedRowModel().flatRows.map((row) => {
          //         alert('activating ' + row.getValue('name'));
          //     });
          // };

          // const handleContact = () => {
          //     table.getSelectedRowModel().flatRows.map((row) => {
          //         alert('contact ' + row.getValue('name'));
          //     });
          // };

          return (
            <div style={{ display: "flex", gap: "0.5rem" }}>
              <Button
                variant="contained"
                onClick={() => {
                  // addProgramRef.current.handleOpen();
                  addProgramRef.current.handleOpen();
                  // window.open('editor.html');
                }}>
                {t("common.common_add_program")}
              </Button>
              <Button
                color="secondary"
                // disabled
                disabled={
                  !table.getIsSomeRowsSelected() &&
                  !table.getIsAllRowsSelected()
                }
                onClick={() => {
                  const ids = [];
                  const names = [];
                  table.getSelectedRowModel().rows.map((row) => {
                    ids.push(row.original.id);
                    names.push(row.original.name);
                  });
                  handleRemove(ids, names);
                }}
                variant="contained">
                {t("common.common_op_batch_del")}
              </Button>
              {/* <Button color="info" variant="contained">
                                导出节目列表
                            </Button> */}
            </div>
          );
        }}
        // 多选底部提示
        positionToolbarAlertBanner="none"
        // 开启action操作
        enableRowActions
        // action操作位置
        positionActionsColumn="last"
        displayColumnDefOptions={{
          "mrt-row-actions": {
            header: t("common.common_relatedOp"), //change header text
            size: 180, //make actions column wider
          },
        }}
        renderRowActions={(row, index, table) => (
          <Stack direction="row" spacing={1} alignItems="center">
            {/* <Link component="button" underline="none">
                            {t('common.common_op_preview')}
                        </Link> */}
            <Link
              component="button"
              underline="none"
              onClick={() => handleEditProgram(row.row.original)}>
              {t("common.common_op_modify")}
            </Link>
            <Link
              component="button"
              underline="none"
              onClick={() =>
                handleRemove(row.row.original.id, row.row.original.name)
              }>
              {t("common.common_op_del")}
            </Link>
          </Stack>
        )}
      />
      <ProductDialog ref={addProgramRef} />
    </>
  );
};

export default Example;
