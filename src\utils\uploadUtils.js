// import axios from 'axios';
import i18n from 'i18next';
import request from './request';
import SparkMD5 from 'spark-md5';
import { toast } from "react-toastify";

class ChunkUploader {
  constructor(config) {
    this.config = config;
    // 添加停止上传的控制标志
    this.isUploadStopped = false;
  }
  // 新增停止上传的方法
  stopUpload() {
    this.isUploadStopped = true;
  }
  async upload(file, progressCallback) {
    const { uploadUrl, mergeUrl, checkUrl } = this.config;
    const chunkSize = this.config.chunkSize || 2 * 1024 * 1024; // 默认分片大小为2MB
    const chunks = Math.ceil(file.size / chunkSize);
    let uploadedChunks = 0;

    // 计算文件的MD5值
    const md5 = await this.calculateMD5(file, chunkSize);

    const uploadPromises = [];

    for (let currentChunk = 0; currentChunk < chunks; currentChunk++) {
      const start = currentChunk * chunkSize;
      const end = Math.min(start + chunkSize, file.size);
      const chunk = file.slice(start, end);
      const formData = new FormData();
      formData.append('file', chunk);
      formData.append('chunk', currentChunk);
      formData.append('chunks', chunks);
      formData.append('fileName', file.name);
      formData.append('chunkSize', chunk.size)
      formData.append('totalSize', file.size);
      formData.append('identifier', md5); // 传递MD5值到后端
      if (this.isUploadStopped) {
        console.log('上传已停止');
        return Promise.reject('上传已手动停止');
      }
      try {
        await request({
          url: uploadUrl,
          method: 'POST',
          headers: {
            isRetry: true,
            "Content-Type": "multipart/form-data",
          },
          data: formData,
        });
        uploadedChunks++;
        const overallProgress = Math.round((uploadedChunks * 100) / chunks);
        if (progressCallback) {
          progressCallback(overallProgress);
        }
      } catch (error) {
        console.error('分片上传失败');
        return Promise.reject();
      }
      // const uploadPromise = request({
      //   url: uploadUrl,
      //   method: 'POST',
      //   headers: {
      //     isRetry: true,
      //     "Content-Type": "multipart/form-data",
      //   },
      //   data: formData,
      // })
      //   .then(() => {
      //     uploadedChunks++;
      //     const overallProgress = Math.round((uploadedChunks * 100) / chunks);
      //     if (progressCallback) {
      //       progressCallback(overallProgress);
      //     }
      //   })
      //   .catch(() => {
      //     console.error('分片上传失败');
      //     return Promise.reject();
      //   });

      // uploadPromises.push(uploadPromise);
    }

    try {
      await Promise.all(uploadPromises);
    } catch (error) {
      // 处理上传失败逻辑
      console.error('分片上传失败');
      toast.error(i18n.t('upload.warnMsg'))
      return Promise.reject();
    }

    // 检查是否所有分片上传完成
    if (uploadedChunks === chunks) {
      return request({
        url: mergeUrl,
        method: 'post',
        data: { fileName: file.name, md5: md5 }
      })
        .then(res => {
          console.log('文件上传完成');
          return Promise.resolve(res?.data);
        })
        .catch(() => {
          console.error('文件合并失败');
          return Promise.reject();
        });
    } else {
      console.error('未能完成文件上传');
      return Promise.reject();
    }
  }

  async calculateMD5(file, chunkSize) {
    return new Promise((resolve, reject) => {
      const chunks = Math.ceil(file.size / chunkSize);
      const spark = new SparkMD5.ArrayBuffer();

      const fileReader = new FileReader();

      let currentChunk = 0;

      fileReader.onload = function (e) {
        spark.append(e.target.result);
        currentChunk++;

        if (currentChunk < chunks) {
          loadNextChunk();
        } else {
          resolve(spark.end());
        }
      };

      fileReader.onerror = function () {
        reject('文件读取失败');
      };

      function loadNextChunk() {
        const start = currentChunk * chunkSize;
        const end = Math.min(start + chunkSize, file.size);
        const chunk = file.slice(start, end);
        fileReader.readAsArrayBuffer(chunk);
      }

      loadNextChunk();
    });
  }
}

export default ChunkUploader;
