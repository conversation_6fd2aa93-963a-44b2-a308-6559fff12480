/* eslint-disable no-undef */
/* eslint-disable react/prop-types */
import React, {
  useState,
  useRef,
  useMemo,
  useImperativeHandle,
  forwardRef,
} from "react";
import MaterialReactTable from "material-react-table";
import { useTranslation } from "react-i18next";
// 消息提示
import { toast } from "react-toastify";
import { tableI18n } from "@/utils/tableLang";

import {
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  Typography,
  IconButton,
} from "@mui/material";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import CloseIcon from "@mui/icons-material/Close";
import { listScreenByStoreId } from "@/service/api/screen";
import CurrentPlayList from "./retailer/currentPlayList";
import { getCurrentPlaying } from "@/service/api/schedule";
import DictTag from "@/components/DictTag";
import { screenStatus } from "@/dict/commonDict";

const StoreDetail = forwardRef((props, ref) => {
  const [data, setData] = useState({});
  const [open, setOpen] = useState(false);
  const merchantNameRef = useRef("");
  const currentPlayRef = useRef(null);
  const { t } = useTranslation();
  // 用于hash路由保存到sessionStore拼接
  const [isError, setIsError] = useState(false);

  useImperativeHandle(ref, () => ({
    handleOpen,
  }));

  const handleOpen = (storeIdList, merchantName) => {
    setOpen(true);
    merchantNameRef.current = merchantName;
    listScreenByStoreId(storeIdList).then((res) => {
      setData(res.data);
    });
  };

  const handleClose = () => {
    setOpen(false);
  };

  //获取当前播放的广告画面
  const handleGetPlaying = (id) => {
    getCurrentPlaying(id).then((res) => {
      const { data } = res;
      if (data && data.length > 0) {
        currentPlayRef.current.handleOpen(data);
        return;
      }
      toast.error(t("ips.ips_current_time_no_play"));
    });
  };
  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "name",
        header: t("ips.ips_store_client_name"),
        minSize: 80,
        maxSize: 120,
        enableColumnActions: false,
        enableSorting: false,
        Cell: () => {
          return <Typography>{merchantNameRef.current}</Typography>;
        },
      },
      {
        accessorKey: "storeName",
        header: t("ips.ips_store_outlet_name"),
        enableColumnActions: false,
        enableSorting: false,
        minSize: 80,
        maxSize: 120,
      },
      {
        accessorKey: "deviceName",
        header: t("ips.ips_device"),
        enableColumnActions: false,
        enableSorting: false,
        minSize: 80,
        maxSize: 120,
      },
      {
        accessorKey: "status",
        header: t("ips.ips_device_online"),
        enableColumnActions: false,
        enableSorting: false,
        minSize: 90,
        maxSize: 120,
        Cell: ({ cell, row }) => {
          return (
            <DictTag
              dicts={screenStatus}
              fieldName={{ title: "label" }}
              value={row.original.status}
            />
          );
        },
      },
      {
        accessorKey: "address",
        header: t("common.common_location"),
        enableColumnActions: false,
        enableSorting: false,
        minSize: 100,
        maxSize: 120,
      },
      {
        header: t("ips.ips_playing"),
        minSize: 90,
        maxSize: 120,
        enableColumnActions: false,
        enableSorting: false,
        Cell: ({ cell, row }) => {
          return (
            <IconButton
              size="large"
              sx={{ backgroundColor: "#eaf0f5", color: "#7ac143" }}
              onClick={() => handleGetPlaying(row.original.id)}>
              <PlayArrowIcon fontSize="large" />
            </IconButton>
          );
        },
      },
    ],
    []
  );
  return (
    <>
      <Dialog
        maxWidth="lg"
        open={open}
        fullWidth={true}
        onClose={handleClose}
        aria-describedby="alert-dialog-slide-description">
        <DialogTitle>
          {t("common.common_op_detail")}
          <IconButton
            aria-label="close"
            onClick={handleClose}
            sx={{
              position: "absolute",
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500],
            }}>
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <MaterialReactTable
            //右上角工具栏是否显示
            enableTopToolbar={false}
            // 解决列太多宽度太长问题
            enableColumnResizing
            // 初始化状态
            initialState={{ columnVisibility: { createTime: false } }}
            enablePagination={false}
            muiToolbarAlertBannerProps={
              isError
                ? {
                    color: "error",
                    children: t("table.loading_error"),
                  }
                : undefined
            }
            // 固定头部
            enableStickyHeader
            // 处理表格高度
            muiTablePaperProps={{
              elevation: 0,
              sx: {
                borderRadius: "5px",
                border: "1px solid #eaf0f5",
              },
            }}
            muiTableBodyProps={{
              sx: (theme) => ({
                "& tr:nth-of-type(odd)": {
                  backgroundColor: "#eaf0f5",
                },
                // 'tr:hover': {
                //     backgroundColor: '#f9fff0'
                // }
              }),
            }}
            muiTableBodyCellProps={{
              sx: { color: "#61686b", fontSize: "14px" },
            }}
            muiTableHeadRowProps={{
              sx: { backgroundColor: "#eaf0f5" },
            }}
            muiTableBodyRowProps={{
              sx: { "tr td:hover": { backgroundColor: "red" } },
            }}
            muiTableHeadCellProps={{
              sx: { color: "#8f9a9e", fontWeight: 500, fontSize: "14px" },
            }}
            muiTableContainerProps={{ sx: { maxHeight: "500px" } }}
            // 设置背景颜色
            // muiTableBodyCellProps={{ sx: { backgroundColor: 'white' } }}
            muiTableProps={{ sx: { backgroundColor: "white" } }}
            muiBottomToolbarProps={{ sx: { backgroundColor: "white" } }}
            muiTopToolbarProps={{ sx: { backgroundColor: "white" } }}
            // 记得开启这些 tips:否则会出现useEffect 反作用函数发起多次请求点击下一页会出问题
            manualFiltering
            manualPagination
            manualSorting
            // 列定义
            columns={columns}
            // 数据
            data={data}
            // 初始化国际化语言  --- todo: i18n key doc address: https://github.com/KevinVandy/material-react-table/blob/main/packages/material-react-table/src/_locales/en.ts
            localization={tableI18n}
            muiLinearProgressProps={({ isTopToolbar }) => ({
              sx: { display: isTopToolbar ? "block" : "none" },
            })}
          />
        </DialogContent>
      </Dialog>
      <CurrentPlayList ref={currentPlayRef} />
    </>
  );
});

export default StoreDetail;
