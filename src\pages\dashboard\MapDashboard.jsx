import React, { useEffect, useState } from "react";
import {
  <PERSON>rid,
  <PERSON>,
  Button,
  Typography,
  Box,
  Stack,
  InputLabel,
} from "@mui/material";
import Bar<PERSON>hart from "./MapDashboard/BarChart";
import GoogleMap from "./MapDashboard/GoogleMap";
import BaiDuMap from "./MapDashboard/BaiDuMap";
import MerchantSelect from "@/components/MerchantSelect";
// import RegionSelect from "@/components/RegionSelect";
import ZKSearchTree from "@/components/ZKSearchTree/ZKSearchTree.jsx";
import { useTranslation } from "react-i18next";
import { getBoards, getStore } from "@/service/api/mapService";

const MapDashboard = (props) => {
  const { t } = useTranslation();

  const [retailClientId, setRetailClientId] = useState("");
  const [areaId, setAreaId] = useState("");
  const [location, setLocation] = useState("");
  const [totalOutlet, setTotalOutlet] = useState("");
  const [totalDigitalSignage, setTotalDigitalSignage] = useState("");
  const [digitalSignageMonthTrend, setDigitalSignageMonthTrend] = useState([]);
  const [storeData, setStoreData] = useState({});
  const [merchantError, setMerchantError] = useState("");
  const [regionError, setRegionError] = useState("");

  const [localVersion, setLocalVersion] = useState("EN");
  useEffect(() => {
    let version = localStorage.getItem("localVersion");
    if (version === "cn" || version === "CN") {
      setLocalVersion("CN");
    } else {
      setLocalVersion("EN");
    }
  }, []);

  // const regionChange = (e) => {
  //   setRegionError("");
  //   if (e) {
  //     let id = e.id;
  //     setAreaId(id);
  //     setLocation(e.location);
  //   } else {
  //     setAreaId("");
  //   }
  // };

  const regionChange = (e, areaList) => {
    setRegionError("");
    localStorage.setItem("mapSelectedRetailValue", JSON.stringify(e));
    setLocation(e.location);
    setAreaId(e.id);
    // if (areaList && areaList.length > 0) {
    //   const combinedAreaId = areaList.join(",");
    //   setLocation(e.location);
    //   setAreaId(combinedAreaId);
    // } else {
    //   setAreaId("");
    // }
  };

  const retailClientChange = (v) => {
    setRetailClientId(v);
    setMerchantError("");
  };
  useEffect(() => {
    let retailData = localStorage.getItem("mapSelectedRetailValue");
    let areaData = localStorage.getItem("mapSelectedRegionValue");
    if (retailData) {
      setRetailClientId(retailData);
    }
    if (areaData) {
      const area = JSON.parse(areaData);
      setAreaId(area.id);
    }
  }, []);
  const query = () => {
    if (!retailClientId) {
      setMerchantError(t("common.common_please_select_retail"));
      if (!areaId) {
        setRegionError(t("common.common_select_area"));
        return false;
      }
      return false;
    }
    if (!areaId) {
      setRegionError(t("common.common_select_area"));
      return false;
    }
    loadChartData();
    loadMapData();
  };

  const loadChartData = () => {
    getBoards({
      clientId: retailClientId,
      areaId,
    })
      .then((res) => {
        if (res.code === 0) {
          let result = res.data;
          setTotalOutlet(result.totalOutlet);
          setTotalDigitalSignage(result.totalDigitalSignage);
          setDigitalSignageMonthTrend(result.digitalSignageMonthTrend || []);
        } else {
          setTotalOutlet(0);
          setTotalDigitalSignage(0);
          setDigitalSignageMonthTrend([]);
        }
      })
      .catch((e) => {});
  };

  const loadMapData = () => {
    getStore({
      clientId: retailClientId,
      areaId,
    })
      .then((res) => {
        if (res.code === 0) {
          setStoreData(res.data);
        } else {
          setStoreData({});
        }
      })
      .catch((e) => {});
  };

  const retailClear = () => {
    setRetailClientId("");
    setMerchantError("");
  };
  const regionClear = () => {
    setAreaId("");
    setRegionError("");
  };

  return (
    <Grid>
      <Grid
        sx={{
          justifyContent: "space-between",
        }}
        container>
        <Grid
          sx={{
            display: "flex",
            mr: 2,
            mb: 2,
            justifyContent: "space-between",
            alignItems: "center",
          }}>
          <Grid>
            <MerchantSelect
              retailKey="mapSelectedRetailValue"
              label={t("dashboard.retail")}
              onClear={retailClear}
              error={merchantError}
              onChange={retailClientChange}
            />
          </Grid>
          <Grid sx={{ ml: 2 }}>
            {/* <RegionSelect
              regionKey="mapSelectedRegionValue"
              label={t("dashboard.region")}
              onClear={regionClear}
              error={regionError}
              onChange={regionChange}
            /> */}

            <Stack spacing={1}>
              <InputLabel htmlFor="sort-name">
                {t("dashboard.region")}
                <i style={{ color: "red" }}>*</i>
              </InputLabel>
              <ZKSearchTree
                optionValue="id"
                optionLabel="name"
                isContainOldData="1"
                regionKey="mapSelectedRegionValue"
                placeholder={t("common.common_select_area")}
                //   label={t("dashboard.region")}
                onClear={regionClear}
                error={regionError}
                onChange={regionChange}
              />
            </Stack>
          </Grid>
          <Grid sx={{ ml: 2, pt: 3 }}>
            <Button
              type="submit"
              variant="contained"
              onClick={() => {
                query();
              }}>
              {t("dashboard.search")}
            </Button>
          </Grid>
        </Grid>
        <Grid sx={{ display: "flex" }}>
          <Card
            sx={{
              p: 2,
              minWidth: "300px",
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
            }}>
            <Typography variant="h5"> {t("mapDashboard.details")} </Typography>
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                mt: 2,
              }}>
              <Box>
                <Typography variant="subtitle1">
                  {" "}
                  {t("mapDashboard.total")}
                </Typography>
                <Typography variant="subtitle1">
                  {" "}
                  {t("outlet.outlet")}{" "}
                </Typography>
              </Box>
              <Typography variant="h2" sx={{ ml: 4 }}>
                {totalOutlet}
              </Typography>
            </Box>
          </Card>
          <Card sx={{ ml: 2, p: 2, minWidth: "400px", display: "flex" }}>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                justifyContent: "center",
              }}>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  mt: 4,
                }}>
                <Box>
                  <Typography variant="subtitle1">
                    {" "}
                    {t("mapDashboard.total")}{" "}
                  </Typography>
                  <Typography variant="subtitle1">
                    {" "}
                    {t("mapDashboard.digital_signage")}
                  </Typography>
                </Box>
                <Typography variant="h2" sx={{ ml: 4 }}>
                  {totalDigitalSignage}
                </Typography>
              </Box>
            </Box>
            <div
              style={{
                height: "100%",
                width: "2px",
                backgroundColor: "#7ac143",
                margin: "0px 20px",
              }}></div>
            <Box>
              <BarChart MonthTrendData={digitalSignageMonthTrend}></BarChart>
            </Box>
          </Card>
        </Grid>
      </Grid>
      <Grid sx={{ mt: 2 }}>
        {/* {localVersion === "EN" && (
          <GoogleMap center={location} mapData={storeData}></GoogleMap>
        )} */}
        {/* {localVersion === "CN" && (
          <BaiDuMap center={location} mapData={storeData}></BaiDuMap>
        )} */}

        <BaiDuMap center={location} mapData={storeData}></BaiDuMap>
      </Grid>
    </Grid>
  );
};
export default MapDashboard;
