/* eslint-disable react/prop-types */
/* eslint-disable react-hooks/rules-of-hooks */
import React, { useMemo, useState, useEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import MainCard from '@/components/MainCard';
import AddIcon from '@mui/icons-material/Add';
import {
    Box,
    Stack,
    Grid,
    Button,
    Typography,
    InputLabel,
    FormHelperText,
    RadioGroup,
    Radio,
    FormControlLabel,
    Select,
    MenuItem,
    OutlinedInput,
    InputAdornment,
    InputBase,
    Paper,
    TextField
} from '@mui/material';
import * as Yup from 'yup';
import { useFormik } from 'formik';
import IconButton from '@mui/material/IconButton';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import CloseIcon from '@mui/icons-material/Close';
import { styled } from '@mui/material/styles';
import PropTypes from 'prop-types';
import Autocomplete from '@mui/material/Autocomplete';
import ZKSelect from '@/components/ZKSelect';
import { getCountry, getProvince, getCity } from '@/service/api/city';
const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
    PaperProps: {
        style: {
            maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
            width: 250
        }
    }
};
const BootstrapDialog = styled(Dialog)(({ theme }) => ({
    '& .MuiDialogContent-root': {
        padding: theme.spacing(2)
    },
    '& .MuiDialogActions-root': {
        padding: theme.spacing(1)
    }
}));
const BootstrapDialogTitle = (props) => {
    const { children, onClose, ...other } = props;

    return (
        <DialogTitle sx={{ m: 0, p: 2 }} {...other}>
            {children}
            {onClose ? (
                <IconButton
                    aria-label="close"
                    onClick={onClose}
                    sx={{
                        position: 'absolute',
                        right: 8,
                        top: 8,
                        color: (theme) => theme.palette.grey[500]
                    }}
                >
                    <CloseIcon />
                </IconButton>
            ) : null}
        </DialogTitle>
    );
};
BootstrapDialogTitle.propTypes = {
    children: PropTypes.node,
    onClose: PropTypes.func.isRequired
};
// 方向
const locations = [
    {
        label: '顶部',
        value: 1
    },
    {
        label: '底部',
        value: 2
    }
];
const weatherLocations = [
    {
        label: '左上',
        value: 1
    },
    {
        label: '右上',
        value: 2
    },
    {
        label: '左下',
        value: 3
    },
    {
        label: '右下',
        value: 4
    }
];

const AddWeather = forwardRef((props, ref) => {
    const [open, setOpen] = React.useState(false);
    const [countries, setCountries] = React.useState([]);
    const [provinces, setProvinces] = React.useState([]);
    const [cities, setCities] = React.useState([]);
    useImperativeHandle(ref, () => ({
        handleClose,
        handleClickOpen
    }));
    const handleClickOpen = () => {
        weatherFormik.handleReset();
        getCountryOptions();
        setOpen(true);
    };
    const getCountryOptions = () => {
        getCountry().then((res) => {
            setCountries(res.data);
        });
    };
    const handleClose = () => {
        setOpen(false);
    };
    const handelAddWeatherSubmit = (value) => {
        const weather = {
            type: '2',
            name: '天气',
            advertiserName: '',
            lonlatPosition: value.position,
            // todo 天气参数待定
            lonlat: '111.22,123.1'
        };
        props.setMaterialList(weather);
        handleClose();
    };
    //  表单
    const weatherFormik = useFormik({
        initialValues: {
            country: '',
            province: '',
            city: '',
            area: '',
            position: ''
        },
        onSubmit: (values, { setErrors, setStatus, setSubmitting }) => {
            try {
                handelAddWeatherSubmit(values);
                setStatus({ success: false });
                setSubmitting(false);
            } catch (err) {
                setStatus({ success: false });
                setErrors({ submit: err.message });
                setSubmitting(false);
            }
        },
        validationSchema: Yup.object().shape({
            country: Yup.string().required('请选择国家'),
            province: Yup.string().required('请选择省份'),
            city: Yup.string().required('请选择城市'),
            area: Yup.string().required('请选择区域'),
            position: Yup.string().required('请选择放置位置')
        })
    });
    const getProvinceOptions = (value) => {
        getProvince(value).then((res) => {
            setProvinces(res.data);
        });
    };
    const getCityOpions = (value) => {
        getCity(value).then((res) => {
            setCities(res.data);
        });
    };
    return (
        <div>
            <BootstrapDialog fullWidth maxWidth="sm" onClose={handleClose} aria-labelledby="customized-dialog-title" open={open}>
                <form noValidate onSubmit={weatherFormik.handleSubmit}>
                    <DialogContent>
                        <Grid container spacing={3}>
                            <Grid item xs={6}>
                                <Stack spacing={1}>
                                    <InputLabel htmlFor="country">
                                        国家 <i style={{ color: 'red' }}>*</i>
                                    </InputLabel>
                                    <ZKSelect
                                        id="country"
                                        name="country"
                                        onBlur={weatherFormik.handleBlur}
                                        onChange={(e) => {
                                            weatherFormik.handleChange(e);
                                            getProvinceOptions(e.target.value);
                                        }}
                                        placeholder="请选择国家"
                                        value={weatherFormik.values.country}
                                        onClear={() => {
                                            weatherFormik.setFieldValue('country', '');
                                            setProvinces([]);
                                        }}
                                        error={Boolean(weatherFormik.touched.country && weatherFormik.errors.country)}
                                        options={countries}
                                    />
                                    {weatherFormik.touched.country && weatherFormik.errors.country && (
                                        <FormHelperText error id="country-error">
                                            {weatherFormik.errors.country}
                                        </FormHelperText>
                                    )}
                                </Stack>
                            </Grid>
                            <Grid item xs={6}>
                                <Stack spacing={1}>
                                    <InputLabel htmlFor="phone">
                                        省 <i style={{ color: 'red' }}>*</i>
                                    </InputLabel>
                                    <ZKSelect
                                        id="province"
                                        name="province"
                                        onBlur={weatherFormik.handleBlur}
                                        onChange={(e) => {
                                            weatherFormik.handleChange(e);
                                            getCityOpions(e.target.value);
                                        }}
                                        placeholder="请选择省份"
                                        value={weatherFormik.values.province}
                                        onClear={() => {
                                            weatherFormik.setFieldValue('province', '');
                                        }}
                                        error={Boolean(weatherFormik.touched.province && weatherFormik.errors.province)}
                                        options={provinces}
                                    />
                                    {weatherFormik.touched.province && weatherFormik.errors.province && (
                                        <FormHelperText error id="province-error">
                                            {weatherFormik.errors.province}
                                        </FormHelperText>
                                    )}
                                </Stack>
                            </Grid>
                            <Grid item xs={6}>
                                <Stack spacing={1}>
                                    <InputLabel htmlFor="city">
                                        市 <i style={{ color: 'red' }}>*</i>
                                    </InputLabel>
                                    <ZKSelect
                                        id="city"
                                        name="city"
                                        onBlur={weatherFormik.handleBlur}
                                        onChange={weatherFormik.handleChange}
                                        placeholder="请选择市"
                                        value={weatherFormik.values.city}
                                        onClear={() => {
                                            weatherFormik.setFieldValue('city', '');
                                        }}
                                        error={Boolean(weatherFormik.touched.city && weatherFormik.errors.city)}
                                        options={cities}
                                    />
                                    {weatherFormik.touched.city && weatherFormik.errors.city && (
                                        <FormHelperText error id="city-error">
                                            {weatherFormik.errors.city}
                                        </FormHelperText>
                                    )}
                                </Stack>
                            </Grid>
                            <Grid item xs={6}>
                                <Stack spacing={1}>
                                    <InputLabel htmlFor="phone">
                                        县/区 <i style={{ color: 'red' }}>*</i>
                                    </InputLabel>
                                    <ZKSelect
                                        id="area"
                                        name="area"
                                        onBlur={weatherFormik.handleBlur}
                                        onChange={weatherFormik.handleChange}
                                        placeholder="请选择县/区"
                                        value={weatherFormik.values.area}
                                        onClear={() => {
                                            weatherFormik.setFieldValue('area', '');
                                        }}
                                        error={Boolean(weatherFormik.touched.area && weatherFormik.errors.area)}
                                        options={[
                                            { value: '1', label: '集美区' },
                                            { value: '2', label: '思明区' }
                                        ]}
                                    />
                                    {weatherFormik.touched.area && weatherFormik.errors.area && (
                                        <FormHelperText error id="area-error">
                                            {weatherFormik.errors.area}
                                        </FormHelperText>
                                    )}
                                </Stack>
                            </Grid>
                            <Grid item xs={6}>
                                <Stack spacing={1}>
                                    <InputLabel htmlFor="position">
                                        位置 <i style={{ color: 'red' }}>*</i>
                                    </InputLabel>
                                    <ZKSelect
                                        name="position"
                                        onChange={weatherFormik.handleChange}
                                        onBlur={weatherFormik.handleBlur}
                                        value={weatherFormik.values.position}
                                        placeholder="请选择放置位置"
                                        onClear={() => {
                                            weatherFormik.setFieldValue('position', '');
                                        }}
                                        error={Boolean(weatherFormik.touched.position && weatherFormik.errors.position)}
                                        options={locations}
                                    />
                                    {weatherFormik.touched.position && weatherFormik.errors.position && (
                                        <FormHelperText error id="position-error">
                                            {weatherFormik.errors.position}
                                        </FormHelperText>
                                    )}
                                </Stack>
                            </Grid>
                        </Grid>
                    </DialogContent>
                    <DialogActions>
                        <Button color="info" variant="outlined" onClick={handleClose}>
                            取消
                        </Button>
                        <Button disableElevation type="submit" variant="contained" color="primary">
                            保存
                        </Button>
                    </DialogActions>
                </form>
            </BootstrapDialog>
        </div>
    );
});

export default AddWeather;
