import React from "react";
import { forwardRef, useEffect, useRef, useState } from "react";
import {
  Grid,
  InputLabel,
  Stack,
  Typography,
  Alert,
  AlertTitle,
  Box,
} from "@mui/material";
import { Dropzone, FileCard, useFakeProgress } from "@files-ui/react";
import LoadingButton from "@mui/lab/LoadingButton";
import AnimateButton from "@/components/@extended/AnimateButton";
// 消息提示
import { toast } from "react-toastify";
import { updateMaterialCoverImage } from "@/service/api/material";
// i18n
import { useTranslation } from "react-i18next";
import { getFileSize } from "@/utils/zkUtils";
import {
  BootstrapContent,
  BootstrapDialog,
  BootstrapDialogTitle,
} from "@/components/dialog";
import { uploadCompnatLang } from "@/utils/langUtils";

const UploadMaterial = forwardRef((props, ref) => {
  const { t } = useTranslation();
  const supportedFormats = ["gif", "jpg", "jpeg", "png"];
  const dropzoneRef = useRef(null);
  const [isFilesError, setIsFilesError] = useState(false);
  const [files, setFiles] = useState([]);
  const errorStyle = { border: "1px solid #ff4d4f" };
  const progress = useFakeProgress();
  const [disabled, setDisabled] = useState(false);
  const [uploadLoading, setUploadLoading] = useState(false);
  const [errorMsg, setErrorMsg] = useState(undefined);
  //上传进度条
  // 支持的格式，用于正则校验
  const suffix = `(gif|jpg|png|jpeg)`;
  // 上传素材弹窗
  const [show, setShow] = React.useState(false);
  const [materialId, setMaterialId] = useState(undefined);

  React.useImperativeHandle(ref, () => ({
    handleClose,
    handleOpen,
  }));

  // 关闭弹窗
  const handleClose = () => {
    setFiles([]);
    setShow(false);
  };

  // 打开弹窗
  const handleOpen = (id) => {
    console.log("idddd", id);
    setMaterialId(id);
    setShow(true);
  };

  const handleSubmit = async (id) => {
    // 校验文件
    if (files.length === 0) {
      setErrorMsg(t("common.common_plese_select_file"));
      setIsFilesError(true);
      return;
    }
    setIsFilesError(false);
    // 设置按钮等于加载
    setUploadLoading(true);
    // 设置禁用
    setDisabled(true);
    const values = {
      id: id,
    };
    values.progressBar = Math.floor(Math.random() * 100) + 1;
    values.file = files[0].file;
    files[0].uploadStatus = "uploading";
    files[0].progress = progress;
    const errorFiles = [];
    await updateMaterialCoverImage(values)
      .then((res) => {
        files[0].progress = 100;
        files[0].uploadStatus = "success";
        // 设置按钮等于加载
        setUploadLoading(false);
        // 设置禁用
        setDisabled(false);
        toast.success(t("common.common_upload_success"));
        handleClose();
        props.callback();
      })
      .catch((error) => {
        files[0].uploadStatus = "error";
        files[0].uploadMessage = [
          typeof error === "string" ? error : error.message,
        ];
        files[0].errors = [typeof error === "string" ? error : error.message];
        // 设置按钮等于加载
        setUploadLoading(false);
        // 设置禁用
        setDisabled(false);
      });
  };

  const updateFiles = (incommingFiles) => {
    setIsFilesError(false);
    setErrorMsg(undefined);
    setFiles(incommingFiles);
  };

  const removeFile = (id) => {
    setFiles(files.filter((x) => x.id !== id));
  };

  return (
    <>
      <BootstrapDialog
        open={show}
        fullWidth
        maxWidth="xs"
        onClose={handleClose}
        aria-describedby="alert-dialog-slide-description">
        <BootstrapDialogTitle
          id="customized-dialog-title"
          onClose={handleClose}>
          <Typography variant="h4" component="p">
            {t("common.common_cover_upload")}
          </Typography>
        </BootstrapDialogTitle>
        <BootstrapContent>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <InputLabel htmlFor="group-material">
                  {t("common.common_select_image")}
                  <i style={{ color: "red" }}>*</i>
                </InputLabel>
                <Dropzone
                  localization={() => uploadCompnatLang()}
                  ref={dropzoneRef}
                  style={{ ...(isFilesError ? errorStyle : {}) }}
                  autoClean
                  fakeUpload
                  //   uploading={true}
                  onUploadStart={(uploadAbleFiles) => {
                    console.log(uploadAbleFiles);
                  }}
                  uploadConfig={{ url: "#", autoUpload: false }}
                  disabled={files.length >= 1 ? true : false}
                  footer={false}
                  accept={"image/png, image/jpeg, image/gif, image/jpg"}
                  validator={(file) => {
                    let fileName = file.name.substring(
                      0,
                      file.name.lastIndexOf(".")
                    );
                    // 校验文件类型名称无法同时上传相同的
                    for (let i = 0; i < files.length; i++) {
                      let filestemName = files[i].name.substring(
                        0,
                        file.name.lastIndexOf(".")
                      );
                      if (fileName === filestemName) {
                        setErrorMsg(
                          t("common.common_upload_name_exist", {
                            name: file.name,
                          })
                        );
                        return {
                          valid: false,
                          errors: [
                            t("common.common_upload_name_exist", {
                              name: file.name,
                            }),
                          ],
                        };
                      }
                    }
                    const fileSize = getFileSize(file.size);
                    // eslint-disable-next-line no-useless-escape
                    let regular = new RegExp(`.*\.${suffix}`);
                    if (!regular.test(file.name.toLocaleLowerCase())) {
                      setErrorMsg(
                        t("common.common_upload_type_not_support", {
                          type: file.type,
                        })
                      );
                      return {
                        valid: false,
                        errors: [
                          t("common.common_upload_type_not_support", {
                            type: file.type,
                          }),
                        ],
                      };
                    } else if (fileSize > 100) {
                      setErrorMsg(
                        t("common.common_upload_file_size_max", {
                          fileSize: file.size,
                        })
                      );
                      return {
                        valid: false,
                        errors: [
                          t("common.common_upload_file_size_max", {
                            fileSize: file.size,
                          }),
                        ],
                      };
                    } else {
                      return { valid: true };
                    }
                    // return regular.test(file.name) ? true : null;
                  }}
                  header={false}
                  onChange={updateFiles}
                  value={files}
                  maxFileSize={1 * 1024 * 1024 * 1024}
                  maxFiles={1}
                  // accept={"image/*"}
                >
                  <Stack
                    direction="column"
                    justifyContent="center"
                    alignItems="center"
                    spacing={1}>
                    <svg
                      t="1688974869714"
                      className="icon"
                      viewBox="0 0 1024 1024"
                      version="1.1"
                      xmlns="http://www.w3.org/2000/svg"
                      p-id="2826"
                      width="80"
                      height="80">
                      <path
                        d="M1024 640.192C1024 782.912 919.872 896 787.648 896h-512C123.904 896 0 761.6 0 597.504 0 451.968 94.656 331.52 226.432 302.976 284.16 195.456 391.808 128 512 128c152.32 0 282.112 108.416 323.392 261.12C941.888 413.44 1024 519.04 1024 640.192z m-341.312-139.84L512 314.24 341.312 500.48h341.376z m-213.376 0v256h85.376v-256H469.312z"
                        fill="#bfbfbf"
                        p-id="2827"></path>
                    </svg>
                    <Typography>
                      {t("common.common_drage_branch_file")}
                    </Typography>
                  </Stack>
                </Dropzone>
                {errorMsg && (
                  <Alert
                    severity="error"
                    onClose={() => {
                      setErrorMsg(undefined);
                    }}
                    sx={{ marginBottom: 1 }}>
                    {errorMsg}
                  </Alert>
                )}
              </Stack>
            </Grid>
            <Grid item xs={12}>
              <Alert severity="warning">
                <AlertTitle>{t("common.common_prompt_title")}</AlertTitle>
                <div
                  dangerouslySetInnerHTML={{
                    __html: t("common.common_upload_image_tips"),
                  }}
                />
                {supportedFormats.join(",")}
              </Alert>
            </Grid>
            <Grid item xs={12}>
              <Box key="preview" className="preview">
                {files.map((file, index) => (
                  <FileCard
                    style={{
                      width: "100%",
                      boxShadow: "none",
                      border: "1px solid rgba(0, 0, 0, 0.1)",
                    }}
                    key={index}
                    id={index}
                    progress={progress}
                    {...file}
                    preview
                    onDelete={removeFile}
                  />
                ))}
              </Box>
            </Grid>
            <Grid item xs={12}>
              <AnimateButton>
                <LoadingButton
                  loading={uploadLoading}
                  disableElevation
                  disabled={disabled}
                  fullWidth
                  onClick={() => {
                    handleSubmit(materialId);
                  }}
                  size="large"
                  type="submit"
                  variant="contained"
                  color="primary">
                  {t("common.common_upload")}
                </LoadingButton>
              </AnimateButton>
            </Grid>
          </Grid>
        </BootstrapContent>
      </BootstrapDialog>
      {/* <ProgressBarDiglog
        ref={progressBarDiglogRef}
        progressBar={progressBar}
      ></ProgressBarDiglog> */}
    </>
  );
});

export default UploadMaterial;
