import { useEffect, useMemo, useRef, useState } from "react";
import MaterialReactTable from "material-react-table";
import {
  Button,
  Stack,
  Typography,
  Tooltip,
  Link,
  Grid,
  IconButton,
  TextField,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import { audit } from "@/service/api/ipsAudit";
// api
import {
  listByPage,
  removeSchedule,
  publishCommonSchedule,
  onClickPublishSchedule,
  publishLinkSchedule,
} from "@/service/api/schedule";
import { getScreenByDeviceId } from "@/service/api/screen";
// 消息提示
import { toast } from "react-toastify";
import { tableI18n } from "@/utils/tableLang";
import { useFormik } from "formik";
// 删除确认
import { useConfirm } from "@/components/zkconfirm";
// i18n
import { useTranslation } from "react-i18next";
// api
import { useNavigate } from "react-router-dom";
import CommonScheduleDetail from "./commonScheduleDetail";
import LinkScheduleDetail from "./linkScheduleDetail";
import MainCard from "@/components/MainCard";
import { removeEmpty } from "@/utils/StringUtils";
import ZKSelect from "@/components/ZKSelect";
import AuthButton from "@/components/AuthButton";
import { scheduleType, publishStatus, scheduleStatus } from "@/dict/commonDict";
import DictTag from "@/components/DictTag";
import CopySchedule from "./CopySchedule";
const ProgramScheduleTableList = () => {
  const navigate = useNavigate();
  const confirm = useConfirm();
  const { t } = useTranslation();

  const [rowSelection, setRowSelection] = useState([]);
  const deviceTypeFlag = useRef(false);
  // 表格数据
  const [data, setData] = useState([]);

  // //计算监听选中的行
  // useEffect(() => {
  //   //当前页的ID
  //   let ids = data.map((item) => {
  //     return item.id;
  //   });

  //   //其他页面选中的记录
  //   let ortherPageSelectId = allRowSelection.filter((item) => {
  //     if (ids.indexOf(item.id) > -1) {
  //       return false;
  //     } else {
  //       return true;
  //     }
  //   });

  //   //当前页面选中的ID
  //   let currentPageSelectId = Object.keys(rowSelection).map((item) => {
  //     return data[item];
  //   });

  //   setAllRowSelection([...ortherPageSelectId, ...currentPageSelectId]);

  // }, [rowSelection]);

  // useEffect(() => {
  //   let allSelectIds = allRowSelection.map((item) => {
  //     return item.id;
  //   });
  //   //计算当前选中行
  //   let selectRow = data
  //     .map((item, index) => {
  //       if (allSelectIds.indexOf(item.id) > -1) {
  //         return {
  //           [index]: true,
  //         };
  //       } else {
  //         return null;
  //       }
  //     })
  //     .filter((item) => {
  //       if (item) {
  //         return true;
  //       } else {
  //         return false;
  //       }
  //     });
  //   setRowSelection(selectRow);
  // }, [data]);

  //是否显示更多的清单列表
  const showMoreRef = useRef(false);
  const commonScheduleDetailRef = useRef(null);
  const linkScheduleDetailRef = useRef(null);
  const [isError, setIsError] = useState(false);
  const [copyOpen, setCopyOpen] = useState(false);
  const [currentRowId, setCurrentRowId] = useState(undefined);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);

  // 分页的参数
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);
  // 排序参数
  const [sorting, setSorting] = useState([{ id: "createTime", desc: true }]);
  // 查询参数
  const requestParams = useRef(null);
  // 构建参数
  const buildParams = () => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      orderProp: sorting[0]?.id,
      orderBy: sorting[0]?.desc == true ? "DESC" : "ASC",
      ...requestParams.current,
    };

    return params;
  };
  // 获取数据
  const getTableData = async () => {
    if (!data.length) {
      setIsLoading(true);
    } else {
      setIsRefetching(true);
    }
    await listByPage(buildParams())
      .then((res) => {
        setData(res.data.data);
        // 设置总记录数
        setRowCount(res.data.total);
        setIsLoading(false);
        setIsRefetching(false);
      })
      .catch((err) => {
        setIsError(true);
        setIsLoading(false);
        setIsRefetching(false);
      });
  };
  useEffect(() => {
    // 发请求
    getTableData();
  }, [pagination.pageIndex, pagination.pageSize]);

  const handleShowMore = (value) => {
    showMoreRef.current = value;
  };
  // 列字段
  const columns = useMemo(
    () => [
      // {
      //   accessorKey: "id", //access nested data with dot notation
      //   header: 'ID',
      //   enableColumnActions: false,
      //   enableSorting: false,
      // },
      {
        accessorKey: "name", //access nested data with dot notation
        header: t("ips.ips_scheduling"),
        enableColumnActions: false,
        enableSorting: false,
        Cell: ({ cell, row }) => {
          return (
            <Tooltip title={row.original.name} placement="top">
              <Typography className="textSpace">{row.original.name}</Typography>
            </Tooltip>
          );
        },
      },
      {
        accessorKey: "screenName", //access nested data with dot notation
        header: t("ips.ips_screen_name"),
        enableColumnActions: false,
        enableSorting: false,
        minSize: 150,
        maxSize: 200,
        Cell: ({ cell, row }) => {
          return (
            <Tooltip title={row.original.screenName} placement="top">
              <Typography className="textSpace">
                {row.original.screenName}
              </Typography>
            </Tooltip>
          );
        },
      },
      {
        accessorKey: "type", //access nested data with dot notation
        header: t("ips.ips_schedule_type"),
        minSize: 80,
        filterVariant: "select",
        enableColumnActions: false,
        enableSorting: false,
        maxSize: 100,
        size: 140,
        Cell: ({ row }) => {
          return (
            <DictTag
              dicts={scheduleType}
              value={row.original.type}
              fieldName={{ value: "value", title: "label" }}
            />
          );
        },
      },
      {
        accessorKey: "dateBetween", //access nested data with dot notation
        header: t("ips.ips_startDate_endDate"),
        minSize: 150,
        enableColumnActions: false,
        enableSorting: false,
        maxSize: 150,
        size: 140,
      },
      {
        accessorKey: "schedulePlayList", //access nested data with dot notation
        header: t("ips.ips_schedule_list"),
        minSize: 200,
        enableColumnActions: false,
        enableSorting: false,
        maxSize: 200,
        Cell: ({ row }) => {
          let playListNames = row.original.playListNames;
          if (!playListNames) {
            return [];
          }
          if (playListNames.length > 5) {
            if (showMoreRef.current) {
              return (
                <Typography>
                  {playListNames.map((item, index) => (
                    <>
                      {index > 0 &&
                      item.indexOf(":") >= 0 &&
                      row.original.type === "1" ? (
                        <br />
                      ) : null}
                      <Tooltip title={item} placement="top">
                        <Typography className="textSpace"> {item}</Typography>
                      </Tooltip>

                      {index === playListNames.length - 1 ? (
                        <IconButton
                          onClick={() => handleShowMore(false)}
                          size="middle">
                          <ExpandLessIcon />
                          <Typography>
                            {t("common.common_form_packup")}
                          </Typography>
                        </IconButton>
                      ) : null}
                    </>
                  ))}
                </Typography>
              );
            } else {
              let sliceArray = playListNames.slice(0, 5);
              return (
                <Typography>
                  {sliceArray.map((item, index) => (
                    <>
                      {index > 0 &&
                      item.indexOf(":") >= 0 &&
                      row.original.type === "1" ? (
                        <br />
                      ) : null}
                      <Tooltip title={item} placement="top">
                        <Typography className="textSpace"> {item}</Typography>
                      </Tooltip>

                      {index === sliceArray.length - 1 ? (
                        <IconButton
                          onClick={() => handleShowMore(true)}
                          size="middle">
                          <ExpandMoreIcon />
                          <Typography>{t("common.common_more")}</Typography>
                        </IconButton>
                      ) : null}
                    </>
                  ))}
                </Typography>
              );
            }
          } else {
            return (
              <Typography>
                {playListNames.map((item, index) => (
                  <>
                    {index > 0 &&
                    item.indexOf(":") > 0 &&
                    row.original.type === "1" ? (
                      <br />
                    ) : null}
                    <Tooltip title={item} placement="top">
                      <Typography className="textSpace"> {item}</Typography>
                    </Tooltip>
                  </>
                ))}
              </Typography>
            );
          }
        },
      },
      {
        accessorKey: "playStatus",
        header: t("ips.ips_schedule_play_status"),
        size: 80,
        enableColumnActions: false,
        enableSorting: false,
        Cell: ({ row }) => {
          return (
            <DictTag
              dicts={scheduleStatus}
              value={row.original.playStatus}
              fieldName={{ value: "value", title: "label" }}
            />
          );
        },
      },
      {
        accessorKey: "status",
        header: t("common.common_status"),
        size: 80,
        enableColumnActions: false,
        enableSorting: false,
        Cell: ({ row }) => {
          return (
            <DictTag
              dicts={publishStatus}
              value={row.original.status}
              fieldName={{ value: "value", title: "label" }}
            />
          );
        },
      },
    ],
    []
  );

  // 构建删除对象集合
  const buildRemoveObject = (scheduleList) => {
    let newObjectList = [];
    scheduleList.forEach((schedule) => {
      let newObject = {
        scheduleId: schedule.id,
        scheduleDeviceId: schedule.deviceId,
        schedulePlayListIds: schedule.playListIdList,
      };
      newObjectList.push(newObject);
    });

    let params = {
      scheduleRemoveList: newObjectList,
    };

    return params;
  };
  // 批量删除
  // 删除节目
  const handleRemove = (scheduleList, names) => {
    //判断排期是否正在使用，如果在使用则提示是否删除
    confirm({
      title: t("message.messageBox_title"),
      confirmationText: t("common.common_edit_ok"),
      cancellationText: t("common.common_edit_cancel"),
      description: t("ips.ips_scheduling_del", { id: names }),
    }).then(() => {
      removeSchedule(buildRemoveObject(scheduleList)).then((res) => {
        toast.success(res.message);
        // 重新请求数据
        getTableData();
        //重置选中行框
        setRowSelection([]);
      });
    });
  };

  const handleOneClickPublish = (ids, names) => {
    // 映射数组并在每个名字后添加逗号
    const namesWithComma = names.map((name) => name + ", ");
    // 如果数组不为空，则移除最后一个逗号
    let description;
    if (namesWithComma.length > 0) {
      description = namesWithComma.join("").slice(0, -2); // 移除最后一个逗号和空格
    } else {
      description = ""; // 如果数组为空，则直接设置为空字符串
    }
    confirm({
      title: t("message.messageBox_title"),
      confirmationText: t("common.common_edit_ok"),
      cancellationText: t("common.common_edit_cancel"),
      description:
        t("common.common_publish_scheduleName") +
        " <b>" +
        description +
        "</b> <br />" +
        t("common.common_oneclick_description_tips") +
        "<br />" +
        t("ips.ips_oneClick_schedule_L101_valid"),
    }).then(() => {
      const loading = toast.loading(t("dictData.dict_convert"));
      setTimeout(() => {
        onClickPublishSchedule(ids).then((res) => {
          toast.remove(loading);
          toast.success(res?.message);
          // 重新请求数据
          getTableData();
          //重置选中行框
          setRowSelection([]);
        });
      }, 2000);
    });
  };

  //节目发布
  const handlePublish = (id, name, type, deviceId) => {
    getScreenByDeviceId(deviceId).then((res) => {
      if (res.data.screenModel == "LCD-L101") {
        deviceTypeFlag.current = true;
      } else {
        deviceTypeFlag.current = false;
      }
    });
    //判断排期是否正在使用，如果在使用则提示是否删除
    confirm({
      title: t("message.messageBox_title"),
      confirmationText: t("common.common_edit_ok"),
      cancellationText: t("common.common_edit_cancel"),
      description: t("ips.ips_scheduling_publish", { id: name }),
    }).then(() => {
      if (type === "0") {
        publishCommonSchedule(id).then((res) => {
          if (deviceTypeFlag.current) {
            toast.success(t("ips.ips_schedule_LCD_L101_Invalid"));
          } else {
            toast.success(res.message);
          }
          // 重新请求数据
          getTableData();
          //重置选中行框
          setRowSelection([]);
        });
      }
      if (type === "1") {
        publishLinkSchedule(id).then((res) => {
          toast.success(res.message);
          // 重新请求数据
          getTableData();
          //重置选中行框
          setRowSelection([]);
        });
      }
    });
  };

  // 审核弹窗
  const handleAuditClose = () => {
    // setOpenAudit(false);
  };
  // 排期审核
  const handleAuditSchedule = (values) => {
    console.log(values);
    audit(values).then((res) => {
      toast.success(res.message);
      handleAuditClose();
      // 重新请求数据
      getTableData();
    });
  };

  // 查询表单
  const queryFormik = useFormik({
    initialValues: {
      name: "",
      screenName: "",
      type: "",
    },
    onSubmit: async (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        // 拷贝value值
        const tempValue = { ...values };
        await removeEmpty(tempValue);
        requestParams.current = tempValue;
        setPagination({
          pageIndex: 0,
          pageSize: 10,
        });
        setRowSelection([]);
        getTableData();

        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
  });
  // 重置查询
  const resetQuery = () => {
    // 重置表单
    queryFormik.resetForm();
    requestParams.current = null;
    setPagination({
      pageIndex: 0,
      pageSize: 10,
    });
    setRowSelection([]);
    getTableData();
  };
  return (
    <>
      <MainCard style={{ marginBottom: "10px" }}>
        <form noValidate onSubmit={queryFormik.handleSubmit}>
          <Grid
            container
            direction="row"
            justifyContent="flex-start"
            alignItems="center"
            spacing={5}>
            <Grid item md={3} lg={3} xs={12}>
              <TextField
                label={t("ips.ips_scheduling")}
                value={queryFormik.values.name}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                size="small"
                type="text"
                name="name"
                fullWidth
                placeholder={t("common.common_input_scheduling_name")}
              />
            </Grid>
            <Grid item md={3} lg={3} xs={12}>
              <TextField
                label={t("ips.ips_screen_name")}
                value={queryFormik.values.screenName}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                size="small"
                type="text"
                name="screenName"
                fullWidth
                placeholder={t("common.common_input_screen_name")}
              />
            </Grid>
            <Grid item md={3} lg={2} xs={12}>
              <ZKSelect
                id="type"
                size={"small"}
                name="type"
                value={queryFormik.values.type}
                options={scheduleType}
                onClear={() => {
                  queryFormik.setFieldValue("type", "");
                }}
                onBlur={queryFormik.handleBlur}
                onChange={queryFormik.handleChange}
                type="text"
                menuWidth={150}
                placeholder={t("common.common_select_schedule")}
              />
            </Grid>
            <Grid item md={3} lg={2} xs={12}>
              <ZKSelect
                id="status"
                size={"small"}
                name="status"
                value={queryFormik.values.status}
                options={publishStatus}
                onClear={() => {
                  queryFormik.setFieldValue("status", "");
                }}
                onBlur={queryFormik.handleBlur}
                onChange={queryFormik.handleChange}
                type="text"
                menuWidth={150}
                placeholder={t("common.common_status")}
              />
            </Grid>
            <Grid item md={3} lg={2} xs={12}>
              <Stack
                direction="row"
                justifyContent="flex-start"
                alignItems="flex-start"
                spacing={2}>
                <Button
                  disableElevation
                  type="submit"
                  variant="contained"
                  size="small">
                  {t("common.common_table_query")}
                </Button>
                <Button
                  variant="outlined"
                  onClick={resetQuery}
                  color="info"
                  size="small"
                  sx={{
                    minWidth: "90px",
                  }}>
                  {t("common.common_op_reset")}
                </Button>
              </Stack>
            </Grid>
          </Grid>
        </form>
      </MainCard>

      <MaterialReactTable
        // table 状态
        state={{
          // 加载状态
          isLoading,
          // 分页参数
          pagination,
          // 重新拉取
          showProgressBars: isRefetching,
          showAlertBanner: isError,

          sorting,
          rowSelection,
        }}
        renderToolbarInternalActions={({ table }) => <></>}
        muiTablePaperProps={{
          elevation: 0,
          sx: {
            borderRadius: "5px",
            border: "1px solid #f0f0f0",
          },
        }}
        muiTableHeadRowProps={{
          sx: { backgroundColor: "#fafafa", boxShadow: "none" },
        }}
        getRowId={(row) => {
          if (row.id) {
            return JSON.stringify({
              id: row.id,
              name: row.name,
              deviceId: row.deviceId,
              playListIdList: row.playListIdList,
            });
          }
          return row.id;
        }}
        // 关闭过滤搜素
        enableColumnFilters={true}
        // 关闭排序
        enableSorting={false}
        // 布局方式
        layoutMode="grid"
        // 开启列对齐
        muiTableHeadCellProps={{
          sx: {
            "& .Mui-TableHeadCell-Content": {
              justifyContent: "space-between",
            },
          },
        }}
        // 解决列太多宽度太长问题
        enableColumnResizing
        // 初始化状态
        initialState={{ columnVisibility: { createTime: false } }}
        muiToolbarAlertBannerProps={
          isError
            ? {
                color: "error",
                children: "Error loading data",
              }
            : undefined
        }
        //行选中
        onRowSelectionChange={setRowSelection}
        muiTableBodyRowProps={({ row }) => ({
          onClick: row.getToggleSelectedHandler(),
          sx: { cursor: "pointer" },
        })}
        // 开启多选
        enableRowSelection
        // 列数
        rowCount={rowCount}
        // 固定头部
        enableStickyHeader
        // 处理表格高度
        muiTableContainerProps={{ sx: { maxHeight: "620px" } }}
        // 设置背景颜色
        muiTableBodyCellProps={{ sx: { backgroundColor: "white" } }}
        muiTableProps={{
          sx: { backgroundColor: "white", tableLayout: "fixed" },
        }}
        muiBottomToolbarProps={{ sx: { backgroundColor: "white" } }}
        muiTopToolbarProps={{ sx: { backgroundColor: "white" } }}
        // 分页回调函数
        onPaginationChange={setPagination}
        // 记得开启这些 tips:否则会出现useEffect 反作用函数发起多次请求点击下一页会出问题
        manualFiltering
        manualPagination
        manualSorting
        // 排序
        onSortingChange={setSorting}
        // 开启分页
        enablePagination
        // 列定义
        columns={columns}
        // 数据
        data={data}
        // 初始化国际化语言  --- todo: i18n key doc address: https://github.com/KevinVandy/material-react-table/blob/main/packages/material-react-table/src/_locales/en.ts
        localization={tableI18n}
        // 自定义表头按钮
        renderTopToolbarCustomActions={({ table }) => {
          return (
            <Stack direction="row" spacing={1} alignItems="center">
              <AuthButton button="sd:playlist:schedule:save">
                <Button
                  variant="contained"
                  onClick={() =>
                    navigate("/playList/schedule/commonSchedule/add")
                  }>
                  {t("menu.button_scheduling_add_common")}
                </Button>
              </AuthButton>

              <AuthButton button="sd:playlist:schedule:save:link_schedule">
                <Button
                  variant="contained"
                  onClick={() =>
                    navigate("/playList/schedule/linkSchedule/add")
                  }>
                  {t("menu.button_scheduling_add_link")}
                </Button>
              </AuthButton>
              <AuthButton button="sd:playlist:schedule:publish:one_click">
                <Button
                  variant="outlined"
                  color="error"
                  // color="wronning"
                  disabled={Object.keys(rowSelection).length < 1}
                  onClick={() => {
                    let scheduleObjectListIds = [];
                    let names = [];
                    Object.keys(rowSelection).map((key) => {
                      let row = JSON.parse(key);
                      scheduleObjectListIds.push(row.id);
                      names.push(row.name);
                    });
                    handleOneClickPublish(scheduleObjectListIds, names);
                  }}>
                  {t("server.onclickpublish")}
                </Button>
              </AuthButton>
              <AuthButton button="sd:playlist:schedule:delete">
                <Button
                  variant="contained"
                  color="secondary"
                  disabled={Object.keys(rowSelection).length < 1}
                  onClick={() => {
                    let names = [];
                    Object.keys(rowSelection).map((key) => {
                      let row = JSON.parse(key);
                      names.push(row.name);
                    });
                    let deleteList = Object.keys(rowSelection).map((key) => {
                      let row = JSON.parse(key);
                      return row;
                    });

                    handleRemove(deleteList, names);
                  }}>
                  {t("common.common_op_batch_del")}
                </Button>
              </AuthButton>
            </Stack>
          );
        }}
        // 多选底部提示
        positionToolbarAlertBanner="none"
        // 开启action操作
        enableRowActions
        // action操作位置
        positionActionsColumn="last"
        displayColumnDefOptions={{
          "mrt-row-actions": {
            header: t("common.common_relatedOp"), //change header text
            size: 300, //make actions column wider
          },
        }}
        renderRowActions={(row, index, table) => (
          <Stack direction="row" spacing={2} alignItems="center">
            <AuthButton button="sd:playlist:schedule:detail">
              <Link
                component="button"
                underline="none"
                onClick={() => {
                  {
                    row.row.original.type === "1"
                      ? linkScheduleDetailRef.current.handleOpen(
                          row.row.original.id
                        )
                      : commonScheduleDetailRef.current.handleOpen(
                          row.row.original.id
                        );
                  }
                }}>
                {t("common.common_op_detail")}
              </Link>
            </AuthButton>
            <AuthButton button="sd:playlist:schedule:update">
              <Link
                component="button"
                underline="none"
                onClick={() => {
                  {
                    row.row.original.type === "1"
                      ? navigate(
                          `/playList/schedule/linkSchedule/update?id=${row.row.original.id}`
                        )
                      : navigate(
                          `/playList/schedule/commonSchedule/update?id=${row.row.original.id}`
                        );
                  }
                }}>
                {t("common.common_op_modify")}
              </Link>
            </AuthButton>

            {(row.row.original.status === "1" ||
              row.row.original.status === "3") && (
              <AuthButton button="sd:playlist:schedule:publish">
                <Link
                  component="button"
                  underline="none"
                  onClick={() => {
                    handlePublish(
                      row.row.original.id,
                      row.row.original.name,
                      row.row.original.type,
                      row.row.original.deviceId
                    );
                  }}>
                  {t("common.common_op_publish")}
                </Link>
              </AuthButton>
            )}
            <Link
              component="button"
              underline="none"
              onClick={() => {
                setCopyOpen(true);
                setCurrentRowId(row.row.original.id);
              }}>
              {t("common.common_schedule_copy")}
            </Link>
            <AuthButton button="sd:playlist:schedule:delete">
              <Link
                component="button"
                underline="none"
                color="error"
                onClick={() => {
                  let scheduleList = [];
                  scheduleList.push(row.row.original);
                  handleRemove(scheduleList, row.row.original.name);
                }}>
                {t("common.common_op_del")}
              </Link>
            </AuthButton>
          </Stack>
        )}
      />
      <CommonScheduleDetail ref={commonScheduleDetailRef} />
      <LinkScheduleDetail ref={linkScheduleDetailRef} />
      <CopySchedule
        open={copyOpen}
        id={currentRowId}
        onClose={() => {
          setCopyOpen(false);
          setCurrentRowId(undefined);
        }}
        refresh={() => {
          //刷新列表
          getTableData();
        }}
      />
    </>
  );
};

export default ProgramScheduleTableList;
