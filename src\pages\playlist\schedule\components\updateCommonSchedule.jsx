/* eslint-disable react/prop-types */
import React, { useEffect, useState, useRef } from "react";
import {
  Grid,
  Box,
  Button,
  OutlinedInput,
  Stack,
  InputAdornment,
  InputBase,
  FormHelperText,
  Typography,
  InputLabel,
} from "@mui/material";
import LoadingButton from "@mui/lab/LoadingButton";
import { toast } from "react-toastify";
import dayjs from "dayjs";
import Popover from "@mui/material/Popover";
import TimePickert from "@/components/zktime";
import * as Yup from "yup";
import { useFormik } from "formik";
import AdvancedDialog from "./AdvancedModal";

// api
import {
  getScheduleInfo,
  updateCommonScheduleInfo,
} from "@/service/api/schedule";

import "@amir04lm26/react-modern-calendar-date-picker/lib/DatePicker.css";
import { Calendar, utils } from "@amir04lm26/react-modern-calendar-date-picker";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import { getCalendarLocales } from "@/utils/calendarLocales";
// i18n
import { useTranslation } from "react-i18next";
import DateSvg from "./DateSvg";
import MainCard from "@/components/MainCard";
import { useNavigate, useSearchParams } from "react-router-dom";
import moment from "moment";
import AddPlayList from "./AddPlayList";
import TimeButtonSelect from "./TimeButtonSelect";
import Container from "@mui/material/Container";
import { getPlayListInfo } from "@/service/api/playList";
import { getScreenInfo } from "@/service/api/screen";
export default function UpdateCommonSchedule(props) {
  const addPlayListRef = useRef(null);
  const timeButtonSelectRef = useRef(null);
  const [screenInfoValue, setScreenInfoValue] = useState(null);
  const statMaterialSizeRef = useRef(null);
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useSearchParams();
  const [anchorEl, setAnchorEl] = useState(null);
  const [advancedOpen, setAdvancedOpen] = useState(false);
  const [advancedData, setAdvancedData] = useState([]);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);

  //清单选择对象
  const inventoryObject = {
    playListId: "",
    playListName: "",
    startTime: "",
    stopTime: "",
  };

  //获取排期数据
  const handleGetScheduleInfo = async () => {
    await getScheduleInfo(search.get("id")).then((res) => {
      const result = res.data;
      setFormData(result);
      //只处理单屏情况,联屏无需处理
      if (result.type === "0") {
        getScreenInfo(result.deviceId).then((res) => {
          setScreenInfoValue(res.data);
        });
      }
      const monthWeekDayMap =
        timeButtonSelectRef.current.getDaysWeeksAndMonthsDate(
          result.startDate,
          result.stopDate
        );
      timeButtonSelectRef.current.handleSelectedDate(result);
      timeButtonSelectRef.current.handleChangeDaysAble(
        result.playWeeksNum,
        result.playMonths,
        monthWeekDayMap,
        result.playDays
      );
    });
  };
  useEffect(() => {
    handleGetScheduleInfo();
  }, []);

  // 表单赋值
  const setFormData = (data) => {
    scheduleForm.setValues(
      {
        id: data.id,
        name: data.name,
        inventoryList: data.inventoryList,
        startDate: data.startDate,
        stopDate: data.stopDate,
        playWeeks: data.playWeeksNum,
        playDays: data.playDays,
        playMonths: data.playMonths,
        status: data.status,
        screenIds: data.deviceId,
      },
      true
    );
  };

  const scheduleForm = useFormik({
    initialValues: {
      name: "",
      playDirection: "0",
      startDate: "",
      stopDate: "",
      screenIds: "",
      inventoryList: [],
    },
    onSubmit: async (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        handelTableSubmit(values);
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
    validationSchema: Yup.object().shape({
      name: Yup.string().required(t("common.common_input_scheduling_name")),
      startDate: Yup.date().required(t("common.common_select_dateInterval")),
      stopDate: Yup.date().required(t("common.common_select_dateInterval")),
    }),
  });

  const handelTableSubmit = async (data) => {
    //这是判断LCD-L101设备的校验
    if (screenInfoValue !== null) {
      if (screenInfoValue.screenModel === "LCD-L101") {
        if (data.inventoryList.length == 1) {
          await getPlayListInfo(data.inventoryList[0].playListId).then(
            (res) => {
              const totalSize = res.data.playListMaterialList.reduce(
                (total, item) => total + (item.size || 0),
                0
              );
              statMaterialSizeRef.current = totalSize;
            }
          );
          if (
            statMaterialSizeRef.current !== null &&
            statMaterialSizeRef.current > 60
          ) {
            toast.error(t("ips.ips_schedule_LCD_L101_valid"));
            return;
          }
        } else {
          toast.error(t("ips.ips_schedule_LCD_L101_valid"));
          return;
        }
      }
    }
    if (timeButtonSelectRef.current.monthsSelectList.length === 0) {
      toast.error(t("ips.ips_select_month"));
      return;
    }
    if (timeButtonSelectRef.current.daysSelectList.length === 0) {
      toast.error(t("ips.ips_select_date"));
      return;
    }
    //清除冗余播放清单对象
    let newInventoryList = scheduleForm.values.inventoryList.filter(
      (inventory) =>
        !(
          inventory.playListId === "" &&
          inventory.startTime === "" &&
          inventory.stopTime === ""
        )
    );
    if (newInventoryList.length === 0) {
      toast.error(t("ips.ips_please_select_playlist"));
      return;
    }
    let errorInventory = newInventoryList.filter(
      (newInventory) =>
        newInventory.playListId == "" ||
        newInventory.startTime == "" ||
        newInventory.stopTime == ""
    );
    if (errorInventory.length > 0) {
      toast.error(t("ips.ips_playlist_exist"));
      return;
    }
    //表单重新赋值，清除清单名称和时间都未选择的清单对象
    scheduleForm.values.inventoryList = newInventoryList;
    if (loading) {
      toast.error(t("ips.ips_schdule_publishing"));
    }

    let params = scheduleForm.values;
    // 表单赋值
    params.playWeeks = timeButtonSelectRef.current.weeksSelectList;
    params.playDays = timeButtonSelectRef.current.daysSelectList;
    params.playMonths = timeButtonSelectRef.current.monthsSelectList;

    setLoading(true);
    //提交表单
    await updateCommonScheduleInfo(params)
      .then((res) => {
        //所选设备包含LCD-L101,给不同提示
        if (
          screenInfoValue !== null &&
          screenInfoValue.screenModel === "LCD-L101"
        ) {
          toast.success(t("ips.ips_schedule_LCD_L101_Invalid"));
        } else {
          toast.success(res.message);
        }
        setLoading(false);
        scheduleForm.setStatus({ success: true });
        scheduleForm.setSubmitting(true);
        //跳转到投放排期界面
        navigate(-1);
      })
      .catch((error) => {
        setLoading(false);
      });
  };

  const defaultRange = {
    from: scheduleForm.values.startDate,
    to: scheduleForm.values.stopDate,
  };

  const [selectedDayRange, setSelectedDayRange] = useState(defaultRange);

  //设置清单id和名称
  const setPlayListFormValues = (playListId, playListName, index) => {
    scheduleForm.values.inventoryList[index].playListId = playListId;
    scheduleForm.values.inventoryList[index].playListName = playListName;
    //刷新表单
    scheduleForm.setFieldValue();
  };

  //清单选择组件
  function PlayListSelect(props) {
    const [anchorTime, setAnchorTime] = useState(null);
    const handleTimeClick = (event) => {
      setAnchorTime(event.currentTarget);
    };
    const handleTimeClose = () => {
      setAnchorTime(null);
    };
    const openTimeRange = Boolean(anchorTime);
    const timeRangeId = openTimeRange ? "time-popover" : undefined;
    return (
      <>
        <Grid
          container
          sx={{ marginBottom: "10px" }}
          justifyContent="flex-start"
          alignItems="center">
          <Grid item xs={5.5} sm={5.5} md={5.5}>
            <OutlinedInput
              id="schedule-playListName"
              type="text"
              name="playListName"
              sx={{ width: "100%" }}
              readOnly
              value={
                scheduleForm.values.inventoryList[props.index].playListName
              }
              onBlur={scheduleForm.handleBlur}
              onChange={scheduleForm.handleChange}
              placeholder={t("ips.ips_please_select_playlist")}
              onClick={() => {
                addPlayListRef.current.handleOpen();
                addPlayListRef.current.setIndex(props.index);
              }}></OutlinedInput>
          </Grid>
          <Grid item xs={1}>
            <Typography variant="body1" align="center">
              {t("common.common_from")}
            </Typography>
          </Grid>
          <Grid item xs={5.5} sm={5.5} md={5.5}>
            <OutlinedInput
              onClick={handleTimeClick}
              value={scheduleForm.values.inventoryList[props.index].stopTime}
              id="infor-firstName"
              type="text"
              name="stopTime"
              readOnly
              sx={{ width: "100%" }}
              placeholder={t("common.common_endTime")}
              startAdornment={
                <InputAdornment position="start" sx={{ width: "120%" }}>
                  <InputBase
                    readOnly
                    endAdornment={<DateSvg />}
                    value={
                      scheduleForm.values.inventoryList[props.index].startTime
                    }
                    name="startTime"
                    // id="quick-area"
                    type="text"
                    placeholder={t("common.common_startTime")}
                    sx={{ width: "100%" }}
                  />
                </InputAdornment>
              }
            />
            <Popover
              id={timeRangeId}
              open={openTimeRange}
              anchorEl={anchorTime}
              onClose={handleTimeClose}
              anchorOrigin={{
                vertical: "bottom",
                horizontal: "left",
              }}>
              <TimePickert
                endTimeText={t("common.common_endTime")}
                startTimeText={t("common.common_startTime")}
                confirmText={t("common.common_edit_ok")}
                onChange={(time) => {
                  //时间段是否存在重叠标识
                  let flag = false;
                  //已选播放清单
                  let timePeriodList = [...scheduleForm.values.inventoryList];
                  //开始时间
                  let startTime = new Date("2022-06-01T" + time.startTime);
                  //结束时间
                  let endTime = new Date("2022-06-01T" + time.endTime);

                  if (startTime.getTime() >= endTime.getTime()) {
                    toast.error(t("ips.ips_starttime_greater_endtime"));
                    return;
                  }

                  timePeriodList.forEach((timePeriod, index) => {
                    //排除自身与其它时间段比较
                    if (props.index != index) {
                      //排除未选择的播放时间段
                      if (
                        timePeriod.startTime != "" &&
                        timePeriod.stopTime != ""
                      ) {
                        //该时间段开始时间
                        let thisStartTime = new Date(
                          "2022-06-01T" + timePeriod.startTime
                        );
                        //该时间段结束时间
                        let thisStopTime = new Date(
                          "2022-06-01T" + timePeriod.stopTime
                        );
                        //交集判断结果
                        let result =
                          (startTime.getTime() < thisStartTime.getTime() &&
                            thisStartTime.getTime() < endTime.getTime()) ||
                          (startTime.getTime() < thisStopTime.getTime() &&
                            thisStopTime.getTime() < endTime.getTime()) ||
                          (startTime.getTime() >= thisStartTime.getTime() &&
                            endTime.getTime() <= thisStopTime.getTime()) ||
                          (startTime.getTime() <= thisStartTime.getTime() &&
                            endTime.getTime() >= thisStopTime.getTime());

                        if (result) {
                          //时间段存在交集
                          flag = true;
                          return;
                        }
                      }
                    }
                  });

                  if (flag) {
                    toast.error(t("ips.ips_overlap_play_time"));
                    return;
                  } else {
                    scheduleForm.values.inventoryList[props.index].startTime =
                      time.startTime;
                    scheduleForm.values.inventoryList[props.index].stopTime =
                      time.endTime;
                    // 刷新表单验证
                    scheduleForm.setFieldValue();
                    handleTimeClose();
                  }
                }}
              />
            </Popover>
          </Grid>
        </Grid>
      </>
    );
  }
  const handleRestPlayListFormField = (values) => {
    return new Promise((reslove, reject) => {
      scheduleForm.setFieldValue("inventoryList", values);
      reslove();
    });
  };
  const handleOpenAdvanced = () => {
    const playLists = scheduleForm.values?.inventoryList;
    const tempData = [];
    // playLists.forEach((item) => {
    //   if (
    //     item.playListId !== "" ||
    //     item.playListName !== "" ||
    //     item.startTime !== "" ||
    //     item.stopTime !== ""
    //   ) {
    //     tempData.push({
    //       id: item.playListId,
    //       name: item.playListName,
    //       time: {
    //         startTime: item.startTime,
    //         endTime: item?.stopTime,
    //       },
    //     });
    //   }
    // });
    setAdvancedData(playLists);
    setAdvancedOpen(true);
  };
  return (
    <MainCard
      title={t("server.server_scheduling_update")}
      border={false}
      contentSX={{ p: 0 }}>
      <form noValidate onSubmit={scheduleForm.handleSubmit}>
        <Container maxWidth="sm" sx={{ padding: 3 }}>
          {/* <Grid xs={12} container spacing={1} direction="row" justifyContent="flex-start" alignItems="center"></Grid> */}
          <Grid container spacing={3} sx={{ marginBottom: "5px" }}>
            <Grid item xs={6}>
              <Stack spacing={1}>
                <InputLabel htmlFor="schedule-name">
                  {t("ips.ips_scheduling")}
                  <i style={{ color: "red" }}>*</i>
                </InputLabel>
                {/* <Stack sx={{ width: '60%' }}> */}
                <OutlinedInput
                  sx={{
                    width: "100%",
                  }}
                  fullWidth
                  id="schedule-name"
                  type="text"
                  name="name"
                  value={scheduleForm.values.name}
                  onBlur={scheduleForm.handleBlur}
                  onChange={scheduleForm.handleChange}
                  placeholder={t("common.common_input_scheduling_name")}
                  error={Boolean(
                    scheduleForm.touched.name && scheduleForm.errors.name
                  )}
                />
                {scheduleForm.touched.name && scheduleForm.errors.name && (
                  <FormHelperText
                    error
                    id="standard-weight-helper-text-name-schedule">
                    {scheduleForm.errors.name}
                  </FormHelperText>
                )}
              </Stack>
            </Grid>
            <Grid item xs={6}>
              <Stack spacing={1}>
                <InputLabel htmlFor="infor-firstName">
                  {t("ips.ips_scheduling_playTime")}
                  <i style={{ color: "red" }}>*</i>
                </InputLabel>
                <OutlinedInput
                  readOnly
                  autoFocus={false}
                  onClick={handleClick}
                  autoComplete="off"
                  name="startDate"
                  type="text"
                  startAdornment={
                    <InputAdornment position="start">
                      <CalendarMonthIcon />
                    </InputAdornment>
                  }
                  value={scheduleForm.values.startDate}
                  onBlur={scheduleForm.handleBlur}
                  onChange={scheduleForm.handleChange}
                  fullWidth
                  error={Boolean(
                    scheduleForm.touched.startDate &&
                      scheduleForm.errors.startDate
                  )}
                  endAdornment={
                    <InputAdornment position="end" sx={{ width: "120%" }}>
                      <InputBase
                        readOnly
                        autoComplete="off"
                        startAdornment={<DateSvg></DateSvg>}
                        autoFocus={false}
                        name="stopDate"
                        onClick={handleClick}
                        onChange={scheduleForm.handleChange}
                        onBlur={scheduleForm.handleBlur}
                        value={scheduleForm.values.stopDate}
                        error={Boolean(
                          scheduleForm.touched.stopDate &&
                            scheduleForm.errors.stopDate
                        )}
                        sx={{
                          width: "100%",
                        }}
                        type="text"
                        placeholder={t("common.common_startTime")}
                      />
                    </InputAdornment>
                  }
                  sx={{
                    width: "100%",
                  }}
                  placeholder={t("common.common_endTime")}
                />
                {(scheduleForm.touched.startDate &&
                  scheduleForm.errors.startDate) ||
                (scheduleForm.touched.stopDate &&
                  scheduleForm.errors.stopDate) ? (
                  <FormHelperText
                    error
                    id="standard-weight-helper-text-startDate-schedule">
                    {scheduleForm.errors.startDate}
                  </FormHelperText>
                ) : null}
                <Popover
                  elevation={3}
                  id="date"
                  open={open}
                  anchorEl={anchorEl}
                  onClose={handleClose}
                  anchorOrigin={{
                    vertical: "bottom",
                    horizontal: "center",
                  }}
                  transformOrigin={{
                    vertical: "top",
                    horizontal: "center",
                  }}>
                  <Calendar
                    colorPrimary="#7ac143" // added this
                    colorPrimaryLight="rgba(122, 193, 67, 0.1)"
                    minimumDate={utils().getToday()}
                    locale={getCalendarLocales()}
                    value={selectedDayRange}
                    onChange={(date) => {
                      setSelectedDayRange(date);
                      const startDate = date.from
                        ? dayjs(
                            date.from.year +
                              `${
                                date.from.month <= 9
                                  ? "0" + date.from.month
                                  : date.from.month
                              }` +
                              date.from.day
                          ).format("YYYY-MM-DD")
                        : "";
                      const endDate = date.to
                        ? dayjs(
                            date.to.year +
                              `${
                                date.to.month <= 9
                                  ? "0" + date.to.month
                                  : date.to.month
                              }` +
                              date.to.day
                          ).format("YYYY-MM-DD")
                        : "";
                      //计算开始时间和结束时间跨度不能超过一整年
                      let timeSpan = moment(new Date(startDate)).diff(
                        moment(new Date(endDate)),
                        "years"
                      );

                      if (timeSpan >= 1) {
                        toast.error(t("ips.ips_exceeds_one_year"));
                        return;
                      } else {
                        scheduleForm.setFieldValue("startDate", startDate);
                        scheduleForm.setFieldValue("stopDate", endDate);
                        //将排期播放时间传给时间按钮选择组件进行计算
                        timeButtonSelectRef.current.getDaysWeeksAndMonthsDate(
                          startDate,
                          endDate
                        );
                      }
                    }}
                  />
                </Popover>
              </Stack>
            </Grid>
            <Grid
              item
              xs={12}
              style={{
                display: "flex",
                justifyContent: "flex-end",
              }}>
              <Button
                variant="contained"
                onClick={() => {
                  handleOpenAdvanced();
                }}>
                {t("common.common_advanced_menu_title")}
              </Button>
            </Grid>
          </Grid>
          <Grid
            container
            spacing={1}
            direction="row"
            justifyContent="center"
            alignItems="flex-start">
            <Grid item xs={12} justifyContent="center" alignItems="flex-start">
              <InputLabel htmlFor="schedule-name">
                {t("ips.ips_select_playlist")}
                <i style={{ color: "red" }}>*</i>
              </InputLabel>
            </Grid>
            <Grid item xs={12} justifyContent="center" alignItems="flex-start">
              <Grid container>
                {scheduleForm.values.inventoryList.map((inventoryOb, index) => (
                  <PlayListSelect
                    key={index}
                    index={index}
                    inventoryOb={inventoryOb}
                  />
                ))}
              </Grid>
            </Grid>
            <Grid
              container
              direction="row"
              justifyContent="space-between"
              item
              xs={12}>
              <InputLabel sx={{ color: "grey", fontSize: "0.8rem" }}>
                {t("common.common_time_not_repeat_annotation")}
              </InputLabel>
              <Stack spacing={1} direction="row">
                <Button
                  variant="contained"
                  color="primary"
                  onClick={() => {
                    let newList = scheduleForm.values.inventoryList;
                    newList.push(inventoryObject);
                    scheduleForm.setValues({
                      ...scheduleForm.values,
                      inventoryList: newList,
                    });
                  }}>
                  {t("common.common_more")}
                </Button>
                <Button
                  variant="contained"
                  color="error"
                  onClick={() => {
                    if (scheduleForm.values.inventoryList.length <= 1) {
                      return;
                    }
                    let newList = scheduleForm.values.inventoryList;
                    //删除最后一个清单对象
                    newList.pop();
                    scheduleForm.setValues({
                      ...scheduleForm.values,
                      inventoryList: newList,
                    });
                  }}>
                  {t("common.common_op_del")}
                </Button>
              </Stack>
            </Grid>
          </Grid>
          {/* 时间选择按钮组 */}
          <TimeButtonSelect ref={timeButtonSelectRef} />
          <Grid
            sx={{ marginTop: "10px" }}
            container
            justifyContent="center"
            alignItems="center">
            <Stack direction="row" spacing={2}>
              <Button
                size="large"
                variant="contained"
                color="secondary"
                onClick={() => {
                  navigate(-1);
                }}>
                {t("common.common_op_return")}
              </Button>
              <LoadingButton
                loading={loading}
                variant="contained"
                size="large"
                type="submit"
                color="primary">
                {scheduleForm.values.status === "0"
                  ? t("common.common_op_publish")
                  : t("common.common_edit_save")}
              </LoadingButton>
            </Stack>
          </Grid>
        </Container>
      </form>
      <AddPlayList
        ref={addPlayListRef}
        setPlayListFormValues={setPlayListFormValues}
        playListType={"0"}
        isLayout={"1"}
      />
      <AdvancedDialog
        open={advancedOpen}
        onClose={() => {
          setAdvancedData([]);
          setAdvancedOpen(false);
        }}
        data={advancedData}
        onSubmit={handleRestPlayListFormField}
      />
    </MainCard>
  );
}
