import { useEffect, useRef, useState } from "react";
import * as echarts from "echarts";
import { useTranslation } from "react-i18next";
const chartData = {
  yAxis: [
    "Online",
    "Offline",
    "Online",
    "Offline",
    "Online",
    "Offline",
    "Online",
    "Offline",
    "Online",
    "Offline",
    "Online",
    "Offline",
    "Online",
    "Offline",
  ],
  xAxis: [
    "2024-03-14 00:54:37",
    "2024-03-14 01:54:37",
    "2024-03-14 02:54:37",
    "2024-03-14 07:57:37",
    "2024-03-14 10:31:37",
    "2024-03-14 12:54:37",
    "2024-03-14 15:54:37",
    "2024-03-14 21:54:37",
    "2024-03-14 22:54:37",
    "2024-03-20 15:54:37",
    "2024-04-14 15:54:37",
    "2024-05-14 15:54:37",
    "2024-07-14 15:54:37",
    "2024-11-14 15:54:37",
  ],
};

const dateFormate = (date, fmt) => {
  if (fmt === undefined || fmt === null) {
    fmt = "yyyy-MM-dd HH:mm:ss";
  }
  if (date === "" || date === undefined) {
    return "";
  }
  if (typeof date === "number") {
    date = new Date(date);
  }
  var o = {
    "M+": date.getMonth() + 1,
    "d+": date.getDate(),
    "h+": date.getHours() % 12 === 0 ? 12 : date.getHours() % 12,
    "H+": date.getHours(),
    "m+": date.getMinutes(),
    "s+": date.getSeconds(),
    "q+": Math.floor((date.getMonth() + 3) / 3),
    S: date.getMilliseconds(),
  };
  var week = {
    0: "/u65e5",
    1: "/u4e00",
    2: "/u4e8c",
    3: "/u4e09",
    4: "/u56db",
    5: "/u4e94",
    6: "/u516d",
  };
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (date.getFullYear() + "").substr(4 - RegExp.$1.length)
    );
  }
  if (/(E+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (RegExp.$1.length > 1
        ? RegExp.$1.length > 2
          ? "/u661f/u671f"
          : "/u5468"
        : "") + week[this.getDay() + ""]
    );
  }
  for (var k in o) {
    let rg = "(" + k + ")";
    if (new RegExp(rg).test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length)
      );
    }
  }
  return fmt;
};

const LineChart = (props) => {
  const chartRef = useRef(null);
  const [myEcharts, setMyEcharts] = useState(null);
  const { t } = useTranslation();

  const initChart = () => {
    let chart = echarts.init(chartRef.current, null, { renderer: "svg" });
    setMyEcharts(chart);
    chart.resize();
    window.addEventListener("resize", handleResize);
    const options = getOptions(props.ChartDataValue);
    chart.setOption(options);
  };

  const handleResize = () => {
    if (myEcharts) {
      myEcharts.resize();
    }
  };

  useEffect(() => {
    // 在组件挂载时进行初始化
    initChart();
    return () => {
      window.removeEventListener("resize", handleResize);
      if (myEcharts) {
        myEcharts.dispose();
        setMyEcharts(null);
      }
    };
  }, []);
  useEffect(() => {
    if (myEcharts === null) {
      initChart();
    } else {
      const options = getOptions(props.ChartDataValue);
      myEcharts.setOption(options);
    }
  }, [props.ChartDataValue]);

  const getOptions = () => {
    var data = [];
    let xAxis = chartData.xAxis;
    let length = xAxis.length - 1;
    let yAxis = chartData.yAxis;
    let startTime = new Date(xAxis[0]).getTime();
    let endTime = new Date(xAxis[length]).getTime();
    var categories = ["设备名称"];

    for (let i = 0; i < length; i++) {
      let sTime = new Date(xAxis[i]).getTime();
      let eTime = new Date(xAxis[i + 1]).getTime();
      let itemStatus = yAxis[i];
      data.push({
        name: "设备在线率",
        value: [i, sTime, eTime, eTime - sTime, itemStatus],
        itemStyle: {
          normal: {
            color: itemStatus === "Online" ? "#5a9c2f" : "gray",
          },
        },
      });
    }
    function renderItem(params, api) {
      var categoryIndex = api.value(0);
      var start = api.coord([api.value(1), categoryIndex]);
      var end = api.coord([api.value(2), categoryIndex]);
      var height = 40;
      var rectShape = echarts.graphic.clipRectByRect(
        {
          x: start[0],
          y: start[1] - height / 2,
          width: end[0] - start[0],
          height: height,
        },
        {
          x: params.coordSys.x,
          y: params.coordSys.y,
          width: params.coordSys.width,
          height: params.coordSys.height,
        }
      );
      return (
        rectShape && {
          type: "rect",
          transition: ["shape"],
          shape: rectShape,
          style: api.style(),
        }
      );
    }
    return {
      tooltip: {
        formatter: function (params) {
          let p1 = params.value[1];
          let p2 = params.value[2];
          
          if (params.value[4] === "Online") {
            return params.marker + "设备在线" + ": " + dateFormate(p1) + "-" + dateFormate(p2)+'<br/>&nbsp&nbsp&nbsp&nbsp'+'在线时长'+params.value[3]/60000 +'分钟';
          } else {
            return params.marker + "设备离线" + ": " + dateFormate(p1) + "-" + dateFormate(p2)+'<br/>&nbsp&nbsp&nbsp&nbsp'+'在线时长'+params.value[3]/60000 +'分钟';
          }
        },
      },
      title: {
        text: "",
        left: "center",
      },
      dataZoom: [
        {
          type: "slider",
          showDataShadow: false,
          top: 250,
          startValue: startTime,
          endValue: endTime,
          filterMode: "weakFilter",
          labelFormatter: "",
        },
        {
          type: "inside",
          showDataShadow: false,
          top: 250,
          startValue: startTime,
          endValue: endTime,
          labelFormatter: "",
          filterMode: "weakFilter",
          zoomOnMouseWheel: true, // 滚轮是否触发缩放
          moveOnMouseMove: true, // 鼠标滚轮触发滚动
          moveOnMouseWheel: true,
        },
      ],
      grid: {
        height: 150,
      },
      xAxis: {
        min: startTime - 5000000,
        scale: true,
        axisLabel: {
          formatter: function (val) {
            return dateFormate(val);
          },
        },
      },
      yAxis: {
        data: categories,
      },
      series: [
        {
          type: "custom",
          renderItem: renderItem,
          itemStyle: {
            opacity: 0.8,
          },
          encode: {
            x: [1, 2],
            y: 0,
          },
          data: data,
        },
      ],
    };
  };

  return <div style={{ width: "100%", height: "300px" }} ref={chartRef}></div>;
};

export default LineChart;
