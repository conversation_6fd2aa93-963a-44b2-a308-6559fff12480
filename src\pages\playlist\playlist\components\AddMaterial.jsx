import React, {
  useMemo,
  useState,
  useEffect,
  useRef,
  forwardRef,
  useImperativeHandle,
} from "react";
import MainCard from "@/components/MainCard";
import MaterialReactTable from "material-react-table";
import { tableI18n } from "@/utils/tableLang";
import TabList from "@mui/lab/TabList";
import {
  Box,
  Stack,
  Grid,
  Button,
  Typography,
  Tooltip,
  InputLabel,
  FormHelperText,
  OutlinedInput,
  InputAdornment,
  Snackbar,
  Tab,
  Alert,
  AlertTitle,
  TextField,
} from "@mui/material";
import * as Yup from "yup";
import { useFormik } from "formik";
import RemoveCircleOutlineIcon from "@mui/icons-material/RemoveCircleOutline";
import IconButton from "@mui/material/IconButton";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import CloseIcon from "@mui/icons-material/Close";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import { removeEmpty } from "@/utils/StringUtils";
import ZKSelect from "@/components/ZKSelect";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import VisibilityIcon from "@mui/icons-material/Visibility";
import { listByPage } from "@/service/api/material";
import { useTranslation } from "react-i18next";
import TabContext from "@mui/lab/TabContext";
import Preview from "@/pages/program/material/components/Preview";
// import UploadMaterial from "@/pages/program/material/components/UploadMaterial";
import PreViewRa from "@/pages/neweditor/PreViewRa";

import { getPrincipaList} from '@/service/api/L3Sevice.js'
import {
  BootstrapDialog,
  BootstrapContent,
  BootstrapActions,
  BootstrapDialogTitle,
} from "@/components/dialog";
import { directions, merchantTypes } from "@/dict/commonDict";
import DictTag from "@/components/DictTag";
let isSave = false;
const AddMaterial = forwardRef((props, ref) => {
  const { t } = useTranslation();
  const [open, setOpen] = React.useState(false);
  const [durationOpen, setDurationOpen] = React.useState(false);
  const [fullScreen, setFullScreen] = React.useState(false);
  const [isError, setIsError] = useState(false);
  const { playListTypeValue } = props;
  // 查询参数
  const requestParams = useRef(null);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  // 表格数据
  const [data, setData] = useState([]);
  const [tempValue, setTemplateValue] = useState({});
  const [currentData, setCurrentData] = useState([]);
  // 分页的参数
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);

  const preview = React.useRef(null);
  // 预览url
  const [previewUrl, setPreviewUrl] = useState("");
  //预览文件类型
  const [previewType, setPreviewType] = useState("");
  //广告商数据
  const [merchantList, setMerchantList] = useState([]);
  const [advertiserType, setAdvertiserType] = useState("");
  const [show, setShow] = useState(false);

  // 构建参数
  const buildParams = (type) => {
    if (type == undefined || type == null) {
      type = tabValue;
    }


    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      type: playListTypeValue === "2" ? "layout" : type,

      showType: "1",
      showAudited: true,
      ...requestParams.current,
    };
    return params;

  
  };
  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "name",
        header: t("common.common_name"),
        size: 100,
      },
      // {
      //   accessorKey: "advertiserType",
      //   header: t("ips.ips_merchant_type"),
      //   size: 110,
      //   Cell: ({ cell, row }) => {
      //     return (
      //       <DictTag
      //         dicts={merchantTypes}
      //         fieldName={{ title: "label" }}
      //         value={row.original.advertiserType}
      //       />
      //     );
      //   },
      // },
      {
        accessorKey: "advertiserName",
        header: t("ips.ips_belong_retail"),
        size: 110,
      },
      {
        accessorKey: "groupName",
        header: t("common.common_material_category_table_column_name"),
        enableColumnActions: false,
        size: 80,
        Cell: ({ row }) => {
          return (
            <>
              {row?.original?.groupName === null ? (
                <Typography>-</Typography>
              ) : (
                <Tooltip title={row?.original?.groupName} placement="top">
                  <Typography
                    sx={{
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap",
                    }}
                  >
                    {row?.original?.groupName}
                  </Typography>
                </Tooltip>
              )}
            </>
          );
        },
      },
      {
        accessorKey: "duration",
        header: t("ips.ips_program_durationss"),
        size: 80,
        Cell: ({ cell, row }) => {
          if (row.original.type === "image") {
            return <Typography> / </Typography>;
          } else {
            return <Typography>{row.original.duration}s</Typography>;
          }
        },
      },
      {
        accessorKey: "resolution",
        header: t("common.common_resolution_ratio"),
        size: 80,
      },
      {
        accessorKey: "direction",
        header: t("common.common_direction"),
        size: 60,
        Cell: ({ row }) => {
          if (row.original.direction) {
            return (
              <DictTag
                dicts={directions}
                value={row.original.direction}
                fieldName={{ value: "value", title: "label", color: "color" }}
              />
            );
          } else {
            return <>-</>;
          }
        },
      },
    ],
    []
  );

  useEffect(() => {
    if (open) {
      handleListMaterial();
    }
  }, [pagination, playListTypeValue]);

  useEffect(() => {
    getMerchant();
  }, []);

  //请求素材列表
  const handleListMaterial = async (type) => {
    setIsLoading(true);
 await listByPage(buildParams(type))
      .then((res) => {
        // 设置数据
        setData(res.data.data);
        // 设置总记录数
        setRowCount(res.data.total);
        setIsLoading(false);
        setIsRefetching(false);
      })
      .catch((err) => {
        setIsError(true);
        setIsLoading(false);
        setIsRefetching(false);
      });
  };
  //获取零售商列表
  const getMerchant = async () => {
    await getPrincipaList().then((res) => {
      setMerchantList(res.data);
    });
  };

  const columnsTemp = useMemo(
    () => [
      {
        accessorKey: "name",
        header: t("common.common_name"),
        size: 120,
      },
      {
        accessorKey: "advertiserName",
        header: t("ips.ips_belong_retail"),
        size: 120,
      },
      {
        accessorKey: "groupName",
        header: t("common.common_material_category_table_column_name"),
        enableColumnActions: false,
        size: 80,
        Cell: ({ row }) => {
          return (
            <>
              {row?.original?.groupName === null ? (
                <Typography>-</Typography>
              ) : (
                <Tooltip title={row?.original?.groupName} placement="top">
                  <Typography
                    sx={{
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap",
                    }}
                  >
                    {row?.original?.groupName}
                  </Typography>
                </Tooltip>
              )}
            </>
          );
        },
      },
      {
        accessorKey: "duration",
        header: t("ips.ips_program_durationss"),
        size: 120,
        Cell: ({ cell, row }) => {
          if (
            row.original.duration == undefined ||
            row.original.duration === null
          ) {
            return <Typography>-</Typography>;
          } else {
            return <Typography>{row.original.duration}s</Typography>;
          }
        },
      },
    ],
    []
  );
  useImperativeHandle(ref, () => ({
    handleClose,
    handleClickOpen,
  }));
  const handleClickOpen = () => {
    setCurrentData([]);
    setOpen(true);
    handleListMaterial();
  };
  const handleClose = () => {
    setOpen(false);
    setPagination({
      pageIndex: 0,
      pageSize: 10,
    });
  };

  const handleClickDurationOpen = (original) => {
    durationFormik.handleReset();
    if (original.duration && original.duration != "0") {
      //
      durationFormik.setFieldValue("duration", original.duration);
    }
    setDurationOpen(true);
  };
  const handleDurationClose = () => {
    setDurationOpen(false);
  };
  // 添加一个到右边的表中
  const addMaterial = (row) => {
    return new Promise((resolve, reject) => {
      // durationFormik.setFieldValue("duration", 10);
      setTemplateValue(row.original);
      handleClickDurationOpen(row.original);
      resolve("success");
    });
  };
  // 表单提交
  const handelMaterialSubmit = (values) => {
    setDurationBtnDisabled(true);
    const temp = { ...tempValue };
    temp.duration = values.duration;
    durationFormik.setFieldValue("duration", "");


    const tempMaterialValue = [...currentData];

    tempMaterialValue.push(temp);
    setCurrentData(tempMaterialValue);
    handleDurationClose();
    setDurationBtnDisabled(false);
  };
  const [removeDisabled, setRemoveDisabled] = useState(false);
  //删除右侧已选中素材列表
  const removeByIndex = (row) => {
    setRemoveDisabled(false);
    setTimeout(() => {
      remove(row?.index)
        .then(() => {
          setRemoveDisabled(false);
        })
        .catch(() => {
          setRemoveDisabled(false);
        });
    }, 100);
  };
  const remove = async (index) => {
    return await new Promise(() => {
      console.log(":aaa");
      // const tempMaterialData = [...currentData];
      currentData.splice(index, 1);
      console.log(currentData);
      setCurrentData([...currentData]);
      return Promise.resolve();
    }).catch((err) => {
      return Promise.reject();
    });
  };

  //  表单
  const durationFormik = useFormik({
    initialValues: {
      duration: "",
    },
    onSubmit: (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        handelMaterialSubmit(values);
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
    validationSchema: Yup.object().shape({
      duration: Yup.number()
        .min(1,t("playlist.durationRange"))
        .max(999999,t("playlist.durationRange"))
        .required(t("common.common_input_duration"))
        .moreThan(1, t("ips.ips_duration_greater")),
    }),
  });

  //控制提示框是否显示
  const [tipsOpen, setTipsOpen] = React.useState(false);
  const handleCloseTips = () => {
    setTipsOpen(false);
  };
  const handelAppendMaterial = () => {
    if (isSave) {
      return false;
    }
    isSave = true;
    //将id赋值给materialId，然后id再置为空
    let selectedMaterialTypeList = [];
    currentData.forEach((data) => {
      data.materialId = data.id;
      // data.id = null;
      selectedMaterialTypeList.push(data.type);
    });
    let newMaterialTypeList = [
      ...selectedMaterialTypeList,
      ...props.materialTypeList(),
    ];
    if (
      newMaterialTypeList.indexOf("media") != -1 &&
      newMaterialTypeList.indexOf("audio") != -1
    ) {
      setTipsOpen(true);
    } else {
      // 最后添加
      props.setMaterialLists(currentData);
      handleClose();
    }
    setTimeout(() => {
      isSave = false;
    }, 1000);
  };
  const [tabValue, setTableValue] = React.useState("all");
  const [layoutData, setLayoutData] = React.useState(null);
  const [visible, setVisible] = React.useState(false);
  //获取预览地址
  const handlePreview = async (values) => {
    if (values.original.type === "layout") {
      console.log(values?.original);
      setLayoutData(values?.original?.layoutJson);
      setVisible(true);
    } else {
      setPreviewUrl(values.original.downloadUrl);
      setPreviewType(values.original.type);
      preview.current.handleOpen();
    }
  };
  const addUploadMaterial = React.useRef(null);
  // 查询表单
  const queryFormik = useFormik({
    initialValues: {
      name: "",
      advertiserId: "",
    },
    onSubmit: async (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        // 拷贝value值
        const tempValue = { ...values };
        await removeEmpty(tempValue);
        requestParams.current = tempValue;
        handleListMaterial();
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
  });
  // 重置查询
  const resetQuery = () => {
    // 重置表单
    queryFormik.resetForm();
    requestParams.current = null;
    handleListMaterial();
  };

  const [durationBtnDisabled, setDurationBtnDisabled] = useState(false);

  return (
    <div>
      {/* <Snackbar
        open={tipsOpen}
        autoHideDuration={5000}
        onClose={handleCloseTips}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
      >
        <Alert severity="error" onClose={handleCloseTips}>
          <AlertTitle>Error</AlertTitle>
          <strong>{t("editor.editor_video_and_audio")}</strong>
        </Alert>
      </Snackbar> */}
      <BootstrapDialog
        fullScreen={fullScreen}
        fullWidth
        maxWidth="xl"
        onClose={handleClose}
        aria-labelledby="customized-dialog-title"
        open={open}
      >
        <BootstrapDialogTitle
          fullscreen={fullScreen}
          onFullscreen={() => {
            setFullScreen(!fullScreen);
          }}
        >
          <Typography variant="h4" component="p">
            {playListTypeValue !== "2"
              ? t("ips.ips_add_media_iamge")
              : t("common.common_add_layout_resource")}
          </Typography>
        </BootstrapDialogTitle>
        <BootstrapContent>
          <Grid container spacing={7}>
            <Grid item xs={7}>
              <MainCard style={{ marginBottom: "10px" }}>
                <form noValidate onSubmit={queryFormik.handleSubmit}>
                  <Grid
                    container
                    direction="row"
                    justifyContent="flex-start"
                    alignItems="center"
                    spacing={2}
                  >
                    <Grid item xs={6} sm={4} md={2.8}>
                      <TextField
                        label={t("common.common_name")}
                        value={queryFormik.values.name}
                        onChange={queryFormik.handleChange}
                        onBlur={queryFormik.handleBlur}
                        size="small"
                        type="text"
                        name="name"
                        fullWidth
                        placeholder={t("common.common_input_name")}
                      />
                    </Grid>

                    <Grid item xs={6} sm={4} md={3.1}>
                      <Stack
                        direction="row"
                        justifyContent="flex-start"
                        alignItems="center"
                        spacing={1}
                      >
                        {/* <InputLabel sx={{ width: 40 }}>
                          {t("ips.ips_merchant")}
                        </InputLabel> */}
                        <ZKSelect
                          id="advertiserId"
                          name="advertiserId"
                          value={queryFormik.values.advertiserId}
                          options={props.merchants}
                          onClear={() => {
                            queryFormik.setFieldValue("advertiserId", "");
                          }}
                          onBlur={queryFormik.handleBlur}
                          onChange={queryFormik.handleChange}
                          type="text"
                          menuWidth={250}
                          placeholder={t("ips.ips_select_merchant")}
                        />
                      </Stack>
                    </Grid>

                    <Grid item xs={2}>
                      <Stack
                        direction="row"
                        justifyContent="flex-start"
                        alignItems="flex-start"
                        spacing={2}
                      >
                        <Button
                          disableElevation
                          type="submit"
                          variant="contained"
                          size="small"
                        >
                          {t("common.common_table_query")}
                        </Button>
                        <Button
                          variant="outlined"
                          onClick={resetQuery}
                          color="info"
                          sx={{
                            minWidth: "90px",
                          }}
                          size="small"
                        >
                          {t("common.common_op_reset")}
                        </Button>
                      </Stack>
                    </Grid>
                  </Grid>
                </form>
              </MainCard>

              <MaterialReactTable
                // table 状态
                state={{
                  // 加载状态
                  isLoading,
                  // 分页参数
                  pagination,
                  // 重新拉取
                  showProgressBars: isRefetching,
                  showAlertBanner: isError,
                  density: "compact",
                }}
                // enableRowSelection
                renderToolbarInternalActions={({ table }) => <></>}
                displayColumnDefOptions={{
                  "mrt-row-actions": {
                    header: t("common.common_relatedOp"), //change header text
                    size: 80,
                  },
                }}
                muiTablePaperProps={{
                  elevation: 0,
                  sx: {
                    border: "1px solid",
                    borderColor: "#e6ebf1",
                  },
                }}
                autoResetPageIndex={false}
                // 是否开启关闭头部底部工具类
                enableTopToolbar={true}
                enableColumnActions={false}
                enableBottomToolbar={true}
                // 关闭过滤搜素
                enableColumnFilters={false}
                // 关闭排序
                enableSorting={false}
                // 布局方式
                layoutMode="grid"
                // 开启列对齐
                muiTableHeadCellProps={{
                  sx: {
                    "& .Mui-TableHeadCell-Content": {
                      justifyContent: "space-between",
                    },
                  },
                }}
                // 解决列太多宽度太长问题
                enableColumnResizing
                // enablePinning
                // 初始化状态
                initialState={{ columnVisibility: { createTime: true } }}
                muiToolbarAlertBannerProps={
                  isError
                    ? {
                        color: "error",
                        children: "Error loading data",
                      }
                    : undefined
                }
                // 固定头部
                enableStickyHeader
                sx={{ boxShadow: "none" }}
                // 处理表格高度
                muiTableContainerProps={{ sx: { minHeight: "400px" } }}
                // 设置背景颜色
                muiTableBodyCellProps={({ row }) => ({
                  onDoubleClick: (event) => {
                    // handleClickOpen();
                    // console.info(event, row);
                  },
                  sx: {
                    backgroundColor: "white",
                  },
                })}
                muiTableHeadRowProps={{ sx: { boxShadow: "none" } }}
                muiTableBodyProps={{
                  sx: {
                    backgroundColor: "white",
                    tableLayout: "fixed",
                    boxShadow: "none",
                  },
                }}
                muiTableProps={{
                  sx: {
                    backgroundColor: "white",
                    tableLayout: "fixed",
                    boxShadow: "none",
                  },
                }}
                muiBottomToolbarProps={{
                  sx: { backgroundColor: "white", boxShadow: "none" },
                }}
                muiTopToolbarProps={{
                  sx: { backgroundColor: "white", boxShadow: "none" },
                }}
                // 分页回调函数
                onPaginationChange={setPagination}
                // 记得开启这些 tips:否则会出现useEffect 反作用函数发起多次请求点击下一页会出问题
                manualFiltering
                manualPagination
                manualSorting
                // 列数
                rowCount={rowCount}
                // 开启分页
                enablePagination={true}
                // 列定义
                columns={columns}
                // 数据
                data={data}
                // 初始化国际化语言  --- todo: i18n key doc address: https://github.com/KevinVandy/material-react-table/blob/main/packages/material-react-table/src/_locales/en.ts
                localization={tableI18n}
                // 多选底部提示
                positionToolbarAlertBanner="none"
                // 开启action操作
                enableRowActions
                // action操作位置
                positionActionsColumn="last"
                // 自定义表头按钮
                renderTopToolbarCustomActions={({ table }) => {
                  const handleTabChange = (event, newValue) => {
                    setTableValue(newValue);
                    setPagination({
                      pageIndex: 0,
                      pageSize: 10,
                    });
                  };
                  return (
                    <>
                      {playListTypeValue !== "2" && (
                        <TabContext value={tabValue}>
                          <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
                            <TabList
                              onChange={handleTabChange}
                              aria-label="lab API tabs example"
                            >
                              <Tab
                                label={t("common.common_all")}
                                value="all"
                              />
                              <Tab
                                label={t("common.common_picture")}
                                value="image"
                              />
                              <Tab
                                label={t("common.common_video")}
                                value="media"
                              />
                              {/* <Tab label={t('common.common_audio')} value="audio" /> */}
                            </TabList>
                          </Box>
                        </TabContext>
                      )}
                    </>
                  );
                }}
                // 自定义表头按钮
                renderRowActions={({ row, table }) => (
                  <Stack direction="row" alignItems="center">
                    <IconButton
                      color="primary"
                      aria-label="cliear"
                      component="label"
                      onClick={() => handlePreview(row)}
                    >
                      <VisibilityIcon />
                    </IconButton>
                    <IconButton
                      color="primary"
                      aria-label="cliear"
                      component="label"
                      onClick={() => {
                        addMaterial(row);
                      }}
                    >
                      <AddCircleOutlineIcon />
                    </IconButton>
                  </Stack>
                )}
              />
            </Grid>
            <Grid item xs={5}>
              <MaterialReactTable
                // table 状态
                state={{
                  // 重新拉取
                  showProgressBars: isRefetching,
                  showAlertBanner: isError,
                  density: "compact",
                }}
                enableRowOrdering
                displayColumnDefOptions={{
                  "mrt-row-actions": {
                    header: t("common.common_relatedOp"), //change header text
                    size: 70,
                  },
                  "mrt-row-drag": {
                    header: t("common.common_sort"),
                  },
                }}
                muiTablePaperProps={{
                  elevation: 0,
                  sx: {
                    border: "1px solid",
                    borderColor: "#e6ebf1",
                  },
                }}
                muiTableBodyRowDragHandleProps={({ table }) => ({
                  onDragEnd: () => {
                    const { draggingRow, hoveredRow } = table.getState();
                    if (hoveredRow && draggingRow) {
                      currentData.splice(
                        hoveredRow.index,
                        0,
                        currentData.splice(draggingRow.index, 1)[0]
                      );
                      setCurrentData([...currentData]);
                    }
                  },
                })}
                renderToolbarInternalActions={({ table }) => <></>}
                autoResetPageIndex={false}
                // 是否开启关闭头部底部工具类
                enableTopToolbar={true}
                enableColumnActions={false}
                enableBottomToolbar={true}
                // 关闭过滤搜素
                enableColumnFilters={true}
                // 关闭排序
                enableSorting={false}
                // 布局方式
                layoutMode="grid"
                // 开启列对齐
                muiTableHeadCellProps={{
                  sx: {
                    "& .Mui-TableHeadCell-Content": {
                      justifyContent: "space-between",
                    },
                  },
                }}
                // 解决列太多宽度太长问题
                enableColumnResizing
                // enablePinning
                // 初始化状态
                initialState={{ columnVisibility: { createTime: true } }}
                muiToolbarAlertBannerProps={
                  isError
                    ? {
                        color: "error",
                        children: "Error loading data",
                      }
                    : undefined
                }
                // 列数
                // rowCount={rowCount}
                // 固定头部
                enableStickyHeader
                sx={{ boxShadow: "none" }}
                // 处理表格高度
                muiTableContainerProps={{ sx: { height: "540px" } }}
                // 设置背景颜色
                muiTableBodyCellProps={({ row }) => ({
                  onDoubleClick: (event) => {
                    // handleClickOpen();
                    console.info(event, row);
                  },
                  sx: {
                    backgroundColor: "white",
                  },
                })}
                muiTableHeadRowProps={{ sx: { boxShadow: "none" } }}
                muiTableBodyProps={{
                  sx: {
                    backgroundColor: "white",
                    tableLayout: "fixed",
                    boxShadow: "none",
                  },
                }}
                muiTableProps={{
                  sx: {
                    backgroundColor: "white",
                    tableLayout: "fixed",
                    boxShadow: "none",
                  },
                }}
                muiBottomToolbarProps={{
                  sx: { backgroundColor: "white", boxShadow: "none" },
                }}
                muiTopToolbarProps={{
                  sx: { backgroundColor: "white", boxShadow: "none" },
                }}
                // 记得开启这些 tips:否则会出现useEffect 反作用函数发起多次请求点击下一页会出问题
                manualFiltering
                manualPagination
                manualSorting
                // 开启分页
                enablePagination={false}
                // 列定义
                columns={columnsTemp}
                // 数据
                data={currentData}
                // 初始化国际化语言  --- todo: i18n key doc address: https://github.com/KevinVandy/material-react-table/blob/main/packages/material-react-table/src/_locales/en.ts
                localization={tableI18n}
                // 多选底部提示
                positionToolbarAlertBanner="none"
                // 开启action操作
                enableRowActions
                // action操作位置
                positionActionsColumn="last"
                renderRowActions={({ row, table }) => (
                  <Stack direction="row" alignItems="center">
                    <IconButton
                      color="primary"
                      aria-label="cliear"
                      component="label"
                      disabled={removeDisabled}
                      onClick={() => {
                        removeByIndex(row);
                      }}
                    >
                      <RemoveCircleOutlineIcon />
                    </IconButton>
                  </Stack>
                )}
              />
            </Grid>
          </Grid>
        </BootstrapContent>
        <BootstrapActions>
          <Button color="info" variant="outlined" onClick={handleClose}>
            {t("common.common_edit_cancel")}
          </Button>
          {/* <Button
            onClick={() => {
              setShow(true);
              //   addUploadMaterial.current.handleOpen();
            }}
            disableElevation
            variant="contained"
            color="warning"
          >
            {t("ips.ips_add_resource")}
          </Button> */}

          <Button
            onClick={() => {
              handelAppendMaterial();
            }}
            disableElevation
            variant="contained"
            color="primary"
          >
            {t("common.common_edit_save")}
          </Button>
        </BootstrapActions>
      </BootstrapDialog>
      <Dialog
        open={durationOpen}
        onClose={handleDurationClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <form noValidate onSubmit={durationFormik.handleSubmit}>
          <DialogTitle id="alert-dialog-title">
            {t("common.common_set_duration")}
            <IconButton
              aria-label="close"
              onClick={handleDurationClose}
              sx={{
                position: "absolute",
                right: 8,
                top: 8,
                color: (theme) => theme.palette.grey[500],
              }}
            >
              <CloseIcon />
            </IconButton>
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Stack spacing={1}>
                  <OutlinedInput
                    type="number"
                    id="text"
                    fullWidth
                    value={durationFormik.values.duration}
                    name="duration"
                    startAdornment={
                      <InputAdornment position="start">
                        <AccessTimeIcon />
                      </InputAdornment>
                    }
                    rows={8}
                    onBlur={durationFormik.handleBlur}
                    onChange={durationFormik.handleChange}
                    placeholder={t("common.common_input_duration")}
                    error={Boolean(
                      durationFormik.touched.duration &&
                        durationFormik.errors.duration
                    )}
                    disabled={props.disableDuration}
                  />
                  {durationFormik.touched.duration &&
                    durationFormik.errors.duration && (
                      <FormHelperText error id="text-error">
                        {durationFormik.errors.duration}
                      </FormHelperText>
                    )}
                </Stack>
                <Stack
                  sx={{ marginTop: "10px", color: "grey", fontSize: "0.75rem" }}
                >
                  {t("common.common_duraation_annotation")}
                </Stack>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Stack
              sx={{ width: "100%" }}
              direction="row"
              justifyContent="space-around"
              alignItems="center"
              spacing={2}
            >
              <Button
                onClick={handleDurationClose}
                color="info"
                variant="outlined"
              >
                {t("common.common_edit_cancel")}
              </Button>
              <Button
                type="submit"
                disabled={durationBtnDisabled}
                variant="contained"
                color="primary"
              >
                {t("common.common_confirm")}
              </Button>
            </Stack>
          </DialogActions>
        </form>
      </Dialog>
      <Dialog
        open={visible}
        maxWidth="lg"
        fullWidth={true}
        onClose={() => setVisible(false)}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <BootstrapDialogTitle onClose={() => setVisible(false)}>
          {t("common.common_op_preview")}
        </BootstrapDialogTitle>
        <DialogContent sx={{ height: "40em" }}>
          <PreViewRa showScale={true} programData={JSON.parse(layoutData)} />
        </DialogContent>
      </Dialog>
      <Preview ref={preview} url={previewUrl} type={previewType}></Preview>
      {/* <UploadMaterial
        ref={addUploadMaterial}
        open={show}
        onCancel={() => {
          setPagination({
            pageIndex: 0,
            pageSize: 10,
          });
          setShow(false);
        }}


      /> */}
    </div>
  );
});
export default AddMaterial;
