import {
  Grid,
  Card,
  InputAdornment,
  IconButton,
  Button,
  TextField,
  Typography,
  Stack,
  CircularProgress,
  Box,
} from "@mui/material";
import React, { forwardRef, useRef, useEffect, useState } from "react";
import { getSummaryOutletAndScreen } from "@/service/api/summary";
import OutletData from "./OutletData";
import DigitalSignage from "./DigitalSignage";
import { useTranslation } from "react-i18next";
const SummaryBottom = (props) => {
  const { t } = useTranslation();
  const [outletAndScreen, setOutletAndScreen] = useState({});
  useEffect(() => {
    if (props.retailClientId) {
      getSummaryOutletAndScreen({
        retailClientId: props.retailClientId,
      })
        .then((res) => {
          if (res.code == "00000000") {
            let resData = res.data;
            setOutletAndScreen(resData);
          } else {
            setOutletAndScreen({});
          }
        })
        .catch((e) => {
          setOutletAndScreen({});
        });
    } else {
      setOutletAndScreen({});
    }
  }, [props.retailClientId, props.areaId]);

  return (
    <Grid spacing={4} sx={{ mt: 2 }} container>
      <Grid
        style={{
          display: "flex",
          flexDirection: "column",
          justifyContent: "stretch",
          flexGrow: 1,
        }}
        item
        xs={12}
        sm={12}
        md={6}
        lg={6}>
        <Typography sx={{ ml: 2 }}> {t("summary.data_outlet")} </Typography>
        <OutletData outletData={outletAndScreen.outlet || {}} />
      </Grid>
      <Grid item xs={12} sm={12} md={6} lg={6}>
        <Typography sx={{ ml: 2 }}>{t("summary.signage")}</Typography>
        <DigitalSignage signageData={outletAndScreen.screen || {}} />
      </Grid>
    </Grid>
  );
};

export default SummaryBottom;
