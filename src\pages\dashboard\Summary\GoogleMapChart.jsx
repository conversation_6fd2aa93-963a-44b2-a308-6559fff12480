import React, { forwardRef, useRef, useEffect, useState } from "react";
import * as echarts from "echarts";
import { Loader } from "@googlemaps/js-api-loader";
import GradientBox from "@/components/GradientBox";
import { getSummaryRetailFmap } from "@/service/api/summary";
import { useTranslation } from "react-i18next";
import "echarts-extension-gmap";
const GoogleMapChart = (props) => {
  const { center } = props;
  const chartRef = useRef(null);
  const [myEcharts, setMyEcharts] = useState(null);
  const [chartData, setChartData] = useState([]);
  const [position, setPosition] = useState({ lat: 24.608866, lng: 118.03619 });
  const [map, setMap] = useState(null);
  const [zoom, setZoom] = useState(1);
  const { t } = useTranslation();
  const initMapChart = () => {
    let language = "en";
    let zkBioCloudMediaLang = sessionStorage.getItem("zkBioCloudMediaLang");
    if (zkBioCloudMediaLang && zkBioCloudMediaLang == "zh") {
      language = "zh";
    }
    const loader = new Loader({
      apiKey: "AIzaSyA9MaTVJlWIWpINjcgyJl5eS6JDhe60238",
      version: "weekly",
      libraries: ["maps,marker"],
      language: language,
      id: "map",
    });
    loader.load().then(async () => {
      let chart = echarts.init(chartRef.current, null, { renderer: "svg" });
      setMyEcharts(chart);
      const options = getOptions(chartData);
      chart.setOption(options);
      // get google map instance
      var gmap = chart.getModel().getComponent("gmap").getGoogleMap();
      // Add some markers to map
      // eslint-disable-next-line no-undef
      // var marker = new google.maps.Marker({ position: gmap.getCenter() });
      // marker.setMap(gmap);
      // Add TrafficLayer to map
      // eslint-disable-next-line no-undef
      var trafficLayer = new google.maps.TrafficLayer();
      trafficLayer.setMap(gmap);
      gmap.addListener("zoom_changed", (e) => {
        let zoom = gmap.getZoom();

        setZoom(zoom);
        // // 3 seconds after the center of the map has changed, pan back to the
        // // marker.
        // window.setTimeout(() => {
        //   map.panTo(marker.getPosition() as google.maps.LatLng);
        // }, 3000);
      });
      // 设置初始大小
      chart.resize();
      // 监听窗口大小变化，自动调整图表大小
      window.addEventListener("resize", handleResize);
      chart.on("click", "series", function (e) {
        let position = e.data.position;
        if (position) {
          let center = { lat: position[1], lng: position[0] };
          setPosition(center);
          setZoom(14);
        }
      });
      setMap(gmap);
    });
  };

  useEffect(() => {
    if (props.retailClientId && props.areaId) {
      getSummaryRetailFmap({
        retailClientId: props.retailClientId,
        areaId: props.areaId,
      })
        .then((res) => {
          if (res.code == "00000000") {
            let resData = res.data || [];
            let dataTemp = resData.map((item) => {
              let location = item.location.split(",");
              return {
                name: item.areaName,
                totalOutlet: item.totalOutlet,
                totalScreen: item.totalScreen,
                position: [Number(location[0]), Number(location[1])],
              };
            });

            setChartData(dataTemp);
          } else {
            setChartData([]);
          }
        })
        .catch((e) => {
          setChartData([]);
        });
    } else {
      setChartData([]);
    }
  }, [props.retailClientId, props.areaId]);

  const handleResize = () => {
    if (myEcharts) {
      myEcharts.resize();
    }
  };

  useEffect(() => {
    if (center) {
      let location = center.split(",");
      if (location.length === 2) {
        let position = { lat: Number(location[1]), lng: Number(location[0]) };
        setPosition(position);
      }
    }
  }, [center]);
  useEffect(() => {
    if (map) {
      map?.panTo(position);
    }
  }, [position]);
  useEffect(() => {
    if (myEcharts === null) {
      initMapChart();
    } else {
      const options = getOptions(chartData);
      myEcharts.setOption(options);
    }
  }, [chartData, zoom]);

  const getOptions = (tempData) => {
    var option = {
      gmap: {
        // initial options of Google Map
        // See https://developers.google.com/maps/documentation/javascript/reference/map#MapOptions for details
        // initial map center, accepts an array like [lng, lat] or an object like { lng, lat }
        center: position,
        // center: { lng: 108.39, lat: 39.9 },
        // initial map zoom
        zoom: zoom,
        // whether ECharts layer should be re-rendered when the map is moving. `true` by default.
        // if false, it will only be re-rendered after the map `moveend`.
        // It's better to set this option to false if data is large.
        renderOnMoving: true,
        // the zIndex of echarts layer for Google Map. `2000` by default.
        echartsLayerZIndex: 2019,
        // whether to enable gesture handling. `true` by default.
        // since v1.4.0
        roam: true,
        maxZoom: 14,
        minZoom: 1,
        mapTypeControl: false,
        zoomControl: true,
        ZoomControlOptions: {
          // eslint-disable-next-line no-undef
          style: {
            fontSize: "12px",
          },
        },
        mapTypeId: "roadmap",
        fullscreenControl: true,
        streetViewControl: false,
        // More initial options...
      },
      animation: true,
      tooltip: {
        show: true,
        trigger: "item",
      },
      series: [
        {
          type: "lines",
          z: 3,
          coordinateSystem: "gmap",
          symbolSize: [10, 0], //只保留地图端标记
          opacity: 1,
          label: {
            show: true,
            position: "end",
            formatter: function (params) {
              //文本提示框
              return (
                "{value|" +
                params.data.totalOutlet +
                "} & {value2|" +
                params.data.totalScreen +
                "}"
              );
            },
            backgroundColor: "#ffffff",
            borderColor: "#7ac143",
            borderWidth: 1,
            borderRadius: 4,
            height: 20,
            lineHeight: 20,
            padding: 2,
            align: "center",
            rich: {
              //标题样式
              value: {
                //内容样式
                height: 20,
                fontSize: "12px",
                color: "blue",
                backgroundColor: "#fff",
              },
              value2: {
                //内容样式
                height: 20,
                fontSize: "12px",
                color: "#7ac143",
                backgroundColor: "#fff",
              },
            },
          },
          lineStyle: {
            //视觉引导线属性
            type: "solid",
            opacity: 1,
            color: "#595959", //引导线颜色
            curveness: 0,
          },
          tooltip: {
            show: true,
            trigger: "item",
            formatter: function (params, ticket, callback) {
              let { name, totalOutlet, totalScreen } = params.data;
              return `<div>
                               <div>${name}</div>
                               <div><label> ${t(
                                 "outlet.total_outlets"
                               )} :<label>${totalOutlet}</div>
                               <div><label> ${t(
                                 "summary.digital_signage"
                               )} :<label>${totalScreen}</div>
                            </div>`;
            },
          },
          data: tempData.map((item, index) => {
            let l = 9;
            let t = 9;
            let curveness = 0.2;
            if (zoom === 1) {
              l = 17;
              t = 16;
            } else if (zoom === 2) {
              l = 9;
              t = 9;
            } else if (zoom === 3) {
              l = 6;
              t = 4;
            } else if (zoom === 4) {
              l = 3;
              t = 3;
            } else if (zoom === 5) {
              l = 2;
              t = 2;
            } else if (zoom === 6) {
              l = 0.7;
              t = 0.7;
            } else if (zoom === 7) {
              l = 0.3;
              t = 0.3;
            } else if (zoom === 8) {
              l = 0.2;
              t = 0.2;
            } else if (zoom === 9) {
              l = 0.1;
              t = 0.1;
            } else if (zoom === 10) {
              l = 0.05;
              t = 0.05;
            } else if (zoom === 11) {
              l = 0.02;
              t = 0.02;
            } else if (zoom === 12) {
              l = 0.01;
              t = 0.01;
            } else if (zoom === 13) {
              l = 0.005;
              t = 0.005;
            } else if (zoom === 14) {
              l = 0.002;
              t = 0.002;
            }
            let label = {
              lineStyle: {
                curveness: curveness,
              },
            };
            let indexTemp = index % 2 === 0 ? 1 : -1;
            return Object.assign(
              {
                coords: [
                  item.position,
                  [item.position[0] + indexTemp * l, item.position[1] + t],
                ],
                ...label,
              },
              item
            );
          }),
        },
      ],
    };
    return option;
  };

  return (
    <GradientBox
      style={{
        width: "100%",
        height: "100%",
        flexGrow: 1,
        padding: "2px",
        margin: 0,
      }}>
      <div
        style={{
          width: "100%",
          height: "100%",
        }}
        ref={chartRef}></div>
    </GradientBox>
  );
};

export default GoogleMapChart;
