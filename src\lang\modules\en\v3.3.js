export default {
  common: {
    add_tenant_user_title: "Add Tenant User",
    edit_tenant_user_title: "Edit Tenant User",
    tenant_user: "Tenant User",
    allocate_merchant: "Allocate Principal",
    loading_message: "Processing, please wait...",
    no_select_lower_version:
      "Cannot select a firmware version lower than the current one.",
    confirm_upgrade_version:
      "Are you sure you want to upgrade the device to version: {{version}}?",
    device_upgrade_list: "Device Upgrade List",
    warm_tips: "Helpful Tips",
    if_same_version:
      "If the versions are the same, the one with the latest time will be prioritized!",
    please_choose_larger_version:
      "Please choose a version greater than the currently selected device version; otherwise, the upgrade will not be possible:",
    all_current_device_version: "Current versions of all devices:",
    upgrade_list_column_name: "Firmware Name",
    upgrade_list_column_version: "Version Number",
    upgrade_list_column_time: "Release Date",
    deivce_modal_inconsistency: "Inconsistent device model",
    create_time_subscription_start: "Sub Start Time",
    create_time_subscription_end: "Sub End Time",
    expirate_time_subscription_start: "Exp Start Time",
    expirate_time_subscription_end: "Exp End Time",
    gen_picture_num_error:
      "The number of images to generate is incorrect. The range should be between 1 and 4.",
    please_picture_content: "Please enter the image content.",
    ai_text_to_picture: "AI Text-to-Image",
    ali_ai_wanxiang: "Model 1",
    picture_content_desc: "Image Content Description",
    please_content_placeholder: "Please enter content.",
    gen_picture_num: "Number of Images to Generate",
    gen_image_btn_text: "Generate Image",
    gen_image_size: "Image Size",
    gen_image_style: "Image Style",
    gen_image_style_random: "Random Below",
    gen_image_style_photography: "Photographic",
    gen_image_style_portrait: "Portrait",
    gen_image_style_anime: "Anime Style",
    gen_image_style_oil_painting: "Oil Painting",
    gen_image_style_watercolor: "Watercolor",
    gen_image_style_sketch: "Sketch",
    gen_image_style_flat_illustration: "Flat Illustration",
    gen_image_style_painting: "Chinese Painting",
    gen_image_style_cartoon: "3D Cartoon",
    gen_image_preview: "Preview",
    select_image_import: "Select Image to Import",
    import_resource: "Import Content Resources",
    ai_model_loading_gen:
      "The large model is generating. Please wait patiently...",
    text_to_image: "Text-to-Image",
    text_to_image_desc:
      "Enter the natural language description for the image you want to generate on the left.",
    image_no_gen_error: "The image has not been generated yet.",
    import_image_btn: "Import Image",
    gen_model_1: "Model 1",
    gen_model_2: "Model 2",
    gen_model_2_tips:
      "Does not support Chinese, and only one image can be generated at a time.",
    negative_prompt_max_length_error:
      "The negative prompt must not exceed 500 characters.",
    prompt_max_length_error: "The prompt must not exceed 500 characters.",
    negative_prompt_label: "Negative Prompt",
    negative_prompt_label_tips:
      "Negative Prompt: Refers to content that you do not want to see in the image. By describing the negative prompt, you can limit the image. Supports Chinese and English, with a maximum length of 500 characters. Any excess will be automatically truncated.",
    disclaimer_message:
      "This service is developed based on open-source AI models. The generated content does not represent the views of this site. Please use it in compliance with laws and regulations, and assume all responsibilities for the generated content.",
    event_config: "Event Configuration",
    event_push_channel: "Push Channel",
    event_offline_push: "Offline Event Push",
    event_offline_push_desc:
      "When the device is offline, push to the responsible person",
    event_enable: "Enable",
    event_disable: "Disable",
    event_push_channel_field: "Push Channel",
    event_push_channel_field_desc:
      "Which contact methods will be used to push device event notifications.",
    event_push_channel_sms: "SMS",
    event_push_channel_email: "Email",
    event_push_admin_config: "Admin Push Contact Information",
    event_push_admin_phone: "Phone Number",
    event_push_admin_email: "Email",
    event_push_admin_config_desc:
      "Global push contact, by default only the store's contact phone number and email are pushed. Configure multiple contacts separated by commas (English comma)",
    event_push_admin_config_phone_tips:
      "Separated by commas, for example: +86-13xxxx,+86-17xxxx",
    event_push_admin_config_email_tips:
      "Separated by commas, for example: <EMAIL>,<EMAIL>",
    event_push_channel_form_error_msg:
      "At least one push channel must be selected",
    event_push_admin_config_email_form_msg:
      "Incorrect email format, multiple emails should be separated by commas",
    event_push_admin_config_phone_form_msg:
      "Incorrect phone number format, should be +country code-phone number, multiple numbers separated by commas",
    event_push_admin_config_email_form_unique_msg:
      "Phone numbers cannot be repeated",
    event_push_admin_config_phone_form_unique_msg: "Emails cannot be repeated",
    please_merchant_operation:
      "Please select a retailer before performing the operation",

    veryWeak: "Very Weak",
    weak: "Weak",
    general: "General",
    strong: "Strong",
    veryStrong: "Very Strong",
    adb_mode: "ADB mode",
    clean_time_power_device:
      "The equipment for clearing timed power on/off is: {{names}}",
    psd_material: "PSD materials",
  },
  menu: {
    add_tenant_user: "Add Tenant User",
    edit_tenant_user: "Edit Tenant User",
    upload_firmware: "Upload firmware package",
    media_system_config: "System Configuration",
    material_add_label: "Material Tags",
    event_push_config: "Event Push Configuration",
    event_push_config_update: "Update Alert Configuration",
    mobile_manager: "O&M  Management",
    app_version: "Mobile App  Management",
    app_version_add: "Add APP version",
    app_version_edit: "Edit APP version",
    app_version_del: "Delete APP version",
    common_mobile_layout: "Mobile Template",
    templateList_psd: "Import PSD",
    time_power_on_off: "Timed power",
    clean_time_power_on_off: "Clear Timed power",
  },
  ips: {
    ips_in_year: "Year",
    ips_sub_month: "Subscription Month",
    ips_sub_year: "Subscription Term",
    ips_clear_log_report: "Clear Log Records",
    ips_pls_day_num: "Please select the number of days",
    ips_clear_time_offscale_record:
      "Clear records outside the duration range (days)",
    ips_clear_device_offline_stat_time: "Device downtime statistics duration",
    ips_clear_real_time_content_time: "Real time content duration",
    ips_clear_infomercial_stat_time: "Ad statistics duration",
    ips_clear_login_log_time: "Login log duration",
    ips_clear_oper_log_time: "Operation log duration",
    ips_clear_device_shot_time: "Device screenshot duration",
    ips_clear_device_log_time: "Device log duration",
    ips_selected_retailer_list: "Selected principal list",
    ips_template_publish_type: "Publication Type",
    ips_template_publish_public: "Public",
    ips_template_publish_private: "Private",
    ips_plz_select_template_type: "Please select the publishing type",
    ips_everyday_execute: "Execute once a day",
  },
  server: {
    server_eventConfig_update: "Update Alert Configuration",
  },
  subscription: {
    updateSub: "Update",
    moreDevice: "More Device",
    extend: "Extend",
    plsInputMonthNum: "Please enter the number of months",
    plsInputYearNum: "Please enter the year and quantity",
    plsInputAddMonthNum: "Please enter the number of new months added",
    plsInputAddYearNum: "Please enter the number of new years added",
    currentSubDeviceNum: "Current number of subscribed devices",
    generalDashboard: "General Dashboard",
    demographicDashboard: "Demographic Dashboard",
    targetedPromotion: "Targeted Promotion (Age and gender)",
    US$5: "US$5",
    advanced_package_tip:
      "per device/month(billed annually) US$6 billed monthly",
    material_add_label: "Material Tags",
    material_label_age: "Age Group",
    added_device_num: "Number of newly added devices",
  },
  event: {
    device_alert: "Device Alert",
    device_event_network_offline: "Device Network Disconnected",
    eventlevels_1: "Severe",
    eventlevels_2: "High",
    eventlevels_3: "Medium",
    eventlevels_4: "Low",
    eventlevels_5: "Very Low",
    column_event_name: "Event Name",
    column_event_level: "Event Level",
    column_event_alert_time: "Event Alert Time",
    event_table_tips:
      "The current browser time zone is: {{timezone}}, the event alert times have been converted to the browser time zone.",
  },
  appVersion: {
    delete_application:
      "Confirm whether to delete the version named: {{name}}?",
    "AppVersionForm.238884-0": "Please enter the app name",
    "AppVersionForm.238884-1": "Please select the app type",
    "AppVersionForm.238884-2": "Please enter the version number",
    "AppVersionForm.238884-3": "Please choose whether to remind",
    "AppVersionForm.238884-4": "Please select whether to force an update",
    "AppVersionForm.238884-5": "Please enter the update content",
    "AppVersionForm.238884-6": "Please enter the update address",
    "AppVersionForm.238884-7": "Add new APP version",
    "AppVersionForm.238884-8": "APP Name",
    "AppVersionForm.238884-9": "Please enter the app name",
    "AppVersionForm.238884-10": "Type",
    "AppVersionForm.238884-11": "Android",
    "AppVersionForm.238884-12": "Please select",
    "AppVersionForm.238884-13": "Minimum version number",
    "AppVersionForm.238884-14": "Please enter the app version number",
    "AppVersionForm.238884-15": "Is there a reminder",
    "AppVersionForm.238884-16": "No",
    "AppVersionForm.238884-17": "Yes",
    "AppVersionForm.238884-18": "Force Update",
    "AppVersionForm.238884-19": "No",
    "AppVersionForm.238884-20": "Yes",
    "AppVersionForm.238884-21": "Update Content",
    "AppVersionForm.238884-22": "Update Address",
    "AppVersionForm.238884-23": "Edit APP Version",
    "AppVersionForm.238884-24": "Version number format error",
    "index.511972-0": "Version Name",
    "index.511972-1": "Minimum version number",
    "index.511972-2": "Type",
    "index.511972-3": "Android",
    "index.511972-4": "Is there a reminder",
    "index.511972-5": "Yes",
    "index.511972-6": "No",
    "index.511972-7": "Force Update",
    "index.511972-8": "Yes",
    "index.511972-9": "No",
    "index.511972-10": "Update Content",
    "index.511972-11": "Update Address",
    "index.511972-12": "Please enter the name",
    "index.511972-13": "Add new version",
    "AppVersionForm.290174-0": "Please select the app type",
    "AppVersionForm.290174-1": "Please choose whether to remind",
    "AppVersionForm.290174-2": "Please select if it is a forced update",
    "index.546717-0": "Please select the version type",
    "index.546717-1": "Please choose whether to remind",
    "index.546717-2": "Please select if it is a forced update",
  },
  RealTime: {
    RealTimeAge: "Age",

    RealTimeAgePercentageOf: "Percentage of",
    RealTimeAgePercentage: "Visitors Age Range",

    RealTimeGenderPercentageOf: "Percentage of",
    RealTimeGenderPercentage: "Visitors Gender Range",

    RealTimeVisitors: "Visitors",
    RealTimeMale: "Male",
    RealTimeFemale: "Female",
    RealTime_Entry_Flow: "Entry Flow ",
    RealTime_Trend: " Trend",

    RealTime_Gender: "Gender ",
    RealTime_Distribution: " Distribution",
    applyFillter: "Query",
    enter_device_name: "please input device name",
    Period: "Period",
    input_screen_name: "Enter the signage name",
    input_select_time_range: "Select the time range",
    duration: "Duration ",
    Trend: "Trend",
  },
  screen: {
    valudate_number_error_text: "Please enter a number between 0 and 10000.",
    base_parameter: "Basic parameters",
    play_parameter: "Play parameters",
    play_material_fill: "Material filling",
    play_material_fill_direction: "Filling direction",
    play_material_fill_direction_please: "Please select the filling direction",
    play_material_fill_quantity: "Number of pixels",
    saveing: "Saving...",
    up: "Up",
    down: "Down",
    left: "Left",
    right: "Right"
  }
};
