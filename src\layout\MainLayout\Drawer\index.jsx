import React from "react";
import PropTypes from "prop-types";
import { useMemo } from "react";

// material-ui
import { useTheme } from "@mui/material/styles";
import { Box, Drawer, useMediaQuery } from "@mui/material";

// project import
// import DrawerHeader from "./DrawerHeader";
import DrawerContent from "./DrawerContent";
import MiniDrawerStyled from "./MiniDrawerStyled";
import { drawerWidth } from "@/config/config";

// ==============================|| MAIN LAYOUT - DRAWER ||============================== //

const MainDrawer = ({ open, handleDrawerToggle, window, className }) => {
  const theme = useTheme();
  const matchDownMD = useMediaQuery(theme.breakpoints.down("lg"));

  // responsive drawer container
  const container =
    window !== undefined ? () => window().document.body : undefined;

  // header content
  const drawerContent = useMemo(() => <DrawerContent />, []);
  // const drawerHeader = useMemo(() => <DrawerHeader open={open} />, [open]);

  return (
    <div className={className}>
      {!matchDownMD ? (
        <div className={"h-full w-full flex relative flex-col gap-2"}>
          <div className="flex-1 h-full w-full">{drawerContent}</div>
        </div>
      ) : (
        <Drawer
          container={container}
          variant="temporary"
          open={open}
          onClose={handleDrawerToggle}
          className="w-full"
          ModalProps={{ keepMounted: true }}
          sx={{
            display: { xs: "block", lg: "none" },
            "&.MuiDrawer-paper": {
              background: "transparent",
              boxSizing: "border-box",
              width: drawerWidth,
              // borderRight: `1px solid ${theme.palette.divider}`,
              backgroundImage: "none",
              boxShadow: "inherit",
            },
          }}>
          {/* {open && drawerHeader} */}
          {open && drawerContent}
        </Drawer>
      )}
    </div>
  );
};

MainDrawer.propTypes = {
  open: PropTypes.bool,
  handleDrawerToggle: PropTypes.func,
  window: PropTypes.object,
};

export default MainDrawer;

// {
//   !matchDownMD ? (
//     <div className={"h-full w-full flex relative flex-col gap-2"}>
//       <div className="w-full flex items-center h-[50px] justify-center">
//         <Logo />
//       </div>
//       <div className="flex-1 h-full w-full">{drawerContent}</div>
//     </div>
//   ) : (
//     <Drawer
//       container={container}
//       variant="temporary"
//       open={open}
//       onClose={handleDrawerToggle}
//       ModalProps={{ keepMounted: true }}
//       sx={{
//         display: { xs: "block", lg: "none" },
//         "&.MuiDrawer-paper": {
//           background: "transparent",
//           boxSizing: "border-box",
//           width: drawerWidth,
//           // borderRight: `1px solid ${theme.palette.divider}`,
//           backgroundImage: "none",
//           boxShadow: "inherit",
//         },
//       }}>
//       {/* {open && drawerHeader} */}
//       {open && drawerContent}
//     </Drawer>
//   );
// }
