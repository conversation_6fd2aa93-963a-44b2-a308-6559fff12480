/* eslint-disable react/prop-types */
/* eslint-disable react-hooks/rules-of-hooks */
import React, {
  useMemo,
  useState,
  useEffect,
  useRef,
  forwardRef,
  useImperativeHandle,
} from "react";
import MaterialReactTable, {
  MRT_ShowHideColumnsButton,
  MRT_ToggleGlobalFilterButton,
} from "material-react-table";
import { tableI18n } from "@/utils/tableLang";
import {
  Box,
  Stack,
  Grid,
  Button,
  Typography,
  InputLabel,
  FormHelperText,
  RadioGroup,
  Radio,
  FormControlLabel,
  Select,
  MenuItem,
  OutlinedInput,
  InputAdornment,
  Snackbar,
  Alert,
  AlertTitle,
} from "@mui/material";
import * as Yup from "yup";
import { useFormik } from "formik";
import RemoveCircleOutlineIcon from "@mui/icons-material/RemoveCircleOutline";
import IconButton from "@mui/material/IconButton";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import CloseIcon from "@mui/icons-material/Close";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import { styled } from "@mui/material/styles";

import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import VisibilityIcon from "@mui/icons-material/Visibility";
import FullscreenIcon from "@mui/icons-material/Fullscreen";
import FullscreenExitIcon from "@mui/icons-material/FullscreenExit";
import { listByPage } from "@/service/api/material";
import Preview from "@/pages/program/material/components/Preview";
import { useTranslation } from "react-i18next";
import UploadMaterial from "@/pages/program/material/components/UploadMaterial";

const BootstrapDialog = styled(Dialog)(({ theme }) => ({
  "& .MuiDialogContent-root": {
    padding: theme.spacing(2),
  },
  "& .MuiDialogActions-root": {
    padding: theme.spacing(1),
  },
}));

const BootstrapDialogTitle = (props) => {
  const { children, onFullscreen, fullscreen, ...other } = props;
  onFullscreen;
  return (
    <DialogTitle sx={{ m: 0, p: 2 }} {...other}>
      {children}
      {onFullscreen ? (
        <IconButton
          aria-label="close"
          onClick={onFullscreen}
          sx={{
            position: "absolute",
            right: 8,
            top: 8,
            color: (theme) => theme.palette.grey[500],
          }}>
          {fullscreen || fullscreen == true ? (
            <FullscreenExitIcon />
          ) : (
            <FullscreenIcon />
          )}
        </IconButton>
      ) : null}
    </DialogTitle>
  );
};

const AddMusic = forwardRef((props, ref) => {
  const { t } = useTranslation();

  const [open, setOpen] = React.useState(false);
  const [durationOpen, setDurationOpen] = React.useState(false);
  const [fullScreen, setFullScreen] = React.useState(false);
  const [isError, setIsError] = useState(false);
  const preview = React.useRef(null);
  // 预览url
  const [previewUrl, setPreviewUrl] = useState("");
  //预览文件类型
  const [previewType, setPreviewType] = useState("");
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  // 表格数据
  const [data, setData] = useState([]);
  const [tempValue, setTemplateValue] = useState({});
  const [currentData, setCurrentData] = useState([]);
  // 分页的参数
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);
  // 过滤参数
  const [globalFilter, setGlobalFilter] = useState("");
  // 排序参数
  const [sorting, setSorting] = useState([]);
  // 列过滤
  const [columnFilters, setColumnFilters] = useState([]);
  // 构建参数
  const buildParams = () => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      keyWard: globalFilter,
      type: "audio",
      orderProp: "createTime",
      orderBy: "desc",
    };
    return params;
  };
  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "name", //access nested data with dot notation
        header: t("common.common_name"),
        size: 120,
      },
      {
        accessorKey: "advertiserName", //access nested data with dot notation
        header: t("common.common_advertiser"),
        size: 120,
      },
      {
        accessorKey: "duration", //access nested data with dot notation
        header: t("editor.editor_audio_duration"),
        size: 120,
        Cell: ({ cell, row }) => {
          return <Typography>{row.original.duration} s</Typography>;
        },
      },
    ],
    []
  );

  const columnsTemp = useMemo(
    () => [
      {
        accessorKey: "name", //access nested data with dot notation
        header: t("common.common_name"),
        size: 120,
      },
      {
        accessorKey: "advertiserName", //access nested data with dot notation
        header: t("common.common_advertiser"),
        size: 120,
      },
      {
        accessorKey: "duration", //access nested data with dot notation
        header: t("ips.ips_program_durationss"),
        size: 120,
        Cell: ({ cell, row }) => {
          if (
            row.original.duration == undefined ||
            row.original.duration === null
          ) {
            return <Typography>-</Typography>;
          } else {
            return <Typography>{row.original.duration}s</Typography>;
          }
        },
      },
    ],
    []
  );

  useEffect(() => {
    if (open) {
      handleListMaterial();
    }
  }, [globalFilter, pagination]);

  //请求素材列表
  const handleListMaterial = async () => {
    await listByPage(buildParams()).then((res) => {
      setData(res.data.data);
      // 设置总记录数
      setRowCount(res.data.total);
      setIsLoading(false);
      setIsRefetching(false);
    });
  };

  useImperativeHandle(ref, () => ({
    handleClose,
    handleClickOpen,
  }));
  const handleClickOpen = () => {
    setCurrentData([]);
    setOpen(true);
    handleListMaterial();
  };
  const handleClose = () => {
    setOpen(false);
  };

  const handleClickDurationOpen = () => {
    durationFormik.handleReset();
    setDurationOpen(true);
  };
  const handleDurationClose = () => {
    setDurationOpen(false);
  };
  // 添加一个到右边的表中
  const addMaterial = (row) => {
    return new Promise((resolve, reject) => {
      setTemplateValue(row.original);
      handleClickDurationOpen();
      resolve("success");
    });
  };
  // 表单提交
  const handelMaterialSubmit = (values) => {
    const temp = { ...tempValue };
    temp.duration = values.duration;
    const tempMaterialValue = [...currentData];
    tempMaterialValue.push(temp);
    setCurrentData(tempMaterialValue);
    handleDurationClose();
  };
  //  表单
  const durationFormik = useFormik({
    initialValues: {
      duration: "",
    },
    onSubmit: (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        handelMaterialSubmit(values);
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
    validationSchema: Yup.object().shape({
      duration: Yup.number()
        .required("请输入播放时长")
        .moreThan(1, "时长至少大于1s"),
    }),
  });

  //控制提示框是否显示
  const [tipsOpen, setTipsOpen] = React.useState(false);
  const handleCloseTips = () => {
    setTipsOpen(false);
  };
  const handelAppendMaterial = () => {
    let selectedMaterialTypeList = [];
    currentData.forEach((data) => {
      data.materialId = data.id;
      data.id = null;
      selectedMaterialTypeList.push(data.type);
    });
    let newMaterialTypeList = [
      ...selectedMaterialTypeList,
      ...props.materialTypeList(),
    ];
    console.log(newMaterialTypeList);
    if (
      newMaterialTypeList.indexOf("media") != -1 &&
      newMaterialTypeList.indexOf("audio") != -1
    ) {
      setTipsOpen(true);
    } else {
      // 最后添加
      props.setMaterialLists(currentData);
      handleClose();
    }
  };

  //获取预览地址
  const handlePreview = async (values) => {
    setPreviewUrl(values.original.downloadUrl);
    setPreviewType(values.original.type);
    preview.current.handleOpen();
  };
  const addUploadMaterial = React.useRef(null);
  return (
    <div>
      <Snackbar
        open={tipsOpen}
        autoHideDuration={5000}
        onClose={handleCloseTips}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}>
        <Alert severity="error" onClose={handleCloseTips}>
          <AlertTitle>Error</AlertTitle>
          <strong> {t("editor.editor_video_and_audio")} </strong>
        </Alert>
      </Snackbar>
      <BootstrapDialog
        fullScreen={fullScreen}
        fullWidth
        maxWidth="xl"
        onClose={handleClose}
        aria-labelledby="customized-dialog-title"
        open={open}>
        <BootstrapDialogTitle
          fullscreen={fullScreen}
          onFullscreen={() => {
            setFullScreen(!fullScreen);
          }}>
          <Typography variant="h4" component="p">
            {t("ips.ips_add_media_music")}
          </Typography>
        </BootstrapDialogTitle>
        <DialogContent dividers>
          <Grid container spacing={7}>
            <Grid item xs={7}>
              <MaterialReactTable
                // table 状态
                state={{
                  // 加载状态
                  isLoading,
                  // 分页参数
                  pagination,
                  // 重新拉取
                  showProgressBars: isRefetching,
                  showAlertBanner: isError,
                  columnFilters,
                  globalFilter,
                  sorting,
                  density: "compact",
                }}
                renderToolbarInternalActions={({ table }) => (
                  <>
                    <MRT_ToggleGlobalFilterButton table={table} />
                    {/* <MRT_ToggleFiltersButton table={table} /> */}
                    <MRT_ShowHideColumnsButton table={table} />
                    {/* <MRT_ToggleDensePaddingButton table={table} /> */}
                    {/* <MRT_FullScreenToggleButton table={table} /> */}
                  </>
                )}
                displayColumnDefOptions={{
                  "mrt-row-actions": {
                    // header: "操作",
                    header: t("common.common_relatedOp"), //change header text
                    size: 70,
                  },
                }}
                muiTablePaperProps={{
                  elevation: 0,
                  sx: {
                    border: "1px solid",
                    borderColor: "#e6ebf1",
                  },
                }}
                autoResetPageIndex={false}
                // 是否开启关闭头部底部工具类
                enableTopToolbar={true}
                enableColumnActions={false}
                enableBottomToolbar={true}
                // 关闭过滤搜素
                enableColumnFilters={false}
                // 关闭排序
                enableSorting={false}
                // 布局方式
                layoutMode="grid"
                // 开启列对齐
                muiTableHeadCellProps={{
                  sx: {
                    "& .Mui-TableHeadCell-Content": {
                      justifyContent: "left",
                    },
                  },
                }}
                // 解决列太多宽度太长问题
                enableColumnResizing
                // enablePinning
                // 初始化状态
                initialState={{ columnVisibility: { createTime: true } }}
                muiToolbarAlertBannerProps={
                  isError
                    ? {
                        color: "error",
                        children: "Error loading data",
                      }
                    : undefined
                }
                // 开启多选
                // enableRowSelection
                // 列数
                rowCount={rowCount}
                // 固定头部
                enableStickyHeader
                sx={{ boxShadow: "none" }}
                // 处理表格高度
                muiTableContainerProps={{ sx: { minHeight: "600px" } }}
                // 设置背景颜色
                muiTableBodyCellProps={({ row }) => ({
                  onDoubleClick: (event) => {
                    handleClickOpen();
                    console.info(event, row);
                  },
                  sx: {
                    backgroundColor: "white",
                  },
                })}
                muiTableHeadRowProps={{ sx: { boxShadow: "none" } }}
                muiTableBodyProps={{
                  sx: {
                    backgroundColor: "white",
                    tableLayout: "fixed",
                    boxShadow: "none",
                  },
                }}
                muiTableProps={{
                  sx: {
                    backgroundColor: "white",
                    tableLayout: "fixed",
                    boxShadow: "none",
                  },
                }}
                muiBottomToolbarProps={{
                  sx: { backgroundColor: "white", boxShadow: "none" },
                }}
                muiTopToolbarProps={{
                  sx: { backgroundColor: "white", boxShadow: "none" },
                }}
                // 分页回调函数
                onPaginationChange={setPagination}
                // 记得开启这些 tips:否则会出现useEffect 反作用函数发起多次请求点击下一页会出问题
                manualFiltering
                manualPagination
                manualSorting
                // 列过滤
                onColumnFiltersChange={setColumnFilters}
                // 全局过滤
                onGlobalFilterChange={setGlobalFilter}
                // 排序
                onSortingChange={setSorting}
                // 开启分页
                enablePagination={true}
                // 列定义
                columns={columns}
                // 数据
                data={data}
                // 初始化国际化语言  --- todo: i18n key doc address: https://github.com/KevinVandy/material-react-table/blob/main/packages/material-react-table/src/_locales/en.ts
                localization={tableI18n}
                // 多选底部提示
                positionToolbarAlertBanner="none"
                // 开启action操作
                enableRowActions
                // action操作位置
                positionActionsColumn="last"
                renderRowActions={({ row, table }) => (
                  <Stack direction="row" alignItems="center">
                    <IconButton
                      color="primary"
                      aria-label="cliear"
                      component="label"
                      onClick={() => handlePreview(row)}>
                      <VisibilityIcon />
                    </IconButton>
                    <IconButton
                      color="primary"
                      aria-label="cliear"
                      component="label"
                      onClick={() => {
                        addMaterial(row);
                      }}>
                      <AddCircleOutlineIcon />
                    </IconButton>
                  </Stack>
                )}
              />
            </Grid>
            <Grid item xs={5}>
              <MaterialReactTable
                // table 状态
                state={{
                  // 加载状态
                  isLoading,
                  // 分页参数
                  pagination,
                  // 重新拉取
                  showProgressBars: isRefetching,
                  showAlertBanner: isError,
                  columnFilters,
                  globalFilter,
                  sorting,
                  density: "compact",
                }}
                enableRowOrdering
                // enableRowSelection
                // muiTableBodyCellProps={({ cell }) => ({
                //     onDoubleClick: (event) => {
                //         console.log(event, cell);
                //     }
                // })}
                displayColumnDefOptions={{
                  "mrt-row-actions": {
                    header: "操作",
                    // header: t("common.common_relatedOp"), //change header text
                    size: 70,
                  },
                  "mrt-row-drag": {
                    header: "排序",
                  },
                }}
                muiTablePaperProps={{
                  elevation: 0,
                  sx: {
                    border: "1px solid",
                    borderColor: "#e6ebf1",
                  },
                }}
                muiTableBodyRowDragHandleProps={({ table }) => ({
                  onDragEnd: () => {
                    const { draggingRow, hoveredRow } = table.getState();
                    if (hoveredRow && draggingRow) {
                      currentData.splice(
                        hoveredRow.index,
                        0,
                        currentData.splice(draggingRow.index, 1)[0]
                      );
                      setCurrentData([...currentData]);
                    }
                  },
                })}
                // 设置table宽度
                // muiTablePaperProps={{
                //     sx: {
                //         maxWidth: '1588px',
                //         m: 'auto'
                //     }
                // }}
                renderToolbarInternalActions={({ table }) => (
                  <>
                    <MRT_ToggleGlobalFilterButton table={table} />
                    <MRT_ShowHideColumnsButton table={table} />
                  </>
                )}
                autoResetPageIndex={false}
                // 是否开启关闭头部底部工具类
                enableTopToolbar={true}
                enableColumnActions={false}
                enableBottomToolbar={true}
                // 关闭过滤搜素
                enableColumnFilters={false}
                // 关闭排序
                enableSorting={false}
                // 布局方式
                layoutMode="grid"
                // 开启列对齐
                muiTableHeadCellProps={{
                  sx: {
                    "& .Mui-TableHeadCell-Content": {
                      justifyContent: "space-between",
                    },
                  },
                }}
                // 解决列太多宽度太长问题
                enableColumnResizing
                // enablePinning
                // 初始化状态
                initialState={{ columnVisibility: { createTime: true } }}
                muiToolbarAlertBannerProps={
                  isError
                    ? {
                        color: "error",
                        children: "Error loading data",
                      }
                    : undefined
                }
                // 开启多选
                // enableRowSelection
                // 列数
                // rowCount={rowCount}
                // 固定头部
                enableStickyHeader
                sx={{ boxShadow: "none" }}
                // 处理表格高度
                muiTableContainerProps={{ sx: { minHeight: "600px" } }}
                // 设置背景颜色
                muiTableBodyCellProps={({ row }) => ({
                  onDoubleClick: (event) => {
                    handleClickOpen();
                    console.info(event, row);
                  },
                  sx: {
                    backgroundColor: "white",
                  },
                })}
                muiTableHeadRowProps={{ sx: { boxShadow: "none" } }}
                muiTableBodyProps={{
                  sx: {
                    backgroundColor: "white",
                    tableLayout: "fixed",
                    boxShadow: "none",
                  },
                }}
                muiTableProps={{
                  sx: {
                    backgroundColor: "white",
                    tableLayout: "fixed",
                    boxShadow: "none",
                  },
                }}
                muiBottomToolbarProps={{
                  sx: { backgroundColor: "white", boxShadow: "none" },
                }}
                muiTopToolbarProps={{
                  sx: { backgroundColor: "white", boxShadow: "none" },
                }}
                // 分页回调函数
                onPaginationChange={setPagination}
                // 记得开启这些 tips:否则会出现useEffect 反作用函数发起多次请求点击下一页会出问题
                manualFiltering
                manualPagination
                manualSorting
                // 列过滤
                onColumnFiltersChange={setColumnFilters}
                // 全局过滤
                onGlobalFilterChange={setGlobalFilter}
                // 排序
                onSortingChange={setSorting}
                // 开启分页
                enablePagination={true}
                // 列定义
                columns={columnsTemp}
                // 数据
                data={currentData}
                // 初始化国际化语言  --- todo: i18n key doc address: https://github.com/KevinVandy/material-react-table/blob/main/packages/material-react-table/src/_locales/en.ts
                localization={tableI18n}
                // 多选底部提示
                positionToolbarAlertBanner="none"
                // 开启action操作
                enableRowActions
                // action操作位置
                positionActionsColumn="last"
                renderRowActions={({ table }) => (
                  <Stack direction="row" alignItems="center">
                    <IconButton
                      color="primary"
                      aria-label="cliear"
                      component="label">
                      <RemoveCircleOutlineIcon />
                    </IconButton>
                  </Stack>
                )}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => {
              addUploadMaterial.current.handleOpen();
            }}
            disableElevation
            variant="contained"
            color="primary">
            {t("ips.ips_add_resource")}
          </Button>
          <Button color="info" variant="outlined" onClick={handleClose}>
            {t("common.common_edit_cancel")}
          </Button>
          <Button
            onClick={() => {
              handelAppendMaterial();
            }}
            disableElevation
            variant="contained"
            color="primary">
            {t("editor.editor_save")}
          </Button>
        </DialogActions>
      </BootstrapDialog>
      <Dialog
        open={durationOpen}
        onClose={handleDurationClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description">
        <form noValidate onSubmit={durationFormik.handleSubmit}>
          <DialogTitle id="alert-dialog-title">
            {t("common.common_set_duration")}
            <IconButton
              aria-label="close"
              onClick={handleDurationClose}
              sx={{
                position: "absolute",
                right: 8,
                top: 8,
                color: (theme) => theme.palette.grey[500],
              }}>
              <CloseIcon />
            </IconButton>
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Stack spacing={1}>
                  <OutlinedInput
                    type="number"
                    id="text"
                    fullWidth
                    value={durationFormik.values.duration}
                    name="duration"
                    startAdornment={
                      <InputAdornment position="start">
                        <AccessTimeIcon />
                      </InputAdornment>
                    }
                    rows={8}
                    onBlur={durationFormik.handleBlur}
                    onChange={durationFormik.handleChange}
                    placeholder={t("common.common_input_playback_duration")}
                    error={Boolean(
                      durationFormik.touched.duration &&
                        durationFormik.errors.duration
                    )}
                  />
                  {durationFormik.touched.duration &&
                    durationFormik.errors.duration && (
                      <FormHelperText error id="text-error">
                        {durationFormik.errors.duration}
                      </FormHelperText>
                    )}
                </Stack>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Stack
              sx={{ width: "100%" }}
              direction="row"
              justifyContent="space-around"
              alignItems="center"
              spacing={2}>
              <Button
                onClick={handleDurationClose}
                color="info"
                variant="outlined">
                {t("common.common_edit_cancel")}
              </Button>
              <Button type="submit" variant="contained" color="primary">
                {t("common.common_confirm")}
              </Button>
            </Stack>
          </DialogActions>
        </form>
      </Dialog>
      <Preview ref={preview} url={previewUrl} type={previewType}></Preview>
      <UploadMaterial
        ref={addUploadMaterial}
        callback={() => {
          setPagination({
            pageIndex: 0,
            pageSize: 10,
          });
        }}
      />
    </div>
  );
});
export default AddMusic;
