import React from "react";
/* eslint-disable no- */
import { forwardRef, useState } from "react";
import PropTypes from "prop-types";
import Dot from "@/components/@extended/Dot";
import { Stack, Typography } from "@mui/material";
import { prototype } from "react-copy-to-clipboard";

/**
 * dicts =[
 *  {
 *     value:"",  具体的值
 *     color:"",  颜色
 *     title:""   回显文字
 *  }
 * ]
 *
 *字典翻译组件
 * @returns
 */
const DictTag = forwardRef((props, ref) => {
  const {
    dicts,
    value,
    fieldName = {
      value: "value",
      title: "title",
      color: "color",
    },
    defaultValue = "",
  } = props;

  const getDom = () => {
    for (let i = 0; i < dicts.length; i++) {
      if (
        dicts[i][fieldName.value ? fieldName.value : "value"].toString() ===
        value?.toString()
      ) {
        return (
          <Stack direction="row" spacing={1} alignItems="center">
            {dicts[i][fieldName.color] && dicts[i][fieldName.color] !== "" && (
              <Dot color={dicts[i][fieldName.color]} />
            )}
            <Typography>{dicts[i][fieldName.title]}</Typography>
          </Stack>
        );
      } else {
        if (defaultValue != "") {
          return <Typography>{defaultValue}</Typography>;
        }
      }
    }
  };

  return <>{getDom()}</>;
});
DictTag.propTypes = {
  dicts: PropTypes.array,
  value: PropTypes.string,
  fieldName: PropTypes.object,
};
export default DictTag;
