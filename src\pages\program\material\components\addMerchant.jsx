import React from "react";
import { useState, forwardRef, useImperativeHandle } from "react";
import * as Yup from "yup";
import { useFormik } from "formik";
import AnimateButton from "@/components/@extended/AnimateButton";
// material-ui
import LoadingButton from "@mui/lab/LoadingButton";
import { save } from "@/service/api/merchant";
import { Avatar } from "@files-ui/react";
import { isEmpty } from "lodash-es";
// 消息提示
import { toast } from "react-toastify";
import {
  FormHelperText,
  Alert,
  Grid,
  InputLabel,
  OutlinedInput,
  Stack,
  Typography,
} from "@mui/material";
import ZKSelect from "@/components/ZKSelect";
import {
  BootstrapActions,
  BootstrapContent,
  BootstrapDialog,
  BootstrapDialogTitle,
} from "@/components/dialog";
import { useTranslation } from "react-i18next";
import { merchantTypes } from "@/dict/commonDict";
import { getFileSize } from "@/utils/zkUtils";
const addMerchantForm = forwardRef((props, ref) => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const [errorMsg, setErrorMsg] = useState(undefined);
  const [saveMerchantLoading, setSaveMerchantLoading] = useState(false);
  useImperativeHandle(ref, () => ({
    handleClose,
    handleOpen,
  }));
  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    merchantFormik.handleReset();
    setOpen(false);
  };
  // 保存商户
  const handleSaveMerchant = async (values) => {
    setSaveMerchantLoading(true);
    save(values)
      .then((res) => {
        setSaveMerchantLoading(false);
        toast.success(res.message);
        props.getAdvertiserList();
        handleClose();
      })
      .catch((err) => {
        setSaveMerchantLoading(false);
      });
  };
  // 支持的格式，用于正则校验
  const suffix = `(gif|jpg|png|jpeg)`;
  // 表单设置
  const merchantFormik = useFormik({
    initialValues: {
      name: "",
      type: "",
      logo: "",
    },
    onSubmit: (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        handleSaveMerchant(values);
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
    validationSchema: Yup.object().shape({
      name: Yup.string()
        .max(60, t("common.common_rule_name_len60"))
        .required(t("ips.ips_enter_merchant_name")),
      type: Yup.string().required(t("ips.ips_select_merchant_type")),
      logo: Yup.mixed().test({
        name: "logo",
        test(value, ctx) {
          if (value === undefined) {
            return ctx.createError({
              message: t("common.common_merchant_please_logo"),
            });
          }
          if (typeof value === "string") {
            if (isEmpty(value)) {
              return ctx.createError({
                message: t("common.common_merchant_please_logo"),
              });
            }
          } else {
            if (value === null) {
              return ctx.createError({
                message: t("common.common_merchant_please_logo"),
              });
            }
          }
          return true;
        },
      }),
    }),
  });

  return (
    <>
      <BootstrapDialog
        onClose={handleClose}
        maxWidth="xs"
        fullWidth
        aria-labelledby="customized-dialog-title"
        open={open}>
        <form noValidate onSubmit={merchantFormik.handleSubmit}>
          <BootstrapDialogTitle onClose={handleClose}>
            <Typography variant="h4" component="p">
              {t("ips.ips_new_merchant")}
            </Typography>
          </BootstrapDialogTitle>
          <BootstrapContent dividers>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="merchant-name">
                    {t("common.common_merchant_logo")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <div
                    style={{
                      width: "100%",
                      height: "100%",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}>
                    <Avatar
                      style={{ borderColor: "1px solid red" }}
                      src={merchantFormik.values.logo}
                      alt="logo"
                      accept={"image/png, image/jpeg, image/gif, image/jpg"}
                      changeLabel={t("common.common_merchant_please_logo")}
                      name="logo"
                      emptyLabel={t("common.common_merchant_please_logo")}
                      onChange={(selectedFile) => {
                        // eslint-disable-next-line no-useless-escape
                        let regular = new RegExp(`.*\.${suffix}`);
                        const fileSize = getFileSize(selectedFile.size);
                        if (
                          !regular.test(selectedFile.name.toLocaleLowerCase())
                        ) {
                          setErrorMsg(
                            t("common.common_upload_type_not_support", {
                              type: selectedFile.type,
                            })
                          );
                          return;
                        } else if (fileSize > 30) {
                          setErrorMsg(
                            t("common.common_file_max_size", {
                              size: "30M",
                            })
                          );
                          return;
                        }
                        merchantFormik.setFieldValue("logo", selectedFile);
                      }}
                    />
                  </div>
                  {merchantFormik.touched.logo &&
                    merchantFormik.errors.logo && (
                      <FormHelperText
                        error
                        id="standard-weight-helper-text-merchant-logo">
                        {merchantFormik.errors.logo}
                      </FormHelperText>
                    )}
                  {errorMsg && (
                    <Alert
                      severity="error"
                      onClose={() => {
                        setErrorMsg(undefined);
                      }}
                      sx={{ marginBottom: 1 }}>
                      {errorMsg}
                    </Alert>
                  )}
                </Stack>
              </Grid>
              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="merchant-name">
                    {t("ips.ips_merchant_name")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <OutlinedInput
                    id="merchant-name"
                    type="text"
                    value={merchantFormik.values.name}
                    name="name"
                    onBlur={merchantFormik.handleBlur}
                    onChange={merchantFormik.handleChange}
                    placeholder={t("ips.ips_enter_merchant_name")}
                    fullWidth
                    error={Boolean(
                      merchantFormik.touched.name && merchantFormik.errors.name
                    )}
                  />
                  {merchantFormik.touched.name &&
                    merchantFormik.errors.name && (
                      <FormHelperText
                        error
                        id="standard-weight-helper-text-merchant-name">
                        {merchantFormik.errors.name}
                      </FormHelperText>
                    )}
                </Stack>
              </Grid>
              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="merchant-type">
                    {t("ips.ips_merchant_type")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <ZKSelect
                    options={merchantTypes}
                    placeholder={t("ips.ips_select_merchant_type")}
                    name="type"
                    onClear={() => {
                      merchantFormik.setFieldValue("type", undefined);
                    }}
                    value={merchantFormik.values.type}
                    onBlur={merchantFormik.handleBlur}
                    onChange={merchantFormik.handleChange}
                    error={Boolean(
                      merchantFormik.touched.type && merchantFormik.errors.type
                    )}
                  />
                  {merchantFormik.touched.type &&
                    merchantFormik.errors.type && (
                      <FormHelperText
                        error
                        id="standard-weight-helper-text-merchant-type">
                        {merchantFormik.errors.type}
                      </FormHelperText>
                    )}
                </Stack>
              </Grid>
            </Grid>
          </BootstrapContent>
          <BootstrapActions>
            <AnimateButton>
              <LoadingButton
                loading={saveMerchantLoading}
                disableElevation
                disabled={merchantFormik.isSubmitting}
                fullWidth
                size="large"
                type="submit"
                variant="contained"
                color="primary">
                {t("common.common_edit_save")}
              </LoadingButton>
            </AnimateButton>
          </BootstrapActions>
        </form>
      </BootstrapDialog>
    </>
  );
});

export default addMerchantForm;
