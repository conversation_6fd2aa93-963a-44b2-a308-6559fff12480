import React from 'react'
import { Grid } from "@mui/material";
import RenderCommon from "./RenderCommon";
const RenderLive = (props) => {
  const info = props.info;
  if (info.hide) {
    return "";
  }

  return (
    <RenderCommon {...props}>
      <Grid
        sx={{
          width: "100%",
          height: "100%",
          backgroundColor: info.bgColor,
          opacity: info.transparency,
          overflow: "hidden",
        }}
      >
        <Grid
          style={{
            width: "100%",
            height: "100%",
            backgroundColor: 'gray',
            display: 'flex',
            justifyContent: 'center',
            alignItems:'center'
          }}
        >
        {info.url}
        </Grid>
      </Grid>
    </RenderCommon>
  );
};

export default RenderLive;
