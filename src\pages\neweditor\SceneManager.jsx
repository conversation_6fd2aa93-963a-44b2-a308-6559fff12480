import React from 'react'
import {  <PERSON><PERSON>, <PERSON><PERSON> } from "@mui/material";
import RemoveIcon from "@mui/icons-material/Remove";
import AddIcon from "@mui/icons-material/Add";
import { PrettoSlider } from "./PrettoSlider";
import NavigateBeforeIcon from "@mui/icons-material/NavigateBefore";
import { message } from "./common/i18n";
import { useState } from "react";
// import { useConfirm } from "@/components/zkconfirm";
// const SceneBox = (props) => {
//   const click = () => {
//     if (props.onClick) {
//       props.onClick();
//     }
//   };

//   return (
//     <Grid
//       onClick={click}
//       sx={{
//         height: "80px",
//         width: "140px",
//         background: "#EEEEEE 0% 0% no-repeat padding-box",
//         borderRadius: "5px",
//         display: "flex",
//         justifyContent: "center",
//         alignItems: "center",
//         m: 1,
//         flexShrink: 0,
//         cursor: "pointer",
//         border: props.active ? "1px solid #78BC27" : "none",
//         position: "relative",
//         "&:hover": {
//           cursor: "pointer",
//           backgroundColor: "#dce3ea",
//         },
//         "&:hover .delte_btn": {
//           display: "block",
//           cursor: "pointer",
//           zIndex: 100,
//         },
//       }}
//     >
//       {props.children}
//     </Grid>
//   );
// };

const SceneManager = (props) => {
  const currentPageIndex = props.currentPageIndex;
  const pages = JSON.parse(JSON.stringify(props.pages));
  const currentPage = pages[currentPageIndex];

//   const confirmFn = useConfirm();
  const handleSliderChange = (event, newValue) => {
    if (props.setScale) {
      props.setScale(newValue);
    }
  };

  const plusScale = () => {
    if (props.scale < 1) {
      let result = (props.scale + 0.01).toFixed(2);
      props.setScale(parseFloat(result));
    }
  };

  const minusScale = () => {
    if (props.scale > 0.01) {
      let result = (props.scale - 0.01).toFixed(2);
      props.setScale(parseFloat(result));
    }
  };

  const [accordion, setAccordion] = useState(true);
  const closeTrig = () => {
    setAccordion(!accordion);
  };

  const addArea = () => {
    const currentPageIndex = props.currentPageIndex;
    const pages = JSON.parse(JSON.stringify(props.pages));
    const currentPage = pages[currentPageIndex];
    currentPage.tempLayout.push({
      left: 0,
      top: 0,
      width: 200,
      height: 200,
      duration: 10,
      bgColor: "#ffffff",
      bgImg: "",
      checksum: "",
      key: new Date().getTime(),
      componentList: [],
    });
    if (props.setPages) {
      props.setPages(pages);
    }
    if (props.setActiveTempIndex) {
      props.setActiveTempIndex(currentPage.tempLayout.length - 1);
    }
  };

  return (
    <Grid>
      <Grid
        sx={{
          height: "60px",
          backgroundColor: "rgb(248,248,248)",
          display: "flex",
          justifyContent: "space-between",
          position: "relative",
        }}
      >
        <Grid
          sx={{
            height: "60px",
            backgroundColor: "rgb(248,248,248)",
            display: "flex",
            alignItems: "center",
          }}
        >
          {currentPage.customTemplate && (
            <Button
              sx={{
                marginRight: "20px",
                marginLeft:"10px"
              }}
              variant="contained"
              onClick={(e) => {
                e.stopPropagation();
                addArea();
              }}
            >
              <AddIcon
                fontSize="small"
                sx={{
                  cursor: "pointer",
                }}
              ></AddIcon>
              {message("editor_add_temp_area")}
            </Button>
          )}
        </Grid>

        <Grid
          sx={{ width: "150px", display: "flex", alignItems: "center", mr: 3 }}
        >
          <RemoveIcon
            sx={{
              fontSize: "12px",
              marginRight: "10px",
              color: "#323232",
            }}
            onClick={minusScale}
          ></RemoveIcon>
          <PrettoSlider
            onChange={handleSliderChange}
            size="small"
            min={0.01}
            max={3}
            step={0.01}
            color="secondary"
            value={props.scale}
            aria-label="Small"
            valueLabelDisplay="on"
          />
          <AddIcon
            sx={{
              fontSize: "12px",
              marginLeft: "10px",
              color: "#323232",
            }}
            onClick={() => {
              plusScale();
            }}
          ></AddIcon>
        </Grid>

        <Grid
          style={{
            position: "absolute",
            left: "50%",
            bottom: "-10px",
            height: "20px",
            width: "50px",
            borderRadius: "20px",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: "#ffffff",
            zIndex: "100",
            cursor: "pointer",
          }}
          onClick={closeTrig}
        >
          {accordion ? (
            <NavigateBeforeIcon
              sx={{
                color: "#707070",
                fontSize: "16px",
                transform: "rotate(270deg)",
              }}
            />
          ) : (
            <NavigateBeforeIcon
              sx={{
                color: "#707070",
                fontSize: "16px",
                transform: "rotate(90deg)",
              }}
            />
          )}
        </Grid>
      </Grid>
      {/* <Grid
                sx={{
                    backgroundColor: "#ffffff",
                    display: "flex",
                    justifyContent: "flex-start",
                    alignItems: "center",
                    width: "100%",
                    overflow: "auto",
                    height: accordion ? "150px" : "10px",
                    transition: "height .5s",
                }}
            >
                <Grid
                    sx={{
                        backgroundColor: "#ffffff",
                        display: "flex",
                        justifyContent: "flex-start",
                        alignItems: "center",
                        overflowY: "hidden",
                        height: accordion ? "150px" : "0px",
                        transition: "height .5s",
                    }}
                >
                    {props.pages.map((item, index) => {
                        return (
                            <SceneBox
                                active={index === props.currentPageIndex}
                                onClick={() => {
                                    sceneClick(index);
                                }}
                                key={item.title + index}
                            >
                                {props.pages.length > 1 && (
                                    <Grid
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            deleteScene(index);
                                        }}
                                        className="delte_btn"
                                        sx={{
                                            position: "absolute",
                                            top: "-24px",
                                            display: "none",
                                        }}
                                    >
                                        <img style={{ width: "20px" }} src={Delete} />
                                    </Grid>
                                )}

                                {item.title}
                            </SceneBox>
                        );
                    })}
                   <SceneBox onClick={addScene}>
                        <AddIcon
                            sx={{
                                fontSize: "22px",
                                marginLeft: "6px",
                                color: "#323232",
                            }}
                        ></AddIcon>
                    </SceneBox> 
                </Grid>
            </Grid> */}
    </Grid>
  );
};
export default SceneManager;
