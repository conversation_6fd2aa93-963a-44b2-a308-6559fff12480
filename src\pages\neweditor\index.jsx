import React from "react";
import { <PERSON>, Grid, Button } from "@mui/material";
import Header from "./Header";
import LeftArea from "./LeftArea";
import SceneManager from "./SceneManager";
import ComponentProperties from "./ComponentProperties";
import CenterArea from "./CenterArea";
import { useEffect, useState, useRef } from "react";
import "./editor.css";
import "./fonts/fonts.css";
import CustomInput from "./components/CustomInput";
import NavigateBeforeIcon from "@mui/icons-material/NavigateBefore";
import PSD from "psd.js";
import { isBase64, showBase64 } from "@/utils/zkUtils";
import { pageDuration } from "./common/utils";
import {
  saveLayout,
  updateLayout,
  saveTemplate,
  updateTemplate,
  uploadPsdImg,
} from "@/service/api/layout";
import { useNavigate } from "react-router-dom";
import Moveable from "react-moveable";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import CircularProgress from "@mui/material/CircularProgress";
import { useConfirm } from "@/components/zkconfirm";
import PreViewDia from "./PreViewDia";
import { toast } from "react-toastify";
import Delete from "./icon/deleteScene.png";
import invisible from "./icon/invisible.png";
import see from "./icon/see.png";
import logo from "./icon/logo.png";
import { message } from "./common/i18n";
import html2canvas from "html2canvas";
import PreView from "./PreView";
import AiButton from "./AiButton";
import AuthButton from "@/components/AuthButton";

const NewEditor = () => {
  const confirmFn = useConfirm();

  const navigate = useNavigate();
  //当前组件类型
  const [currentType, setCurrentType] = useState("ZKTecoText");
  const centerRef = useRef(null);
  const selectPsdREf = useRef(null);
  const [loadMessage, setLoadMessage] = useState("");

  //当前页面下标
  const [currentPageIndex, setCurrentPageIndex] = useState(0);

  //当前组件ID
  const [currentComponentId, setCurrentComponentId] = useState("");

  //当前组件下标
  const [currentComponentIndex, setCurrentComponentIndex] = useState("");

  const [contextMenu, setContextMenu] = useState({
    pageX: 0,
    pageY: 0,
    show: false,
    component: null,
  });

  const [openPre, setOpenPre] = useState(false);
  const [preJson, setPreJson] = useState({});
  const [isMobile, setIsMobile] = useState(false);
  const [templateLayout, setIsTemplateLayout] = useState(false);
  const [templateLayoutName, setTemplateLayoutName] = useState("");

  //场景缩放
  const [scale, setScale] = useState(1);

  // const [savePre, setSavePre] = useState(false);

  //
  const [activeTempIndex, setActiveTempIndex] = useState("");
  //场景缩放
  const [loading, setLoading] = useState(false);

  const [layoutInfo, setLayoutInfo] = useState({
    id: null, //节目ID
    IS_TEMPLATE: 0, //是否是模板
    name: "", //节目名称
    groupId: 14, //节目分组ID
    groupName: "", //节目分组名称
    width: 1920, //节目宽度
    height: 1080, //节目高度
    scale: 1, //编辑器缩放比
    remarks: "", //备注
    bgColor: "", // 背景颜色 （未使用）
    bgImg: "", // 背景颜色 （未使用）
    isTemplate: "0", //是否是模板
  });

  const [form, setForm] = useState({});
  //场景页面信息
  const [pages, setPages] = useState([
    {
      title: message("editor_scene") + 1,
      duration: 10,
      bgColor: "#ffffff",
      imgId: null,
      bgImg: "",
      datasetId: "",
      datasetName: "",
      isTemplate: false,
      checksum: "",
      datasetFields: "",
      componentList: [],
    },
  ]);

  const [accordion, setAccordion] = useState(true);

  const [comAccordion, setComAccordion] = useState(true);
  const [tempAccordion, setTempComAccordion] = useState(true);
  const [isNewTemplate, setIsNewTemplate] = useState(false);
  const [selectPsd, setSelectPsd] = useState(false);

  const closeTrig = () => {
    setAccordion(!accordion);
  };

  useEffect(() => {
    let templateLayout = sessionStorage.getItem("templateLayout");
    if (templateLayout === "true") {
      //模版相关，包括新增模版，编辑模版
      setIsTemplateLayout(true);
      let isAddTemplateLayout = sessionStorage.getItem("isAddTemplateLayout");
      if (isAddTemplateLayout === "true") {
        setIsNewTemplate(true);
        let formData = JSON.parse(sessionStorage.getItem("templateInfo"));
        formData.departmentId = formData.advertiserId;
        setTemplateLayoutName(formData.name);
        setForm(formData);
        let width, height;
        if (formData.resolution !== "custom") {
          [width, height] = formData.resolution.split("*");
        } else {
          width = formData.customWidth;
          height = formData.customHeight;
        }
        setLayoutInfo({
          ...layoutInfo,
          name: formData.name,
          width: width,
          height: height,
        });
        if (centerRef !== null) {
          let offsetHeight = centerRef.current.offsetHeight;
          let offsetWidth = centerRef.current.offsetWidth;
          if (width > offsetWidth || height > offsetHeight) {
            let widtScale = (offsetWidth - 40) / width;
            let heightScale = (offsetHeight - 40) / height;
            if (widtScale > heightScale) {
              let result = (heightScale + 0.01).toFixed(2);
              setScale(parseFloat(result));
            } else {
              let result = (widtScale + 0.01).toFixed(2);
              setScale(parseFloat(result));
            }
          }
        }
      } else {
        let templateInfo = sessionStorage.getItem("templateInfo");

        if (templateInfo) {
          let valueJson = JSON.parse(templateInfo);

          let templateJson = valueJson.templateJson;
          let result = JSON.parse(templateJson);

          let editScale = result?.scale ? result?.scale : 1;
          let tempScale = parseFloat(editScale);
          let scaleResult = (tempScale + 0.001).toFixed(2);
          setTemplateLayoutName(result.name);
          setScale(parseFloat(scaleResult));
          setPages([...result?.pages]);
          delete result.pages;
          setLayoutInfo({
            ...result,
            name: valueJson.name || result.name,
          });
          delete valueJson.layoutJson;
          setForm({
            ...valueJson,
          });
        }
      }
    } else {
      // 内容资源，移动端模版
      setIsTemplateLayout(false);
      let isAdd = sessionStorage.getItem("isAdd");
      let mobile = sessionStorage.getItem("isMobile");
      if (mobile === "true") {
        setIsMobile(true);
      } else {
        setIsMobile(false);
      }
      if (isAdd === "true") {
        let formData = JSON.parse(sessionStorage.getItem("PROGRAM_INFO"));
        setForm(formData);
        let width, height;
        if (formData.resolution !== "custom") {
          [width, height] = formData.resolution.split("*");
        } else {
          width = formData.customWidth;
          height = formData.customHeight;
        }
        setLayoutInfo({
          ...layoutInfo,
          name: formData.name,
          width: width,
          height: height,
        });
        if (centerRef !== null) {
          let offsetHeight = centerRef.current.offsetHeight;
          let offsetWidth = centerRef.current.offsetWidth;
          if (width > offsetWidth || height > offsetHeight) {
            let widtScale = (offsetWidth - 40) / width;
            let heightScale = (offsetHeight - 40) / height;
            if (widtScale > heightScale) {
              let result = (heightScale + 0.01).toFixed(2);
              setScale(parseFloat(result));
            } else {
              let result = (widtScale + 0.01).toFixed(2);
              setScale(parseFloat(result));
            }
          }
        }
        let isUserTemp = sessionStorage.getItem("isUserTemp");
        if (isUserTemp === "true") {
          let userTemplateJson = sessionStorage.getItem("userTemplateJson");
          if (userTemplateJson) {
            let tempObj = JSON.parse(userTemplateJson);
            const { pages, name, ...orther } = tempObj;
            setScale(orther.scale);
            setPages(pages);
            setLayoutInfo({
              ...layoutInfo,
              ...orther,
              name: formData.name,
              width: width,
              height: height,
            });
          }
        }
      } else if (isAdd === "false") {
        let editValue = sessionStorage.getItem("editValue");
        if (editValue) {
          let valueJson = JSON.parse(editValue);
          let layoutJson = valueJson.layoutJson;
          let result = JSON.parse(layoutJson);

          let editScale = result?.scale ? result?.scale : 1;
          let tempScale = parseFloat(editScale);
          let scaleResult = (tempScale + 0.001).toFixed(2);
          setScale(parseFloat(scaleResult));
          setPages([...result?.pages]);
          delete result.pages;
          setLayoutInfo({
            ...result,
            name: valueJson.name || result.name,
          });
          delete valueJson.layoutJson;
          setForm({
            ...valueJson,
          });
        }
      }
    }
  }, []);

  const [target, setTarget] = useState(null);
  const [frame, setFrame] = useState({
    translate: [0, 0],
  });
  useEffect(() => {
    setTarget(document.querySelector(".moveable_target"));
  }, []);

  const htmlToImg = (callback) => {
    var htmlcanvas = document.getElementById("htmlcanvas");
    html2canvas(htmlcanvas, {
      useCORS: true,
      allowTaint: true,
      scale: 1.2,
    }).then(function (canvas) {
      let base64Str = canvas.toDataURL(); //生成base64图片
      // showBase64(base64Str)
      let temp1 = base64Str.split(",");
      if (temp1.length === 2) {
        callback(temp1[1]);
      } else {
        callback("");
      }
    });
  };

  const handleClosePre = () => {
    setOpenPre(false);
  };

  const importPsd = () => {
    selectPsdREf.current.click();
  };

  const setBgImg = (bgImg) => {
    const newPages = JSON.parse(JSON.stringify(pages));
    newPages[currentPageIndex].bgImg = bgImg;
    setPages([...newPages]);
  };

  const getTextLayer = (layerA) => {
    let info = layerA.typeTool();
    let colors = info.colors();
    let fonts = info.fonts();
    let sizes = info.sizes();
    let { obj } = info;
    let textValue = obj?.textValue || "";
    let color = "#000000";
    if (colors && colors.length > 0) {
      let textColor = colors[0];
      const [r = 0, g = 0, b = 0] = textColor;
      color =
        "#" + [r, g, b].map((c) => c.toString(16).padStart(2, "0")).join("");
    }

    return {
      width: layerA.width,
      height: layerA.height,
      left: layerA.left,
      top: layerA.top,
      fontSize: (sizes && sizes[0]) || "",
      font: (fonts && fonts[0]) || "",
      name: layerA.name,
      color: color,
      textValue: textValue,
      bgColor: "transparent",
      visible: layerA.visible === undefined ? true : layerA.visible,
      lineHeight: sizes[0],
      type: "text",
    };
  };

  const parseChild = (list, _child) => {
    _child.forEach((i) => {
      if (i.type === "group") {
        parseChild(list, i._children);
      } else {
        const layerA = i.layer;

        if (layerA.typeTool) {
          console.log("---------");
          console.log(getTextLayer(layerA));
          list.push(getTextLayer(layerA));
        } else {
          try {
            const child_image_base64 = layerA.image.toBase64();
            list.push({
              base64: child_image_base64,
              width: layerA.width,
              height: layerA.height,
              left: layerA.left,
              top: layerA.top,
              name: layerA.name,
              visible: layerA.visible === undefined ? true : layerA.visible,
              type: "image",
            });
          } catch (error) {
            console.error(error);
          }
        }
      }
    });
  };

  const parsePSD = (file) => {
    if (!file.name.endsWith(".psd")) {
      toast.error(message("neweditor.index.573996-0"));
      return false;
    }
    setLoadMessage(message("neweditor.index.983604-0"));
    setLoading(true);
    // psd文件
    var url = URL.createObjectURL(file);
    // 解析psd文件
    PSD.fromURL(url)
      .then((psd) => {
        let list = [];
        let header = psd.header;
        let width = header.width;
        let height = header.height;
        setLayoutInfo({
          ...layoutInfo,
          width: width,
          height: height,
        });
        if (centerRef !== null) {
          let offsetHeight = centerRef.current.offsetHeight;
          let offsetWidth = centerRef.current.offsetWidth;
          if (width > offsetWidth || height > offsetHeight) {
            let widtScale = (offsetWidth - 40) / width;
            let heightScale = (offsetHeight - 40) / height;
            if (widtScale > heightScale) {
              let result = (heightScale + 0.01).toFixed(2);
              setScale(parseFloat(result));
            } else {
              let result = (widtScale + 0.01).toFixed(2);
              setScale(parseFloat(result));
            }
          }
        }

        // 获取图层数据
        const childrens = psd.tree().children();

        childrens.forEach((item) => {
          if (item.type === "group") {
            const _child = item._children;
            parseChild(list, _child);
          } else {
            const layer = item.layer;
            if (layer.typeTool) {
              console.log(getTextLayer(layer));
              list.push(getTextLayer(layer));
            } else {
              try {
                const child_image = layer.image.toBase64();
                list.push({
                  base64: child_image,
                  width: layer.width,
                  height: layer.height,
                  left: layer.left,
                  top: layer.top,
                  name: layer.name,
                  visible: layer.visible === undefined ? true : layer.visible,
                  type: "image",
                });
              } catch (error) {
                console.error(error);
              }
            }
            const _child = item._children;
            parseChild(list, _child);
          }
        });

        if (list.length > 50) {
          setSelectPsd(true);
          setLoadMessage("");
          setLoading(false);
          toast.error(message("neweditor.index.106103-0"));
          return false;
        } else {
          // psd背景图片
          const l_background = psd.image.toBase64();
          setBgImg(l_background);

          addPsdImage(list.reverse());
          setSelectPsd(true);
          setLoadMessage("");
          setLoading(false);
        }
      })
      .catch((e) => {
        setLoadMessage("");
        setLoading(false);
        toast.error(message("neweditor.index.573996-1"));
      });
  };

  const addPsdImage = (list) => {
    const newPages = JSON.parse(JSON.stringify(pages));
    newPages[currentPageIndex].componentList = [];
    list.forEach((item, index) => {
      if (item.type === "text") {
        newPages[currentPageIndex].componentList.push({
          isBold: false, //是否加粗
          isItaly: false, //是否斜体
          isUnderline: false, //下划线
          textAlign: "center", //字体位置（居中还是左对齐右对齐等）
          rotate: 0, //宣传
          isScroll: true, //是否滚动
          duration: 60, //默认时长
          scrollDirection: "none", //滚动方向
          speed: 60, //滚动速度
          anim: "none", //动画样式名称

          fontColor: item.color || "#262626", //颜色
          fontSize: item.fontSize || 32, //字体大小
          bgColor: item.bgColor, //背景颜色
          title: item.name, //标题
          name: item.name, //名称
          type: "ZKTecoText", //组件类型
          tag: "",
          lineHeight: item.lineHeight || 30, //行高
          text: item.textValue, //文字
          left: item.left,
          top: item.top,
          width: item.width,
          height: item.height,
          componentId: "componentId" + index,
          hide: !item.visible,
          zIndex: 11 + index,
          font: item.font || "MyriadPro-Light", //字体

          // anim: "",
          // borderRadius: 0,
          // componentId: "componentId" + index,
          // duration: 10,
          // editType: "ZKTecoSlideShowImgEdit",
          // height: item.height,
          // hide: false,
          // icon: "icon-img",
          // imgList: [
          //   {
          //     duration: 10,
          //     imgSrc: item.base64,
          //     imgId: "",
          //     checksum: "",
          //   },
          // ],
          // left: item.left,
          // top: item.top,

          // name: item.name,
          // rotate: 0,
          // tag: "",
          // title: item.name,

          // transparency: 1,
          // type: "ZKTecoSlideShowImg",
          // width: item.width,
          // zIndex: 11 + index,
        });
      } else {
        newPages[currentPageIndex].componentList.push({
          anim: "",
          borderRadius: 0,
          componentId: "componentId" + index,
          duration: 10,
          editType: "ZKTecoSlideShowImgEdit",

          hide: !item.visible,
          icon: "icon-img",
          imgList: [
            {
              duration: 10,
              imgSrc: item.base64,
              imgId: "",
              checksum: "",
            },
          ],
          left: item.left,
          name: item.name,
          rotate: 0,
          tag: "",
          title: item.name,
          top: item.top,
          transparency: 1,
          type: "ZKTecoSlideShowImg",
          width: item.width,
          height: item.height,
          zIndex: 11 + index,
        });
      }
    });
    setPages([...newPages]);
  };

  const fileChange = (e) => {
    const file = e.target.files[0];
    parsePSD(file);
  };

  const componentClick = (e, componentIndex, tempIndex) => {
    if (setCurrentComponentId) {
      setCurrentComponentId(e.componentId);
      setCurrentComponentIndex(componentIndex);
    }
    if (setCurrentType) {
      setCurrentType(e.type);
    }
    if (tempIndex !== undefined) {
      setActiveTempIndex(tempIndex);
    }
  };

  const sliceArr = (arr, num) => {
    // arr是你要分割的数组，num是你要每隔几位分割；
    let newArr = [];
    for (let i = 0; i < arr.length; ) {
      newArr.push(arr.slice(i, (i += num)));
    }
    return newArr;
  };

  //上传base64
  const uploadBaseImage = (page, callback) => {
    let list = page.componentList;

    let array = sliceArr(list, 4);
    let romiseList = array
      .map((item) => {
        let imgList = [];

        item.forEach((imgItem) => {
          if (imgItem?.imgList && imgItem?.imgList.length > 0) {
            let imgUrl = imgItem?.imgList[0]?.imgSrc;
            if (isBase64(imgUrl)) {
              showBase64(imgUrl);
              imgList.push({
                key: imgItem.componentId,
                base64Img: imgUrl,
              });
            }
          }
        });

        if (imgList && imgList.length > 0) {
          return uploadPsdImg({
            departmentId: form.departmentId,
            groupId: form.groupId,
            imgList: imgList,
          });
        } else {
          return "";
        }
      })
      .filter((item) => {
        if (item === "") {
          return false;
        }
        return true;
      });

    if (romiseList && romiseList.length > 0) {
      setLoadMessage(message("neweditor.index.983604-1"));

      Promise.allSettled(romiseList).then((results) => {
        let tempObj = {};
        results.forEach((result) => {
          if (result.status == "fulfilled") {
            let data = result?.value?.data || [];
            data.forEach((dataItem) => {
              tempObj[dataItem.key] = dataItem;
            });
          }
        });

        console.log("tempObj tempObj tempObj", tempObj);

        let list = page.componentList;

        list.forEach((item) => {
          if (item.type == "ZKTecoSlideShowImg") {
            let componentId = item.componentId;
            let obj = tempObj[componentId] || {};
            let imgObj = item?.imgList.length > 0 ? item?.imgList[0] : null;
            if (imgObj && obj.downloadUrl && obj.checksum) {
              imgObj.checksum = obj.checksum;
              imgObj.imgId = obj.materialId;
              imgObj.imgSrc = obj.downloadUrl;
            } else if (imgObj) {
              if (isBase64(imgObj.imgSrc)) {
                imgObj.checksum = "";
                imgObj.imgId = "";
                imgObj.imgSrc = "";
              }
            }
          }
        });

        page.componentList = list.filter((item) => {
          if (item.type == "ZKTecoSlideShowImg") {
            let imgObj = item?.imgList.length > 0 ? item?.imgList[0] : null;
            if (imgObj === null) {
              return false;
            }
            if (imgObj.imgSrc === "") {
              return false;
            }
            return true;
          } else {
            return true;
          }
        });

        callback();
      });
    } else {
      callback();
    }
  };

  //保存模版
  const saveTempalte = (img, duration) => {
    if (form.id) {
      setLoadMessage(message("neweditor.index.983604-2"));
      let page = pages[0];
      const callback = () => {
        //保存编辑模版
        let params = {
          templateName: form.name, //模板名称
          industryType: form.industryType, //行业类型
          license: form.license,
          departmentId: form.departmentId,
          groupId: form.groupId,
          content: JSON.stringify({
            ...layoutInfo,
            scale,
            pages: pages,
          }), //模板json
          resolution: `${layoutInfo.width}x${layoutInfo.height}`, //分辨率  如1920x1080(宽x高), 中间的是x 小写的
          previewImage: img, //预览图
        };
        params.templateName = templateLayoutName;
        params.id = form.id;
        delete params.resolution;

        if (params.templateName === "") {
          toast.error(message("neweditor.index.983604-6"));
          setLoading(false);
        } else {
          setLoading(true);
          updateTemplate(params)
            .then((res) => {
              navigate("/resource/templateList");
            })
            .catch((e) => {})
            .finally(() => {
              setLoading(false);
            });
        }
      };
      uploadBaseImage(page, callback);
    } else {
      //保存新增模版
      setLoading(true);
      let page = pages[0];

      const callback = () => {
        let params = {
          templateName: templateLayoutName, //模板名称
          industryType: form.industryType, //行业类型
          license: form.license,
          departmentId: form.departmentId,
          groupId: form.groupId,
          content: JSON.stringify({
            ...layoutInfo,
            scale,
            pages: pages,
          }), //模板json
          resolution: `${layoutInfo.width}x${layoutInfo.height}`, //分辨率  如1920x1080(宽x高), 中间的是x 小写的
          previewImage: img, //预览图
        };
        if (params.templateName === "") {
          toast.error(message("neweditor.index.983604-6"));
          setLoading(false);
        } else {
          setLoadMessage(message("neweditor.index.983604-2"));
          saveTemplate(params)
            .then((res) => {
              navigate("/resource/templateList");
            })
            .catch((e) => {})
            .finally(() => {
              // setSavePre(false);
              setLoading(false);
            });
        }
      };
      uploadBaseImage(page, callback);
    }
  };

  //检查json中text文字内容是否为空
  const checkTextProperty = (obj) => {
    const hasTextKey = obj.some((page) => {
      if (page.isTemplate) {
        return page.tempLayout.some((layout) => {
          return layout.componentList.some((components) => {
            return (
              components.type === "ZKTecoText" && components.text.trim() === ""
            );
          });
        });
      } else {
        return page.componentList.some((components) => {
          return (
            components.type === "ZKTecoText" && components.text.trim() === ""
          );
        });
      }
    });
    return hasTextKey;
  };

  const saveInfo = async () => {
    setPreJson({
      ...layoutInfo,
      scale,
      pages: pages,
    });
    // setSavePre(true);

    let duration = pages.reduce(function (prev, cur) {
      if (cur.isTemplate) {
        let layoutDuration = cur.tempLayout.map((item) => {
          return parseInt(item.duration);
        });
        let result = Math.max(0, ...layoutDuration);
        return prev + result;
      } else {
        // let layoutDuration = cur.tempLayout.map((item) => {
        //   return parseInt(item.duration);
        // });
        // let result = Math.max(0, ...layoutDuration);
        // return prev + result;
        let duration = parseInt(cur.duration);
        return prev + duration;
      }
    }, 0);

    setContextMenu({
      pageX: 0,
      pageY: 0,
      show: false,
      component: null,
    });

    let emptyTemlate = pages.filter((item) => {
      if (item.isTemplate) {
        if (item.tempLayout.length === 0) {
          return true;
        } else {
          return false;
        }
      }
      return false;
    });

    if (emptyTemlate.length > 0) {
      toast.error(message("editor_tempNoEmpyt"));
      return false;
    }

    let emptyPages = pages.filter((item) => {
      if (item.isTemplate) {
        let cmpList = item.tempLayout
          .map((item) => {
            return item.componentList.length;
          })
          .filter((item) => {
            return item < 1;
          });
        if (cmpList.length > 0) {
          return true;
        } else {
          return false;
        }
      } else {
        if (item.componentList.length === 0) {
          return true;
        } else {
          return false;
        }
      }
    });

    let emptyLiveUrl = pages.filter((item) => {
      let result = item.componentList.filter((item) => {
        if (item.type === "ZKTecoLive") {
          if (item.url === "") {
            return true;
          } else {
            return false;
          }
        }
      });
      if (result.length > 0) {
        return true;
      } else {
        return false;
      }
    });
    if (emptyLiveUrl && emptyLiveUrl.length > 0) {
      toast.error(message("editor_liveUrlNoEmpty"));
      return false;
    }

    if (emptyPages.length > 0) {
      toast.error(message("editor_sceneNoEmpty"));
      return false;
    }
    let textIsNull = checkTextProperty(pages);
    if (textIsNull) {
      toast.error(message("editor_textContentNoEmpty"));
      return;
    }

    let isReturn = false;

    pages.forEach((item) => {
      if (item.isTemplate) {
        let listLength = item.tempLayout
          .map((layout) => {
            return layout?.length || 0;
          })
          .reduce((accumulator, currentValue) => accumulator + currentValue, 0);
        if (listLength > 50) {
          toast.error(message("neweditor.index.106103-1"));
          setLoading(false);
          isReturn = true;
          return false;
        }
      } else if (item?.componentList?.length > 50) {
        isReturn = true;
        toast.error(message("neweditor.index.106103-1"));
        setLoading(false);
        return false;
      }
    });

    if (isReturn) {
      return false;
    }

    if (isMobile) {
      let labelEmpty = pages.filter((item) => {
        let result = item.componentList.filter((item) => {
          if (!item.tag) {
            return true;
          } else {
            return false;
          }
        });

        if (result.length > 0) {
          return true;
        } else {
          return false;
        }
      });
      if (labelEmpty && labelEmpty.length > 0) {
        toast.error(message("labelEmpty"));
        return false;
      }
    }
    let saveImg = (img) => {
      if (templateLayout) {
        saveTempalte(img, duration);
      } else {
        let parsms = {
          name: form.name,
          type: isMobile ? "mobile" : "layout",
          duration,
          departmentId: form.departmentId, //商户ID
          groupId: form.groupId,
          // advertiserType: form.advertiserType, //商户类型
          resolution: `${layoutInfo.width}x${layoutInfo.height}`,
          audit: form.audit,
          auditorId: form.auditorId,
          layoutJson: JSON.stringify({
            ...layoutInfo,
            scale,
            pages: pages,
          }),
          fileData: img,
        };
        setLoadMessage(message("neweditor.index.983604-3"));
        if (form.id) {
          setLoading(true);
          let result = {
            ...JSON.parse(JSON.stringify(form)),
            ...parsms,
          };
          updateLayout(result)
            .then((res) => {
              navigate("/resource/material");
            })
            .catch((e) => {})
            .finally(() => {
              // setSavePre(false);
              setLoading(false);
            });
        } else {
          setLoading(true);
          saveLayout(parsms)
            .then((res) => {
              navigate("/resource/material");
            })
            .catch((e) => {})
            .finally(() => {
              // setSavePre(false);
              setLoading(false);
            });
        }
      }
    };

    setCurrentComponentId("");
    setCurrentComponentIndex("");

    setLoading(true);
    setLoadMessage(message("neweditor.index.983604-4"));
    let res = await new Promise((resolve) => {
      setTimeout(() => {
        resolve(true);
      }, 100);
    });
    htmlToImg(saveImg);
  };

  function handleDragStart(e) {
    e.set(frame.translate);
  }
  function handleDrag(e) {
    frame.translate = e.beforeTranslate;
    e.target.style.transform = `translate(${e.beforeTranslate[0]}px, ${e.beforeTranslate[1]}px)`;
  }

  const visibilityClick = (item, index, tempIndex) => {
    const newPages = JSON.parse(JSON.stringify(pages));
    if (tempIndex !== undefined) {
      let currentPage = newPages[currentPageIndex];
      let oldInfo = currentPage.tempLayout[tempIndex].componentList[index];
      let newInfo = {
        ...oldInfo,
        hide: !oldInfo.hide,
      };
      currentPage.tempLayout[tempIndex].componentList[index] = newInfo;
    } else {
      let oldInfo = newPages[currentPageIndex].componentList[index];
      let newInfo = {
        ...oldInfo,
        hide: !oldInfo.hide,
      };
      newPages[currentPageIndex].componentList[index] = newInfo;
    }
    if (setPages) {
      setPages(newPages);
    }
  };

  const deleteComp = (deleteItem, index, tempIndex) => {
    confirmFn({
      title: message("delete_cmp_tip"),
      confirmationText: message("editor_edit_ok"),
      cancellationText: message("editor_edit_cancel"),
      description: message("editor_del_component"),
    }).then(() => {
      if (tempIndex !== undefined) {
        const pagesInfo = JSON.parse(JSON.stringify(pages));
        let currentPage = pagesInfo[currentPageIndex];
        let componentList = currentPage.tempLayout[tempIndex].componentList;
        let newComponentList = componentList.filter((item) => {
          if (deleteItem.componentId === item.componentId) {
            return false;
          } else {
            return true;
          }
        });

        currentPage.tempLayout[tempIndex].componentList = newComponentList;
        if (setPages) {
          setPages(pageDuration(pagesInfo));
        }
        if (setCurrentType) {
          setCurrentType(null);
        }
        if (setCurrentComponentId) {
          setCurrentComponentId(null);
          setCurrentComponentIndex(null);
        }
      } else {
        const pagesInfo = JSON.parse(JSON.stringify(pages));
        let componentList = pagesInfo[currentPageIndex].componentList;
        let newComponentList = componentList.filter((item) => {
          if (deleteItem.componentId === item.componentId) {
            return false;
          } else {
            return true;
          }
        });
        pagesInfo[currentPageIndex].componentList = newComponentList;

        if (setPages) {
          setPages(pageDuration(pagesInfo));
        }
        if (setCurrentType) {
          setCurrentType(null);
        }
        if (setCurrentComponentId) {
          setCurrentComponentId(null);
          setCurrentComponentIndex(null);
        }
      }
    });
  };

  const getName = (item) => {
    return item?.type;
  };

  const clickPre = () => {
    setPreJson({
      ...layoutInfo,
      scale,
      pages: pages,
    });
    setContextMenu({
      pageX: 0,
      pageY: 0,
      show: false,
      component: null,
    });
    setOpenPre(true);
  };

  const clickBack = () => {
    if (templateLayout) {
      navigate("/resource/templateList");
    } else {
      navigate("/resource/material");
    }
  };

  const deleteTemp = (index) => {
    confirmFn({
      title: message("delete_cmp_tip"),
      confirmationText: message("editor_edit_ok"),
      cancellationText: message("editor_edit_cancel"),
      description: message("editor_delete_temp_area_tip"),
    }).then(() => {
      try {
        const currentPage = pages[currentPageIndex];
        currentPage.tempLayout.splice(index, 1);
        if (setActiveTempIndex) {
          let temLength = currentPage.tempLayout.length;
          if (temLength > 0) {
            setActiveTempIndex(currentPage.tempLayout.length - 1);
          } else {
            setActiveTempIndex("");
          }
        }
      } catch (err) {
        console.log(err);
      }
    });
  };

  const tempClick = (index, event) => {
    event.preventDefault();
    event.stopPropagation();
    setActiveTempIndex(index);
    setCurrentComponentId("");
    setCurrentComponentIndex("");
    setCurrentType("templateSetting");
  };

  return (
    <Grid
      sx={{
        display: "flex",
        flexDirection: "column",
        height: "100vh",
        overflow: "hidden",
      }}>
      {loading && (
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            height: "100vh",
            width: "100%",
            backgroundColor: "#00000087",
            position: "absolute",
            zIndex: "10000",
          }}>
          <Grid
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              flexDirection: "column",
            }}>
            <CircularProgress />
            <Grid
              sx={{
                color: "#7ac143",
                mt: 2,
              }}>
              {loadMessage}
            </Grid>
          </Grid>
        </Box>
      )}

      <Header>
        <Grid
          sx={{
            display: "flex",
            alignItems: "center",
            width: "100%",
            justifyContent: "space-between",
          }}>
          <Grid
            sx={{
              display: "flex",
              alignItems: "center",
            }}>
            <Grid>
              <img style={{ height: "25px" }} src={logo}></img>
            </Grid>
            <Grid
              sx={{
                marginLeft: "40px",
                color: "#707070",
              }}>
              <CustomInput
                margintop={"0px !important"}
                disabled={!templateLayout}
                onChange={(e) => {
                  setTemplateLayoutName(e.target.value);
                  setLayoutInfo({
                    ...layoutInfo,
                    name: e.target.value,
                  });
                }}
                value={layoutInfo.name}></CustomInput>
            </Grid>
            <Grid sx={{ marginLeft: "40px" }}>
              <CustomInput
                margintop={"0px !important"}
                disabled
                value={
                  layoutInfo.width + "*" + layoutInfo.height
                }></CustomInput>
            </Grid>
          </Grid>
          <Grid
            sx={{
              display: "flex",
              alignItems: "center",
            }}>
            {isNewTemplate && (
              // <AuthButton button="resource:templateList:psd">
              <Button
                size="small"
                sx={{ mr: 2 }}
                onClick={importPsd}
                color="info"
                variant="outlined">
                {message("neweditor.index.983604-5")}
              </Button>
              // </AuthButton>
            )}

            <input
              style={{ display: "none" }}
              accept=".psd"
              ref={selectPsdREf}
              onChange={fileChange}
              type="file"></input>

            <Button
              size="small"
              sx={{ mr: 2 }}
              onClick={clickBack}
              color="info"
              variant="outlined">
              {message("editor_cancel")}
            </Button>

            <Button
              size="small"
              sx={{ mr: 2 }}
              onClick={clickPre}
              color="info"
              variant="outlined">
              {message("editor_preview")}
            </Button>

            <Button size="small" onClick={saveInfo} variant="contained">
              {message("editor_save")}
            </Button>
          </Grid>
        </Grid>
      </Header>
      <Grid
        sx={{
          flexGrow: 1,
          display: "flex",
          maxHeight: "calc(100vh - 50px)",
        }}>
        <Grid
          sx={{
            backgroundColor: "#f6f6f6",
            width: "90px",
            flexShrink: 0,
          }}>
          <LeftArea
            isMobile={isMobile}
            currentPageIndex={currentPageIndex}
            pages={pages}
            setActiveTempIndex={setActiveTempIndex}
            currentComponentIndex={currentComponentIndex}
            setCurrentComponentIndex={setCurrentComponentIndex}
            setCurrentComponentId={setCurrentComponentId}
            currentType={currentType}
            setCurrentType={setCurrentType}></LeftArea>
        </Grid>

        <Grid
          sx={{
            backgroundColor: "rgb(227,227,227)",
            width: accordion ? "340px" : "12px",
            flexShrink: 0,
            position: "relative",
            transition: "width .25s",
            height: "calc(100vh - 50px)",
          }}>
          <Grid
            style={{
              width: accordion ? "340px" : "0px",
              transition: "width .25s",
              overflow: "hidden",
              height: "calc(100vh - 50px)",
            }}>
            <ComponentProperties
              isMobile={isMobile}
              activeTempIndex={activeTempIndex}
              setActiveTempIndex={setActiveTempIndex}
              layoutInfo={layoutInfo}
              currentType={currentType}
              setScale={setScale}
              setCurrentType={setCurrentType}
              setLayoutInfo={setLayoutInfo}
              currentPageIndex={currentPageIndex}
              setCurrentPageIndex={setCurrentPageIndex}
              currentComponentId={currentComponentId}
              setCurrentComponentId={setCurrentComponentId}
              currentComponentIndex={currentComponentIndex}
              setCurrentComponentIndex={setCurrentComponentIndex}
              pages={pages}
              setPages={setPages}></ComponentProperties>
          </Grid>
          <Grid
            style={{
              position: "absolute",
              top: "50%",
              right: "-10px",
              marginTop: "-25px",
              width: "20px",
              height: "50px",
              backgroundColor: "rgb(227,227,227)",
              borderRadius: "10px",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              cursor: "pointer",
              zIndex: 100,
            }}
            onClick={closeTrig}>
            {accordion ? (
              <NavigateBeforeIcon
                sx={{
                  color: "#707070",
                  fontSize: "16px",
                }}
              />
            ) : (
              <NavigateBeforeIcon
                sx={{
                  color: "#707070",
                  fontSize: "16px",
                  transform: "rotate(180deg)",
                }}
              />
            )}
          </Grid>
        </Grid>
        <Grid
          sx={{
            flexGrow: 1,
            backgroundColor: "#eceef0",
            display: "flex",
            flexDirection: "column",
            width: `calc(100% - ${accordion ? "340" : "10"}px - 90px)`,
          }}>
          <Grid
            sx={{
              flexGrow: 1,
              position: "relative",
            }}
            ref={centerRef}>
            <div
              style={{
                minHeight: "40px",
                position: "absolute",
                top: "50px",
                left: "50px",
                zIndex: 1000,
                backgroundColor: "#ffff",
                borderRadius: "5px",
                boxShadow: "0px 0px 6px #00000029",
              }}
              className="moveable_target">
              <Grid
                style={{
                  height: comAccordion ? "auto" : "40px",
                  transition: "height .25s",
                  overflow: "hidden",
                }}
                sx={{ pl: 1, pr: 1 }}>
                <Grid
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    height: "40px",
                  }}
                  onClick={() => {
                    setComAccordion(!comAccordion);
                  }}>
                  <Grid>{message("editor_componentList")}</Grid>
                  <Grid>
                    {comAccordion ? <ExpandMoreIcon /> : <ChevronRightIcon />}
                  </Grid>
                </Grid>

                <Grid
                  sx={{
                    maxHeight: "80vh",
                    overflow: "auto",
                  }}>
                  {pages[currentPageIndex].isTemplate &&
                    pages[currentPageIndex].tempLayout
                      .map((tempLayoutItem, tmpIndex) => {
                        return tempLayoutItem.componentList.map(
                          (cmpItem, index) => {
                            return (
                              <Grid
                                sx={{
                                  display: "flex",
                                  justifyContent: "space-between",
                                  alignItems: "center",
                                  padding: "4px",
                                }}
                                key={cmpItem.componentId}>
                                <Grid
                                  sx={{
                                    color:
                                      currentComponentId === cmpItem.componentId
                                        ? "#78BC27"
                                        : "#707070",
                                    "&:hover": {
                                      color: "#78BC27",
                                      cursor: "pointer",
                                    },
                                  }}
                                  onClick={() => {
                                    componentClick(cmpItem, index, tmpIndex);
                                  }}>
                                  {getName(cmpItem)}
                                </Grid>
                                <Grid
                                  sx={{
                                    display: "flex",
                                    justifyContent: "space-between",
                                    alignItems: "center",
                                  }}>
                                  <Grid sx={{ ml: 2 }}>
                                    <Grid
                                      sx={{
                                        width: "15px",
                                        cursor: "pointer",
                                        "&:hover": {
                                          opacity: "0.8",
                                        },
                                      }}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        deleteComp(cmpItem, index, tmpIndex);
                                      }}>
                                      <img
                                        style={{ width: "100%" }}
                                        src={Delete}
                                      />
                                    </Grid>
                                  </Grid>
                                  <Grid sx={{ ml: 1 }}>
                                    {cmpItem.hide ? (
                                      <Grid
                                        sx={{
                                          width: "20px",
                                          cursor: "pointer",
                                          "&:hover": {
                                            opacity: "0.8",
                                          },
                                        }}
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          visibilityClick(
                                            cmpItem,
                                            index,
                                            tmpIndex
                                          );
                                        }}>
                                        <img
                                          style={{ width: "100%" }}
                                          src={invisible}
                                        />
                                      </Grid>
                                    ) : (
                                      <Grid
                                        sx={{
                                          width: "20px",
                                          cursor: "pointer",
                                          "&:hover": {
                                            opacity: "0.8",
                                          },
                                        }}
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          visibilityClick(
                                            cmpItem,
                                            index,
                                            tmpIndex
                                          );
                                        }}>
                                        <img
                                          style={{ width: "100%" }}
                                          src={see}
                                        />
                                      </Grid>
                                    )}
                                  </Grid>
                                </Grid>
                              </Grid>
                            );
                          }
                        );
                      })
                      .reduce(function (acc, curr) {
                        return acc.concat(curr);
                      }, [])}

                  {pages[currentPageIndex].componentList.map(
                    (cmpItem, index) => {
                      return (
                        <Grid
                          sx={{
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                            padding: "4px",
                          }}
                          key={cmpItem.componentId}>
                          <Grid
                            sx={{
                              color:
                                currentComponentId === cmpItem.componentId
                                  ? "#78BC27"
                                  : "#707070",
                              "&:hover": {
                                color: "#78BC27",
                                cursor: "pointer",
                              },
                            }}
                            onClick={() => {
                              componentClick(cmpItem, index);
                            }}>
                            {getName(cmpItem)}
                          </Grid>
                          <Grid
                            sx={{
                              display: "flex",
                              justifyContent: "space-between",
                              alignItems: "center",
                            }}>
                            <Grid sx={{ ml: 2 }}>
                              <Grid
                                sx={{
                                  width: "15px",
                                  cursor: "pointer",
                                  "&:hover": {
                                    opacity: "0.8",
                                  },
                                }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  deleteComp(cmpItem, index);
                                }}>
                                <img style={{ width: "100%" }} src={Delete} />
                              </Grid>
                            </Grid>
                            <Grid sx={{ ml: 1 }}>
                              {cmpItem.hide ? (
                                <Grid
                                  sx={{
                                    width: "20px",
                                    cursor: "pointer",
                                    "&:hover": {
                                      opacity: "0.8",
                                    },
                                  }}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    visibilityClick(cmpItem, index);
                                  }}>
                                  <img
                                    style={{ width: "100%" }}
                                    src={invisible}
                                  />
                                </Grid>
                              ) : (
                                <Grid
                                  sx={{
                                    width: "20px",
                                    cursor: "pointer",
                                    "&:hover": {
                                      opacity: "0.8",
                                    },
                                  }}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    visibilityClick(cmpItem, index);
                                  }}>
                                  <img style={{ width: "100%" }} src={see} />
                                </Grid>
                              )}
                            </Grid>
                          </Grid>
                        </Grid>
                      );
                    }
                  )}
                </Grid>

                {pages[currentPageIndex]?.isTemplate &&
                  pages[currentPageIndex]?.customTemplate &&
                  pages[currentPageIndex].tempLayout.length > 0 && (
                    <Grid
                      style={{
                        marginTop: "20px",
                        height: tempAccordion ? "auto" : "40px",
                        transition: "height .25s",
                        overflow: "hidden",
                      }}
                      sx={{ pl: 1, pr: 1 }}>
                      <Grid
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center",
                          height: "40px",
                        }}
                        onClick={() => {
                          setTempComAccordion(!tempAccordion);
                        }}>
                        <Grid>{message("editor_temp_area_list")}</Grid>
                        <Grid>
                          {tempAccordion ? (
                            <ExpandMoreIcon />
                          ) : (
                            <ChevronRightIcon />
                          )}
                        </Grid>
                      </Grid>

                      <Grid>
                        {pages[currentPageIndex].tempLayout.map(
                          (item, index) => {
                            return (
                              <Grid
                                sx={{
                                  display: "flex",
                                  justifyContent: "space-between",
                                  alignItems: "center",
                                  cursor: "pointer",
                                  p: "10px",
                                  color:
                                    activeTempIndex === index
                                      ? "#78BC27"
                                      : "#707070",
                                  "&:hover": {
                                    color: "#78BC27",
                                    cursor: "pointer",
                                  },
                                }}
                                key={item.key}
                                onClick={(e) => {
                                  tempClick(index, e);
                                }}>
                                <Grid>
                                  {message("temp_area") + (index + 1)}
                                </Grid>

                                <Grid
                                  sx={{
                                    width: "15px",
                                    cursor: "pointer",
                                    "&:hover": {
                                      opacity: "0.8",
                                    },
                                  }}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    deleteTemp(index);
                                  }}>
                                  <img
                                    style={{ width: "100%", cursor: "pointer" }}
                                    src={Delete}
                                  />
                                </Grid>
                              </Grid>
                            );
                          }
                        )}
                      </Grid>
                    </Grid>
                  )}
              </Grid>
            </div>
            <Moveable
              target={target} // moveable的对象
              draggable // 是否可以拖拽
              padding={{ left: 0, top: 0, right: 0, bottom: 0 }} // padding距离
              zoom={1} // 缩放包裹的moveable
              origin={false} // 显示中心点
              className="center_Moveable"
              throttleDrag={0} // 拖拽阈值 达到这个值才执行拖拽
              onDragStart={handleDragStart} // 拖动开始执行
              onDrag={handleDrag} // 拖动中
            />

            <CenterArea
              activeTempIndex={activeTempIndex}
              setActiveTempIndex={setActiveTempIndex}
              currentType={currentType}
              setCurrentType={setCurrentType}
              currentPageIndex={currentPageIndex}
              setCurrentPageIndex={setCurrentPageIndex}
              currentComponentId={currentComponentId}
              setCurrentComponentId={setCurrentComponentId}
              currentComponentIndex={currentComponentIndex}
              setCurrentComponentIndex={setCurrentComponentIndex}
              contextMenu={contextMenu}
              setContextMenu={setContextMenu}
              pages={pages}
              setPages={setPages}
              scale={scale}
              setScale={setScale}
              width={layoutInfo.width}
              height={layoutInfo.height}></CenterArea>
          </Grid>
          <Grid>
            <SceneManager
              scale={scale}
              setScale={setScale}
              currentPageIndex={currentPageIndex}
              setCurrentPageIndex={(index) => {
                setCurrentPageIndex(index);
              }}
              currentType={currentType}
              setCurrentType={setCurrentType}
              currentComponentId={currentComponentId}
              setCurrentComponentId={setCurrentComponentId}
              currentComponentIndex={currentComponentIndex}
              setActiveTempIndex={setActiveTempIndex}
              setCurrentComponentIndex={setCurrentComponentIndex}
              pages={pages}
              setPages={setPages}></SceneManager>
          </Grid>
        </Grid>
      </Grid>

      <PreViewDia
        visible={openPre}
        onClose={handleClosePre}
        programData={preJson}></PreViewDia>
      {/* {savePre && (
        <Grid
          sx={{
            position: "absolute",
            top: "10000px",
            right: "10000",
          }}
        >
          <PreView programData={preJson}></PreView>
        </Grid>
      )} */}
      {/* <AiButton /> */}
    </Grid>
  );
};

export default NewEditor;
