import React from 'react'
import { <PERSON>, <PERSON>rid, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Button } from "@mui/material";
import AppBar from "@mui/material/AppBar";
import Toolbar from "@mui/material/Toolbar";
import { styled } from "@mui/material/styles";
import  { useEffect, useMemo, useState, useRef } from "react";
import Moveable from "react-moveable";
import { debounce } from "@mui/material/utils";

export default function RenderCommon(props) {
  const info = props.info;
  const isPre = props.isPre;
  const targetRef = useRef(null);
  const moveableRef = useRef(null);
  let [dropBounds, setDropBounds] = useState({
    top: 0,
    left: 0,
    right: 10000,
    bottom: 10000,
  });

  useEffect(() => {
    let dom = document.querySelector(".centerArea");
    if (dom) {
      dropBounds = dom.getBoundingClientRect();
      setDropBounds({
        top: 0,
        left: 0,
      });
    }
  }, []);

  useEffect(() => {
    targetRef.current.style.width = info.width + "px";
    targetRef.current.style.height = info.height + "px";
    targetRef.current.style.transform = `translate(${info.left}px, ${
      info.top
    }px) rotate(${info.rotate ? info.rotate : 0}deg)`;
    if (!isPre) {
      moveableRef.current.moveable.updateRect();
    }
  }, [info]);

  const setComponentInfo = (baseInfo) => {
    const componentIndex = props.componentIndex;
    const currentPageIndex = props.currentPageIndex;
    const pages = JSON.parse(JSON.stringify(props.pages));
    const currentPage = pages[currentPageIndex];
    if (currentPage.isTemplate) {
      let oldInfo =
        currentPage.tempLayout[props.activeTempIndex].componentList[
          componentIndex
        ];
      let newInfo = {
        ...oldInfo,
        ...baseInfo,
      };
      currentPage.tempLayout[props.activeTempIndex].componentList[
        componentIndex
      ] = newInfo;
      if (props.setPages) {
        props.setPages(pages);
      }
    } else {
      let oldInfo = pages[currentPageIndex].componentList[componentIndex];
      let newInfo = {
        ...oldInfo,
        ...baseInfo,
      };
      pages[currentPageIndex].componentList[componentIndex] = newInfo;
      if (props.setPages) {
        props.setPages(pages);
      }
    }
  };
  const validationDebounced = debounce(setComponentInfo, 200);

  const setClickInfo = (event) => {
    event.preventDefault();
    event.stopPropagation();
    if (props.setContextMenu) {
      props.setContextMenu({
        pageX: 0,
        pageY: 0,
        show: false,
        component: null,
      });
    }
    if (props.setCurrentComponentId) {
      props.setCurrentComponentId(info.componentId);
      props.setCurrentComponentIndex(props.componentIndex);
    }
    if (props.setActiveTempIndex) {
      props.setActiveTempIndex(props.tempIndex);
    }
    if (props.setCurrentType) {
      props.setCurrentType(info.type);
    }
  };

  return (
    <>
      <div
        onClick={setClickInfo}
        ref={targetRef}
        style={{
          zIndex: info.zIndex,
          width: info.width,
          height: info.height,
          transform: `translate(${info.left}px, ${info.top}px) rotate(${
            info.rotate ? info.rotate : 0
          }deg)`,
          position: "absolute",
        }}
        onContextMenu={(e) => {
          if (props.setCurrentComponentId) {
            props.setCurrentComponentId(info.componentId);
            props.setCurrentComponentIndex(props.componentIndex);
          }
          if (props.setCurrentType) {
            props.setCurrentType(info.type);
          }

          if (props.setCurrentComponentId) {
            props.setCurrentComponentId(info.componentId);
            props.setCurrentComponentIndex(props.componentIndex);
          }
          if (props.setActiveTempIndex) {
            props.setActiveTempIndex(props.tempIndex);
          }

          let nativeEvent = e.nativeEvent;
          if (props.setContextMenu) {
            props.setContextMenu({
              pageX: nativeEvent.pageX,
              pageY: nativeEvent.pageY,
              show: true,
              component: info,
            });
          }

          e.preventDefault();
        }}
        className={
          info.componentId === props.currentComponentId ? "select_element" : ""
        }
      >
        <div
          style={{
            zIndex: info.zIndex,
            width: "100%",
            height: "100%",
            backgroundColor: info.bgColor,
          }}
          className={`animated ${info.anim}`}
        >
          {props.children}
        </div>
      </div>
      {!isPre && (
        <Moveable
          key={info.rotate + info.left + info.top + info.width}
          target={targetRef} // moveable的对象
          ref={moveableRef}
          style={{
            zIndex: 100,
          }}
          padding={{ left: 0, top: 0, right: 0, bottom: 0 }} // padding距离
          throttleDrag={0} // 拖拽阈值 达到这个值才执行拖拽
          draggable={info.componentId === props.currentComponentId}
          origin={false}
          edgeDraggable={false}
          resizable={info.componentId === props.currentComponentId}
          keepRatio={false}
          className={
            info.componentId === props.currentComponentId
              ? ""
              : "select_Moveable"
          }
          snappable={false} // 是否初始化磁吸功能
          bounds={dropBounds} // 边界点
          rotatable={false}
          onDrag={(e) => {
            e.target.style.transform = e.transform;
            let left = Math.floor(e.translate[0]);
            let top = Math.floor(e.translate[1]);
            validationDebounced({
              left: left < 0 ? 0 : left,
              top: top < 0 ? 0 : top,
            });
          }}
          onResize={(e) => {
            let translate = e.drag.translate;
            let width = Math.floor(e.width);
            let height = Math.floor(e.height);
            let left = Math.floor(translate[0]);
            let top = Math.floor(translate[1]);
            e.target.style.width = `${width}px`;
            e.target.style.height = `${height}px`;
            e.target.style.transform = e.drag.transform;
            validationDebounced({
              width: width,
              height: height,
              left: left < 0 ? 0 : left,
              top: top < 0 ? 0 : top,
            });
          }}
        />
      )}
    </>
  );
}
