// material-ui
import { Box, Grid, Typography } from "@mui/material";
import React from "react";
// project import
import NavGroup from "./NavGroup";
import NavItem from "./NavItem";
import NavCollapse from "./NavCollapse";
import { useSelector } from "react-redux";

// ==============================|| DRAWER CONTENT - NAVIGATION ||============================== //
const Navigation = () => {
  const menu = useSelector((state) => state.menu);
  const { menuList } = menu;
  const navGroups = menuList?.map((item) => {
    switch (item.type) {
      case "0":
        return <NavCollapse key={item.code} item={item}></NavCollapse>;
      case "group":
        return <NavGroup key={item.code} item={item} />;
      case "item":
        return <NavItem key={item.code} item={item} />;
      case "hidden":
        return null;
      default:
        return (
          <Typography key={item.code} variant="h6" color="error" align="center">
            Fix - Navigation Group
          </Typography>
        );
    }
  });
  return <Box className="w-full">{navGroups}</Box>;
};

export default Navigation;
