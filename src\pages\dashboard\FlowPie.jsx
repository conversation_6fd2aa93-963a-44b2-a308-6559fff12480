import { useEffect, useRef } from "react";
// import { Grid, Stack, Typography } from "@mui/material";
import * as echarts from "echarts";
import "echarts-liquidfill";
import { t } from "i18next";

//水波图
const Instrument = (props) => {
  const chartRef = useRef(null);
  const { materialData } = props;
  useEffect(() => {
    let myEcharts = null;
    const initChart = () => {
      myEcharts = echarts.init(chartRef.current, null, { renderer: "svg" });
      // 设置初始大小
      myEcharts.resize();
      // 监听窗口大小变化，自动调整图表大小
      window.addEventListener("resize", handleResize);
      const options = getOptions();
      myEcharts.setOption(options);
    };
    const handleResize = () => {
      myEcharts.resize();
    };
    // 在组件挂载时进行初始化
    initChart();

    // 在组件卸载时移除事件监听
    return () => {
      window.removeEventListener("resize", handleResize);
      myEcharts.dispose();
    };
  }, [materialData]);
  
  const devicePixelRatio = window.devicePixelRatio || 1; // 获取设备的像素比
  const getOptions = () => {
    let option = {
      // backgroundColor: '#000',
      // title: {
      //   text: "总资源\n\r500M",
      //   textStyle: {
      //     color: "#fff",
      //     fontSize: 16,
      //     fontWeight: "bold",
      //   },
      //   left: "center",
      //   bottom: "20%",
      //   borderWidth: 1,
      //   borderColor: "rgba(122, 193, 67,.2)",
      //   backgroundColor: "rgba(122, 193, 67,1)",
      //   borderRadius: 10,
      //   padding: [10, 40, 10, 40],
      // },
      series: [
        {
          name: "数据",
          type: "pie",
          startAngle: 315,
          radius: ["63%", "80%"],
          legendHoverLink: false,
          scale: false,
          avoidLabelOverlap: false,
          labelLine: {
            show: false,
          },
          // 100
          //  100M
          //  30M
          // 70M
          //  100 / 0.75   133 * 0.25
          // 75%，25
          data: [
            {
              value:
                ((materialData.sysTotalStorage === 0
                  ? 10
                  : materialData.sysTotalStorage) /
                  0.75) *
                0.25,
              name: "总资源" + ":" + "139MB", //其实就是data[1].value
              label: {
                position: "center",
                border: "10px solid red",
                color: "black",
                formatter:
                  "{total|" +
                  materialData.sysPercent +
                  "}" +
                  "\n\r" +
                  `{active|${t("ips.ips_sys_used_storage_percent")}}`,

                fontSize: 20 * devicePixelRatio, // 调整字体大小
                rich: {
                  total: {
                    fontSize: "1.5rem", // 调整字体大小
                    fontFamily: "Source Han Sans CN",
                    fontWeight: 600,
                    color: "#52c41a",
                    lineHeight: 35,
                  },
                  active: {
                    fontFamily: "微软雅黑",
                    fontSize: "0.78rem", // 调整字体大小
                    fontWeight: 400,
                    color: "#92A5B7",
                  },
                },
              },

              type: "pie",
              radius: ["58%", "68%"],
              legendHoverLink: false,
              scale: false,
              avoidLabelOverlap: false,
              labelLine: {
                show: false,
              },
              itemStyle: {
                color: "rgba(55,244,255,0)",
              },
            },

            {
              value:
                materialData.sysTotalStorage === 0
                  ? 0
                  : materialData.sysUsedStorage,
              //name: '直接访问2(实际)',
              itemStyle: {
                color: "rgba(122, 193, 67,1)",
              },
            },

            {
              value:
                materialData.sysTotalStorage === 0
                  ? 10
                  : materialData.sysTotalStorage - materialData.sysUsedStorage,
              //name: '直接访问3（总共）',
              itemStyle: {
                color: "rgba(122, 193, 67,.2)",
              },
            },
          ],
        },
        //内层圆环
        {
          name: "数据",
          type: "pie",
          startAngle: 315,
          radius: ["60%", "55%"],
          legendHoverLink: false,
          scale: false,
          avoidLabelOverlap: false,
          labelLine: {
            show: false,
          },

          data: [
            {
              value:
                ((materialData.sysTotalStorage === 0
                  ? 10
                  : materialData.sysTotalStorage) /
                  0.75) *
                0.25,
              label: {
                position: "center",
                border: "5px solid red",
                color: "black",
              },

              type: "pie",
              radius: ["58%", "68%"],
              legendHoverLink: false,
              scale: false,
              avoidLabelOverlap: false,
              labelLine: {
                show: false,
              },
              itemStyle: {
                color: "rgba(55,244,255,0)",
              },
            },

            {
              value:
                materialData.sysTotalStorage === 0
                  ? 0
                  : materialData.sysUsedStorage,
              //name: '直接访问2(实际)',
              itemStyle: {
                color: "rgba(122, 193, 67,1)",
              },
            },

            {
              value:
                materialData.sysTotalStorage === 0
                  ? 10
                  : materialData.sysTotalStorage - materialData.sysUsedStorage,
              //name: '直接访问3（总共）',
              itemStyle: {
                color: "rgba(122, 193, 67,.2)",
              },
            },
          ],
        },
      ],
    };
    return option;
  };
  return <div ref={chartRef} style={{ width: "240px", height: "390px" }}></div>;
};

export default Instrument;
