import React, {
  useImperativeHandle,
  forwardRef,
  useState,
  useRef,
  useEffect,
  useMemo,
} from "react";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  Container,
  Stack,
  IconButton,
  Grid,
  Typography,
} from "@mui/material";
import "react-responsive-carousel/lib/styles/carousel.min.css";
import CloseIcon from "@mui/icons-material/Close";
import "./components/img.css";
import { useTranslation } from "react-i18next";
import PreViewRa from "@/pages/neweditor/PreViewRa";

import RemoveIcon from "@mui/icons-material/Remove";
import AddIcon from "@mui/icons-material/Add";
import { PrettoSlider } from "@/pages/neweditor/PrettoSlider";
import SimpleBar from "@/components/third-party/SimpleBar";
const MediaCarousel = forwardRef((props, ref) => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  //设置的播放时长
  const [duration, setDuration] = useState(0);
  const [items, setItems] = useState([]);
  const resourceRef = useRef(null);
  //当前定时器
  const [intervalNow, setIntervalNow] = useState(null);
  //当前播放资源的下标
  const [currentIndex, setCurrentIndex] = useState(0);
  //播放单个资源的标志
  const [singleMaterialFlag, setSingleMaterialFlag] = useState(false);
  //点击后的资源内容
  const [singleMaterialContent, setSingleMaterialContent] = useState(null);
  const [scale, setScale] = useState(1);

  const handleClose = () => {
    setOpen(false);
    setScale(1);
    if (intervalNow) {
      clearInterval(intervalNow);
    }
  };
  const handleOpen = (playListData, type) => {
    console.log(playListData, type);
    setItems(playListData);
    setOpen(true);
    setCurrentIndex(0);
    setDuration(playListData[0].duration);
    setSingleMaterialFlag(false);
  };
  useImperativeHandle(ref, () => ({
    handleOpen,
  }));

  //播放资源的下标修改
  const onChange = (index) => {
    const nextIndex = index >= items.length ? 0 : index;
    setCurrentIndex(nextIndex);
    setDuration(items[nextIndex].duration);
  };

  //视频、音频播放结束触发方法
  const handleVideoEnded = (index) => {
    const video = resourceRef.current;
    //视频时长
    const intDuration = parseInt(video.duration);
    const currentDuration = items[currentIndex].duration;
    //设置的播放时长大于视频本身的时长
    if (currentDuration > intDuration) {
      //视频重置到未播放状态
      video.currentTime = 0;
      video.play();
      return;
    }
    //只有一个视频或音频时，要重置播放进度为0
    if (items[currentIndex].type != "image" && items.length === 1) {
      const video = resourceRef.current;
      video.currentTime = 0;
      video.play();
    }
    const nextIndex = index >= items.length ? 0 : index;
    setDuration(items[nextIndex].duration);
    setCurrentIndex(nextIndex);
  };

  useEffect(() => {
    if (items.length > 0) {
      const interval = setInterval(() => {
        if (items[currentIndex].type !== "image" && items.length === 1) {
          const video = resourceRef.current;
          video.currentTime = 0;
          video.play();
        }
        onChange(currentIndex + 1);
      }, duration * 1000);
      setIntervalNow(interval);
      return () => clearInterval(interval);
    }
  }, [items, currentIndex]);

  const switchDom = (type) => {
    switch (type) {
      case "image":
        return (
          <img
            key={items[currentIndex].id}
            src={items[currentIndex].downloadUrl}
            alt={`Image ${items[currentIndex].id}`}
            className="preview-img"
          />
        );
      case "media":
        return (
          <video
            ref={resourceRef}
            autoPlay
            controls
            key={items[currentIndex].id}
            onEnded={() => handleVideoEnded(currentIndex + 1)}
            style={{ width: "100%", height: "500px" }}
          >
            <source
              src={items[currentIndex].downloadUrl}
              type="video/mp4"
            ></source>
          </video>
        );
      case "layout":
        return (
          <PreViewRa
            programData={JSON.parse(items[currentIndex]?.layoutJson)}
          ></PreViewRa>
        );
      default:
        return (
          <>
            <svg
              data-v-56e751f7=""
              t="1592532095422"
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              p-id="2906"
              width="500"
              height="400"
              className="icon music-icon svg-img"
            >
              <path
                data-v-56e751f7=""
                d="M742.3 100.3l-25.6 44.3c126.2 73 204.7 208.9 204.7 354.6 0 225.7-183.6 409.3-409.3 409.3S102.8 724.8 102.8 499.1c0-145.7 78.4-281.5 204.7-354.6l-25.6-44.3c-142 82.1-230.2 235-230.2 398.8 0 253.9 206.6 460.5 460.5 460.5S972.6 753 972.6 499.1c0-163.9-88.2-316.7-230.3-398.8z"
                fill="#1296db"
                p-id="2907"
              ></path>{" "}
              <path
                data-v-56e751f7=""
                d="M464.2 437l-25.6-44.3c-45.3 26.2-73.5 75-73.5 127.3 0 81 65.9 147 147 147s147-65.9 147-147v-6.3L451.2 115.4h164V64.2H366.8l241 461.8c-3.1 50.1-44.8 89.9-95.6 89.9-52.8 0-95.8-43-95.8-95.8-0.1-34.1 18.2-66 47.8-83.1z"
                fill="#1296db"
                p-id="2908"
              ></path>
            </svg>
            <audio
              ref={resourceRef}
              key={items[currentIndex].id}
              src={items[currentIndex].downloadUrl}
              className="preview-audio"
              autoPlay
              onEnded={() => handleVideoEnded(currentIndex + 1)}
              controls
            ></audio>
          </>
        );
    }
  };

  const switchSingleDom = (item) => {
    switch (item.type) {
      case "image":
        return (
          <img
            key={item.id}
            src={item.downloadUrl}
            alt={`Image ${item.id}`}
            className="preview-img"
          />
        );
      case "media":
        return (
          <video
            ref={resourceRef}
            autoPlay
            controls
            key={item.id}
            style={{ width: "100%", height: "500px" }}
          >
            <source src={item.downloadUrl} type="video/mp4"></source>
          </video>
        );
      case "layout":
        return (
          <PreViewRa programData={JSON.parse(item?.layoutJson)}></PreViewRa>
        );
      default:
        return (
          <>
            <svg
              data-v-56e751f7=""
              t="1592532095422"
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              p-id="2906"
              width="500"
              height="400"
              className="icon music-icon svg-img"
            >
              <path
                data-v-56e751f7=""
                d="M742.3 100.3l-25.6 44.3c126.2 73 204.7 208.9 204.7 354.6 0 225.7-183.6 409.3-409.3 409.3S102.8 724.8 102.8 499.1c0-145.7 78.4-281.5 204.7-354.6l-25.6-44.3c-142 82.1-230.2 235-230.2 398.8 0 253.9 206.6 460.5 460.5 460.5S972.6 753 972.6 499.1c0-163.9-88.2-316.7-230.3-398.8z"
                fill="#1296db"
                p-id="2907"
              ></path>{" "}
              <path
                data-v-56e751f7=""
                d="M464.2 437l-25.6-44.3c-45.3 26.2-73.5 75-73.5 127.3 0 81 65.9 147 147 147s147-65.9 147-147v-6.3L451.2 115.4h164V64.2H366.8l241 461.8c-3.1 50.1-44.8 89.9-95.6 89.9-52.8 0-95.8-43-95.8-95.8-0.1-34.1 18.2-66 47.8-83.1z"
                fill="#1296db"
                p-id="2908"
              ></path>
            </svg>
            <audio
              ref={resourceRef}
              key={item.id}
              src={item.downloadUrl}
              className="preview-audio"
              autoPlay
              controls
            ></audio>
          </>
        );
    }
  };

  const memoizdCarousel = useMemo(
    () => (
      <>{items[currentIndex] ? switchDom(items[currentIndex].type) : null}</>
    ),
    [currentIndex, items]
  );

  const masterialListClick = (item) => {
    setSingleMaterialFlag(true);
    setSingleMaterialContent(item);
  };

  const plusScale = () => {
    if (scale < 2) {
      let result = (scale + 0.01).toFixed(2);
      setScale(parseFloat(result));
    }
  };

  const minusScale = () => {
    if (props.scale > 0.01) {
      let result = (scale - 0.01).toFixed(2);
      setScale(parseFloat(result));
    }
  };

  const handleSliderChange = (event, newValue) => {
    let result = (newValue + 0.01).toFixed(2);
    setScale(result);
  };

  return (
    <>
      <Dialog
        maxWidth="lg"
        open={open}
        fullWidth={true}
        onClose={handleClose}
        aria-describedby="alert-dialog-slide-description"
      >
        <DialogTitle sx={{ m: 0, p: 2, pt: 3 }}>
          <Stack direction={"row"} justifyContent={"space-between"}>
            <Typography variant={"h5"}>
              {t("common.common_op_preview")}
            </Typography>
            <div>
              <Stack direction="row" alignItems={"center"} spacing={1}>
                <Stack direction="row" alignItems={"center"}>
                  <IconButton onClick={minusScale}>
                    <RemoveIcon
                      sx={{
                        color: "#323232",
                      }}
                    />
                  </IconButton>

                  <PrettoSlider
                    onChange={handleSliderChange}
                    size="small"
                    min={0.01}
                    sx={{
                      width: 150,
                      height: 8,
                      "& .MuiSlider-thumb": {
                        width: 20,
                        height: 20,
                        backgroundColor: "#fff",
                        "&::before": {
                          boxShadow: "0 4px 8px rgba(0,0,0,0.4)",
                        },
                        "&:hover, &.Mui-focusVisible, &.Mui-active": {
                          boxShadow: "none",
                        },
                      },
                    }}
                    max={2}
                    step={0.01}
                    color="secondary"
                    value={scale}
                    aria-label="Small"
                    valueLabelDisplay="on"
                  />
                  <IconButton
                    onClick={() => {
                      plusScale();
                    }}
                  >
                    <AddIcon
                      sx={{
                        color: "#323232",
                      }}
                    ></AddIcon>
                  </IconButton>
                </Stack>
                <IconButton aria-label="close" onClick={handleClose}>
                  <CloseIcon />
                </IconButton>
              </Stack>
            </div>
          </Stack>
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={1}>
            <Grid item xs={10}>
              <Container
                sx={{
                  // width: "80%",
                  // height: "calc(100vh - 140px)",
                  // overflowY: "hidden",
                  maxHeight:'520px',
                  overflow:"auto",
                  // overflow: "hidden",
                }}
              >
                <Grid
                  sx={{
                    display:"flex",
                    alignItems:"center",
                    justifyContent:"center",
                    width: "100%",
                    height: "100%",
                  zoom: scale, // 使用zoom属性进行缩放

                    // transformOrigin: 'top left',
                    // transform: `scale(${scale})`,
                  }}
                >
                  {singleMaterialFlag
                    ? switchSingleDom(singleMaterialContent)
                    : items[currentIndex]
                    ? switchDom(items[currentIndex].type)
                    : null}
                </Grid>
              </Container>
            </Grid>
            <Grid
              xs={2}
              item
              sx={{
                maxHeight: 500,
              }}
            >
              <SimpleBar
                xs={{
                  "& .simplebar-content": {
                    display: "flex",
                    flexDirection: "column",
                    background: "white",
                    border: `1px solid #f4f4f4`,
                  },
                }}
              >
                {items?.map((item, index) => {
                  if (item.type === "image" || item.type === "layout") {
                    return (
                      <img
                        key={index}
                        style={{ cursor: "pointer", borderRadius: "5px",border: '1px solid' }}
                        width={"100%"}
                        src={item.downloadUrl}
                        onClick={() => {
                          masterialListClick(item);
                        }}
                      />
                    );
                  }
                  if (item.type === "media") {
                    return (
                      <img
                        key={index}
                        style={{ cursor: "pointer", borderRadius: "5px" }}
                        width={"100%"}
                        src={item.coverImage}
                        onClick={() => {
                          masterialListClick(item);
                        }}
                      />
                    );
                  }
                })}
              </SimpleBar>
            </Grid>
          </Grid>
        </DialogContent>
      </Dialog>
    </>
  );
});

export default MediaCarousel;
