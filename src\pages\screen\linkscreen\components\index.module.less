.main {
    position: relative;
    width: 100%;
    height: 100%;
    // background: red;
}

.main_child {
    display: flex;
    height: 400px;
    // background: green;
}

.right {
    width: 100%;
    height: 100%;
    // background: #f60;
}

.right_top {
    width: 100%;
    height: 100%;
    margin: 0px;
    // background: rgb(221, 213, 213);
    display: flex;
    flex-wrap: wrap;
    border-radius: 8px;
}

.card {
    background: #F2F2F2;
    border-radius:10px;
    font-size: 18px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap; /* 文字不换行 */
    overflow: hidden; /* 超出部分隐藏 */
    text-overflow: ellipsis; /* 超出部分以省略号显示 */
    box-shadow: 4px 2px 3px 0px #b5adad
}

