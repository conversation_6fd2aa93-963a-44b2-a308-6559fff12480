import { useEffect, useRef, useState } from "react";
import * as echarts from "echarts";
import { t } from "i18next";
const ScreenChart = (props) => {
  const { materialDownload } = props;
  const chartRef = useRef();
  const [data, setData] = useState({});
  useEffect(() => {
    let myEcharts = null;
    const initChart = () => {
      // 初始化Echarts图表
      myEcharts = echarts.init(chartRef.current, null, { renderer: "svg" });

      // 设置初始大小
      myEcharts.resize();

      // 监听窗口大小变化，自动调整图表大小
      window.addEventListener("resize", handleResize);
      const options = getOptions(materialDownload);
      myEcharts.setOption(options);
    };

    const handleResize = () => {
      myEcharts.resize();
    };

    // 在组件挂载时进行初始化
    initChart();

    // 在组件卸载时移除事件监听
    return () => {
      window.removeEventListener("resize", handleResize);
      myEcharts.dispose();
    };
  }, [materialDownload]);

  const getOptions = (data) => {
    const { unit } = data;
    let option = {
      xAxis: {
        boundaryGap: false,
        type: "category",
        data: data.dateList,

        splitLine: {
          show: true, //让网格显示
          lineStyle: {
            //网格样式
            color: "#7AC143", //网格的颜色
            width: 0.5, //网格的宽度
            type: "dashed", //网格是实实线，可以修改成虚线以及其他的类型
          },
        },
      },
      tooltip: {
        trigger: "axis",
        //自定义提示框
        formatter: function (params) {
          const { axisValue, data } = params[0];
          let context = `<span >${axisValue}</span></br>`;
          context += `<span style='color:#7AC143'>●</span>&nbsp;<span >${
            data + unit
          }</span>`;
          return context;
        },
      },
      yAxis: {
        boundaryGap: false,
        type: "value",
        splitLine: {
          show: true,
          //   lineStyle: {
          //     width: 1,
          //     color: "#A7A7C6",
          //     type: "solid",
          //   },
        },
      },
      dataZoom: [
        {
          type: "slider",
          xAxisIndex: 0,
          filterMode: "none",
          height: 15,
          // fillerColor: "#7ac143",
        },
        // {
        //   type: "slider",
        //   yAxisIndex: 0,
        //   filterMode: "none",
        // },
        {
          type: "inside",
          xAxisIndex: 0,
          filterMode: "none",
          height: 15,
          // fillerColor: "#7ac143",
        },
        // {
        //   type: "inside",
        //   yAxisIndex: 0,
        //   filterMode: "none",
        // },
      ],
      grid: {
        left: "6%",
        right: "6%",
        //   show: true,
        //   borderWidth: 0, // 网格线宽度设为0，实现只显示内部网格
        //   backgroundColor: "transparent", // 设置网格背景透明
      },
      //   graphic: {
      //     elements: [
      //       {
      //         type: "text",
      //         right: 130,
      //         top: 34,
      //         style: {
      //           text: "流量/时间",
      //           font: "18px  Source Han Sans CN",
      //           fontWidth: 400,
      //           //   color: "#92A5B7",
      //           fill: "#92A5B7",
      //         },
      //       },
      //     ],
      //   },

      title: [
        {
          right: "5.5%", // 位置
          top: "8%", // 位置
          // 标题
          text: t("ips.ips_traffic_time"),
          //   left: "right",
          textStyle: {
            fontSize: "16px",
            // font: "18px  Source Han Sans CN",
            fontWidth: 400,
            color: "#92A5B7",
            // fill: "#92A5B7",
          },
        },
      ],
      series: [
        {
          data: data.downloadDataList,
          //   showSymbol: "none",
          type: "line",
          smooth: true,
          boundaryGap: false,
          areaStyle: {
            background: "linear-gradient(0deg, #7BBC49, #7ABF45)",
            opacity: 0.2,
          },
          itemStyle: {
            color: "#6cb041",
            lineStyle: {
              width: 2, //设置线条粗细
            },
          },
        },
      ],
    };
    return option;
  };
  return <div ref={chartRef} style={{ width: "100%", height: "350px" }}></div>;
};
export default ScreenChart;
