# 用法
将您的应用程序包装在ConfirmProvider组件中。
注意：如果您使用的是 Material UI ThemeProvider，请确保ConfirmProvider是它的子项。
```

import { ConfirmProvider } from "material-ui-confirm";

const App = () => {
  return <ConfirmProvider>{/* ... */}</ConfirmProvider>;
};

export default App;

```

在需要函数的useConfirm地方调用挂钩。注意：组件调用必须是.confirm
useConfirmConfirmProvider

```

import Button from "@mui/material/Button";
import { useConfirm } from "material-ui-confirm";

const Item = () => {
  const confirm = useConfirm();

  const handleClick = () => {
    confirm({ description: "This action is permanent!" })
      .then(() => {
        /* ... */
      })
      .catch(() => {
        /* ... */
      });
  };

  return <Button onClick={handleClick}>Click</Button>;
};

export default Item;
```

##  API


## API

#### `ConfirmProvider`

为了在组件树中呈现对话框，需要此组件。

##### Props

| Name                 | Type     | Default | Description                                                             |
| -------------------- | -------- | ------- | ----------------------------------------------------------------------- |
| **`defaultOptions`** | `object` | `{}`    | Overrides the default options used by [`confirm`](#useconfirm-confirm). |

#### `useConfirm() => confirm`

该钩子返回`confirm`函数。

#### `confirm([options]) => Promise`

这个函数打开一个确认对话框并返回一个承诺
表示用户选择(确认时解决，取消时拒绝)。

##### Options

| Name                                    | Type        | Default           | Description                                                                                                                                                                                                                           |
| --------------------------------------- | ----------- | ----------------- |---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **`title`**                             | `ReactNode` | `'Are you sure?'` | 对话框的标题。                                                                                                                                                                                                                               |
| **`description`**                       | `ReactNode` | `''`              | 对话框内容，自动包装在 `DialogContentText`.                                                                                                                                                                                                      |
| **`content`**                           | `ReactNode` | `null`            | 对话框内容， `description` 与 `DialogContentText`. `description`如果存在则取代。                                                                                                                     |
| **`confirmationText`**                  | `ReactNode` | `'Ok'`            | 确认按钮标题。.                                                                                                                                                                                                          |
| **`cancellationText`**                  | `ReactNode` | `'Cancel'`        | 取消按钮标题。                                                                                                                                                                                                         |
| **`dialogProps`**                       | `object`    | `{}`              | Material-UI [Dialog](https://mui.com/material-ui/api/dialog/#props) props.                                                                                                                                                            |
| **`dialogActionsProps`**                | `object`    | `{}`              | Material-UI [DialogActions](https://mui.com/material-ui/api/dialog-actions/#props) props.                                                                                                                                             |
| **`confirmationButtonProps`**           | `object`    | `{}`              | Material-UI [Button](https://mui.com/material-ui/api/button/#props) props for the confirmation button.                                                                                                                                |
| **`cancellationButtonProps`**           | `object`    | `{}`              | Material-UI [Button](https://mui.com/material-ui/api/dialog/#props) props for the cancellation button.                                                                                                                                |
| **`titleProps`**                        | `object`    | `{}`              | Material-UI [DialogTitle](https://mui.com/api/dialog-title/#props) props for the dialog title.                                                                                                                                        |
| **`contentProps`**                      | `object`    | `{}`              | Material-UI [DialogContent](https://mui.com/api/dialog-content/#props) props for the dialog content.                                                                                                                                  |
| **`allowClose`**                        | `boolean`   | `true`            | 自然关闭（退出或点击背景）是否应关闭对话框。当设置为false强制用户明确取消或确认时。                                                                                  |
| **`confirmationKeyword`**               | `string`    | `undefined`       | 如果提供，默认情况下将禁用确认按钮，并呈现一个额外的文本字段。只有当文本域的内容匹配的值时，确认按钮才会启用 `confirmationKeyword` |
| **`confirmationKeywordTextFieldProps`** | `object`    | `{}`              | 用于确认关键字文本字段的Material-UI                                                                                                                    |

## Useful notes

### Confirm by pressing _Enter_

您可以通过向确认按钮添加`autoFocus`属性来获得这种行为。
这样，只要打开对话框并点击_enter，按钮就会被聚焦
自然触发点击。

##### Locally

```jsx
const MyComponent = () => {
  // ...
  const handleClick = () => {
    confirm({ confirmationButtonProps: { autoFocus: true } })
      .then(() => {
        /* ... */
      })
      .catch(() => {
        /* ... */
      });
  };
  // ...
};
```

##### Globally

```jsx
const App = () => {
  return (
    <ConfirmProvider
      defaultOptions={{
        confirmationButtonProps: { autoFocus: true },
      }}
    >
      {/* ... */}
    </ConfirmProvider>
  );
};
```
