import { selectedOutlet } from "@/service/api/L3Sevice.js";
import { getPrincipaList } from "@/service/api/L3Sevice.js";
import {
    getSplicingScreenById,
} from "@/service/api/linkScreen";
export const getOption = (merchantId, setStoreOptions) => {
    selectedOutlet(merchantId).then((res) => {
        setStoreOptions(res.data);
    });
};


// 请求商户数据
export const handleRequestMerchant = (setMerchantOptions) => {
    getPrincipaList().then((res) => {
        setMerchantOptions(res.data);
    });
};


export const handleGetLinkScreenInfo = (id, setFormData) => {
    getSplicingScreenById(id).then((res) => {
        setFormData(res.data);
    });
};


//循环生成多个dom
export const getCardMap = (cardsRef) => {
    if (!cardsRef.current) {
        cardsRef.current = new Map();
    }
    return cardsRef.current;
};


//所有dom的节点的值给到后端
const screenResultValue = (cardsRef, linkScreenFormik, allScreenResultValue) => {
    //push前必须先清空
    allScreenResultValue = [];
    cardsRef.current.forEach((item) => {
        allScreenResultValue.push(item.id);
    });
    linkScreenFormik.values.linkScreenArray = allScreenResultValue.join(",");
};

//改变指定dom的值
export const changeCardValue = (itemId, id, name, sn, directionName, linkScreenFormik, cardsRef, allScreenResultValue) => {
    //验证是否重复选择数字标牌
    if (linkScreenFormik.values.linkScreenArray.includes(id)) {
        toast.error(t("ips.ips_not_reSelect_signage"));
        return;
    }
    const map = getCardMap(cardsRef);
    const node = map.get(itemId);
    // let text = name + '\n' + sn + '\n' + directionName;
    node.innerText = name;
    node.title = name;
    node.id = id;
    screenResultValue(cardsRef, linkScreenFormik, allScreenResultValue);
};
