import React from 'react'
import Dialog from '@mui/material/Dialog';
import PreView from './PreView';
const PreViewDia = (props) => {
    return (
        <Dialog
            open={props.visible}
            onClose={() => {
                if (props.onClose) {
                    props.onClose();
                }
            }}
            maxWidth={props.maxWidth || false}
        >
            <PreView {...props}></PreView>
        </Dialog>
    );
};

export default PreViewDia;
