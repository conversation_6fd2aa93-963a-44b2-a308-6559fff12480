/* eslint-disable react/prop-types */
import { Dialog, DialogContent, DialogTitle, IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import React, { useState, forwardRef, useImperativeHandle, memo } from "react";
import PlayListTable from "./PlayListTable";
import { Button, Stack } from "@mui/material";
import { t } from "i18next";
//播放清单
const AddPlayList = memo(
  forwardRef((props, ref) => {
    const { playListType,isLayout } = props;
    // 播放清单选择弹窗
    const [open, setOpen] = useState(false);
    const [index, setIndex] = useState(0);
    const [tableObject, setTableObject] = useState(undefined);

    useImperativeHandle(ref, () => ({
      handleClose,
      handleOpen,
      setIndex,
    }));

    const handlePlayList = (playListId, playListName) => {
      props.setPlayListFormValues(playListId, playListName, index);
    };

    const handleClose = () => {
      setOpen(false);
    };
    const handleOpen = () => {
      setOpen(true);
    };
    return (
      <>
        <Dialog
          open={open}
          maxWidth="md"
          fullWidth={true}
          onClose={handleClose}
          aria-describedby="alert-dialog-slide-description"
        >
          <DialogTitle>
            {t("ips.ips_playlist_select")}
            <IconButton
              aria-label="close"
              onClick={handleClose}
              sx={{
                position: "absolute",
                right: 8,
                top: 8,
                color: (theme) => theme.palette.grey[500],
              }}
            >
              <CloseIcon />
            </IconButton>
          </DialogTitle>
          <DialogContent>
            <PlayListTable
              setTableObject={setTableObject}
              playListType={playListType}
              isLayout={isLayout}
            />
          </DialogContent>
          <Stack
            direction="row"
            justifyContent="flex-end"
            sx={{ margin: "15px" }}
          >
            <Button
              variant="contained"
              color="primary"
              // disabled={!tableObject}
              onClick={() => {
                if (!tableObject) {
                  return;
                }
                handlePlayList(tableObject.id, tableObject.name);
                setTableObject(null);
                handleClose();
              }}
            >
              {t("common.common_edit_ok")}
            </Button>
          </Stack>
        </Dialog>
      </>
    );
  })
);

export default AddPlayList;
