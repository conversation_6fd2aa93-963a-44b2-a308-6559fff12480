import React, { forwardRef, useRef, useEffect, useState } from "react";
import { getToken, setToken } from "@/utils/auth";
import { Grid } from "@mui/material";
import AgeRange from "./real/AgeRange";
import VisitorsGender from "./real/VisitorsGender";
import EntryFlowTrend from "./real/EntryFlowTrend";
import GenderDistriBution from "./real/GenderDistriBution";
import { fetchEventSource } from "@microsoft/fetch-event-source";
import { useTranslation } from "react-i18next";

import Box from "@mui/material/Box";
import Tabs, { tabsClasses } from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";

import { remove } from "@/service/api/sse";
const RealTime = (props) => {
  const [data, setData] = useState([]);
  const { t } = useTranslation();
  const currentIdRef = useRef("");
  const [currentId, setCurrentId] = useState("");
  const [visitors, setVisitors] = useState(0);
  const [deadline, setDeadline] = useState("");

  const [ageRange, setAgeRange] = useState([]);
  const [gender, setGender] = useState([]);
  const [trendValue, setTrendValue] = useState([]);
  const [genderDistribution, setGenderDistribution] = useState([]);

  const ctrlAbout = useRef(null);
  useEffect(() => {
    initEES();
    return () => {
      closeEES();
    };
  }, []);
  const idRef = useRef(null);
  const initEES = () => {
    if (ctrlAbout && ctrlAbout.current) {
      return false;
    }
    const timeId = Date.now();
    idRef.current = timeId;
    ctrlAbout.current = new AbortController();
    fetchEventSource(
      `${import.meta.env.VITE_API_URL}/sse/connect/realtime/${timeId}`,
      {
        method: "POST",
        headers: {
          Accept: "text/event-stream",
          Authorization: import.meta.env.VITE_TOKEN_HEADER + getToken(),
        },
        signal: ctrlAbout.current.signal,
        body: JSON.stringify({}),
        onmessage(msg) {
          let resData = msg.data;
          resData = JSON.parse(resData);
          if (resData.code === "E00000") {
            let result = resData.data;
            setData(result.materials || []);
            let filterResult = result.materials.filter((item) => {
              if (item.materialId === currentIdRef.current) {
                return true;
              } else {
                return false;
              }
            });

            if (filterResult && filterResult.length === 0) {
              setCurrentId(result?.materials[0]?.materialId);
            }

            setVisitors(result.visitors);
            setDeadline(result.deadline);
          }
        },
        onerror() {
          // 服务异常
          console.log("服务异常");
          closeEES();
        },
        onclose() {
          // 服务关闭
          console.log("服务关闭");
        },
      }
    );
  };
  const closeEES = async () => {
    if (ctrlAbout && ctrlAbout.current) {
      await removeSSe();
      ctrlAbout.current.abort();
      ctrlAbout.current = null;
    }
  };

  const removeSSe = async () => {
    await remove(idRef.current).then((res) => {
      return Promise.resolve();
    });
  };

  useEffect(() => {
    let result = data.filter((obj) => {
      return obj.materialId === currentId;
    });
    let selectItem = result.length ? result[0] : null;
    if (selectItem) {
      setAgeRange(selectItem.ageRange || []);
      setGender(selectItem.gender || []);
      setTrendValue(selectItem.trendValue || []);
      setGenderDistribution(selectItem.genderDistribution || []);
    } else {
      setAgeRange([]);
      setGender([]);
      setTrendValue([]);
      setGenderDistribution([]);
    }
    currentIdRef.current = currentId;
  }, [data, currentId]);

  return (
    <Grid
      sx={{
        display: "flex",
        flexDirection: "column",
        padding: "0px 10px",
      }}
    >
      <Grid
        sx={{
          height: "30px",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Grid>{deadline}</Grid>
        <Grid
          sx={{
            display: "flex",
            alignItems: "center",
          }}
        >
          <Grid
            sx={{
              marginRight: "20px",
            }}
          >
            {t("realTime.realTimeVisitors")}
          </Grid>
          <Grid
            sx={{
              height: "30px",
              padding: "10px",
              border: "2px solid  #7ac143",
              borderRadius: "10px",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              fontSize: "20px",
              fontWeight: "600",
            }}
          >
            {visitors}
          </Grid>
        </Grid>
      </Grid>

      <Box
        sx={{
          marginTop:'5px',
          flexGrow: 1,
          padding:"5px",
          borderRadius: "7px",
          bgcolor: "background.paper",
        }}
      >
        <Tabs
          value={currentId}
          onChange={(e, v) => {
            setCurrentId(v);
          }}
          sx={{
            ".MuiTabs-indicator": {
              display: "none",
            },
          }}
          variant="scrollable"
          scrollButtons="auto"
          aria-label="scrollable auto tabs example"
        >
          {data.map((item) => {
            return (
              <Tab
                icon={
                  <Grid>
                    <img
                      style={{
                        borderRadius: "7px",
                      }}
                      height={40}
                      src={item?.url}
                    ></img>
                  </Grid>
                }
                key={item.materialId}
                value={item.materialId}
                sx={{
                  fontSize: "12px",
                  cursor: "pointer",
                  border:item.materialId=== currentId? '1px solid #7ac143':'none',
                  margin: "0px 5px",
                  borderRadius: "7px",
                }}
                label={item.name}
              />
            );
          })}
        </Tabs>
      </Box>

      <Grid
        sx={{
          marginTop: "5px",
        }}
        container
        rowSpacing={1}
        columnSpacing={{ xs: 1, sm: 2, md: 3 }}
      >
        <Grid
          sx={{
            minHeight: "25vh",
          }}
          item
          xs={6}
          sm={6}
          md={6}
        >
          <AgeRange ageRange={ageRange}></AgeRange>
        </Grid>
        <Grid
          sx={{
            minHeight: "25vh",
          }}
          item
          xs={6}
          sm={6}
          md={6}
        >
          <VisitorsGender gender={gender}></VisitorsGender>
        </Grid>
        <Grid
          sx={{
            minHeight: "44vh",
          }}
          item
          xs={6}
          sm={6}
          md={6}
        >
          <EntryFlowTrend trendValue={trendValue}></EntryFlowTrend>
        </Grid>
        <Grid
          sx={{
            minHeight: "44vh",
          }}
          item
          xs={6}
          sm={6}
          md={6}
        >
          <GenderDistriBution
            genderDistribution={genderDistribution}
          ></GenderDistriBution>
        </Grid>
      </Grid>
    </Grid>
  );
};
export default RealTime;
