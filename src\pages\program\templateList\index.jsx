import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Link,
  Tooltip,
  Grid,
  TextField,
  InputLabel,
  OutlinedInput,
  IconButton,
  Pagination,
} from "@mui/material";
import ZKSelect from "@/components/ZKSelect";
import MainCard from "@/components/MainCard";
import AddNewTemplate from "./AddNewTemplate";
import { useEffect, useMemo, useRef, useState } from "react";
import { getTemplateList } from "@/service/api/layout";
import { useNavigate } from "react-router-dom";
import AddNewLayout from "../material/components/AddNewLayout";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import {
  getIndustry,
  deleteTemplate,
  getTemplateById,
} from "@/service/api/layout";
import { useTranslation } from "react-i18next";
import { useFormik } from "formik";
import { useConfirm } from "@/components/zkconfirm";
import EmptyPath from "@/assets/images/icons/empty.svg";
// 消息提示
import { toast } from "react-toastify";
import { getPrincipaList } from "@/service/api/L3Sevice.js";
const TemplateList = () => {
  const { t } = useTranslation();
  const [openTemplate, setOpenTemplate] = useState(false);
  const navigate = useNavigate();
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [pageSize, setPageSize] = useState(20);
  const [tempInfo, setCurrent] = useState({});
  const [openLayout, setPpenLayout] = useState(false);
  const [templateData, setTemplateData] = useState([]);
  const [industryType, setIndustryType] = useState("");
  const [industryList, setIndustryList] = useState([]);
  const [queryParams, setQueryParams] = useState({});
  const [merchantList, setMerchantList] = useState([]);
  const confirm = useConfirm();
  const loadTypeList = () => {
    getIndustry().then((res) => {
      if (res.code == "00000000") {
        setIndustryList([
          {
            label: t("template.all"),
            value: "",
          },
          ...res.data,
        ]);
      } else {
        setIndustryList([]);
      }
    });
  };

  useEffect(() => {
    loadList();
  }, [page, industryType, queryParams]);

  useEffect(() => {
    setQueryParams({});
  }, [industryType]);

  useEffect(() => {
    loadTypeList();
    getPrincipaList().then((res) => {
      setMerchantList(res.data);
    });
  }, []);

  const handleChangeIndustryType = (event, newValue) => {
    setIndustryType(newValue);
  };

  const loadList = () => {
    getTemplateList({
      page: page,
      pageSize: pageSize,
      industryType: industryType,
      ...queryParams,
    }).then((res) => {
      if (res.code == "00000000") {
        let result = res.data;
        setTemplateData(result.data);
        setPage(result.page);
        setTotal(result.total);
      }
    });
  };

  const clickUserTemp = (item) => {
    setCurrent(item);
    setPpenLayout(true);
  };

  const onCancel = () => {
    setOpenTemplate(false);
  };

  const toEditor = (row) => {
    sessionStorage.setItem("templateInfo", JSON.stringify(row));
    sessionStorage.setItem("templateLayout", true);
    sessionStorage.setItem("isAddTemplateLayout", false);
    navigate("/neweditor");
  };

  const handleNewLayoutDiaClo = () => {
    setPpenLayout(false);
  };

  // 查询表单
  const queryFormik = useFormik({
    initialValues: {
      templateName: "",
      direction: "",
      resolutionWidth: "",
      resolutionHeight: "",
      merchantId: "",
    },
    onSubmit: (values) => {
      try {
        setQueryParams(values);
        setPage(1);
      } catch (err) {
        console.log(err);
      }
    },
  });
  // 重置查询
  const resetQuery = () => {
    // 重置表单
    setQueryParams({});
    setPage(1);
    queryFormik.resetForm();
  };

  const deleteTemp = (row) => {
    confirm({
      title: t("message.messageBox_title"),
      confirmationText: t("common.common_edit_ok"),
      cancellationText: t("common.common_edit_cancel"),
      description: t("template.deleteTempTip"),
    }).then(() => {
      deleteTemplate(row.id).then((res) => {
        toast.success(res.message);
        loadList();
      });
    });
  };

  return (
    <Grid
      sx={{
        display: "flex",
        minHeight: "calc(100vh - 110px)",
        flexDirection: "column",
      }}>
      <Grid
        style={{
          marginBottom: "10px",
          padding: "5px",
          backgroundColor: "#ffffff",
          borderRadius: "5px",
        }}>
        <Grid
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}>
          <Grid sx={{ paddingLeft: "40px", paddingTop: "14px" }}>
            <form noValidate onSubmit={queryFormik.handleSubmit}>
              <Grid
                container
                direction="row"
                justifyContent="flex-start"
                alignItems="center"
                spacing={3}>
                <Grid item xs={12} md={4} lg={2}>
                  <ZKSelect
                    id="merchantId"
                    size="small"
                    name="merchantId"
                    value={queryFormik.values.merchantId}
                    options={merchantList}
                    onClear={() => {
                      queryFormik.setFieldValue("merchantId", "");
                    }}
                    onBlur={queryFormik.handleBlur}
                    onChange={queryFormik.handleChange}
                    type="text"
                    menuWidth={200}
                    placeholder={t("ips.ips_select_merchant")}
                  />
                </Grid>
                <Grid item xs={12} md={4} lg={2}>
                  <TextField
                    label={t("layout.program")}
                    value={queryFormik.values.templateName}
                    onChange={queryFormik.handleChange}
                    onBlur={queryFormik.handleBlur}
                    size="small"
                    type="text"
                    name="templateName"
                    fullWidth
                    placeholder={t("template.input_template_name")}
                  />
                </Grid>

                <Grid item xs={12} sm={4} md={2}>
                  {/* <TextField
                    label={t("ips.ips_screen_model")}
                    size="small"
                    type="text"
                    name="screenModel"
                    fullWidth
                    value={queryFormik.values.contacts}
                    onChange={queryFormik.handleChange}
                    onBlur={queryFormik.handleBlur}
                  /> */}
                  <ZKSelect
                    size="small"
                    name="direction"
                    value={queryFormik.values.direction}
                    placeholder={t("ips.ips_please_select_direction")}
                    options={[
                      {
                        label: t("ips.ips_material_landscape"),
                        value: "0",
                      },
                      {
                        label: t("ips.ips_material_portrait_screen"),
                        value: "1",
                      },
                    ]}
                    onChange={queryFormik.handleChange}
                    onBlur={queryFormik.handleBlur}
                    onClear={() => {
                      queryFormik.setFieldValue("direction", undefined);
                    }}
                    error={Boolean(
                      queryFormik.touched.direction &&
                        queryFormik.errors.direction
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={4} lg={2}>
                  <TextField
                    label={t("common.common_width")}
                    value={queryFormik.values.resolutionWidth}
                    onChange={queryFormik.handleChange}
                    onBlur={queryFormik.handleBlur}
                    size="small"
                    type="text"
                    name="resolutionWidth"
                    fullWidth
                    placeholder={t("common.common_input_width")}
                  />
                </Grid>
                <Grid item xs={12} md={4} lg={2}>
                  <TextField
                    label={t("common.common_height")}
                    value={queryFormik.values.resolutionHeight}
                    onChange={queryFormik.handleChange}
                    onBlur={queryFormik.handleBlur}
                    size="small"
                    type="text"
                    name="resolutionHeight"
                    fullWidth
                    placeholder={t("common.common_input_height")}
                  />
                </Grid>

                <Grid item xs={12} md={5} lg={2}>
                  <Stack
                    direction="row"
                    justifyContent="flex-start"
                    alignItems="flex-start"
                    spacing={2}>
                    <Button
                      disableElevation
                      type="submit"
                      variant="contained"
                      size="small">
                      {t("common.common_table_query")}
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={resetQuery}
                      color="info"
                      sx={{
                        minWidth: "80px",
                      }}
                      size="small">
                      {t("common.common_op_reset")}
                    </Button>
                  </Stack>
                </Grid>
              </Grid>
            </form>
          </Grid>

          <Grid
            sx={{
              paddingTop: "14px",
            }}>
            <Button
              sx={{
                padding: "4px",
              }}
              onClick={() => {
                setOpenTemplate(true);
              }}>
              {t("template.addTemplate")}
            </Button>
          </Grid>
        </Grid>

        <Grid>
          <Tabs
            value={industryType}
            onChange={handleChangeIndustryType}
            variant="scrollable"
            scrollButtons="auto"
            aria-label="scrollable auto tabs example">
            {industryList?.map((item) => {
              return (
                <Tab
                  sx={{
                    padding: "10px",
                    minWidth: "40px",
                  }}
                  key={item.label}
                  label={item.label}
                  value={item.value}
                />
              );
            })}
          </Tabs>
        </Grid>
      </Grid>
      <Grid
        sx={{
          display: "flex",
          flexWrap: "wrap",
        }}>
        {templateData && templateData.length === 0 && (
          <Grid
            sx={{
              display: "flex",
              justifyContent: "center",
              width: "100%",
              height: "50vh",
            }}>
            <img width={300} src={EmptyPath} />
          </Grid>
        )}

        {templateData.map((item) => {
          return (
            <Grid
              sx={{
                width: "20%",
                height: "200px",
                padding: "10px",
                cursor: "pointer",
              }}
              key={item.id}>
              <Grid
                sx={{
                  height: "100%",
                  width: "100%",
                  border: "1px solid #f0ebeb",
                  position: "relative",
                  backgroundColor: "#ffffff",
                  borderRadius: "6px",
                  overflow: "hidden",
                }}>
                <Grid
                  sx={{
                    position: "absolute",
                    top: "0px",
                    height: "40px",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    backgroundColor: "#00000040",
                    width: "100%",
                    padding: "6px",
                    whiteSpace: "nowrap",
                    color: "#ffffff",
                  }}>
                  {item.templateName}
                </Grid>
                <Grid
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    height: "100%",
                    width: "100%",
                    backgroundImage: `url('${item.thumbnailUrl}')`,
                    backgroundSize: "contain",
                    backgroundRepeat: "no-repeat",
                    backgroundPosition: "center",
                  }}></Grid>

                <Grid
                  sx={{
                    display: "flex",
                    position: "absolute",
                    bottom: "0px",
                    height: "40px",
                    alignItems: "center",
                    justifyContent: "space-around",
                    width: "100%",
                    backgroundColor: "#00000040",
                    color: "#ffffff",
                  }}>
                  {item.editorFlag && (
                    <Grid
                      sx={{
                        "&:hover": {
                          color: "#7ac143",
                          cursor: "pointer",
                        },
                      }}
                      onClick={async () => {
                        const res = await getTemplateById(item.id);

                        toEditor(res?.data);
                      }}>
                      {t("template.editor")}
                    </Grid>
                  )}

                  {item.editorFlag && (
                    <Grid
                      sx={{
                        "&:hover": {
                          color: "#7ac143",
                          cursor: "pointer",
                        },
                      }}
                      onClick={() => {
                        deleteTemp(item);
                      }}>
                      {t("editor.editor_del")}
                    </Grid>
                  )}

                  <Grid
                    onClick={() => {
                      clickUserTemp(item);
                    }}
                    sx={{
                      "&:hover": {
                        color: "#7ac143",
                        cursor: "pointer",
                      },
                    }}>
                    {t("template.use")}
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          );
        })}
      </Grid>

      <Grid
        sx={{
          display: "flex",
          justifyContent: "flex-end",
        }}>
        {Math.ceil(total / pageSize) > 1 && (
          <Pagination
            count={Math.ceil(total / pageSize)}
            variant="outlined"
            page={page}
            shape="rounded"
            // siblingCount={3}
            onChange={(event, page) => {
              setPage(page);
            }}
          />
        )}
      </Grid>
      {openTemplate && (
        <AddNewTemplate
          open={openTemplate}
          onCancel={onCancel}></AddNewTemplate>
      )}

      {openLayout && (
        <AddNewLayout
          userTemp={true}
          tempInfo={tempInfo}
          open={openLayout}
          onCancel={handleNewLayoutDiaClo}
        />
      )}
    </Grid>
  );
};

export default TemplateList;
