import request from "@/utils/request";
const baseProfixURI = "/merchant";

/**
 *  分页查询 商户管理
 * <AUTHOR>
 * @date 2023-03-28 11:30
 */
export const listByPage = (params) => {
  return request({
    url: `${baseProfixURI}/page`,
    method: "get",
    params: params,
  });
};

/**
 * 商户保存
 * <AUTHOR>
 * @date 2023-03-28 11:41
 */
export const save = (params) => {
  let formData = new FormData();
  formData.append("logo", params.logo);
  formData.append("name", params.name);
  formData.append("contactEmail", params.contactEmail);
  formData.append("contactPhone", params.contactPhone);
  formData.append("password", params.password);
  formData.append("areaName", params.areaName);
  // formData.append("allotAreaId", params.allotAreaId);
  return request({
    url: `${baseProfixURI}/save`,
    method: "PUT",
    headers: {
      isRetry: false,
      "Content-Type": "multipart/form-data",
    },
    data: formData,
  });
};

/**
 * 商户修改
 * <AUTHOR>
 * @date 2023-03-28 11:45
 */
export const update = (params) => {
  let formData = new FormData();
  // todo 后端接收的类型是file 修改没有上传的就是地址,所以需要加一个类型处理
  if (params?.logoType === "url") {
    formData.append("logoUrl", params?.logoUrl);
  } else {
    formData.append("logo", params?.logo);
  }
  formData.append("id", params.id);
  formData.append("name", params.name);
  formData.append("type", params.type);
  formData.append("logoType", params.logoType);
  return request({
    url: `${baseProfixURI}/update`,
    method: "POST",
    headers: {
      isRetry: false,
      "Content-Type": "multipart/form-data",
    },
    data: params,
  });
};

/**
 * 商户修改
 * <AUTHOR>
 * @date 2023-03-28 11:51
 */
export const removeMerchant = (ids) => {
  return request({
    url: `${baseProfixURI}/delete/${ids}`,
    method: "delete",
  });
};

/**
 * 根据类型获取零售商信息(key,value)
 * <AUTHOR>
 * @date 2023-04-10 11:51
 */
export const getMerchantSelect = (type) => {
  return request({
    url: `${baseProfixURI}/option?type=${type}`,
    method: "get",
  });
};

export const getMerchantSelectScope = () => {
  return request({
    url: `${baseProfixURI}/option/scope`,
    method: "get",
  });
};

export const getMerchantOptionByParam = (temp) => {
  return request({
    url: `${baseProfixURI}/option/param?id=${temp.merchantId}&license=${temp.license}`,
    method: "get",
  });
};
