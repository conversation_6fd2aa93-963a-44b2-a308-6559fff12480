import React, { useState, useCallback, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  FormControl,
  FormLabel,
  Switch,
  FormControlLabel,
  Button,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Chip,
  <PERSON>ack,
  <PERSON>ert,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  TextField
} from '@mui/material';
import { styled } from '@mui/material/styles';
import TreeSelect from './index';

// Material-UI 风格的样式组件
const StyledCard = styled(Card)(({ theme }) => ({
  borderRadius: theme.spacing(2),
  boxShadow: theme.shadows[2],
  transition: theme.transitions.create(['box-shadow', 'transform'], {
    duration: theme.transitions.duration.short,
  }),
  '&:hover': {
    boxShadow: theme.shadows[4],
    transform: 'translateY(-2px)',
  },
}));

const ConfigPanel = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: theme.spacing(2),
  background: `linear-gradient(135deg, ${theme.palette.primary.light}08, ${theme.palette.secondary.light}08)`,
  border: `1px solid ${theme.palette.divider}`,
}));

const ResultChip = styled(Chip)(({ theme }) => ({
  fontFamily: 'monospace',
  fontSize: '0.75rem',
  maxWidth: '100%',
  '& .MuiChip-label': {
    overflow: 'hidden',
    textOverflow: 'ellipsis',
  },
}));

// 示例数据 - 兼容 Ant Design 格式
const treeData = [
  {
    title: '技术部',
    value: 'tech',
    children: [
      {
        title: '前端组',
        value: 'frontend',
        children: [
          { title: 'React 开发', value: 'react' },
          { title: 'Vue 开发', value: 'vue' },
          { title: 'Angular 开发', value: 'angular' },
        ],
      },
      {
        title: '后端组',
        value: 'backend',
        children: [
          { title: 'Java 开发', value: 'java' },
          { title: 'Node.js 开发', value: 'nodejs' },
          { title: 'Python 开发', value: 'python' },
        ],
      },
      {
        title: '测试组',
        value: 'test',
        children: [
          { title: '自动化测试', value: 'auto-test' },
          { title: '手动测试', value: 'manual-test' },
        ],
      },
    ],
  },
  {
    title: '产品部',
    value: 'product',
    children: [
      { title: '产品经理', value: 'pm' },
      { title: 'UI 设计师', value: 'ui' },
      { title: 'UX 设计师', value: 'ux' },
    ],
  },
  {
    title: '运营部',
    value: 'operation',
    children: [
      { title: '市场推广', value: 'marketing' },
      { title: '客户服务', value: 'service' },
      { title: '数据分析', value: 'analysis' },
    ],
  },
];

// 简单模式数据示例
const simpleTreeData = [
  { id: 1, pId: 0, value: '1', title: '技术部' },
  { id: 2, pId: 1, value: '1-1', title: '前端组' },
  { id: 3, pId: 1, value: '1-2', title: '后端组' },
  { id: 4, pId: 2, value: '1-1-1', title: 'React 开发' },
  { id: 5, pId: 2, value: '1-1-2', title: 'Vue 开发' },
  { id: 6, pId: 3, value: '1-2-1', title: 'Java 开发' },
  { id: 7, pId: 3, value: '1-2-2', title: 'Node.js 开发' },
];

// 生成大数据量测试数据
const generateLargeTreeData = (nodeCount = 1000) => {
  const data = [];
  const departments = ['技术部', '产品部', '运营部', '市场部', '人事部'];
  const teams = ['前端组', '后端组', '测试组', '设计组', '运维组'];
  const positions = ['初级工程师', '中级工程师', '高级工程师', '技术专家', '架构师'];

  // 生成部门
  departments.forEach((dept, deptIndex) => {
    const deptValue = `dept-${deptIndex}`;
    data.push({
      title: dept,
      value: deptValue,
      children: []
    });

    // 每个部门生成团队
    teams.forEach((team, teamIndex) => {
      const teamValue = `${deptValue}-team-${teamIndex}`;
      const teamNode = {
        title: team,
        value: teamValue,
        children: []
      };

      // 每个团队生成人员
      const memberCount = Math.floor(nodeCount / (departments.length * teams.length));
      for (let i = 0; i < memberCount; i++) {
        const position = positions[i % positions.length];
        const memberValue = `${teamValue}-member-${i}`;
        teamNode.children.push({
          title: `${position} ${i + 1}`,
          value: memberValue,
        });
      }

      data[deptIndex].children.push(teamNode);
    });
  });

  return data;
};

const AntdTreeSelectExample = () => {
  // 基础示例
  const [basicValue, setBasicValue] = useState();

  // 多选示例
  const [multipleValue, setMultipleValue] = useState([]);

  // 可勾选示例
  const [checkableValue, setCheckableValue] = useState([]);

  // 简单模式示例
  const [simpleValue, setSimpleValue] = useState();

  // 异步加载示例
  const [asyncValue, setAsyncValue] = useState();
  const [asyncTreeData, setAsyncTreeData] = useState([
    { title: '根节点', value: 'root', isLeaf: false },
  ]);

  // 大数据量测试
  const [largeDataValue, setLargeDataValue] = useState([]);
  const [largeTreeData, setLargeTreeData] = useState([]);
  const [nodeCount, setNodeCount] = useState(1000);
  const [virtual, setVirtual] = useState(true);
  const [listHeight, setListHeight] = useState(400);

  // 配置选项
  const [showSearch, setShowSearch] = useState(true);
  const [disabled, setDisabled] = useState(false);
  const [allowClear, setAllowClear] = useState(true);
  const [treeCheckable, setTreeCheckable] = useState(false);
  const [treeLine, setTreeLine] = useState(false);

  // 生成大数据量数据
  const generateLargeData = useCallback(() => {
    const data = generateLargeTreeData(nodeCount);
    setLargeTreeData(data);
    setLargeDataValue([]);
  }, [nodeCount]);

  // 初始化大数据量数据
  useEffect(() => {
    generateLargeData();
  }, [generateLargeData]);

  // 异步加载数据
  const loadData = ({ value }) => {
    return new Promise(resolve => {
      setTimeout(() => {
        setAsyncTreeData(origin =>
          updateTreeData(origin, value, [
            { title: `子节点 ${value}-1`, value: `${value}-1`, isLeaf: true },
            { title: `子节点 ${value}-2`, value: `${value}-2`, isLeaf: true },
          ])
        );
        resolve();
      }, 1000);
    });
  };

  const updateTreeData = (list, key, children) => {
    return list.map(node => {
      if (node.value === key) {
        return { ...node, children };
      }
      if (node.children) {
        return { ...node, children: updateTreeData(node.children, key, children) };
      }
      return node;
    });
  };

  return (
    <Box sx={{ p: 3, backgroundColor: 'background.default', minHeight: '100vh' }}>
      <Box sx={{ textAlign: 'center', mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom sx={{ fontWeight: 600 }}>
          Material-UI TreeSelect
        </Typography>
        <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
          兼容 Ant Design API 的 Material-UI 风格树形选择组件
        </Typography>
        <Alert severity="info" sx={{ maxWidth: 600, mx: 'auto' }}>
          <AlertTitle>特性说明</AlertTitle>
          完全兼容 Ant Design TreeSelect API，使用 Material-UI 设计风格
        </Alert>
      </Box>

      {/* 配置面板 */}
      <ConfigPanel sx={{ mb: 4 }}>
        <Typography variant="h5" gutterBottom sx={{ fontWeight: 500, mb: 3 }}>
          🎛️ 配置选项
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={2.4}>
            <FormControlLabel
              control={
                <Switch
                  checked={showSearch}
                  onChange={(e) => setShowSearch(e.target.checked)}
                  color="primary"
                />
              }
              label="showSearch"
              sx={{ '& .MuiFormControlLabel-label': { fontSize: '0.875rem' } }}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <FormControlLabel
              control={
                <Switch
                  checked={disabled}
                  onChange={(e) => setDisabled(e.target.checked)}
                  color="primary"
                />
              }
              label="disabled"
              sx={{ '& .MuiFormControlLabel-label': { fontSize: '0.875rem' } }}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <FormControlLabel
              control={
                <Switch
                  checked={allowClear}
                  onChange={(e) => setAllowClear(e.target.checked)}
                  color="primary"
                />
              }
              label="allowClear"
              sx={{ '& .MuiFormControlLabel-label': { fontSize: '0.875rem' } }}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <FormControlLabel
              control={
                <Switch
                  checked={treeCheckable}
                  onChange={(e) => setTreeCheckable(e.target.checked)}
                  color="primary"
                />
              }
              label="treeCheckable"
              sx={{ '& .MuiFormControlLabel-label': { fontSize: '0.875rem' } }}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <FormControlLabel
              control={
                <Switch
                  checked={treeLine}
                  onChange={(e) => setTreeLine(e.target.checked)}
                  color="primary"
                />
              }
              label="treeLine"
              sx={{ '& .MuiFormControlLabel-label': { fontSize: '0.875rem' } }}
            />
          </Grid>
        </Grid>

        {/* 虚拟滚动配置 */}
        <Divider sx={{ my: 2 }} />
        <Typography variant="h6" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>
          🚀 虚拟滚动配置
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={3}>
            <FormControlLabel
              control={
                <Switch
                  checked={virtual}
                  onChange={(e) => setVirtual(e.target.checked)}
                  color="primary"
                />
              }
              label="启用虚拟滚动"
              sx={{ '& .MuiFormControlLabel-label': { fontSize: '0.875rem' } }}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              label="列表高度"
              type="number"
              value={listHeight}
              onChange={(e) => setListHeight(Number(e.target.value))}
              size="small"
              InputProps={{ endAdornment: 'px' }}
              sx={{ width: '100%' }}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              label="节点数量"
              type="number"
              value={nodeCount}
              onChange={(e) => setNodeCount(Number(e.target.value))}
              size="small"
              sx={{ width: '100%' }}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              variant="contained"
              onClick={generateLargeData}
              size="small"
              sx={{ height: 40 }}
            >
              重新生成数据
            </Button>
          </Grid>
        </Grid>
      </ConfigPanel>

      <Grid container spacing={3}>
        {/* 基础用法 */}
        <Grid item xs={12} md={6}>
          <StyledCard>
            <CardHeader
              title="🎯 基础用法"
              subheader="单选模式示例"
              titleTypographyProps={{ variant: 'h6', fontWeight: 500 }}
            />
            <CardContent>
              <FormControl fullWidth sx={{ mb: 3 }}>
                <FormLabel sx={{ mb: 1, fontWeight: 500 }}>单选模式</FormLabel>
                <TreeSelect
                  value={basicValue}
                  onChange={setBasicValue}
                  treeData={treeData}
                  placeholder="请选择部门"
                  showSearch={showSearch}
                  disabled={disabled}
                  allowClear={allowClear}
                  treeLine={treeLine}
                  treeDefaultExpandAll
                  size="medium"
                />
              </FormControl>

              <Divider sx={{ my: 2 }} />

              <Stack direction="row" spacing={1} alignItems="center">
                <Typography variant="body2" color="text.secondary">
                  选中值:
                </Typography>
                <ResultChip
                  label={basicValue ? JSON.stringify(basicValue) : '未选择'}
                  variant="outlined"
                  size="small"
                  color={basicValue ? "primary" : "default"}
                />
              </Stack>
            </CardContent>
          </StyledCard>
        </Grid>

        {/* 多选模式 */}
        <Grid item xs={12} md={6}>
          <StyledCard>
            <CardHeader
              title="🎯 多选模式"
              subheader="支持多项选择"
              titleTypographyProps={{ variant: 'h6', fontWeight: 500 }}
            />
            <CardContent>
              <FormControl fullWidth sx={{ mb: 3 }}>
                <FormLabel sx={{ mb: 1, fontWeight: 500 }}>multiple=true</FormLabel>
                <TreeSelect
                  multiple
                  value={multipleValue}
                  onChange={setMultipleValue}
                  treeData={treeData}
                  placeholder="请选择部门"
                  showSearch={showSearch}
                  disabled={disabled}
                  allowClear={allowClear}
                  treeLine={treeLine}
                  maxTagCount={2}
                  maxTagPlaceholder={(omittedValues) => `+${omittedValues.length} 更多`}
                  size="medium"
                />
              </FormControl>

              <Divider sx={{ my: 2 }} />

              <Stack direction="row" spacing={1} alignItems="center" flexWrap="wrap">
                <Typography variant="body2" color="text.secondary">
                  选中值 ({multipleValue.length}):
                </Typography>
                {multipleValue.length > 0 ? (
                  <ResultChip
                    label={JSON.stringify(multipleValue)}
                    variant="outlined"
                    size="small"
                    color="primary"
                  />
                ) : (
                  <ResultChip
                    label="未选择"
                    variant="outlined"
                    size="small"
                    color="default"
                  />
                )}
              </Stack>
            </CardContent>
          </StyledCard>
        </Grid>

        {/* 可勾选模式 */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              可勾选模式
            </Typography>

            <FormControl fullWidth sx={{ mb: 2 }}>
              <FormLabel>treeCheckable=true</FormLabel>
              <TreeSelect
                treeCheckable={treeCheckable}
                value={checkableValue}
                onChange={setCheckableValue}
                treeData={treeData}
                placeholder="请选择部门"
                showSearch={showSearch}
                disabled={disabled}
                allowClear={allowClear}
                treeLine={treeLine}
                showCheckedStrategy={TreeSelect.SHOW_PARENT}
              />
            </FormControl>

            <Typography variant="body2" color="text.secondary">
              选中值: {JSON.stringify(checkableValue)}
            </Typography>
          </Paper>
        </Grid>

        {/* 简单模式 */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              简单模式
            </Typography>

            <FormControl fullWidth sx={{ mb: 2 }}>
              <FormLabel>treeDataSimpleMode=true</FormLabel>
              <TreeSelect
                value={simpleValue}
                onChange={setSimpleValue}
                treeData={simpleTreeData}
                treeDataSimpleMode={{
                  id: 'id',
                  pId: 'pId',
                  rootPId: 0,
                }}
                placeholder="请选择部门"
                showSearch={showSearch}
                disabled={disabled}
                allowClear={allowClear}
                treeLine={treeLine}
              />
            </FormControl>

            <Typography variant="body2" color="text.secondary">
              选中值: {JSON.stringify(simpleValue)}
            </Typography>
          </Paper>
        </Grid>

        {/* 异步加载 */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              异步加载
            </Typography>

            <FormControl fullWidth sx={{ mb: 2 }}>
              <FormLabel>loadData 异步加载</FormLabel>
              <TreeSelect
                value={asyncValue}
                onChange={setAsyncValue}
                treeData={asyncTreeData}
                loadData={loadData}
                placeholder="请选择节点"
                showSearch={showSearch}
                disabled={disabled}
                allowClear={allowClear}
              />
            </FormControl>

            <Typography variant="body2" color="text.secondary">
              选中值: {JSON.stringify(asyncValue)}
            </Typography>
          </Paper>
        </Grid>

        {/* 大数据量测试 */}
        <Grid item xs={12}>
          <StyledCard>
            <CardHeader
              title="🚀 大数据量 & 虚拟滚动"
              subheader={`${nodeCount} 个节点的性能测试`}
              titleTypographyProps={{ variant: 'h6', fontWeight: 500 }}
            />
            <CardContent>
              <Alert severity="info" sx={{ mb: 3 }}>
                <AlertTitle>虚拟滚动说明</AlertTitle>
                当节点数量超过 100 个时，自动启用虚拟滚动以提升性能。
                当前数据量: <strong>{largeTreeData.reduce((total, dept) =>
                  total + 1 + dept.children.reduce((teamTotal, team) =>
                    teamTotal + 1 + team.children.length, 0), 0
                )} 个节点</strong>
              </Alert>

              <FormControl fullWidth sx={{ mb: 3 }}>
                <FormLabel sx={{ mb: 1, fontWeight: 500 }}>
                  大数据量测试 (虚拟滚动: {virtual ? '开启' : '关闭'})
                </FormLabel>
                <TreeSelect
                  multiple
                  value={largeDataValue}
                  onChange={setLargeDataValue}
                  treeData={largeTreeData}
                  placeholder="请选择（支持虚拟滚动）"
                  showSearch={showSearch}
                  disabled={disabled}
                  allowClear={allowClear}
                  treeLine={treeLine}
                  virtual={virtual}
                  listHeight={listHeight}
                  virtualThreshold={100}
                  virtualItemSize={40}
                  maxTagCount={3}
                  maxTagPlaceholder={(omitted) => `+${omitted.length} 更多`}
                  size="medium"
                />
              </FormControl>

              <Divider sx={{ my: 2 }} />

              <Stack direction="row" spacing={2} alignItems="center" flexWrap="wrap">
                <Typography variant="body2" color="text.secondary">
                  选中值 ({largeDataValue.length}):
                </Typography>
                {largeDataValue.length > 0 ? (
                  <ResultChip
                    label={`已选择 ${largeDataValue.length} 项`}
                    variant="outlined"
                    size="small"
                    color="primary"
                  />
                ) : (
                  <ResultChip
                    label="未选择"
                    variant="outlined"
                    size="small"
                    color="default"
                  />
                )}

                <Typography variant="body2" color="text.secondary">
                  虚拟滚动: {virtual ? '✅ 已启用' : '❌ 已禁用'}
                </Typography>

                <Typography variant="body2" color="text.secondary">
                  列表高度: {listHeight}px
                </Typography>
              </Stack>
            </CardContent>
          </StyledCard>
        </Grid>

        {/* API 演示 */}
        <Grid item xs={12}>
          <StyledCard>
            <CardHeader
              title="🎯 API 演示"
              subheader="完整的功能演示和测试"
              titleTypographyProps={{ variant: 'h6', fontWeight: 500 }}
            />
            <CardContent>
              <Stack direction="row" spacing={2} sx={{ mb: 3 }}>
                <Button
                  variant="contained"
                  onClick={() => console.log('当前所有值:', {
                    basicValue,
                    multipleValue,
                    checkableValue,
                    simpleValue,
                    asyncValue,
                    largeDataValue: `${largeDataValue.length} 项`
                  })}
                >
                  打印所有值
                </Button>
                <Button
                  variant="outlined"
                  onClick={() => {
                    setBasicValue(undefined);
                    setMultipleValue([]);
                    setCheckableValue([]);
                    setSimpleValue(undefined);
                    setAsyncValue(undefined);
                    setLargeDataValue([]);
                  }}
                >
                  清空所有值
                </Button>
                <Button
                  variant="outlined"
                  color="secondary"
                  onClick={generateLargeData}
                >
                  重新生成大数据
                </Button>
              </Stack>

              <Alert severity="success">
                <AlertTitle>✅ 功能特性</AlertTitle>
                <Typography variant="body2" component="div">
                  • 🎯 100% 兼容 Ant Design TreeSelect API<br/>
                  • 🎨 Material-UI 设计风格<br/>
                  • 🚀 虚拟滚动支持大数据量<br/>
                  • 🔍 实时搜索过滤<br/>
                  • ✅ 多选、单选、可勾选模式<br/>
                  • 📱 响应式设计<br/>
                  • 🎭 主题集成<br/>
                  • ⚡ 性能优化
                </Typography>
              </Alert>
            </CardContent>
          </StyledCard>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AntdTreeSelectExample;
