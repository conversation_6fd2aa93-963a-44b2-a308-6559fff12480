import { saveAs } from "file-saver";
import { toast } from "react-toastify";
import i18n from "i18next";
// 验证是否为blob格式
export function blobValidate(data) {
    return data.type !== "application/json";
}

import request from "@/utils/request";
import loadExpireReload from './validExpireReload';
export function download(url, params, filename, config) {
    return request({
        url: url,
        params: params,
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        responseType: "blob",
        ...config,
    })
        .then(async function (res) {
            const isBlob = blobValidate(res.data);
            if (isBlob) {
                const blob = new Blob([res.data]);
                saveAs(blob, filename);
                toast.success(i18n.t("common.common_op_success"));
            } else {
                const resText = await res.data.text();
                const rspObj = JSON.parse(resText);
                if (rspObj.code == 4) {
                    loadExpireReload(res, rspObj);
                } else if (rspObj.code == 1) {
                    toast.error(rspObj.message);
                } else {
                    toast.error(i18n.t("common.common_export_error"));
                }
            }
        })
        .catch(function (r) {
            console.error(r);
            Promise.reject(r);
        });
}
