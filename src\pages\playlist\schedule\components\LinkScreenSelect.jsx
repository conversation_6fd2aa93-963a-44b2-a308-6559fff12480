/* eslint-disable react/prop-types */
import React, { useEffect, useMemo, useState, useRef } from "react";
import MaterialReactTable from "material-react-table";
import {
  Typo<PERSON>,
  Tooltip,
  Grid,
  Stack,
  TextField,
  Button,
} from "@mui/material";
// api
import { listByPage } from "@/service/api/linkScreen";
import { tableI18n } from "@/utils/tableLang";
// i18n
import { useTranslation } from "react-i18next";

const LinkScreenSelect = (props) => {
  const { t } = useTranslation();
  const { pageSize = 10, line = undefined, column = undefined } = props;
  const [isError, setIsError] = useState(false);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  // 表格数据
  const [data, setData] = useState([]);
  // 分页的参数
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: pageSize,
  });
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);

  const [rowSelection, setRowSelection] = useState([]);
  const tableInstanceRef = useRef(null);
  //多选
  const setTableObject = (tableSelectRow) => {
    props.setTableObject(tableSelectRow);
  };
  const requestParams = useRef({ name: "", storeName: "", num: "" });
  // 构建参数
  const buildParams = () => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      ...requestParams.current,
    };

    return params;
  };
  // 获取数据
  const getTableData = async () => {
    if (!data.length) {
      setIsLoading(true);
    } else {
      setIsRefetching(true);
    }
    // 开启加载
    setIsLoading(true);
    setIsRefetching(true);
    await listByPage(buildParams())
      .then((res) => {
        // 设置数据
        setData(res.data.data);
        // 设置总记录数
        setRowCount(res.data.total);
        setIsLoading(false);
        setIsRefetching(false);
      })
      .catch((err) => {
        setIsError(true);
        setIsLoading(false);
        setIsRefetching(false);
      });
  };
  useEffect(() => {
    // 换取表单数据
    getTableData();
  }, [pagination.pageIndex, pagination.pageSize]);

  useEffect(() => {
    //设置表格中所有选中的行
    setTableObject(tableInstanceRef.current.getSelectedRowModel().rows);
  }, [rowSelection]);
  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "name",
        header: t("common.common_link_screen_name"),
        enableColumnActions: false,
        enableSorting: false,
      },

      {
        accessorKey: "num",
        header: t("common.common_link_screen_num"),
        enableColumnActions: false,
        enableSorting: false,
      },
      {
        accessorKey: "specs",
        header: t("common.common_link_screen_specifca"),
        enableColumnActions: false,
        enableSorting: false,
        Cell: ({ cell, row }) => {
          return (
            <Typography className="textSpace">
              {row.original.line}*{row.original.columns}
            </Typography>
          );
        },
      },
      {
        accessorKey: "storeName",
        header: t("ips.ips_store_name"),
        enableColumnActions: false,
        enableSorting: false,
      },
      {
        accessorKey: "address",
        header: t("common.common_area_name"),
        enableColumnActions: false,
        enableSorting: false,
        Cell: ({ cell, row }) => {
          return (
            <Tooltip title={row.original.address} placement="top">
              <Typography className="textSpace">
                {row.original.address}
              </Typography>
            </Tooltip>
          );
        },
      },
    ],
    []
  );
  // 查询
  const handleQuery = async () => {
    setPagination({
      pageSize: pageSize,
      pageIndex: 0
    })
    getTableData();
  };
  const resetQuery = async () => {
    requestParams.current = {
      name: "",
      storeName: "",
      num: "",
    };
    setNum("");
    setName("");
    setStoreName("");
    getTableData();
  };
  const [num, setNum] = React.useState("");
  const [name, setName] = React.useState("");
  const [storeName, setStoreName] = React.useState("");
  return (
    <>
      <div style={{ width: "100%", marginBottom: "10px" }}>
        <Grid
          container
          direction="row"
          justifyContent="flex-start"
          alignItems="center"
          spacing={2}
        >
          <Grid item xs={12} md={4} lg={2}>
            <TextField
              label={t("common.common_link_screen_name")}
              onChange={(e) => {
                requestParams.current.name = e.target.value;
                setName(e.target.value);
              }}
              size="small"
              name="name"
              value={name}
              type="text"
              fullWidth
              placeholder={t("ips.ips_enter_link_name")}
            />
          </Grid>
          <Grid item xs={12} md={4} lg={2}>
            <TextField
              label={t("ips.ips_store_name")}
              onChange={(e) => {
                requestParams.current.storeName = e.target.value;
                setStoreName(e.target.value);
              }}
              size="small"
              value={storeName}
              name="storeName"
              type="text"
              fullWidth
              placeholder={t("common.common_please_input_store_name")}
            />
          </Grid>
          <Grid item xs={12} md={4} lg={2}>
            <TextField
              label={t("common.common_link_screen_num")}
              onChange={(e) => {
                requestParams.current.num = e.target.value;
                setNum(e.target.value);
              }}
              size="small"
              value={num}
              name="storeName"
              type="number"
              fullWidth
              placeholder={t("common.common_link_screen_num")}
            />
          </Grid>
          <Grid item justifyContent="flex-end">
            <Stack
              direction="row"
              justifyContent="flex-start"
              alignItems="flex-start"
              spacing={2}
            >
              <Button
                disableElevation
                onClick={() => {
                  handleQuery();
                }}
                type="submit"
                variant="contained"
                size="small"
              >
                {t("common.common_table_query")}
              </Button>
              <Button
                disableElevation
                variant="outlined"
                onClick={resetQuery}
                color="info"
                size="small"
                sx={{
                  minWidth: '90px'
                }}
              >
                {t("common.common_op_reset")}
              </Button>
            </Stack>
          </Grid>
        </Grid>
      </div>
      <MaterialReactTable
        // table 状态
        state={{
          // 加载状态
          isLoading,
          // 分页参数
          pagination,
          // 重新拉取
          showProgressBars: isRefetching,
          showAlertBanner: isError,

          rowSelection,
        }}
        tableInstanceRef={tableInstanceRef}
        onRowSelectionChange={setRowSelection}
        // 解决列太多宽度太长问题
        enableColumnResizing
        enableTopToolbar={false}
        // 初始化状态
        initialState={{ columnVisibility: { createTime: false } }}
        muiToolbarAlertBannerProps={
          isError
            ? {
              color: "error",
              children: t("table.loading_error"),
            }
            : undefined
        }
        muiTableHeadRowProps={{
          sx: { backgroundColor: "#fafafa", boxShadow: "none" },
        }}
        muiTablePaperProps={{
          elevation: 0,
          sx: {
            borderRadius: "5px",
            // border: '1px solid #f0f0f0'
          },
        }}
        //行选中
        muiTableBodyRowProps={({ row }) => ({
          onClick: row.getToggleSelectedHandler(),
          sx: { cursor: "pointer" },
        })}
        // 开启多选
        enableRowSelection={(row) => {
          if (line === undefined || column === undefined) {
            return true;
          } else {
            return row.original.line === line && row.original.columns === column
          }
        }}
        // 列数
        rowCount={rowCount}
        // 固定头部
        enableStickyHeader
        // 处理表格高度
        muiTableContainerProps={{ sx: { maxHeight: "620px" } }}
        // 设置背景颜色
        muiTableBodyCellProps={{ sx: { backgroundColor: "white" } }}
        muiTableProps={{ sx: { backgroundColor: "white" } }}
        muiBottomToolbarProps={{ sx: { backgroundColor: "white" } }}
        muiTopToolbarProps={{ sx: { backgroundColor: "white" } }}
        // 分页回调函数
        onPaginationChange={setPagination}
        // 记得开启这些 tips:否则会出现useEffect 反作用函数发起多次请求点击下一页会出问题
        manualFiltering
        manualPagination
        manualSorting
        // 开启分页
        enablePagination
        // 列定义
        columns={columns}
        // 数据
        data={data}
        // 初始化国际化语言  --- todo: i18n key doc address: https://github.com/KevinVandy/material-react-table/blob/main/packages/material-react-table/src/_locales/en.ts
        localization={tableI18n}
        muiLinearProgressProps={({ isTopToolbar }) => ({
          sx: { display: isTopToolbar ? "block" : "none" },
        })}
        // 多选底部提示
        positionToolbarAlertBanner="none"
      />
    </>
  );
};

export default LinkScreenSelect;
