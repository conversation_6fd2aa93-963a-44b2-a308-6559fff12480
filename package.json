{"name": "cms-app", "private": true, "type": "module", "scripts": {"dev": "cross-env QIANKUN=1 vite", "dev:hmr": "cross-env QIANKUN=1 vite --config vite.dev.config.js", "dev:standalone": "cross-env QIANKUN=0 vite", "build": "cross-env NODE_ENV=production QIANKUN=1 vite build", "build:standalone": "cross-env NODE_ENV=production QIANKUN=0 vite build", "preview": "vite preview"}, "dependencies": {"@amir04lm26/react-modern-calendar-date-picker": "^1.0.1", "@ant-design/colors": "^6.0.0", "@ant-design/icons": "^4.7.0", "@aws-sdk/client-s3": "3.726.1", "@aws-sdk/lib-storage": "3.731.1", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@files-ui/react": "^1.0.8", "@googlemaps/js-api-loader": "^1.16.2", "@hookform/resolvers": "^3.3.1", "@iconify/react": "^5.2.1", "@material-ui/core": "^4.12.4", "@material-ui/icons": "^4.11.3", "@microsoft/fetch-event-source": "^2.0.1", "@mui/icons-material": "^5.10.16", "@mui/lab": "5.0.0-alpha.100", "@mui/material": "^5.10.6", "@mui/x-date-pickers": "^6.19.0", "@reduxjs/toolkit": "^1.8.5", "ahooks": "^3.7.2", "autosuggest-highlight": "^3.3.4", "axios": "^0.27.2", "cross-env": "^7.0.3", "crypto-js": "^4.2.0", "date-fns": "^2.29.3", "dayjs": "^1.11.13", "echarts": "^5.4.2", "echarts-extension-gmap": "^1.6.0", "echarts-liquidfill": "^3.1.0", "file-saver": "^2.0.5", "formik": "^2.2.9", "framer-motion": "^7.3.6", "google-map-react": "^2.2.1", "hash-wasm": "^4.12.0", "html2canvas": "^1.4.1", "i18next": "^22.0.6", "js-cookie": "^3.0.1", "jsencrypt": "^3.3.2", "lodash-es": "^4.17.21", "material-react-table": "^1.14.0", "moment": "^2.29.4", "prop-types": "^15.8.1", "psd.js": "^3.9.1", "react": "^18.2.0", "react-color": "^2.19.3", "react-copy-to-clipboard": "^5.1.0", "react-device-detect": "^2.2.2", "react-dom": "^18.2.0", "react-element-to-jsx-string": "^15.0.0", "react-hook-form": "^7.46.1", "react-i18next": "^12.0.0", "react-measure": "2.5.2", "react-modal-image": "^2.6.0", "react-moveable": "^0.56.0", "react-player": "2.11.0", "react-redux": "^8.0.5", "react-responsive-carousel": "^3.2.23", "react-router-dom": "^6.4.2", "react-toastify": "^11.0.3", "react-window": "^1.8.7", "redux": "^4.2.0", "simplebar-react": "^2.4.1", "spark-md5": "^3.0.2", "unplugin-auto-import": "^19.1.0", "vite-plugin-qiankun": "^1.0.15", "yup": "^0.32.11"}, "devDependencies": {"@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "@vitejs/plugin-react": "^2.1.0", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "dotenv": "^16.4.7", "less": "^4.1.3", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "unplugin-icons": "^22.1.0", "vite": "^5.4.10", "vite-plugin-css-injected-by-js": "^3.5.2", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-svgr": "^4.3.0"}}