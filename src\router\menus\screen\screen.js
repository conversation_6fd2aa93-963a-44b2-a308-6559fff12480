// 屏幕管理
const screenRoute = [
  {
    path: "/screen/screenList",
    component: () => import("@/pages/screen/screenList"),
    meta: {
      id: "516489808491577352",
      title: "数字标牌",
      i18n: "media_screen"
    },
  },
  {
    path: "/screen/screenList/add",
    component: () => import("@/pages/screen/screenList/components/addScreenForm"),
    meta: {
      title: "新增数字标牌",
      id:'00000000',
      i18n: "media_screen_add",
    },
  },
  {
    path: "/screen/screenList/editScreen",
    component: () => import("@/pages/screen/screenList/components/editScreen"),
    meta: {
      id:'00000000',
      title: "修改数字标牌",
      i18n: "media_screen_edit",
    },
  },
  {
    path: "/screen/screenList/detail",
    component: () => import("@/pages/screen/screenList/components/screenDetail"),
    meta: {
      id:'00000000',
      title: "屏幕详情",
      i18n: "media_screen_details",
    },
  },
];

export default screenRoute;
