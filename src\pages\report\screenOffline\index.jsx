import React, { useEffect, useMemo, useState, useRef } from "react";
import {
  listByPage,
  getScreenOfflineCharts,
} from "@/service/api/screenStatusApi";
import LineChart from "./lineChart";
import { But<PERSON>, Stack, Grid } from "@mui/material";
import MaterialReactTable from "material-react-table";
import MainCard from "@/components/MainCard";
import { useFormik } from "formik";
import { removeEmpty } from "@/utils/StringUtils";

import { getScreenListByStoreId } from "@/service/api/screen";
import { tableI18n } from "@/utils/tableLang";
import { getOutletList } from "@/service/api/L3Sevice";
// i18n
import { useConfirm } from "@/components/zkconfirm";
// 消息提示
import { toast } from "react-toastify";
import { useTranslation } from "react-i18next";
import ZKSelect from "@/components/ZKSelect";
import BasicDateRangePicker from "@/components/datetimePicker";
import { dateFormate } from "@/utils/zkUtils";
const ScreenOffline = () => {
  const requestParams = useRef(null);
  const [merchantListOption, setMerchantListOption] = useState([]);
  const [storeListOption, setStoreListOption] = useState([]);
  const [screenListOption, setScreenListOption] = useState([]);
  // const [screenOfflineCharts,setScreenOfflineCharts] = useState([]);
  const [chartDataValue, setChartDataValue] = useState([]);
  const [open, setOpen] = useState(false);
  const dateRangeRef = React.useRef(null);
  const confirm = useConfirm();
  const { t } = useTranslation();
  const [rowSelection, setRowSelection] = useState([]);
  const [isError, setIsError] = useState(false);
  // 显示搜索
  // const [showSearch, setShowSearch] = useState(true);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  // 表格数据
  const [data, setData] = useState([]);
  // 分页的参数
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);
  //获取零售商列表
  const getMerchant = () => {
    // getScreenListByStoreId("1").then((res) => {
    //   setMerchantListOption(res.data);
    // });
  };
  //获取门店列表
  const getStoreList = async (value) => {
    await getOutletList(value).then((res) => {
      setStoreListOption(res.data);
    });
  };

  useEffect(() => {
    getMerchant();
  }, []);
  useEffect(() => {
    getTableData();
  }, [pagination.pageIndex, pagination.pageSize]);
  // 重置查询
  const resetQuery = () => {
    // 重置表单
    queryFormik.resetForm();
    requestParams.current = null;
    console.log(dateRangeRef?.current);
    dateRangeRef?.current.restInputValue();
    setPagination({
      pageIndex: 0,
      pageSize: 10,
    });
    setStoreListOption([]);
    setScreenListOption([]);
    getTableData();
  };
  // 构建参数
  const buildParams = () => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      ...requestParams.current,
    };

    return params;
  };
  // 构建参数
  const buildChartsParams = () => {
    const params = {
      ...requestParams.current,
    };

    return params;
  };
  // 获取数据
  const getTableData = async () => {
    if (!data.length) {
      setIsLoading(true);
    } else {
      setIsRefetching(true);
    }

    await listByPage(buildParams())
      .then((res) => {
        // 设置数据
        setData(res.data.data);
        // 设置总记录数
        setRowCount(res.data.total);
        setIsLoading(false);
        setIsRefetching(false);
      })
      .catch((err) => {
        setIsError(true);
        setIsLoading(false);
        setIsRefetching(false);
      });
  };

  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "deviceName",
        header: t("ips.ips_screen_name"),
        enableColumnActions: false,
        enableSorting: false,
      },
      {
        accessorKey: "deviceSn",
        header: t("ips.ips_device_sn"),
        enableColumnActions: false,
        enableSorting: false,
      },
      {
        accessorKey: "clientName",
        header: t("ips.ips_store_client"),
        enableColumnActions: false,
        enableSorting: false,
      },
      {
        accessorKey: "storeName",
        header: t("ips.ips_store_name"),
        enableColumnActions: false,
        enableSorting: false,
      },
      {
        accessorKey: "offlineNum",
        header: t("ips.ips_offline_count"),
        enableColumnActions: false,
        enableSorting: false,
      },
      {
        accessorKey: "lastOnlineTime",
        header: t("ips.ips_last_online_time"),
        enableColumnActions: false,
        enableSorting: false,
      },
    ],
    []
  );
  // 查询表单
  const queryFormik = useFormik({
    initialValues: {
      merchantId: "",
      storeId: "",
      startTime: "",
      endTime: "",
    },
    onSubmit: async (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        // 拷贝value值
        const tempValue = { ...values };
        await removeEmpty(tempValue);
        // setRequestParams(tempValue);
        requestParams.current = tempValue;
        getTableData();
        // 查询table
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
  });
  return (
    <>
      <MainCard style={{ marginBottom: "10px" }}>
        <form noValidate onSubmit={queryFormik.handleSubmit}>
          <Grid
            container
            direction="row"
            justifyContent="flex-start"
            alignItems="center"
            spacing={2}>
            <Grid item xs={7} sm={5} md={4}>
              <BasicDateRangePicker
                ref={dateRangeRef}
                onChange={(value) => {
                  if (value?.startTime && value?.endTime) {
                    if (
                      value.startTime === "Invalid Date" ||
                      value.endTime === "Invalid Date"
                    ) {
                      queryFormik.setValues({
                        startTime: "",
                        endTime: "",
                      });
                      return;
                    }
                    queryFormik.setValues(value);
                  }
                }}
              />
            </Grid>
            <Grid item md={6} lg={2} xs={12}>
              <ZKSelect
                id="merchantId"
                size="small"
                name="merchantId"
                value={queryFormik.values.merchantId}
                options={merchantListOption}
                onClear={() => {
                  queryFormik.setFieldValue("merchantId", "");
                  queryFormik.setFieldValue("storeId", "");
                  // queryFormik.setFieldValue("screenId", "");
                  setStoreListOption([]);
                  setScreenListOption([]);
                }}
                onBlur={queryFormik.handleBlur}
                onChange={(e) => {
                  queryFormik.handleChange(e);
                  getStoreList(e.target.value);
                  queryFormik.setFieldValue("storeId", "");
                  // queryFormik.setFieldValue("screenId", "");
                }}
                type="text"
                menuWidth={200}
                placeholder={t("ips.ips_select_merchant")}
              />
            </Grid>
            <Grid item md={6} lg={2} xs={12}>
              <ZKSelect
                id="storeId"
                size="small"
                name="storeId"
                value={queryFormik.values.storeId}
                options={storeListOption}
                onClear={() => {
                  queryFormik.setFieldValue("storeId", "");
                  // queryFormik.setFieldValue("screenId", "");
                  setScreenListOption([]);
                }}
                onBlur={queryFormik.handleBlur}
                onChange={(e) => {
                  queryFormik.handleChange(e);
                  // getScreenList(e.target.value);
                  // queryFormik.setFieldValue("screenId", "");
                }}
                type="text"
                menuWidth={200}
                placeholder={t("ips.ips_select_a_outlet")}
              />
            </Grid>
            {/* <Grid item md={6} lg={2} xs={12}>
                  <ZKSelect
                      id="screenId"
                      label={"设备"}
                      size="small"
                      name="screenId"
                      value={queryFormik.values.screenId}
                      options={screenListOption}
                      onClear={() => {
                          queryFormik.setFieldValue("screenId", "");
                      }}
                      onBlur={queryFormik.handleBlur}
                      onChange={(e)=>{
                        queryFormik.handleChange(e);
                      }}
                      type="text"
                      menuWidth={200}
                      placeholder={t("common.common_select_device")}
                  />
              </Grid> */}
            <Grid
              item
              xs={5}
              sm={3}
              md={2}
              direction="row"
              justifyContent="flex-end">
              <Stack
                direction="row"
                justifyContent="flex-start"
                alignItems="flex-start"
                spacing={2}>
                <Button
                  disableElevation
                  type="submit"
                  variant="contained"
                  size="small">
                  {t("common.common_table_query")}
                </Button>

                <Button
                  variant="outlined"
                  onClick={resetQuery}
                  color="info"
                  size="small"
                  sx={{
                    minWidth: "90px",
                  }}>
                  {t("common.common_op_reset")}
                </Button>
              </Stack>
            </Grid>
          </Grid>
        </form>
      </MainCard>
      <MaterialReactTable
        // table 状态
        state={{
          // 加载状态
          isLoading,
          // 分页参数
          pagination,
          // 重新拉取
          showProgressBars: isRefetching,
          showAlertBanner: isError,

          rowSelection,
        }}
        renderToolbarInternalActions={({ table }) => <></>}
        muiTablePaperProps={{
          elevation: 0,
          sx: {
            borderRadius: "5px",
            border: "1px solid #f0f0f0",
          },
        }}
        muiTableHeadRowProps={{
          sx: { backgroundColor: "#fafafa", boxShadow: "none" },
        }}
        // 关闭过滤搜素
        enableColumnFilters={true}
        // 关闭排序
        enableSorting={false}
        // 布局方式
        layoutMode="grid"
        // 开启列对齐
        muiTableHeadCellProps={{
          sx: {
            "& .Mui-TableHeadCell-Content": {
              justifyContent: "space-between",
            },
          },
        }}
        // 解决列太多宽度太长问题
        enableColumnResizing
        // enablePinning
        // 初始化状态
        initialState={{ columnVisibility: { createTime: true } }}
        muiToolbarAlertBannerProps={
          isError
            ? {
                color: "error",
                children: "Error loading data",
              }
            : undefined
        }
        //行选中
        // onRowSelectionChange={setRowSelection}
        muiTableBodyRowProps={({ row }) => ({
          onClick: row.getToggleSelectedHandler(),
          sx: { cursor: "pointer" },
        })}
        // 开启多选
        // enableRowSelection
        // 列数
        rowCount={rowCount}
        // 固定头部
        enableStickyHeader
        // 处理表格高度
        muiTableContainerProps={{ sx: { maxHeight: "570px" } }}
        // 设置背景颜色
        muiTableBodyCellProps={{ sx: { backgroundColor: "white" } }}
        muiTableProps={{
          sx: { backgroundColor: "white", tableLayout: "fixed" },
        }}
        muiBottomToolbarProps={{ sx: { backgroundColor: "white" } }}
        muiTopToolbarProps={{ sx: { backgroundColor: "white" } }}
        // 分页回调函数
        onPaginationChange={setPagination}
        // 记得开启这些 tips:否则会出现useEffect 反作用函数发起多次请求点击下一页会出问题
        manualFiltering
        manualPagination
        manualSorting
        // 开启分页
        // enablePagination
        // 列定义
        columns={columns}
        // 数据
        data={data}
        // 初始化国际化语言
        localization={tableI18n}
        // 自定义表头按钮
        // renderTopToolbarCustomActions={({ table }) => {}}
        // 多选底部提示
        // positionToolbarAlertBanner="none"
        // 开启action操作
        // enableRowActions
        // action操作位置
        // positionActionsColumn="last"
        // displayColumnDefOptions={{
        //     "mrt-row-actions": {
        //         header: t("common.common_relatedOp"), //change header text
        //         size: 120, //make actions column wider
        //     },
        // }}
        renderRowActions={(row, index, table) => {}}
      />
      {/* <LineChart ChartDataValue={chartDataValue}></LineChart> */}
    </>
  );
};
export default ScreenOffline;
