/**
 * 节目排期管理api接口定义
 */
import request from '@/utils/request';
// import download from '@/utils/request';
import { download } from '@/utils/request';
const baseProfixURI = '/sd/v1/schedule';
import { uuid } from '@/utils/zkUtils';
/**
 *  分页查询 节目排期列表
 * <AUTHOR>
 * @date 2022-12-14 15:29
 */
export const listByPage = (params) => {
    return request({
        url: `${baseProfixURI}/page`,
        method: 'get',
        params: params
    });
};

/**
 *  新增排期
 * <AUTHOR>
 * @date 2023-01-10 18:09
 */
export const saveCommonSchedule = (params) => {
    return request({
        url: `${baseProfixURI}/common`,
        method: 'POST',
        data: params
    });
};

/**
 *  新增排期
 * <AUTHOR>
 * @date 2023-01-10 18:09
 */
export const saveLinkSchedule = (params) => {
    return request({
        url: `${baseProfixURI}/link`,
        method: 'POST',
        data: params
    });
};

/**
 *  删除排期
 * @param ids string 类型id用逗号隔开
 * <AUTHOR>
 * @date 2022-12-23 11:20
 */
export const removeSchedule = (params) => {
    return request({
        url: `${baseProfixURI}`,
        method: 'delete',
        data: params
    });
};

/**
 * 判断排期是否存在
 * @param ids 排期ids
 * <AUTHOR>
 * @returns
 */
export const isSchedule = (ids) => {
    return request({
        url: `${baseProfixURI}/exist/${ids}`,
        method: 'get'
    });
};

/**
 *  排期导出
 * <AUTHOR>
 * @date 2022-12-14 15:29
 */
export const exportSchedule = () => {
    return download(`${baseProfixURI}/export`, '', uuid() + '.xlsx');
};

/**
 *  获取设备当前播放
 * <AUTHOR>
 * @date 2022-12-14 15:29
 */
export const getCurrentPlaying = (id) => {
    return request({
        url: `${baseProfixURI}/getCurrentPlaying/${id}`,
        method: 'get'
    });
};

/**
 *  获取排期设置的基本信息
 * <AUTHOR>
 * @date 2022-12-14 15:29
 */
export const getScheduleInfo = (id) => {
    return request({
        url: `${baseProfixURI}/${id}`,
        method: 'get'
    });
};

/**
 *  更新单屏播放计划
 * <AUTHOR>
 * @date 2022-12-14 15:29
 */
export const updateCommonScheduleInfo = (params) => {
    return request({
        url: `${baseProfixURI}/common`,
        method: 'PUT',
        data: params
    });
};

/**
 *  更新联屏播放计划
 * <AUTHOR>
 * @date 2022-12-14 15:29
 */
export const updateLinkScheduleInfo = (params) => {
    return request({
        url: `${baseProfixURI}/link`,
        method: 'PUT',
        data: params
    });
};

/**
 *  发布普通排期
 * <AUTHOR>
 * @date 2022-12-14 15:29
 */
export const publishCommonSchedule = (id) => {
    return request({
        url: `${baseProfixURI}/publishCommonSchedule/${id}`,
        method: 'POST'
    });
};

/**
 *  发布联屏播放计划
 * <AUTHOR>
 * @date 2022-12-14 15:29
 */
export const publishLinkSchedule = (id) => {
    return request({
        url: `${baseProfixURI}/publishLinkSchedule/${id}`,
        method: 'POST'
    });
};
export const onClickPublishSchedule = (id) => {
    return request({
        url: `${baseProfixURI}/publish/oneClick/${id}`,
        method: 'POST'
    });
};

export const copySchedule = (data) => {
    return request({
        url: `${baseProfixURI}/copy`,
        method: 'POST',
        data: data
    })
}