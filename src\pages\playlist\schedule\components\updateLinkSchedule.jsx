/* eslint-disable react/prop-types */
import React, { useState, useRef, forwardRef, useEffect } from "react";
import {
  Grid,
  Paper,
  Button,
  InputLabel,
  OutlinedInput,
  Stack,
  InputAdornment,
  InputBase,
  FormHelperText,
  Typography,
  Container,
} from "@mui/material";
import LoadingButton from "@mui/lab/LoadingButton";
import "./link.less";
import IconButton from "@mui/material/IconButton";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import { toast } from "react-toastify";
import dayjs from "dayjs";
import Popover from "@mui/material/Popover";
import TimePickert from "@/components/zktime";
import * as Yup from "yup";
import { useFormik } from "formik";
// api
import AdvancedLinkDialog from "./AdvanceLinkModal";

import {
  getScheduleInfo,
  updateLinkScheduleInfo,
} from "@/service/api/schedule";

import "@amir04lm26/react-modern-calendar-date-picker/lib/DatePicker.css";
import { Calendar, utils } from "@amir04lm26/react-modern-calendar-date-picker";
// i18n
import { useTranslation } from "react-i18next";
import { getCalendarLocales } from "@/utils/calendarLocales";
import MainCard from "@/components/MainCard";
import { useNavigate, useSearchParams } from "react-router-dom";
import moment from "moment";
import AddLinkPlayList from "./AddLinkPlayList";
import TimeButtonSelect from "./TimeButtonSelect";
import { makeStyles } from "@material-ui/core";
import AddCircleIcon from "@mui/icons-material/AddCircle";
import DateSvg from "./DateSvg";

export default function UpdateLinkSchedule(props) {
  const useStyles = makeStyles({
    hidden: { display: "none" },
    show: {},
    big: { fontSize: "3rem" },
  });
  const [advancedOpen, setAdvancedOpen] = useState(false);
  const advancedRef = useRef();
  const classes = useStyles();
  const [search, setSearch] = useSearchParams();
  const addLinkPlayListRef = useRef(null);
  const timeButtonSelectRef = useRef(null);
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);

  const [anchorEl, setAnchorEl] = useState(null);
  //联屏组下的屏幕数量(选择设备后需要设置该值)
  const [screenNumber, setScreenNumber] = useState(1);
  const [xsValue, setXsValue] = useState(3);
  const [heightValue, setHeightValue] = useState(160);
  const [resultData, setResultData] = useState({});
  const [advancedData, setAdvancedData] = useState([]);
  const [tableObject, setTableObject] = useState([
    {
      original: {
        columns: 2,
        line: 2,
      },
    },
  ]);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };
  const open = Boolean(anchorEl);

  //清单对象
  const inventoryObject = { playListId: "", playListName: "" };

  //屏幕组清单对象
  const screenGroupPlayListObject = {
    startTime: "",
    stopTime: "",
    areaScreenPlayList: [],
  };
  //获取排期数据
  const handleGetScheduleInfo = async () => {
    await getScheduleInfo(search.get("id")).then((res) => {
      const result = res.data;
      setFormData(result);
      setResultData(result);
      setScreenNumber(result.screenGroupPlayList[0].areaScreenPlayList.length);
      const monthWeekDayMap =
        timeButtonSelectRef.current.getDaysWeeksAndMonthsDate(
          result.startDate,
          result.stopDate
        );
      timeButtonSelectRef.current.handleSelectedDate(result);
      timeButtonSelectRef.current.handleChangeDaysAble(
        result.playWeeksNum,
        result.playMonths,
        monthWeekDayMap
      );
      setTableObject([
        {
          original: {
            columns: result.columns,
            line: result.line,
          },
        },
      ]);
      handleCalculateXS(
        parseInt(parseInt(result.columns)),
        parseInt(parseInt(result.line))
      );
    });
  };
  useEffect(() => {
    handleGetScheduleInfo();
  }, []);

  // 表单赋值
  const setFormData = (data) => {
    scheduleForm.setValues(
      {
        id: data.id,
        name: data.name,
        inventoryList: data.inventoryList,
        startDate: data.startDate,
        stopDate: data.stopDate,
        playWeeks: data.playWeeksNum,
        playDays: data.playDays,
        playMonths: data.playMonths,
        screenIds: data.screenIds,
        screenGroupPlayList: data.screenGroupPlayList,
        status: data.status,
      },
      true
    );
  };

  //表单
  const scheduleForm = useFormik({
    initialValues: {
      name: "",
      startDate: "",
      stopDate: "",
      screenGroupIds: "",
      //集合内部的集合对象对应为区域设备对象
      screenGroupPlayList: [],
    },
    onSubmit: async (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        handelTableSubmit(values);
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
    validationSchema: Yup.object().shape({
      name: Yup.string().required(t("common.common_input_scheduling_name")),
      startDate: Yup.date().required(t("common.common_select_dateInterval")),
      stopDate: Yup.date().required(t("common.common_select_dateInterval")),
    }),
  });

  const handelTableSubmit = async () => {
    if (timeButtonSelectRef.current.monthsSelectList.length === 0) {
      toast.error(t("ips.ips_select_month"));
      return;
    }
    if (timeButtonSelectRef.current.daysSelectList.length === 0) {
      toast.error(t("ips.ips_select_date"));
      return;
    }
    //清除冗余播放清单对象
    let newGroupPlayList = scheduleForm.values.screenGroupPlayList.filter(
      (playListItem) => {
        let count = 0;
        playListItem.areaScreenPlayList.forEach((item, index) => {
          if (item.playListId === "" && item.playListName === "") {
            //用户未选的清单对象数量
            count++;
          }
        });
        return !(
          playListItem.startTime === "" &&
          playListItem.stopTime === "" &&
          count != 0
        );
      }
    );
    if (newGroupPlayList.length === 0) {
      toast.error(t("ips.ips_please_select_playlist"));
      return;
    }
    let errorPlayLists = newGroupPlayList.filter((newItem) => {
      let count = 0;
      newItem.areaScreenPlayList.forEach((item, index) => {
        if (item.playListId === "" && item.playListName === "") {
          //未进行配置的清单对象个数
          count++;
        }
      });
      return newItem.startTime === "" || newItem.stopTime === "" || count != 0;
    });
    if (errorPlayLists.length > 0) {
      toast.error(t("ips.ips_playlist_exist"));
      return;
    }
    //表单重新赋值，清除清单名称和时间都未选择的清单对象
    scheduleForm.values.screenGroupPlayList = newGroupPlayList;
    if (loading) {
      toast.error(t("ips.ips_schdule_publishing"));
    }

    let params = scheduleForm.values;
    // 表单赋值
    params.playWeeks = timeButtonSelectRef.current.weeksSelectList;
    params.playDays = timeButtonSelectRef.current.daysSelectList;
    params.playMonths = timeButtonSelectRef.current.monthsSelectList;
    //分辨率
    params.resolution = "1920x1080";

    setLoading(true);
    //提交表单
    await updateLinkScheduleInfo(params)
      .then((res) => {
        toast.success(res.message);
        setLoading(false);
        scheduleForm.setStatus({ success: true });
        scheduleForm.setSubmitting(true);
        //跳转到投放排期界面
        navigate(-1);
      })
      .catch((error) => {
        setLoading(false);
      });
  };

  //计算出每个设备所在元素的宽度，设备所在父级元素的高度
  const handleCalculateXS = (cloumnNum, lineNum) => {
    // const newCloumnNum = cloumnNum > 4 ? 4 : cloumnNum;
    const xs = 12 / cloumnNum;
    setXsValue(xs);
    //计算总高度
    // const expandRow = cloumnNum > 4 ? 1 : 0;
    // const newRowNum = lineNum + expandRow;
    // setHeightValue(newRowNum * 160);
  };
  const defaultRange = {
    from: null,
    to: null,
  };

  const [selectedDayRange, setSelectedDayRange] = useState(defaultRange);

  //设置联屏清单id和名称
  const setPlayListFormValues = (
    playListId,
    playListName,
    playListIndex,
    screenIndex
  ) => {
    scheduleForm.values.screenGroupPlayList[playListIndex].areaScreenPlayList[
      screenIndex
    ].playListId = playListId;
    scheduleForm.values.screenGroupPlayList[playListIndex].areaScreenPlayList[
      screenIndex
    ].playListName = playListName;
    //刷新表单
    scheduleForm.setFieldValue();
  };
  const handleRestPlayListFormField = (values) => {
    return new Promise((reslove, reject) => {
      scheduleForm.setFieldValue("screenGroupPlayList", values);
      reslove();
    });
  };
  //联屏清单选择组件
  function LinkPlayListSelect(props) {
    const playListIndex = props.playListIndex;
    //每个设备的宽度，设备所在父级元素的高度
    const { xsValue, heightValue } = props;
    const [anchorTime, setAnchorTime] = useState(null);
    const handleTimeClick = (event) => {
      setAnchorTime(event.currentTarget);
    };
    const handleTimeClose = () => {
      setAnchorTime(null);
    };
    const openTimeRange = Boolean(anchorTime);
    const timeRangeId = openTimeRange ? "time-popover" : undefined;
    const selectPlayListObject = (screenIndex) => {
      addLinkPlayListRef.current.handleOpen();
      addLinkPlayListRef.current.setPlayListIndex(playListIndex);
      addLinkPlayListRef.current.setScreenIndex(screenIndex);
    };

    return (
      <>
        <Grid item xs={6}>
          <Stack spacing={1}>
            <InputLabel htmlFor="infor-firstName">
              {t("ips.ips_playlist_play_time")}
              <i style={{ color: "red" }}>*</i>
            </InputLabel>
            <OutlinedInput
              onClick={handleTimeClick}
              value={
                scheduleForm.values.screenGroupPlayList[playListIndex].stopTime
              }
              id="infor-firstName"
              type="text"
              name="stopTime"
              sx={{ width: "100%" }}
              placeholder={t("common.common_endTime")}
              startAdornment={
                <InputAdornment position="start" sx={{ width: "120%" }}>
                  <InputBase
                    endAdornment={
                      <Stack
                        direction="row"
                        justifyContent="flex-end"
                        alignItems="center">
                        <DateSvg />
                      </Stack>
                    }
                    startAdornment={
                      <InputAdornment position="start">
                        <AccessTimeIcon />
                      </InputAdornment>
                    }
                    value={
                      scheduleForm.values.screenGroupPlayList[playListIndex]
                        .startTime
                    }
                    name="startTime"
                    // id="quick-area"
                    type="text"
                    placeholder={t("common.common_startTime")}
                    sx={{ width: "100%" }}
                  />
                </InputAdornment>
              }
            />
            <Popover
              id={timeRangeId}
              open={openTimeRange}
              anchorEl={anchorTime}
              onClose={handleTimeClose}
              anchorOrigin={{
                vertical: "bottom",
                horizontal: "left",
              }}>
              <TimePickert
                endTimeText={t("common.common_endTime")}
                startTimeText={t("common.common_startTime")}
                confirmText={t("common.common_edit_ok")}
                onChange={(time) => {
                  //时间段是否存在重叠标识
                  let flag = false;
                  //已选播放清单
                  let timePeriodList = [
                    ...scheduleForm.values.screenGroupPlayList,
                  ];
                  //开始时间
                  let startTime = new Date("2022-06-01T" + time.startTime);
                  //结束时间
                  let endTime = new Date("2022-06-01T" + time.endTime);

                  if (startTime.getTime() >= endTime.getTime()) {
                    toast.error(t("ips.ips_starttime_greater_endtime"));
                    return;
                  }

                  timePeriodList.forEach((timePeriod, index) => {
                    //排除自身与其它时间段比较
                    if (playListIndex != index) {
                      //排除未选择的播放时间段
                      if (
                        timePeriod.startTime != "" &&
                        timePeriod.stopTime != ""
                      ) {
                        //该时间段开始时间
                        let thisStartTime = new Date(
                          "2022-06-01T" + timePeriod.startTime
                        );
                        //该时间段结束时间
                        let thisStopTime = new Date(
                          "2022-06-01T" + timePeriod.stopTime
                        );
                        //交集判断结果
                        let result =
                          (startTime.getTime() < thisStartTime.getTime() &&
                            thisStartTime.getTime() < endTime.getTime()) ||
                          (startTime.getTime() < thisStopTime.getTime() &&
                            thisStopTime.getTime() < endTime.getTime()) ||
                          (startTime.getTime() >= thisStartTime.getTime() &&
                            endTime.getTime() <= thisStopTime.getTime()) ||
                          (startTime.getTime() <= thisStartTime.getTime() &&
                            endTime.getTime() >= thisStopTime.getTime());

                        if (result) {
                          //时间段存在交集
                          flag = true;
                          return;
                        }
                      }
                    }
                  });

                  if (flag) {
                    toast.error(t("ips.ips_overlap_play_time"));
                    return;
                  } else {
                    scheduleForm.values.screenGroupPlayList[
                      playListIndex
                    ].startTime = time.startTime;
                    scheduleForm.values.screenGroupPlayList[
                      playListIndex
                    ].stopTime = time.endTime;
                    // 刷新表单验证
                    scheduleForm.setFieldValue();
                    handleTimeClose();
                  }
                }}
              />
            </Popover>
          </Stack>
        </Grid>
        <Grid
          container
          spacing={1}
          justifyContent={
            // scheduleForm.values.screenGroupPlayList[playListIndex]
            //   .areaScreenPlayList.length > 4
            //   ? "flex-start"
            //   : "space-evenly"
            "flex-start"
          }
          // alignItems="center"
          sx={{
            marginTop: "10px",
            // height: `${heightValue}px`,
            width: "900px",
            maxHeight: "270px",
            padding: "20px 0px 30px 0px",
            backgroundColor: "#f9f9f9",
            overflow: "auto",
            // overflowX: "auto",
            // overflowY: "auto",
            marginBottom: "10px",
          }}>
          {scheduleForm.values.screenGroupPlayList[playListIndex] &&
            scheduleForm.values.screenGroupPlayList[
              playListIndex
            ].areaScreenPlayList.map(
              (screenGroupPlayListScreenObject, screenIndex) => {
                return (
                  <Grid
                    item
                    container={resultData.columns > 4 ? true : false}
                    xs={xsValue}
                    key={screenIndex}
                    sx={{ paddingRight: "8px" }}
                    align="center">
                    <Paper
                      className={"box"}
                      elevation={0}
                      style={{
                        border: "1px solid rgba(120, 189, 66,0.7)",
                        height: "100px",
                        width: "200px",
                        boxShadow: "0px 5px 10px 2px rgba(0, 0, 0, 0.1)",
                      }}>
                      {scheduleForm.values.screenGroupPlayList[playListIndex]
                        .areaScreenPlayList[screenIndex].playListName !==
                        "" && (
                        <>
                          <div className="ovrly"></div>
                          <div className="btn">
                            <Button
                              className="bt1"
                              onClick={() => {
                                selectPlayListObject(screenIndex);
                                // setPlayListFormValues('', '', playListIndex, screenIndex);
                              }}
                              style={{ fontSize: "14px" }}>
                              {t("ips.ips_reset")}
                            </Button>
                          </div>
                        </>
                      )}
                      <Stack
                        spacing={1}
                        sx={{ height: "100%", width: "100%" }}
                        justifyContent="center"
                        alignItems="center">
                        {scheduleForm.values.screenGroupPlayList[playListIndex]
                          .areaScreenPlayList[screenIndex].playListName ==
                        "" ? (
                          <>
                            <IconButton
                              color="primary"
                              aria-label="add playList"
                              className={`${
                                scheduleForm.values.screenGroupPlayList[
                                  playListIndex
                                ].areaScreenPlayList[screenIndex]
                                  .playListName == ""
                                  ? classes.show
                                  : classes.hidden
                              }`}
                              variant="contained"
                              onClick={() => {
                                selectPlayListObject(screenIndex);
                              }}>
                              <AddCircleIcon className={classes.big} />
                            </IconButton>
                            <Typography
                              sx={{
                                color: "rgba(0, 0, 0,.3)",
                                lineHeight: "22px",
                                fontSize: "14px",
                              }}>
                              {t("ips.ips_add_playlist")}
                              <span style={{ color: "red" }}>*</span>
                            </Typography>
                          </>
                        ) : (
                          <Typography
                            sx={{
                              color: "rgba(0, 0, 0, 0.3)",
                              wordBreak: "break-all",
                              padding: "5px 10px 5px 10px",
                            }}>
                            {
                              scheduleForm.values.screenGroupPlayList[
                                playListIndex
                              ].areaScreenPlayList[screenIndex].playListName
                            }
                          </Typography>
                        )}
                      </Stack>
                    </Paper>
                  </Grid>
                );
              }
            )}
        </Grid>
      </>
    );
  }
  const handleOpenAdvanced = () => {
    const playLists = scheduleForm.values?.screenGroupPlayList;
    setTimeout(() => {
      setAdvancedData(playLists);
      advancedRef.current?.handeOpenChange();
      setAdvancedOpen(true);
    }, 0);
  };
  return (
    <MainCard
      title={t("server.server_scheduling_link_upload")}
      border={false}
      contentSX={{ p: 0 }}>
      <form noValidate onSubmit={scheduleForm.handleSubmit}>
        <Container maxWidth="md" sx={{ padding: 3 }}>
          <Grid container spacing={1} direction="row">
            <Grid item xs={5.5}>
              <Stack spacing={1}>
                <InputLabel htmlFor="schedule-name" sx={{ width: 120 }}>
                  {t("ips.ips_scheduling")}
                  <i style={{ color: "red" }}>*</i>
                </InputLabel>
                <OutlinedInput
                  fullWidth={true}
                  id="schedule-name"
                  type="text"
                  name="name"
                  value={scheduleForm.values.name}
                  onBlur={scheduleForm.handleBlur}
                  onChange={scheduleForm.handleChange}
                  placeholder={t("common.common_input_scheduling_name")}
                  error={Boolean(
                    scheduleForm.touched.name && scheduleForm.errors.name
                  )}
                />
                {scheduleForm.touched.name && scheduleForm.errors.name && (
                  <FormHelperText
                    error
                    id="standard-weight-helper-text-name-schedule">
                    {scheduleForm.errors.name}
                  </FormHelperText>
                )}
              </Stack>
            </Grid>
            <Grid item xs={5.5}>
              <Stack spacing={1}>
                <InputLabel htmlFor="infor-firstName" sx={{ width: 250 }}>
                  {t("ips.ips_startDate_endDate")}
                  <i style={{ color: "red" }}>*</i>
                </InputLabel>
                <OutlinedInput
                  readOnly
                  autoFocus={false}
                  onClick={handleClick}
                  autoComplete="off"
                  name="startDate"
                  type="text"
                  onBlur={scheduleForm.handleBlur}
                  onChange={scheduleForm.handleChange}
                  value={scheduleForm.values.stopDate}
                  fullWidth
                  error={Boolean(
                    scheduleForm.touched.startDate &&
                      scheduleForm.errors.startDate
                  )}
                  startAdornment={
                    <InputAdornment position="start" sx={{ width: "120%" }}>
                      <InputBase
                        readOnly
                        autoComplete="off"
                        autoFocus={false}
                        name="stopDate"
                        startAdornment={
                          <InputAdornment position="start">
                            <CalendarMonthIcon />
                          </InputAdornment>
                        }
                        onClick={handleClick}
                        onChange={scheduleForm.handleChange}
                        value={scheduleForm.values.startDate}
                        onBlur={scheduleForm.handleBlur}
                        error={Boolean(
                          scheduleForm.touched.stopDate &&
                            scheduleForm.errors.stopDate
                        )}
                        endAdornment={
                          <Stack
                            direction="row"
                            justifyContent="flex-end"
                            sx={{ paddingRight: "3px" }}
                            alignItems="center">
                            <DateSvg />
                          </Stack>
                        }
                        sx={{
                          width: "100%",
                        }}
                        type="text"
                        placeholder={t("common.common_startTime")}
                      />
                    </InputAdornment>
                  }
                  sx={{
                    width: "100%",
                  }}
                  placeholder={t("common.common_endTime")}
                />
                {(scheduleForm.touched.startDate &&
                  scheduleForm.errors.startDate) ||
                (scheduleForm.touched.stopDate &&
                  scheduleForm.errors.stopDate) ? (
                  <FormHelperText
                    error
                    id="standard-weight-helper-text-startDate-schedule">
                    {scheduleForm.errors.startDate}
                  </FormHelperText>
                ) : null}
                <Popover
                  elevation={3}
                  id="date"
                  open={open}
                  anchorEl={anchorEl}
                  onClose={handleClose}
                  anchorOrigin={{
                    vertical: "bottom",
                    horizontal: "center",
                  }}
                  transformOrigin={{
                    vertical: "top",
                    horizontal: "center",
                  }}>
                  <Calendar
                    colorPrimary="#7ac143" // added this
                    colorPrimaryLight="rgba(122, 193, 67, 0.1)"
                    minimumDate={utils().getToday()}
                    locale={getCalendarLocales()}
                    value={selectedDayRange}
                    onChange={(date) => {
                      setSelectedDayRange(date);
                      const startDate = date.from
                        ? dayjs(
                            date.from.year +
                              `${
                                date.from.month <= 9
                                  ? "0" + date.from.month
                                  : date.from.month
                              }` +
                              date.from.day
                          ).format("YYYY-MM-DD")
                        : "";
                      const endDate = date.to
                        ? dayjs(
                            date.to.year +
                              `${
                                date.to.month <= 9
                                  ? "0" + date.to.month
                                  : date.to.month
                              }` +
                              date.to.day
                          ).format("YYYY-MM-DD")
                        : "";
                      //计算开始时间和结束时间跨度不能超过一整年
                      let timeSpan = moment(new Date(startDate)).diff(
                        moment(new Date(endDate)),
                        "years"
                      );

                      if (timeSpan >= 1) {
                        toast.error(t("ips.ips_exceeds_one_year"));
                        return;
                      } else {
                        scheduleForm.setFieldValue("startDate", startDate);
                        scheduleForm.setFieldValue("stopDate", endDate);
                        //将排期播放时间传给时间按钮选择组件进行计算
                        timeButtonSelectRef.current.getDaysWeeksAndMonthsDate(
                          startDate,
                          endDate
                        );
                      }
                    }}
                  />
                </Popover>
              </Stack>
            </Grid>
            <Grid
              item
              xs={11}
              style={{
                display: "flex",
                justifyContent: "flex-end",
              }}>
              <Button
                variant="contained"
                onClick={() => {
                  handleOpenAdvanced();
                }}>
                {t("common.common_advanced_menu_title")}
              </Button>
            </Grid>
            <Grid item sx={{ marginBottom: "10px" }} xs={11}>
              <Stack spacing={2}>
                {scheduleForm.values.screenGroupPlayList.map(
                  (playListObject, playListIndex) => (
                    <LinkPlayListSelect
                      key={playListIndex}
                      playListIndex={playListIndex}
                      xsValue={xsValue}
                      heightValue={heightValue}
                    />
                  )
                )}
              </Stack>
            </Grid>
            <Grid
              item
              xs={12}
              container
              direction="row"
              justifyContent="flex-start">
              <InputLabel sx={{ color: "grey", fontSize: "0.8rem" }}>
                {t("common.common_time_not_repeat_annotation")}
              </InputLabel>
            </Grid>
            <Grid
              xs={11}
              container
              direction="row"
              justifyContent="flex-end"
              alignItems="center">
              <Stack direction="row" spacing={1}>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={() => {
                    let newList = scheduleForm.values.screenGroupPlayList;
                    //对象中的areaScreenPlayList集合中对象的个数对应屏幕组中的设备数量
                    let newAreaScreenObject = [];

                    for (let i = 0; i < screenNumber; i++) {
                      let newInventoryObject = { ...inventoryObject };
                      newAreaScreenObject.push(newInventoryObject);
                    }
                    let newscreenGroupPlayListObject = {
                      ...screenGroupPlayListObject,
                    };
                    newscreenGroupPlayListObject.areaScreenPlayList =
                      newAreaScreenObject;
                    newList.push(newscreenGroupPlayListObject);
                    scheduleForm.setValues({
                      ...scheduleForm.values,
                      screenGroupPlayList: newList,
                    });
                  }}>
                  {t("ips.ips_add_new_playlist")}
                </Button>
                {scheduleForm.values.screenGroupPlayList.length > 1 && (
                  <Button
                    variant="contained"
                    color="error"
                    onClick={() => {
                      if (scheduleForm.values.screenGroupPlayList.length <= 1) {
                        return;
                      }
                      let newList = scheduleForm.values.screenGroupPlayList;
                      //删除最后一个清单对象
                      newList.pop();
                      scheduleForm.setValues({
                        ...scheduleForm.values,
                        inventoryList: newList,
                      });
                    }}>
                    {t("common.common_op_del")}
                  </Button>
                )}
              </Stack>
            </Grid>
            <Grid item xs={12}>
              <TimeButtonSelect ref={timeButtonSelectRef} />
            </Grid>
            <Grid item xs={12}>
              <Stack
                direction="row"
                justifyContent="center"
                alignItems="center"
                spacing={2}>
                <Button
                  size="large"
                  variant="contained"
                  color="secondary"
                  onClick={() => {
                    navigate(-1);
                  }}>
                  {t("common.common_op_return")}
                </Button>
                <LoadingButton
                  loading={loading}
                  variant="contained"
                  size="large"
                  type="submit"
                  color="primary">
                  {scheduleForm.values.status === "0"
                    ? t("common.common_op_publish")
                    : t("common.common_edit_save")}
                </LoadingButton>
              </Stack>
            </Grid>
          </Grid>
        </Container>
      </form>
      <AdvancedLinkDialog
        ref={advancedRef}
        open={advancedOpen}
        advancedData={advancedData}
        onClose={() => {
          setAdvancedOpen(false);
          advancedRef.current?.handelReset();
        }}
        onImport={handleRestPlayListFormField}
        tableObject={tableObject}
        inventoryObject={inventoryObject}
      />
      <AddLinkPlayList
        ref={addLinkPlayListRef}
        setPlayListFormValues={setPlayListFormValues}
        playListType={"1"}
      />
    </MainCard>
  );
}
