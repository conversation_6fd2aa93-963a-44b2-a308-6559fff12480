/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/prop-types */
import React, {
  useImperativeHandle,
  forwardRef,
  useState,
  useRef,
  useEffect,
  useMemo,
} from "react";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  Container,
  Stack,
  IconButton,
} from "@mui/material";
import "react-responsive-carousel/lib/styles/carousel.min.css";
import CloseIcon from "@mui/icons-material/Close";
import "@/pages/playlist/components/img.css";
import { t } from "i18next";

const MediaCarousel = forwardRef((props, ref) => {
  const [open, setOpen] = useState(false);
  //设置的播放时长
  const [duration, setDuration] = useState(0);
  const [items, setItems] = useState([]);
  const [reload, setReload] = useState([1]);
  const resourceRef = useRef(null);
  //当前定时器
  const [intervalNow, setIntervalNow] = useState(null);
  //当前播放资源的下标
  const [currentIndex, setCurrentIndex] = useState(0);

  const handleClose = () => {
    setOpen(false);
    if (intervalNow) {
      clearInterval(intervalNow);
    }
  };
  const handleOpen = (playListData) => {
    setItems(playListData);
    setOpen(true);
    let defaultIndex = 0;
    if (playListData.length > 1) {
      defaultIndex = Math.floor(Math.random() * playListData.length);
    }
    setCurrentIndex(defaultIndex);
    setDuration(playListData[defaultIndex].duration);
  };
  useImperativeHandle(ref, () => ({
    handleOpen,
  }));

  //播放资源的下标修改
  const onChange = (index) => {
    const nextIndex = index >= items.length ? 0 : index;
    setCurrentIndex(nextIndex);
    setDuration(items[nextIndex].duration);
  };

  //视频、音频播放结束触发方法
  const handleVideoEnded = (index) => {
    const video = resourceRef.current;
    //视频时长
    const intDuration = parseInt(video.duration);
    const currentDuration = items[currentIndex].duration;
    //设置的播放时长大于视频本身的时长
    if (currentDuration > intDuration) {
      //视频重置到未播放状态
      video.currentTime = 0;
      video.play();
      return;
    }

    if (items[currentIndex].type != "image" && items.length === 1) {
      const video = resourceRef.current;
      video.currentTime = 0;
      video.play();
    }
    const nextIndex = index >= items.length ? 0 : index;
    setDuration(items[nextIndex].duration);
    setCurrentIndex(nextIndex);
  };

  useEffect(() => {
    if (items.length > 0) {
      const interval = setInterval(() => {
        if (items[currentIndex].type != "image" && items.length === 1) {
          const video = resourceRef.current;
          video.currentTime = 0;
          video.play();
        }
        onChange(currentIndex + 1);
      }, duration * 1000);
      setIntervalNow(interval);
      return () => clearInterval(interval);
    }
  }, [items, currentIndex]);

  const memoizdCarousel = useMemo(
    () => (
      <>
        {items[currentIndex] ? (
          items[currentIndex].type === "image" ? (
            <img
              key={items[currentIndex].id}
              src={items[currentIndex].downloadUrl}
              alt={`Image ${items[currentIndex].id}`}
              className="preview-img"
            />
          ) : items[currentIndex].type === "media" ? (
            <video
              ref={resourceRef}
              autoPlay
              controls
              key={items[currentIndex].id}
              onEnded={() => handleVideoEnded(currentIndex + 1)}
              style={{ width: "100%", height: "500px" }}
            >
              <source
                src={items[currentIndex].downloadUrl}
                type="video/mp4"
              ></source>
            </video>
          ) : (
            <>
              <svg
                data-v-56e751f7=""
                t="1592532095422"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="2906"
                width="500"
                height="400"
                className="icon music-icon svg-img"
              >
                <path
                  data-v-56e751f7=""
                  d="M742.3 100.3l-25.6 44.3c126.2 73 204.7 208.9 204.7 354.6 0 225.7-183.6 409.3-409.3 409.3S102.8 724.8 102.8 499.1c0-145.7 78.4-281.5 204.7-354.6l-25.6-44.3c-142 82.1-230.2 235-230.2 398.8 0 253.9 206.6 460.5 460.5 460.5S972.6 753 972.6 499.1c0-163.9-88.2-316.7-230.3-398.8z"
                  fill="#1296db"
                  p-id="2907"
                ></path>{" "}
                <path
                  data-v-56e751f7=""
                  d="M464.2 437l-25.6-44.3c-45.3 26.2-73.5 75-73.5 127.3 0 81 65.9 147 147 147s147-65.9 147-147v-6.3L451.2 115.4h164V64.2H366.8l241 461.8c-3.1 50.1-44.8 89.9-95.6 89.9-52.8 0-95.8-43-95.8-95.8-0.1-34.1 18.2-66 47.8-83.1z"
                  fill="#1296db"
                  p-id="2908"
                ></path>
              </svg>
              <audio
                ref={resourceRef}
                key={items[currentIndex].id}
                src={items[currentIndex].downloadUrl}
                className="preview-audio"
                autoPlay
                onEnded={() => handleVideoEnded(currentIndex + 1)}
                controls
              ></audio>
            </>
          )
        ) : null}
      </>
    ),
    [currentIndex, items]
  );

  return (
    <>
      <Dialog
        maxWidth="lg"
        open={open}
        fullWidth={true}
        onClose={handleClose}
        aria-describedby="alert-dialog-slide-description"
      >
        <DialogTitle>
          {t("ips.ips_playing")}
          <IconButton
            aria-label="close"
            onClick={handleClose}
            sx={{
              position: "absolute",
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500],
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Container maxWidth="lg">
            <Stack sx={{ width: "100%", height: 500 }}>{memoizdCarousel}</Stack>
          </Container>
        </DialogContent>
      </Dialog>
    </>
  );
});

export default MediaCarousel;
