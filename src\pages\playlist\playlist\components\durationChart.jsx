/* eslint-disable react/prop-types */
/* eslint-disable react-hooks/rules-of-hooks */
import React, {
  useMemo,
  useState,
  useEffect,
  useRef,
  forwardRef,
  useImperativeHandle,
} from "react";
import MainCard from "@/components/MainCard";
import { Typography, Stack } from "@mui/material";
import { useTranslation } from "react-i18next";
import Dot from "@/components/@extended/Dot";
import * as echarts from "echarts";

// 图表页
const DurationChart = forwardRef((props, ref) => {
  const chartRef = useRef();
  const { t } = useTranslation();
  const [durationValues, setDurationValues] = useState([]);
  const [names, setNames] = useState([]);
  const [values, setValues] = useState([]);
  const [types, setTypes] = useState([]);
  const removeLayout = ()=>{
    setDurationValues(durationValues.filter((item,index) => item[3] !== 'layout'));
  }

  const removeNotLayout = ()=>{
    setDurationValues(durationValues.filter((item,index) => item[3] === 'layout'));
  }
  useImperativeHandle(ref, () => ({
    setDurationValues,
    removeLayout,
    removeNotLayout,
    durationValues,
  }));

  useEffect(() => {
    const data = [...durationValues];
    // 处理duration
    const tempNames = [];
    const tempValues = [];
    const tempTypes = [];
    durationValues.forEach((duration) => {
      // 名称
      tempNames.push(duration[0]);
      // 数据
      tempValues.push({
        value: duration[1], //值
        index: duration[2], //下标
        type: duration[3], // 素材类型
      });
      // 类型
      tempTypes.push(duration[2]);
    });
    setNames(tempNames);
    setValues(tempValues);
    setTypes(tempTypes);
  }, [durationValues]);

  const options = {
    // title: {
    //   text: "清单时长预览",
    //   left: "left",
    // },
    //置图例数据
    legend: {
      show: true,
      position: "right",
      valueAnimation: true,
      data: ["media", "image"],
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
      formatter: function (params) {
        var tar = params[0];
        return (
          tar.name + "<br/>" + t("ips.ips_duration_is") + "：" + tar.value + "s"
        );
      },
    },
    xAxis: {
      type: "value",
      name: t("ips.ips_program_durationss"),
    },
    yAxis: {
      type: "category",
      data: names,
      axisLine: {
        show: false, //不显示坐标轴线
      },
      axisLabel: {
        show: true,
        formatter: function (value, index) {
          // 在这里根据索引返回自定义的文字
          return "#" + (index + 1);
        },
      },
      splitLine: {
        //决定是否显示坐标中网格
        show: false,
      },
    },
    series: {
      type: "bar",
      data: values,
      datasetIndex: 1,
      encode: {
        x: "value", // 假设素材类型存储在 'x' 字段
        y: "type", // 假设柱状图数据存储在 'y' 字段
      },
      itemStyle: {
        label: {
          show: false,
          color: "#fff",
          // position: 'top',
          formatter: function (params) {
            console.log(params, "params");
            return `t("common.common_name_as") :${params.name} ,t("common.common_times_as") :${params.value} s;`;
          },
        },
        color: (params) => {

          if (params?.data?.type === "image") {
            return "#d5ebc4";
          } else if(params?.data?.type === "layout"){
            return "#7ac143"
          }else{
            return "#43a0c1";
          }
          // //注意，如果颜色太少的话，后面颜色不会自动循环，最好多定义几个颜色
          // var colorList = ["#7ac143"];
          // return "#7ac143";
        },
      },
    },
  };
  useEffect(() => {
    let chart = null;
    const initChart = () => {
      // 初始化Echarts图表
      chart = echarts.init(chartRef.current, null, { renderer: "svg" });

      // 设置初始大小
      chart.resize();

      // 监听窗口大小变化，自动调整图表大小
      window.addEventListener("resize", handleResize);
      chart.setOption(options);
    };
    const handleResize = () => {
      chart.resize();
    };
    // 在组件挂载时进行初始化
    initChart();
    // 在组件卸载时移除事件监听
    return () => {
      window.removeEventListener("resize", handleResize);
      chart.dispose();
    };
  }, [names]);
 
  return (
    <>
      <MainCard elevation={3} style={{ width: "100%", height: "420px" }}>
        {/* {() => {
          console.log(props);
        }} */}
        <Typography component="h3" variant="h5" align="left">
          {t("ips.ips_preview_time")}
        </Typography>
        <Stack direction="row" spacing={1}>
          <Stack direction="row" spacing={1} alignItems="center">
            <Dot color="#d5ebc4" />
            <Typography component="p">{t("common.common_picture")}</Typography>
          </Stack>
          <Stack direction="row" spacing={1} alignItems="center">
            <Dot color="#43a0c1" />
            <Typography component="p">{t("common.common_video")}</Typography>
          </Stack>
          <Stack direction="row" spacing={1} alignItems="center">
            <Dot color="#7ac143" />
            <Typography component="p">{t("common.common_layout")}</Typography>
          </Stack>
        </Stack>
        <div style={{ width: "100%", height: "400px" }} ref={chartRef}></div>
      </MainCard>
    </>
  );
});
export default DurationChart;
