import React from 'react'
import  { forwardRef, useState } from 'react';
import { Dialog, DialogContent, DialogTitle, IconButton, Stack, Button, Grid, LinearProgress } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
// i18n
import { useTranslation } from 'react-i18next';
const ProgressBarDiglog = forwardRef((props, ref) => {
    React.useImperativeHandle(ref, () => ({
        handelProgressBarOpen,
        handelProgressBarClose
    }));
    const { t } = useTranslation();

    //进度条弹窗
    const [progressBarOpen, setProgressBarOpen] = useState(false);
    //打开弹窗
    const handelProgressBarOpen = () => {
        setProgressBarOpen(true);
    };
    //关闭弹窗
    const handelProgressBarClose = () => {
        setProgressBarOpen(false);
    };

    // eslint-disable-next-line react/prop-types
    const progressBar = props.progressBar;
    return (
        <Dialog
            open={progressBarOpen}
            maxWidth="md"
            fullWidth={true}
            onClose={handelProgressBarClose}
            aria-describedby="alert-dialog-slide-description"
        >
            <DialogTitle>
                {t("common.common_upload_process")}
                <IconButton
                    aria-label="close"
                    onClick={handelProgressBarClose}
                    sx={{
                        position: 'absolute',
                        right: 8,
                        top: 8,
                        color: (theme) => theme.palette.grey[500]
                    }}
                >
                    <CloseIcon />
                </IconButton>
            </DialogTitle>
            <DialogContent>
                <Grid container alignItems="center" justify="center">
                    <Grid item xs={12}>
                        {t("common.common_uploaded_wait")}
                    </Grid>
                    <Grid item xs={11}>
                        <LinearProgress variant="determinate" value={progressBar}></LinearProgress>
                        {/* <div>Uploaded Percentage : {Math.round(progressBar, 2)}%</div> Math.round(progressBar, 2) */}
                    </Grid>
                    <Grid item xs={1}>
                        {progressBar + '%'}
                    </Grid>
                </Grid>
            </DialogContent>
            <Stack direction="row" justifyContent="flex-end" sx={{ margin: '15px' }}>
                <Button
                    variant="contained"
                    color="primary"
                    onClick={() => {
                        handelProgressBarClose();
                    }}
                >
                    {t("common.common_Background_upload")}
                </Button>
            </Stack>
        </Dialog>
    );
});

export default ProgressBarDiglog;
