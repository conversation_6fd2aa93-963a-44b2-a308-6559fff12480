import React from 'react'
import { Grid } from "@mui/material";
import RenderCommon from "./RenderCommon";
import { useRef, useEffect, useState } from "react";
import { isBase64} from '@/utils/zkUtils'
const RenderPhotos = (props) => {
  let info = props.info;

  const [showIndex, setShowIndex] = useState(0);
  const countRef = useRef(0);
  const [imgList, setImgList] = useState([]);
  const [timeOutObj, setTimeOutObj] = useState(null);

  const showImage = () => {
    if (timeOutObj) {
      clearTimeout(timeOutObj);
    }

    let duration = imgList[countRef.current].duration;
    try {
      duration = parseInt(duration);
    } catch (e) {
      duration = 10;
    }
    let timeTemp = setTimeout(() => {
      let result = countRef.current + 1;
      if (result >= imgList.length) {
        countRef.current = 0;
        setShowIndex(0);
      } else {
        countRef.current = result;
        setShowIndex(result);
      }
      showImage();
    }, duration * 1000);

    setTimeOutObj(timeTemp);
  };

  useEffect(() => {
    if (imgList.length > 1) {
      if (countRef.current >= imgList.length) {
        countRef.current = 0;
        setShowIndex(0);
      }
      showImage();
    } else {
      countRef.current = 0;
      setShowIndex(0);
      if (timeOutObj) {
        clearTimeout(timeOutObj);
      }
    }
  }, [imgList]);

  useEffect(() => {
    setImgList([...props.info.imgList]);
  }, [props.info]);

  useEffect(() => {
    return () => {
      if (timeOutObj) {
        clearTimeout(timeOutObj);
      }
    };
  }, []);

  return (
    <RenderCommon {...props}>
      {imgList.map((item, index) => {
        return (
          <Grid
            key={item.imgSrc}
            className={`animated ${info.anim}`}
            sx={{
              width: "100%",
              height: "100%",
              backgroundColor: info.bgColor,
              overflow: "hidden",
              opacity: info.transparency,
              display: index === showIndex ? "block" : "none",
            }}
          >
            {isBase64(imgList[showIndex]?.imgSrc) && (
              <img
                crossOrigin="anonymous"
                style={{
                  height: "100%",
                  width: "100%",
                }}
                src={imgList[showIndex]?.imgSrc}
              ></img>
            )}

            {!isBase64(imgList[showIndex]?.imgSrc) && (
              <img
                crossOrigin="anonymous"
                style={{
                  height: "100%",
                  width: "100%",
                }}
                src={
                 imgList[showIndex]?.imgSrc +
                  "?_aa=" +
                  new Date().getTime()
                }
              ></img>
            )}
          </Grid>
        );
      })}
    </RenderCommon>
  );
};

export default RenderPhotos;
